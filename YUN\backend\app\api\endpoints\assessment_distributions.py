from typing import Any, Dict, List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session

from app.db.base_session import get_db
from app.models.user import User
from app.models.distribution import AssessmentDistribution
from app.models.assessment import Assessment
from app.api import deps
from app.core.auth import get_current_active_user_custom
from typing import Dict, Any, Optional

router = APIRouter()

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_assessment_distributions(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    status: Optional[str] = Query(None, description="分发状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的评估分发列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的评估分发"
        )

    # 构建查询，join assessment表获取评估信息
    query = db.query(AssessmentDistribution, Assessment).join(
        Assessment, AssessmentDistribution.assessment_id == Assessment.id
    ).filter(AssessmentDistribution.custom_id == custom_id)

    # 应用过滤条件
    if status:
        query = query.filter(AssessmentDistribution.status == status)
    if start_date:
        query = query.filter(AssessmentDistribution.created_at >= start_date)
    if end_date:
        query = query.filter(AssessmentDistribution.created_at <= end_date)

    # 获取总数
    total = query.count()

    # 应用分页并获取结果
    distributions = query.order_by(AssessmentDistribution.created_at.desc()).offset(skip).limit(limit).all()

    # 格式化返回数据
    result_data = []
    for distribution, assessment in distributions:
        result_data.append({
            "id": distribution.id,
            "assessment_id": distribution.assessment_id,
            "custom_id": distribution.custom_id,
            "distributor_custom_id": distribution.distributor_custom_id,
            "status": distribution.status,
            "due_date": distribution.due_date,
            "completed_at": distribution.completed_at,
            "message": distribution.message,
            "created_at": distribution.created_at,
            "updated_at": distribution.updated_at,
            "assessment": {
                "id": assessment.id,
                "title": assessment.name,
                "name": assessment.name,
                "description": assessment.notes,
                "notes": assessment.notes,
                "assessment_type": assessment.assessment_type,
                "version": assessment.version
            }
        })

    return {
        "status": "success",
        "data": result_data,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.delete("/{distribution_id}", response_model=Dict[str, Any])
def delete_assessment_distribution(
    *,
    db: Session = Depends(get_db),
    distribution_id: int = Path(..., description="分发记录ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    删除评估分发记录
    """
    # 查找分发记录
    distribution = db.query(AssessmentDistribution).filter(
        AssessmentDistribution.id == distribution_id
    ).first()
    
    if not distribution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到分发记录ID: {distribution_id}"
        )
    
    # 权限校验
    if current_user.custom_id != distribution.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此分发记录"
        )
    
    # 删除分发记录
    db.delete(distribution)
    db.commit()
    
    return {
        "status": "success",
        "message": "分发记录删除成功"
    }