2025-07-07 09:59:52,879 - INFO - 项目根目录: C:\Users\<USER>\Desktop\health-Trea\mobile
2025-07-07 09:59:52,889 - INFO - 开始扫描和修复 API 导入问题...
2025-07-07 09:59:52,890 - INFO - 处理目录: screens
2025-07-07 09:59:52,895 - INFO - 在 screens\assessment_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,899 - INFO - ✓ 已修复文件: screens\assessment_screen.py
2025-07-07 09:59:52,901 - INFO - 在 screens\companion_service_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,905 - INFO - ✓ 已修复文件: screens\companion_service_screen.py
2025-07-07 09:59:52,910 - INFO - 在 screens\consultant_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,912 - INFO - ✓ 已修复文件: screens\consultant_screen.py
2025-07-07 09:59:52,914 - INFO - 在 screens\document_list_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,928 - INFO - ✓ 已修复文件: screens\document_list_screen.py
2025-07-07 09:59:52,943 - INFO - 在 screens\register_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,946 - INFO - ✓ 已修复文件: screens\register_screen.py
2025-07-07 09:59:52,949 - INFO - 在 screens\survey_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,952 - INFO - ✓ 已修复文件: screens\survey_screen.py
2025-07-07 09:59:52,957 - INFO - 在 screens\voice_triage_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,959 - INFO - ✓ 已修复文件: screens\voice_triage_screen.py
2025-07-07 09:59:52,960 - INFO - 处理目录: screens_bak
2025-07-07 09:59:52,963 - INFO - 在 screens_bak\assessment_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,965 - INFO - ✓ 已修复文件: screens_bak\assessment_screen.py
2025-07-07 09:59:52,968 - INFO - 在 screens_bak\companion_service_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,972 - INFO - ✓ 已修复文件: screens_bak\companion_service_screen.py
2025-07-07 09:59:52,973 - INFO - 在 screens_bak\consultant_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,975 - INFO - ✓ 已修复文件: screens_bak\consultant_screen.py
2025-07-07 09:59:52,977 - INFO - 在 screens_bak\document_list_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:52,979 - INFO - ✓ 已修复文件: screens_bak\document_list_screen.py
2025-07-07 09:59:52,999 - INFO - 在 screens_bak\register_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:53,005 - INFO - ✓ 已修复文件: screens_bak\register_screen.py
2025-07-07 09:59:53,011 - INFO - 在 screens_bak\survey_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:53,014 - INFO - ✓ 已修复文件: screens_bak\survey_screen.py
2025-07-07 09:59:53,017 - INFO - 在 screens_bak\voice_triage_screen.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:53,021 - INFO - ✓ 已修复文件: screens_bak\voice_triage_screen.py
2025-07-07 09:59:53,023 - INFO - 处理目录: widgets
2025-07-07 09:59:53,024 - INFO - 在 widgets\camera_view.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:53,026 - INFO - ✓ 已修复文件: widgets\camera_view.py
2025-07-07 09:59:53,027 - INFO - 处理目录: utils
2025-07-07 09:59:53,083 - INFO - 在 utils\health_data_aggregator.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:53,141 - INFO - ✓ 已修复文件: utils\health_data_aggregator.py
2025-07-07 09:59:53,212 - INFO - 在 utils\triage_manager.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (3次)
2025-07-07 09:59:53,264 - INFO - ✓ 已修复文件: utils\triage_manager.py
2025-07-07 09:59:53,323 - INFO - 处理目录: api
2025-07-07 09:59:53,348 - INFO - 处理目录: .
2025-07-07 09:59:53,390 - INFO - 在 migration_tool.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (2次)
2025-07-07 09:59:53,393 - INFO - ✓ 已修复文件: migration_tool.py
2025-07-07 09:59:53,395 - INFO - 在 simple_test.py 中替换了模式: from api\.api_client_optimized import -> from api.api_client import (1次)
2025-07-07 09:59:53,400 - INFO - ✓ 已修复文件: simple_test.py
2025-07-07 09:59:53,444 - INFO - 处理完成: 共检查 186 个文件，修复了 19 个文件
2025-07-07 09:59:53,449 - INFO - 批量修复完成！总共修复了 19 个文件
2025-07-07 09:59:53,450 - INFO - 修复报告:
2025-07-07 09:59:53,450 - INFO -   total_fixed_files: 19
2025-07-07 09:59:53,451 - INFO -   total_replacements: 22
2025-07-07 09:59:53,451 - INFO -   directories_processed: ['screens', 'screens_bak', 'widgets', 'utils', 'api', '.']
2025-07-07 09:59:53,503 - INFO -   timestamp: C:\Users\<USER>\Desktop\health-Trea\mobile
2025-07-07 09:59:53,508 - INFO -   status: completed
2025-07-07 09:59:53,518 - INFO - 修复报告已保存到: C:\Users\<USER>\Desktop\health-Trea\mobile\api_import_fix_report.json
2025-07-07 09:59:53,523 - INFO - API导入修复完成！
