#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端计分API - 支持原始答案提交和后端计分

功能：
1. 接收移动端提交的原始答案
2. 根据模板配置进行自动计分
3. 计算维度分数和总分
4. 生成分析报告
5. 保存结果到数据库
"""

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from typing import Dict, List, Any, Optional
import json
from datetime import datetime

from app.api import deps
from app.api.deps import get_db
from app.models.user import User
from app.models.assessment import (
    Assessment, AssessmentTemplate, AssessmentTemplateQuestion,
    AssessmentResponse
)
from app.models.distribution import AssessmentDistribution
from app.models.questionnaire import (
    Questionnaire, QuestionnaireTemplate, QuestionnaireTemplateQuestion,
    QuestionnaireResponse
)
from app.models.distribution import QuestionnaireDistribution
from app.models.result import AssessmentResult, QuestionnaireResult

router = APIRouter()

class MobileScoringService:
    """
    移动端计分服务类
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_assessment_score(self, template: AssessmentTemplate, raw_answers: List[Dict]) -> Dict[str, Any]:
        """
        计算评估量表分数
        
        Args:
            template: 评估量表模板
            raw_answers: 原始答案列表 [{"question_id": "1", "answer": "选项A"}, ...]
        
        Returns:
            计分结果字典
        """
        try:
            # 获取模板问题和计分规则
            template_questions = self.db.query(AssessmentTemplateQuestion).filter(
                AssessmentTemplateQuestion.template_id == template.id
            ).all()
            
            question_map = {str(q.question_id): q for q in template_questions}
            
            # 计算总分和维度分数
            total_score = 0
            dimension_scores = {}
            scored_answers = []
            
            # 初始化维度分数
            if template.dimensions:
                dimensions = template.dimensions if isinstance(template.dimensions, list) else json.loads(template.dimensions)
                for dim in dimensions:
                    dimension_scores[dim["key"]] = {
                        "name": dim["name"],
                        "score": 0,
                        "max_score": dim.get("max_score", 0),
                        "weight": dim.get("weight", 1.0)
                    }
            
            # 逐题计分
            for answer_data in raw_answers:
                question_id = str(answer_data.get("question_id", ""))
                raw_answer = answer_data.get("answer")
                
                if question_id not in question_map:
                    continue
                
                question = question_map[question_id]
                score = self._calculate_question_score(question, raw_answer)
                
                scored_answer = {
                    "question_id": question_id,
                    "answer": raw_answer,
                    "score": score
                }
                scored_answers.append(scored_answer)
                total_score += score
                
                # 计算维度分数
                if question.dimension and question.dimension in dimension_scores:
                    dimension_scores[question.dimension]["score"] += score
            
            # 应用维度权重
            weighted_total = 0
            for dim_key, dim_data in dimension_scores.items():
                weight = dim_data.get("weight", 1.0)
                weighted_score = dim_data["score"] * weight
                dimension_scores[dim_key]["weighted_score"] = weighted_score
                weighted_total += weighted_score
            
            # 生成结果分析
            result_analysis = self._analyze_assessment_result(
                template, total_score, dimension_scores, scored_answers
            )
            
            return {
                "total_score": total_score,
                "weighted_total": weighted_total,
                "max_score": template.max_score,
                "dimension_scores": dimension_scores,
                "scored_answers": scored_answers,
                "result_analysis": result_analysis
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"计分过程中发生错误: {str(e)}"
            )
    
    def calculate_questionnaire_score(self, template: QuestionnaireTemplate, raw_answers: List[Dict]) -> Dict[str, Any]:
        """
        计算问卷分数
        
        Args:
            template: 问卷模板
            raw_answers: 原始答案列表
        
        Returns:
            计分结果字典
        """
        try:
            # 获取模板问题和计分规则
            template_questions = self.db.query(QuestionnaireTemplateQuestion).filter(
                QuestionnaireTemplateQuestion.template_id == template.id
            ).all()
            
            question_map = {str(q.question_id): q for q in template_questions}
            
            # 计算分数
            total_score = 0
            scored_answers = []
            completion_count = 0
            
            # 逐题计分
            for answer_data in raw_answers:
                question_id = str(answer_data.get("question_id", ""))
                raw_answer = answer_data.get("answer")
                
                if question_id not in question_map:
                    continue
                
                question = question_map[question_id]
                score = self._calculate_question_score(question, raw_answer)
                
                scored_answer = {
                    "question_id": question_id,
                    "answer": raw_answer,
                    "score": score
                }
                scored_answers.append(scored_answer)
                
                if raw_answer is not None and raw_answer != "":
                    completion_count += 1
                    total_score += score
            
            # 计算完成率
            completion_rate = (completion_count / len(template_questions) * 100) if len(template_questions) > 0 else 0
            
            # 生成结果分析
            result_analysis = self._analyze_questionnaire_result(
                template, total_score, completion_rate, scored_answers
            )
            
            return {
                "total_score": total_score,
                "max_score": template.max_score or len(template_questions),
                "completion_rate": completion_rate,
                "completion_count": completion_count,
                "total_questions": len(template_questions),
                "scored_answers": scored_answers,
                "result_analysis": result_analysis
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"问卷计分过程中发生错误: {str(e)}"
            )
    
    def _calculate_question_score(self, question, raw_answer) -> float:
        """
        计算单题分数
        
        Args:
            question: 问题对象
            raw_answer: 原始答案
        
        Returns:
            分数
        """
        if raw_answer is None or raw_answer == "":
            return 0
        
        # 获取计分规则
        scoring_rules = question.scoring
        if not scoring_rules:
            return 0
        
        if isinstance(scoring_rules, str):
            try:
                scoring_rules = json.loads(scoring_rules)
            except:
                return 0
        
        # 根据题型计分
        if question.question_type == "single_choice":
            # 单选题计分
            option_scores = scoring_rules.get("option_scores", {})
            return option_scores.get(str(raw_answer), 0)
        
        elif question.question_type == "multiple_choice":
            # 多选题计分
            if not isinstance(raw_answer, list):
                return 0
            
            option_scores = scoring_rules.get("option_scores", {})
            total_score = 0
            for option in raw_answer:
                total_score += option_scores.get(str(option), 0)
            return total_score
        
        elif question.question_type in ["number", "text"]:
            # 数值题或文本题
            if scoring_rules.get("scoring_type") == "range":
                ranges = scoring_rules.get("ranges", [])
                try:
                    value = float(raw_answer)
                    for range_rule in ranges:
                        min_val = range_rule.get("min", float("-inf"))
                        max_val = range_rule.get("max", float("inf"))
                        if min_val <= value <= max_val:
                            return range_rule.get("score", 0)
                except:
                    pass
            elif scoring_rules.get("scoring_type") == "fixed":
                return scoring_rules.get("score", 0)
        
        return 0
    
    def _analyze_assessment_result(self, template: AssessmentTemplate, total_score: float, 
                                 dimension_scores: Dict, scored_answers: List[Dict]) -> Dict[str, Any]:
        """
        分析评估结果
        """
        result_category = "未分类"
        conclusion = "评估完成"
        recommendations = []
        
        # 使用模板的结果范围配置
        if template.result_ranges:
            result_ranges = template.result_ranges
            if isinstance(result_ranges, str):
                try:
                    result_ranges = json.loads(result_ranges)
                except:
                    result_ranges = []
            
            # 根据得分确定结果分类
            for range_config in result_ranges:
                min_score = range_config.get("min_score", 0)
                max_score = range_config.get("max_score", float('inf'))
                
                if min_score <= total_score <= max_score:
                    result_category = range_config.get("category", "未分类")
                    conclusion = range_config.get("description", "评估完成")
                    recommendations = range_config.get("recommendations", [])
                    break
        else:
            # 默认分析逻辑
            if template.max_score and template.max_score > 0:
                percentage = (total_score / template.max_score) * 100
                if percentage >= 80:
                    result_category = "优秀"
                    conclusion = "评估结果优秀，各项指标表现良好"
                elif percentage >= 60:
                    result_category = "良好"
                    conclusion = "评估结果良好，大部分指标正常"
                elif percentage >= 40:
                    result_category = "一般"
                    conclusion = "评估结果一般，建议关注相关指标"
                else:
                    result_category = "需要关注"
                    conclusion = "评估结果显示需要特别关注，建议咨询专业人士"
        
        return {
            "result_category": result_category,
            "conclusion": conclusion,
            "recommendations": recommendations,
            "percentage": (total_score / template.max_score * 100) if template.max_score else 0
        }
    
    def _analyze_questionnaire_result(self, template: QuestionnaireTemplate, total_score: float,
                                    completion_rate: float, scored_answers: List[Dict]) -> Dict[str, Any]:
        """
        分析问卷结果
        """
        result_category = "已完成"
        conclusion = f"问卷调查完成，完成率: {completion_rate:.1f}%"
        recommendations = []
        
        # 根据问卷类型进行分析
        questionnaire_type = getattr(template, 'questionnaire_type', 'general')
        
        if questionnaire_type == "health":
            result_category = "健康状况调查"
            if completion_rate >= 90:
                recommendations.append("调查完整度良好，数据可靠")
            else:
                recommendations.append("建议完善未完成的问题以获得更准确的分析")
        elif questionnaire_type == "satisfaction":
            result_category = "满意度调查"
            recommendations.append("感谢您的反馈，我们将持续改进服务质量")
        elif questionnaire_type == "psqi":
            result_category = "睡眠质量评估"
            recommendations.append("建议根据评估结果关注睡眠质量")
        
        return {
            "result_category": result_category,
            "conclusion": conclusion,
            "recommendations": recommendations,
            "completion_rate": completion_rate
        }

# API端点定义
@router.post("/assessments/{assessment_id}/submit-raw", response_model=Dict[str, Any])
def submit_assessment_raw_answers(
    assessment_id: int,
    request_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user_custom)
):
    """
    提交评估量表原始答案（移动端新接口）
    
    请求格式：
    {
        "answers": [
            {"question_id": "1", "answer": "选项A"},
            {"question_id": "2", "answer": "选项B"}
        ]
    }
    """
    try:
        # 验证请求数据
        if "answers" not in request_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少answers字段"
            )
        
        raw_answers = request_data["answers"]
        
        # 验证权限
        distribution = db.query(AssessmentDistribution).filter(
            AssessmentDistribution.assessment_id == assessment_id,
            AssessmentDistribution.custom_id == current_user.custom_id
        ).first()
        
        if not distribution:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限提交此评估量表"
            )
        
        # 获取评估量表和模板
        assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
        if not assessment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="评估量表不存在"
            )
        
        template = db.query(AssessmentTemplate).filter(
            AssessmentTemplate.id == assessment.template_id
        ).first()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="评估量表模板不存在"
            )
        
        # 检查是否已完成
        if assessment.status == "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="评估量表已完成，无法重复提交"
            )
        
        # 使用计分服务进行计分
        scoring_service = MobileScoringService(db)
        scoring_result = scoring_service.calculate_assessment_score(template, raw_answers)
        
        # 更新评估量表
        assessment.status = "completed"
        assessment.completed_at = datetime.now()
        assessment.answers = {"answers": scoring_result["scored_answers"]}
        assessment.score = scoring_result["total_score"]
        assessment.max_score = scoring_result["max_score"]
        assessment.result = scoring_result["result_analysis"]["result_category"]
        assessment.conclusion = scoring_result["result_analysis"]["conclusion"]
        
        # 更新分发记录
        distribution.status = "completed"
        distribution.completed_at = datetime.now()
        
        # 创建评估响应记录
        assessment_response = AssessmentResponse(
            assessment_id=assessment_id,
            custom_id=current_user.custom_id,
            answers=scoring_result["scored_answers"],
            score=scoring_result["total_score"],
            dimension_scores=scoring_result["dimension_scores"],
            result=scoring_result["result_analysis"]["result_category"],
            notes=scoring_result["result_analysis"]["conclusion"]
        )
        db.add(assessment_response)
        db.flush()
        
        # 创建评估结果记录
        assessment_result = AssessmentResult(
            assessment_id=assessment_id,
            custom_id=current_user.custom_id,
            template_id=assessment.template_id,
            total_score=scoring_result["total_score"],
            max_score=scoring_result["max_score"],
            percentage=scoring_result["result_analysis"]["percentage"],
            result_level=scoring_result["result_analysis"]["result_category"],
            result_category=scoring_result["result_analysis"]["result_category"],
            interpretation=scoring_result["result_analysis"]["conclusion"],
            recommendations=json.dumps(scoring_result["result_analysis"]["recommendations"], ensure_ascii=False),
            dimension_scores=scoring_result["dimension_scores"],
            raw_answers=raw_answers,
            report_generated=True,
            status="calculated"
        )
        db.add(assessment_result)
        
        db.commit()
        
        return {
            "status": "success",
            "message": "评估量表提交成功",
            "data": {
                "assessment_id": assessment_id,
                "response_id": assessment_response.id,
                "result_id": assessment_result.id,
                "total_score": scoring_result["total_score"],
                "max_score": scoring_result["max_score"],
                "percentage": scoring_result["result_analysis"]["percentage"],
                "result_category": scoring_result["result_analysis"]["result_category"],
                "conclusion": scoring_result["result_analysis"]["conclusion"],
                "dimension_scores": scoring_result["dimension_scores"],
                "completed_at": assessment.completed_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交评估量表时发生错误: {str(e)}"
        )

@router.post("/questionnaires/{questionnaire_id}/submit-raw", response_model=Dict[str, Any])
def submit_questionnaire_raw_answers(
    questionnaire_id: int,
    request_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user_custom)
):
    """
    提交问卷原始答案（移动端新接口）
    
    请求格式：
    {
        "answers": [
            {"question_id": "1", "answer": "选项A"},
            {"question_id": "2", "answer": ["选项A", "选项B"]}
        ]
    }
    """
    try:
        # 验证请求数据
        if "answers" not in request_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少answers字段"
            )
        
        raw_answers = request_data["answers"]
        
        # 验证权限
        distribution = db.query(QuestionnaireDistribution).filter(
            QuestionnaireDistribution.questionnaire_id == questionnaire_id,
            QuestionnaireDistribution.custom_id == current_user.custom_id
        ).first()
        
        if not distribution:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限提交此问卷"
            )
        
        # 获取问卷和模板
        questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
        if not questionnaire:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="问卷不存在"
            )
        
        template = db.query(QuestionnaireTemplate).filter(
            QuestionnaireTemplate.id == questionnaire.template_id
        ).first()
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="问卷模板不存在"
            )
        
        # 检查是否已完成
        if questionnaire.status == "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="问卷已完成，无法重复提交"
            )
        
        # 使用计分服务进行计分
        scoring_service = MobileScoringService(db)
        scoring_result = scoring_service.calculate_questionnaire_score(template, raw_answers)
        
        # 更新问卷
        questionnaire.status = "completed"
        questionnaire.answers = {"answers": scoring_result["scored_answers"]}
        questionnaire.conclusion = scoring_result["result_analysis"]["conclusion"]
        
        # 更新分发记录
        distribution.status = "completed"
        distribution.completed_at = datetime.now()
        
        # 创建问卷响应记录
        questionnaire_response = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            custom_id=current_user.custom_id,
            answers={"answers": scoring_result["scored_answers"]},
            status="completed"
        )
        db.add(questionnaire_response)
        db.flush()
        
        # 创建问卷结果记录
        questionnaire_result = QuestionnaireResult(
            questionnaire_id=questionnaire_id,
            response_id=questionnaire_response.id,
            custom_id=current_user.custom_id,
            template_id=questionnaire.template_id,
            total_score=scoring_result["total_score"],
            max_score=scoring_result["max_score"],
            percentage=(scoring_result["total_score"] / scoring_result["max_score"] * 100) if scoring_result["max_score"] > 0 else 0,
            result_level=scoring_result["result_analysis"]["result_category"],
            result_category=scoring_result["result_analysis"]["result_category"],
            interpretation=scoring_result["result_analysis"]["conclusion"],
            recommendations=json.dumps(scoring_result["result_analysis"]["recommendations"], ensure_ascii=False),
            raw_answers=json.dumps(raw_answers, ensure_ascii=False),
            report_generated=True,
            status="calculated"
        )
        db.add(questionnaire_result)
        
        db.commit()
        
        return {
            "status": "success",
            "message": "问卷提交成功",
            "data": {
                "questionnaire_id": questionnaire_id,
                "response_id": questionnaire_response.id,
                "result_id": questionnaire_result.id,
                "total_score": scoring_result["total_score"],
                "max_score": scoring_result["max_score"],
                "completion_rate": scoring_result["completion_rate"],
                "result_category": scoring_result["result_analysis"]["result_category"],
                "conclusion": scoring_result["result_analysis"]["conclusion"],
                "completed_at": distribution.completed_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交问卷时发生错误: {str(e)}"
        )
