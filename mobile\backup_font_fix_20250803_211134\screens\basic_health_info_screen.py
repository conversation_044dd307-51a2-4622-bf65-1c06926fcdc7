"""基本健康信息屏幕模块

提供用户全面健康信息的录入、编辑和管理功能，包括：
- 基本信息：身高、体重、BMI等
- 生活方式：饮食、运动、睡眠、烟酒习惯（多选形式）
- 医疗历史：慢性病史、手术史、住院史、传染病史（列表管理）
- 家族遗传病史：直系亲属重大疾病史（列表管理）
- 过敏记录：药物/食物/环境过敏源（列表管理）
- 预防接种：疫苗接种记录（列表管理）
"""

from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, DictProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
from datetime import datetime

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog
from widgets.logo import HealthLogo

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles

# 导入工具类
from utils.health_data_manager import get_health_data_manager
from utils.cloud_api import get_cloud_api

# 设置日志
logger = logging.getLogger(__name__)

# 定义KV语言字符串
KV = '''
<HealthCategoryCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(16)]
    spacing: dp(12)
    
    # 分类标题栏
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(12)
        
        MDIcon:
            icon: root.icon
            theme_icon_color: "Custom"
            icon_color: root.icon_color
            icon_size: dp(24)
            size_hint_x: None
            width: dp(24)
            pos_hint: {"center_y": 0.5}
            
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            pos_hint: {"center_y": 0.5}
            
        Widget:
            size_hint_x: 0.1
            
        MDIconButton:
            icon: "chevron-down" if root.expanded else "chevron-right"
            icon_size: dp(20)
            theme_icon_color: "Custom"
            icon_color: app.theme.TEXT_SECONDARY
            size_hint_x: None
            width: dp(40)
            on_release: root.toggle_expand()
    
    # 分隔线
    MDDivider:
        height: dp(1)
        color: app.theme.DIVIDER_COLOR
    
    # 内容区域
    MDBoxLayout:
        id: content_layout
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height if root.expanded else 0
        opacity: 1 if root.expanded else 0
        spacing: dp(8)
        
<MultiSelectItem>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    spacing: dp(12)
    padding: [dp(8), dp(4), dp(8), dp(4)]
    
    MDCheckbox:
        id: checkbox
        size_hint_x: None
        width: dp(32)
        active: root.selected
        on_active: root.on_checkbox_active(self.active)
        
    MDLabel:
        text: root.text
        font_style: "Body"
        role: "medium"
        theme_text_color: "Primary"
        pos_hint: {"center_y": 0.5}
        
<ListManagementItem>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme.SURFACE_COLOR
    radius: [dp(8)]
    padding: [dp(12), dp(8), dp(12), dp(8)]
    spacing: dp(8)
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)
        
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Primary"
            size_hint_x: 0.8
            pos_hint: {"center_y": 0.5}
            
        MDIconButton:
            icon: "pencil"
            icon_size: dp(18)
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR
            size_hint_x: None
            width: dp(32)
            on_release: root.on_edit()
            
        MDIconButton:
            icon: "delete"
            icon_size: dp(18)
            theme_icon_color: "Custom"
            icon_color: app.theme.ERROR_COLOR
            size_hint_x: None
            width: dp(32)
            on_release: root.on_delete()
    
    MDLabel:
        text: root.content
        font_style: "Body"
        role: "small"
        theme_text_color: "Secondary"
        size_hint_y: None
        height: self.texture_size[1]
        text_size: self.width, None
        
<HealthInfoItem>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    spacing: dp(12)
    padding: [dp(8), dp(4), dp(8), dp(4)]
    
    MDLabel:
        text: root.label
        font_style: "Body"
        role: "medium"
        theme_text_color: "Primary"
        size_hint_x: 0.4
        pos_hint: {"center_y": 0.5}
        
    MDLabel:
        text: root.value if root.value else "未填写"
        font_style: "Body"
        role: "medium"
        theme_text_color: "Custom"
        text_color: app.theme.PRIMARY_COLOR if root.value else app.theme.TEXT_SECONDARY
        size_hint_x: 0.5
        pos_hint: {"center_y": 0.5}
        
    MDIconButton:
        icon: "pencil"
        icon_size: dp(18)
        theme_icon_color: "Custom"
        icon_color: app.theme.PRIMARY_COLOR
        size_hint_x: None
        width: dp(40)
        on_release: root.on_edit()

<BasicHealthInfoScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(8)
        
        # 顶部栏
        MDBoxLayout:
            id: top_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(8), dp(0), dp(8), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {"center_y": 0.5}
                on_release: root.go_back()
                
            MDLabel:
                text: "基本健康信息"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                size_hint_x: 0.7
                pos_hint: {"center_y": 0.5}
                halign: "center"
                
            MDIconButton:
                icon: "content-save"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {"center_y": 0.5}
                on_release: root.save_all_data()
        
        # Logo
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(60)
        
        # 主内容区
        MDScrollView:
            id: scroll_view
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(8), dp(16), dp(16)]
                spacing: dp(16)
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class MultiSelectItem(MDBoxLayout):
    """多选项组件"""
    text = StringProperty("")
    selected = BooleanProperty(False)
    callback = ObjectProperty(None)
    
    def on_checkbox_active(self, active):
        """复选框状态改变"""
        self.selected = active
        if self.callback:
            self.callback(self.text, active)

class ListManagementItem(MDCard):
    """列表管理项组件"""
    title = StringProperty("")
    content = StringProperty("")
    item_data = DictProperty({})
    edit_callback = ObjectProperty(None)
    delete_callback = ObjectProperty(None)
    
    def on_edit(self):
        """编辑项目"""
        if self.edit_callback:
            self.edit_callback(self.item_data)
    
    def on_delete(self):
        """删除项目"""
        if self.delete_callback:
            self.delete_callback(self.item_data)

class HealthCategoryCard(MDCard):
    """健康信息分类卡片组件"""
    title = StringProperty("")
    icon = StringProperty("")
    icon_color = ListProperty([1, 1, 1, 1])
    expanded = BooleanProperty(True)
    category_data = DictProperty({})
    screen_ref = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        Clock.schedule_once(self.init_content, 0.1)
        
    def init_content(self, dt=0):
        """初始化内容"""
        self.load_items()
        
    def toggle_expand(self):
        """切换展开/收起状态"""
        self.expanded = not self.expanded
        
    def load_items(self):
        """加载分类下的信息项"""
        content_layout = self.ids.content_layout
        content_layout.clear_widgets()
        
        if not self.category_data or 'items' not in self.category_data:
            return
            
        category_key = self.category_data.get('key', '')
        
        # 根据不同类型加载不同的UI
        if category_key == 'lifestyle':
            self.load_lifestyle_items(content_layout)
        elif category_key in ['medical_history', 'family_history', 'allergies', 'vaccinations']:
            self.load_list_management_items(content_layout)
        else:
            self.load_normal_items(content_layout)
    
    def load_normal_items(self, content_layout):
        """加载普通信息项"""
        for item_key, item_data in self.category_data['items'].items():
            item = HealthInfoItem(
                label=item_data.get('label', ''),
                value=item_data.get('value', ''),
                item_key=item_key,
                category_key=self.category_data.get('key', ''),
                item_data=item_data
            )
            content_layout.add_widget(item)
    
    def load_lifestyle_items(self, content_layout):
        """加载生活方式多选项"""
        lifestyle_sections = {
            'diet': {
                'title': '饮食习惯（可多选）',
                'options': ['肉食为主', '素食为主', '荤素均衡', '喜腌制食品', '喜食粥粉面等易消化食物', '喜热粥热茶', '喜食辛辣', '高盐饮食']
            },
            'exercise': {
                'title': '运动情况',
                'fields': {
                    'exercise_type': {'label': '运动方式', 'options': ['跑步', '游泳', '健身', '瑜伽', '太极', '广场舞', '散步', '骑行', '球类运动', '其他']},
                    'exercise_amount': {'label': '运动量', 'options': ['轻度', '中度', '重度']},
                    'exercise_frequency': {'label': '运动频率', 'options': ['每天', '每周5-6次', '每周3-4次', '每周1-2次', '偶尔', '从不']}
                }
            },
            'sleep': {
                'title': '睡眠情况（可多选）',
                'options': ['主观感觉良好', '主观感觉一般', '主观感觉较差', '入眠困难', '易醒', '早醒', '多梦', '打鼾']
            },
            'smoking': {
                'title': '吸烟情况',
                'fields': {
                    'smoking_years': {'label': '吸烟时长（年）', 'type': 'number'},
                    'smoking_amount': {'label': '吸烟量（包/天）', 'type': 'number'},
                    'quit_smoking': {'label': '有无戒烟', 'options': ['从未吸烟', '已戒烟', '正在戒烟', '未戒烟']},
                    'quit_time': {'label': '戒烟时间', 'type': 'text'}
                }
            },
            'drinking': {
                'title': '饮酒情况',
                'fields': {
                    'drinking_amount': {'label': '饮酒量', 'options': ['从不饮酒', '少量（<25g/天）', '中量（25-50g/天）', '大量（>50g/天）']},
                    'drinking_frequency': {'label': '饮酒频率', 'options': ['从不', '偶尔', '每周1-2次', '每周3-4次', '每天']},
                    'quit_drinking': {'label': '戒酒情况', 'options': ['从未饮酒', '已戒酒', '正在戒酒', '未戒酒']}
                }
            },
            'bowel': {
                'title': '大便情况（可多选）',
                'options': ['正常', '便秘', '腹泻', '便血', '大便不成形', '排便困难', '排便疼痛']
            },
            'urination': {
                'title': '小便情况（可多选）',
                'options': ['正常', '尿频', '尿急', '尿痛', '排尿困难', '血尿', '夜尿增多（>2次/夜）', '尿失禁']
            },
            'work_stress': {
                'title': '工作压力',
                'options': ['很低', '较低', '中等', '较高', '很高']
            }
        }
        
        for section_key, section_data in lifestyle_sections.items():
            # 添加小节标题
            title_label = MDLabel(
                text=section_data['title'],
                font_style="Body",
                role="medium",
                bold=True,
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(32)
            )
            content_layout.add_widget(title_label)
            
            if 'options' in section_data:
                # 多选项
                for option in section_data['options']:
                    selected = option in self.get_lifestyle_selections(section_key)
                    item = MultiSelectItem(
                        text=option,
                        selected=selected,
                        callback=lambda text, active, sk=section_key: self.on_lifestyle_selection(sk, text, active)
                    )
                    content_layout.add_widget(item)
            elif 'fields' in section_data:
                # 字段项
                for field_key, field_data in section_data['fields'].items():
                    if 'options' in field_data:
                        # 单选下拉
                        current_value = self.get_lifestyle_field_value(section_key, field_key)
                        item = HealthInfoItem(
                            label=field_data['label'],
                            value=current_value,
                            item_key=f"{section_key}_{field_key}",
                            category_key='lifestyle',
                            item_data={'type': 'select', 'options': field_data['options']}
                        )
                        content_layout.add_widget(item)
                    else:
                        # 文本输入
                        current_value = self.get_lifestyle_field_value(section_key, field_key)
                        item = HealthInfoItem(
                            label=field_data['label'],
                            value=current_value,
                            item_key=f"{section_key}_{field_key}",
                            category_key='lifestyle',
                            item_data={'type': field_data.get('type', 'text')}
                        )
                        content_layout.add_widget(item)
            
            # 添加分隔线
            content_layout.add_widget(MDBoxLayout(size_hint_y=None, height=dp(8)))
    
    def load_list_management_items(self, content_layout):
        """加载列表管理项"""
        category_key = self.category_data.get('key', '')
        items = self.get_list_items(category_key)
        
        # 添加新增按钮
        add_button = MDButton(
            MDButtonText(text=f"添加{self.category_data['title']}"),
            style="outlined",
            size_hint_y=None,
            height=dp(40),
            on_release=lambda x: self.add_list_item(category_key)
        )
        content_layout.add_widget(add_button)
        
        # 添加现有项目
        for item_data in items:
            list_item = ListManagementItem(
                title=item_data.get('title', ''),
                content=item_data.get('content', ''),
                item_data=item_data,
                edit_callback=lambda data, ck=category_key: self.edit_list_item(ck, data),
                delete_callback=lambda data, ck=category_key: self.delete_list_item(ck, data)
            )
            content_layout.add_widget(list_item)
    
    def get_lifestyle_selections(self, section_key):
        """获取生活方式选择"""
        if not self.screen_ref:
            return []
        return self.screen_ref.lifestyle_selections.get(section_key, [])
    
    def get_lifestyle_field_value(self, section_key, field_key):
        """获取生活方式字段值"""
        if not self.screen_ref:
            return ''
        return self.screen_ref.lifestyle_fields.get(f"{section_key}_{field_key}", '')
    
    def on_lifestyle_selection(self, section_key, text, active):
        """生活方式选择改变"""
        if not self.screen_ref:
            return
        self.screen_ref.update_lifestyle_selection(section_key, text, active)
    
    def get_list_items(self, category_key):
        """获取列表项"""
        if not self.screen_ref:
            return []
        return self.screen_ref.list_data.get(category_key, [])
    
    def add_list_item(self, category_key):
        """添加列表项"""
        if self.screen_ref:
            self.screen_ref.add_list_item(category_key)
    
    def edit_list_item(self, category_key, item_data):
        """编辑列表项"""
        if self.screen_ref:
            self.screen_ref.edit_list_item(category_key, item_data)
    
    def delete_list_item(self, category_key, item_data):
        """删除列表项"""
        if self.screen_ref:
            self.screen_ref.delete_list_item(category_key, item_data)

class HealthInfoItem(MDBoxLayout):
    """健康信息项组件"""
    label = StringProperty("")
    value = StringProperty("")
    item_key = StringProperty("")
    category_key = StringProperty("")
    item_data = DictProperty({})
    
    def on_edit(self):
        """编辑信息项"""
        screen = self.get_root_window().children[0].current_screen
        if hasattr(screen, 'edit_health_item'):
            screen.edit_health_item(self.category_key, self.item_key, self.label, self.value, self.item_data)

class BasicHealthInfoScreen(BaseScreen):
    """基本健康信息屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.health_data = {}
        self.lifestyle_selections = {}  # 生活方式多选数据
        self.lifestyle_fields = {}      # 生活方式字段数据
        self.list_data = {}             # 列表管理数据
        self.init_health_data_structure()
        Clock.schedule_once(self.init_ui, 0.2)
    
    def init_ui(self, dt=0):
        """初始化UI"""
        self.load_data()
        self.refresh_ui()
        
    def init_health_data_structure(self):
        """初始化健康数据结构"""
        self.health_data = {
            'basic_info': {
                'key': 'basic_info',
                'title': '基本信息',
                'icon': 'account-circle',
                'color': [0.2, 0.6, 1.0, 1.0],  # 蓝色
                'items': {
                    'height': {'label': '身高', 'value': '', 'unit': 'cm', 'type': 'number'},
                    'weight': {'label': '体重', 'value': '', 'unit': 'kg', 'type': 'number'},
                    'bmi': {'label': 'BMI指数', 'value': '', 'unit': '', 'type': 'calculated'},
                    'blood_type': {'label': '血型', 'value': '', 'unit': '', 'type': 'select', 'options': ['A型', 'B型', 'AB型', 'O型']},
                    'rh_factor': {'label': 'RH因子', 'value': '', 'unit': '', 'type': 'select', 'options': ['阳性', '阴性']}
                    # 删除了：出生日期、身份证号、紧急联系人、紧急联系电话
                }
            },
            'lifestyle': {
                'key': 'lifestyle',
                'title': '生活方式',
                'icon': 'heart-pulse',
                'color': [1.0, 0.4, 0.4, 1.0],  # 红色
                'items': {}  # 生活方式使用特殊处理
            },
            'medical_history': {
                'key': 'medical_history',
                'title': '医疗史',
                'icon': 'medical-bag',
                'color': [0.4, 0.8, 0.4, 1.0],  # 绿色
                'items': {}  # 使用列表管理
                # 删除了：当前用药、心理健康状况、残疾状况
            },
            'family_history': {
                'key': 'family_history',
                'title': '家族遗传史',
                'icon': 'account-group',
                'color': [0.8, 0.4, 0.8, 1.0],  # 紫色
                'items': {}  # 使用列表管理
            },
            'allergies': {
                'key': 'allergies',
                'title': '过敏记录',
                'icon': 'alert-circle',
                'color': [1.0, 0.6, 0.2, 1.0],  # 橙色
                'items': {}  # 使用列表管理
            },
            'vaccinations': {
                'key': 'vaccinations',
                'title': '预防接种记录',
                'icon': 'needle',
                'color': [0.6, 0.8, 1.0, 1.0],  # 浅蓝色
                'items': {}  # 使用列表管理
            }
        }
        
        # 初始化列表数据结构
        self.list_data = {
            'medical_history': [],
            'family_history': [],
            'allergies': [],
            'vaccinations': []
        }
        
        # 初始化生活方式数据
        self.lifestyle_selections = {
            'diet': [],
            'sleep': [],
            'bowel': [],
            'urination': []
        }
        
        self.lifestyle_fields = {}
    
    def on_enter(self):
        """进入屏幕时的处理"""
        try:
            # 初始化UI
            self.init_ui()
        except Exception as e:
            logger.error(f"进入基本健康信息屏幕时出错: {e}")
    
    def go_back(self):
        """返回上一页"""
        try:
            app = MDApp.get_running_app()
            app.root.transition.direction = 'right'
            app.root.current = 'health_data_management_screen'
        except Exception as e:
            logger.error(f"返回上一页时出错: {e}")
    
    def save_all_data(self):
        """保存所有健康数据"""
        try:
            # 计算BMI
            self.calculate_bmi()
            
            # 统计保存的数据项
            saved_count = 0
            
            # 基本信息
            for item_key, item_data in self.health_data['basic_info']['items'].items():
                if item_data['value']:
                    saved_count += 1
            
            # 生活方式
            for selections in self.lifestyle_selections.values():
                if selections:
                    saved_count += len(selections)
            
            for field_value in self.lifestyle_fields.values():
                if field_value:
                    saved_count += 1
            
            # 列表数据
            for category_items in self.list_data.values():
                saved_count += len(category_items)
            
            if saved_count > 0:
                self.show_message(f"已保存 {saved_count} 项健康信息")
            else:
                self.show_message("请填写健康信息后再保存")
                
        except Exception as e:
            logger.error(f"保存健康数据时出错: {e}")
            self.show_message("保存失败")
    
    def calculate_bmi(self):
        """计算BMI指数"""
        try:
            height_str = self.health_data['basic_info']['items']['height']['value']
            weight_str = self.health_data['basic_info']['items']['weight']['value']
            
            if height_str and weight_str:
                height = float(height_str) / 100  # 转换为米
                weight = float(weight_str)
                bmi = weight / (height * height)
                
                # 更新BMI状态
                if bmi < 18.5:
                    bmi_status = "偏瘦"
                elif bmi < 24:
                    bmi_status = "正常"
                elif bmi < 28:
                    bmi_status = "超重"
                else:
                    bmi_status = "肥胖"
                    
                self.health_data['basic_info']['items']['bmi']['value'] = f"{bmi:.1f} ({bmi_status})"
                
        except (ValueError, KeyError) as e:
            logger.debug(f"BMI计算失败: {e}")
    
    def edit_health_item(self, category_key, item_key, label, value, item_data):
        """编辑健康信息项"""
        try:
            self.show_item_dialog(category_key, item_key, item_data, label, value)
        except Exception as e:
            logger.error(f"编辑健康信息项时出错: {e}")
    
    def show_item_dialog(self, category_key, item_key, item_data, label, value):
        """显示项目编辑对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer, MDDialogContentContainer
            from kivymd.uix.divider import MDDivider
            from kivymd.uix.textfield import MDTextFieldHintText, MDTextFieldHelperText
            
            # 创建内容容器
            content_container = MDDialogContentContainer(
                MDDialogHeadlineText(
                    text=f"编辑{label}",
                    halign="left",
                ),
                MDDivider(),
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(300),
            )
            
            # 根据数据类型创建不同的输入控件
            if item_data.get('type') == 'select' and 'options' in item_data:
                # 下拉选择
                self.input_field = MDTextField(
                    MDTextFieldHintText(text=f"选择{label}"),
                    MDTextFieldHelperText(text=f"选项: {', '.join(item_data['options'])}"),
                    text=value,
                    size_hint_y=None,
                    height=dp(56)
                )
            elif item_data.get('type') == 'number':
                # 数字输入
                self.input_field = MDTextField(
                    MDTextFieldHintText(text=f"输入{label}"),
                    text=value,
                    input_filter='float',
                    size_hint_y=None,
                    height=dp(56)
                )
            else:
                # 普通文本
                self.input_field = MDTextField(
                    MDTextFieldHintText(text=f"输入{label}"),
                    text=value,
                    size_hint_y=None,
                    height=dp(56)
                )
            
            content_container.add_widget(self.input_field)
            
            # 显示单位
            if item_data.get('unit'):
                unit_text = MDDialogSupportingText(
                    text=f"单位: {item_data['unit']}",
                    halign="left",
                )
                content_container.add_widget(unit_text)
            
            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=lambda x: self.save_item(category_key, item_key)
                ),
                spacing=dp(8),
            )
            
            # 创建对话框
            self.dialog = MDDialog(
                title="编辑信息" if item_data else "添加信息",
                content=content_container,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="filled",
                        on_release=lambda x: self.save_item(category_key, item_key)
                    ),
                ],
            )
            
            self.dialog.open()
            
        except Exception as e:
            logger.error(f"显示项目对话框时出错: {e}")
            self.show_message("显示对话框失败")
    
    def save_item(self, category_key, item_key):
        """保存项目数据"""
        try:
            value = self.input_field.text.strip()
            
            if category_key == 'lifestyle':
                # 生活方式字段
                self.lifestyle_fields[item_key] = value
            else:
                # 普通字段
                self.health_data[category_key]['items'][item_key]['value'] = value
            
            # 如果是身高或体重，重新计算BMI
            if category_key == 'basic_info' and item_key in ['height', 'weight']:
                self.calculate_bmi()
            
            # 刷新UI
            self.refresh_ui()
            
            # 关闭对话框
            self.dialog.dismiss()
            
            # 显示成功消息
            self.show_message("保存成功")
            
        except Exception as e:
            logger.error(f"保存项目数据时出错: {e}")
            self.show_message("保存失败")
    
    def update_lifestyle_selection(self, section_key, text, active):
        """更新生活方式选择"""
        if section_key not in self.lifestyle_selections:
            self.lifestyle_selections[section_key] = []
        
        if active:
            if text not in self.lifestyle_selections[section_key]:
                self.lifestyle_selections[section_key].append(text)
        else:
            if text in self.lifestyle_selections[section_key]:
                self.lifestyle_selections[section_key].remove(text)
    
    def add_list_item(self, category_key):
        """添加列表项"""
        try:
            if category_key == 'medical_history':
                self.show_medical_history_dialog()
            elif category_key == 'family_history':
                self.show_family_history_dialog()
            elif category_key == 'allergies':
                self.show_allergy_dialog()
            elif category_key == 'vaccinations':
                self.show_vaccination_dialog()
        except Exception as e:
            logger.error(f"添加列表项时出错: {e}")
    
    def show_medical_history_dialog(self, item_data=None):
        """显示医疗史对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogButtonContainer, MDDialogContentContainer
            from kivymd.uix.divider import MDDivider
            from kivymd.uix.textfield import MDTextFieldHintText
            from kivymd.uix.menu import MDDropdownMenu
            
            # 创建内容容器
            content_container = MDDialogContentContainer(
                MDDialogHeadlineText(
                    text="医疗史记录",
                    halign="left",
                ),
                MDDivider(),
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(400),
            )
            
            # 类型选择
            self.medical_type_field = MDTextField(
                MDTextFieldHintText(text="选择类型"),
                text=item_data.get('type', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.medical_type_field)
            
            # 根据不同类型显示不同字段
            if not item_data or item_data.get('type') == '慢病史':
                # 慢病史：疾病名称、控制情况、病程时长
                self.disease_name_field = MDTextField(
                    MDTextFieldHintText(text="疾病名称"),
                    text=item_data.get('disease_name', '') if item_data else '',
                    size_hint_y=None,
                    height=dp(56)
                )
                content_container.add_widget(self.disease_name_field)
                
                self.control_status_field = MDTextField(
                    MDTextFieldHintText(text="控制情况"),
                    text=item_data.get('control_status', '') if item_data else '',
                    size_hint_y=None,
                    height=dp(56)
                )
                content_container.add_widget(self.control_status_field)
                
                self.duration_field = MDTextField(
                    MDTextFieldHintText(text="病程时长"),
                    text=item_data.get('duration', '') if item_data else '',
                    size_hint_y=None,
                    height=dp(56)
                )
                content_container.add_widget(self.duration_field)
            
            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=lambda x: self.save_medical_history_item(item_data)
                ),
                spacing=dp(8),
            )
            
            # 创建对话框
            self.dialog = MDDialog(
                title="编辑信息" if item_data else "添加信息",
                content=content_container,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="filled",
                        on_release=lambda x: self.save_medical_history_item(item_data)
                    ),
                ],
            )
            
            self.dialog.open()
            
        except Exception as e:
            logger.error(f"显示医疗史对话框时出错: {e}")
    
    def save_medical_history_item(self, existing_item=None):
        """保存医疗史项目"""
        try:
            item_data = {
                'id': existing_item.get('id') if existing_item else len(self.list_data['medical_history']),
                'type': self.medical_type_field.text.strip(),
                'disease_name': getattr(self, 'disease_name_field', None).text.strip() if hasattr(self, 'disease_name_field') else '',
                'control_status': getattr(self, 'control_status_field', None).text.strip() if hasattr(self, 'control_status_field') else '',
                'duration': getattr(self, 'duration_field', None).text.strip() if hasattr(self, 'duration_field') else '',
                'title': self.medical_type_field.text.strip(),
                'content': f"疾病: {getattr(self, 'disease_name_field', None).text.strip() if hasattr(self, 'disease_name_field') else ''}, 控制情况: {getattr(self, 'control_status_field', None).text.strip() if hasattr(self, 'control_status_field') else ''}"
            }
            
            if existing_item:
                # 更新现有项目
                for i, item in enumerate(self.list_data['medical_history']):
                    if item.get('id') == existing_item.get('id'):
                        self.list_data['medical_history'][i] = item_data
                        break
            else:
                # 添加新项目
                self.list_data['medical_history'].append(item_data)
            
            self.dialog.dismiss()
            self.refresh_ui()
            self.show_message("保存成功")
            
        except Exception as e:
            logger.error(f"保存医疗史项目时出错: {e}")
            self.show_message("保存失败")
    
    def show_family_history_dialog(self, item_data=None):
        """显示家族遗传史对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogButtonContainer, MDDialogContentContainer
            from kivymd.uix.divider import MDDivider
            from kivymd.uix.textfield import MDTextFieldHintText
            
            # 创建内容容器
            content_container = MDDialogContentContainer(
                MDDialogHeadlineText(
                    text="家族遗传史记录",
                    halign="left",
                ),
                MDDivider(),
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(350),
            )
            
            # 直系亲属关系
            self.relationship_field = MDTextField(
                MDTextFieldHintText(text="直系亲属关系（父亲/母亲/兄弟/姐妹/祖父/祖母/外祖父/外祖母）"),
                text=item_data.get('relationship', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.relationship_field)
            
            # 疾病种类
            self.disease_type_field = MDTextField(
                MDTextFieldHintText(text="疾病种类"),
                text=item_data.get('disease_type', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.disease_type_field)
            
            # 转归
            self.outcome_field = MDTextField(
                MDTextFieldHintText(text="转归情况"),
                text=item_data.get('outcome', '') if item_data else '',
                multiline=True,
                size_hint_y=None,
                height=dp(80)
            )
            content_container.add_widget(self.outcome_field)
            
            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=lambda x: self.save_family_history_item(item_data)
                ),
                spacing=dp(8),
            )
            
            # 创建对话框
            self.dialog = MDDialog(
                title="编辑信息" if item_data else "添加信息",
                content=content_container,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="filled",
                        on_release=lambda x: self.save_family_history_item(item_data)
                    ),
                ],
            )
            
            self.dialog.open()
            
        except Exception as e:
            logger.error(f"显示家族遗传史对话框时出错: {e}")
    
    def save_family_history_item(self, existing_item=None):
        """保存家族遗传史项目"""
        try:
            item_data = {
                'id': existing_item.get('id') if existing_item else len(self.list_data['family_history']),
                'relationship': self.relationship_field.text.strip(),
                'disease_type': self.disease_type_field.text.strip(),
                'outcome': self.outcome_field.text.strip(),
                'title': f"{self.relationship_field.text.strip()} - {self.disease_type_field.text.strip()}",
                'content': f"关系: {self.relationship_field.text.strip()}, 疾病: {self.disease_type_field.text.strip()}, 转归: {self.outcome_field.text.strip()}"
            }
            
            if existing_item:
                # 更新现有项目
                for i, item in enumerate(self.list_data['family_history']):
                    if item.get('id') == existing_item.get('id'):
                        self.list_data['family_history'][i] = item_data
                        break
            else:
                # 添加新项目
                self.list_data['family_history'].append(item_data)
            
            self.dialog.dismiss()
            self.refresh_ui()
            self.show_message("保存成功")
            
        except Exception as e:
            logger.error(f"保存家族遗传史项目时出错: {e}")
            self.show_message("保存失败")
    
    def show_allergy_dialog(self, item_data=None):
        """显示过敏记录对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogButtonContainer, MDDialogContentContainer
            from kivymd.uix.divider import MDDivider
            from kivymd.uix.textfield import MDTextFieldHintText
            
            # 创建内容容器
            content_container = MDDialogContentContainer(
                MDDialogHeadlineText(
                    text="过敏记录",
                    halign="left",
                ),
                MDDivider(),
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(300),
            )
            
            # 过敏种类
            self.allergy_type_field = MDTextField(
                MDTextFieldHintText(text="过敏种类（药物/食物/环境等）"),
                text=item_data.get('allergy_type', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.allergy_type_field)
            
            # 过敏症状
            self.allergy_symptoms_field = MDTextField(
                MDTextFieldHintText(text="过敏症状"),
                text=item_data.get('symptoms', '') if item_data else '',
                multiline=True,
                size_hint_y=None,
                height=dp(80)
            )
            content_container.add_widget(self.allergy_symptoms_field)
            
            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=lambda x: self.save_allergy_item(item_data)
                ),
                spacing=dp(8),
            )
            
            # 创建对话框
            self.dialog = MDDialog(
                title="编辑信息" if item_data else "添加信息",
                content=content_container,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="filled",
                        on_release=lambda x: self.save_allergy_item(item_data)
                    ),
                ],
            )
            
            self.dialog.open()
            
        except Exception as e:
            logger.error(f"显示过敏记录对话框时出错: {e}")
    
    def save_allergy_item(self, existing_item=None):
        """保存过敏记录项目"""
        try:
            item_data = {
                'id': existing_item.get('id') if existing_item else len(self.list_data['allergies']),
                'allergy_type': self.allergy_type_field.text.strip(),
                'symptoms': self.allergy_symptoms_field.text.strip(),
                'title': self.allergy_type_field.text.strip(),
                'content': f"过敏种类: {self.allergy_type_field.text.strip()}, 症状: {self.allergy_symptoms_field.text.strip()}"
            }
            
            if existing_item:
                # 更新现有项目
                for i, item in enumerate(self.list_data['allergies']):
                    if item.get('id') == existing_item.get('id'):
                        self.list_data['allergies'][i] = item_data
                        break
            else:
                # 添加新项目
                self.list_data['allergies'].append(item_data)
            
            self.dialog.dismiss()
            self.refresh_ui()
            self.show_message("保存成功")
            
        except Exception as e:
            logger.error(f"保存过敏记录项目时出错: {e}")
            self.show_message("保存失败")
    
    def show_vaccination_dialog(self, item_data=None):
        """显示预防接种记录对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogButtonContainer, MDDialogContentContainer
            from kivymd.uix.divider import MDDivider
            from kivymd.uix.textfield import MDTextFieldHintText
            
            # 创建内容容器
            content_container = MDDialogContentContainer(
                MDDialogHeadlineText(
                    text="预防接种记录",
                    halign="left",
                ),
                MDDivider(),
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(350),
            )
            
            # 接种疫苗种类
            self.vaccine_type_field = MDTextField(
                MDTextFieldHintText(text="接种疫苗种类"),
                text=item_data.get('vaccine_type', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.vaccine_type_field)
            
            # 接种时间
            self.vaccination_date_field = MDTextField(
                MDTextFieldHintText(text="接种时间（YYYY-MM-DD）"),
                text=item_data.get('date', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.vaccination_date_field)
            
            # 接种地点
            self.vaccination_location_field = MDTextField(
                MDTextFieldHintText(text="接种地点"),
                text=item_data.get('location', '') if item_data else '',
                size_hint_y=None,
                height=dp(56)
            )
            content_container.add_widget(self.vaccination_location_field)
            
            # 创建按钮容器
            button_container = MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=lambda x: self.save_vaccination_item(item_data)
                ),
                spacing=dp(8),
            )
            
            # 创建对话框
            self.dialog = MDDialog(
                title="编辑信息" if item_data else "添加信息",
                content=content_container,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="filled",
                        on_release=lambda x: self.save_vaccination_item(item_data)
                    ),
                ],
            )
            
            self.dialog.open()
            
        except Exception as e:
            logger.error(f"显示预防接种记录对话框时出错: {e}")
    
    def save_vaccination_item(self, existing_item=None):
        """保存预防接种记录项目"""
        try:
            item_data = {
                'id': existing_item.get('id') if existing_item else len(self.list_data['vaccinations']),
                'vaccine_type': self.vaccine_type_field.text.strip(),
                'date': self.vaccination_date_field.text.strip(),
                'location': self.vaccination_location_field.text.strip(),
                'title': f"{self.vaccine_type_field.text.strip()} ({self.vaccination_date_field.text.strip()})",
                'content': f"疫苗: {self.vaccine_type_field.text.strip()}, 时间: {self.vaccination_date_field.text.strip()}, 地点: {self.vaccination_location_field.text.strip()}"
            }
            
            if existing_item:
                # 更新现有项目
                for i, item in enumerate(self.list_data['vaccinations']):
                    if item.get('id') == existing_item.get('id'):
                        self.list_data['vaccinations'][i] = item_data
                        break
            else:
                # 添加新项目
                self.list_data['vaccinations'].append(item_data)
            
            self.dialog.dismiss()
            self.refresh_ui()
            self.show_message("保存成功")
            
        except Exception as e:
            logger.error(f"保存预防接种记录项目时出错: {e}")
            self.show_message("保存失败")
    
    def edit_list_item(self, category_key, item_data):
        """编辑列表项"""
        try:
            if category_key == 'medical_history':
                self.show_medical_history_dialog(item_data)
            elif category_key == 'family_history':
                self.show_family_history_dialog(item_data)
            elif category_key == 'allergies':
                self.show_allergy_dialog(item_data)
            elif category_key == 'vaccinations':
                self.show_vaccination_dialog(item_data)
        except Exception as e:
            logger.error(f"编辑列表项时出错: {e}")
    
    def delete_list_item(self, category_key, item_data):
        """删除列表项"""
        try:
            if category_key in self.list_data:
                self.list_data[category_key] = [
                    item for item in self.list_data[category_key] 
                    if item.get('id') != item_data.get('id')
                ]
                self.refresh_ui()
                self.show_message("删除成功")
        except Exception as e:
            logger.error(f"删除列表项时出错: {e}")
    
    def load_data(self):
        """加载健康数据"""
        try:
            # 模拟加载已保存的数据
            sample_data = {
                'basic_info': {
                    'height': '175',
                    'weight': '70',
                    'blood_type': 'A型',
                    'rh_factor': '阳性'
                },
                'lifestyle_selections': {
                    'diet': ['荤素均衡'],
                    'sleep': ['主观感觉良好'],
                    'bowel': ['正常'],
                    'urination': ['正常']
                },
                'lifestyle_fields': {
                    'exercise_exercise_type': '跑步',
                    'exercise_exercise_amount': '中度',
                    'exercise_exercise_frequency': '每周3-4次',
                    'smoking_quit_smoking': '从未吸烟',
                    'drinking_drinking_amount': '从不饮酒',
                    'work_stress': '中等'
                }
            }
            
            # 将示例数据填入数据结构
            if 'basic_info' in sample_data:
                for item_key, value in sample_data['basic_info'].items():
                    if item_key in self.health_data['basic_info']['items']:
                        self.health_data['basic_info']['items'][item_key]['value'] = value
            
            # 加载生活方式数据
            if 'lifestyle_selections' in sample_data:
                self.lifestyle_selections.update(sample_data['lifestyle_selections'])
            
            if 'lifestyle_fields' in sample_data:
                self.lifestyle_fields.update(sample_data['lifestyle_fields'])
            
            # 计算BMI
            self.calculate_bmi()
            
        except Exception as e:
            logger.error(f"加载健康数据时出错: {e}")
    
    def refresh_ui(self):
        """刷新UI"""
        try:
            # 清空主布局
            main_layout = self.ids.main_layout
            main_layout.clear_widgets()
            
            # 添加健康分类卡片
            for category_key, category_data in self.health_data.items():
                card = HealthCategoryCard(
                    title=category_data['title'],
                    icon=category_data['icon'],
                    icon_color=category_data['color'],
                    category_data=category_data,
                    screen_ref=self
                )
                main_layout.add_widget(card)
                    
        except Exception as e:
            logger.error(f"刷新UI时出错: {e}")
    
    def show_message(self, message):
        """显示消息"""
        try:
            if hasattr(self.app, 'show_notification'):
                self.app.show_notification(message)
            else:
                # 备用方案
                snackbar = MDSnackbar(
                    MDSnackbarText(text=message),
                    y=dp(24),
                    pos_hint={"center_x": 0.5},
                    size_hint_x=0.8
                )
                snackbar.open()
        except Exception as e:
            logger.error(f"显示消息时出错: {e}")

# 用于测试的应用程序
if __name__ == '__main__':
    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles
        
        def build(self):
            self.user_data = {"username": "测试用户", "user_id": "test123", "gender": "男", "age": 45}
            return BasicHealthInfoScreen()
    
    TestApp().run()