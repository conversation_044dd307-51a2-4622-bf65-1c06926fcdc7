"""添加催办相关字段

Revision ID: add_reminded_fields
Revises: 
Create Date: 2023-08-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine.reflection import Inspector

# revision identifiers, used by Alembic.
revision = 'add_reminded_fields'
down_revision = None  # 请根据实际情况修改
branch_labels = None
depends_on = None

def column_exists(table_name, column_name, bind):
    inspector = Inspector.from_engine(bind)
    columns = [col['name'] for col in inspector.get_columns(table_name)]
    return column_name in columns

def upgrade():
    bind = op.get_bind()
    # assessments表
    if not column_exists('assessments', 'last_reminded_at', bind):
        op.add_column('assessments', sa.Column('last_reminded_at', sa.DateTime(timezone=True), nullable=True))
    # questionnaires表
    if not column_exists('questionnaires', 'last_reminded_at', bind):
        op.add_column('questionnaires', sa.Column('last_reminded_at', sa.DateTime(), nullable=True))
    if not column_exists('questionnaires', 'round_number', bind):
        op.add_column('questionnaires', sa.Column('round_number', sa.Integer(), nullable=True, server_default='1'))
    if not column_exists('questionnaires', 'sequence_number', bind):
        op.add_column('questionnaires', sa.Column('sequence_number', sa.Integer(), nullable=True, server_default='1'))
    # unique_identifier字段和索引只在不存在时添加
    if not column_exists('questionnaires', 'unique_identifier', bind):
        op.add_column('questionnaires', sa.Column('unique_identifier', sa.String(), nullable=True))
        op.create_index(op.f('ix_questionnaires_unique_identifier'), 'questionnaires', ['unique_identifier'], unique=False)

def downgrade():
    bind = op.get_bind()
    if column_exists('assessments', 'last_reminded_at', bind):
        op.drop_column('assessments', 'last_reminded_at')
    if column_exists('questionnaires', 'last_reminded_at', bind):
        op.drop_column('questionnaires', 'last_reminded_at')
    if column_exists('questionnaires', 'unique_identifier', bind):
        op.drop_index(op.f('ix_questionnaires_unique_identifier'), table_name='questionnaires')
        op.drop_column('questionnaires', 'unique_identifier')
    if column_exists('questionnaires', 'round_number', bind):
        op.drop_column('questionnaires', 'round_number')
    if column_exists('questionnaires', 'sequence_number', bind):
        op.drop_column('questionnaires', 'sequence_number') 