"""
健康资料管理屏幕
提供健康资料的上传、删除和管理功能，支持直接文件上传、二维码扫描和拍照上传
"""

from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, DictProperty, NumericProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
import threading
import time
from datetime import datetime

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import <PERSON>List, MDListItem, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.filemanager import MDFileManager
from kivy.uix.progressbar import ProgressBar
from widgets.logo import HealthLogo

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles

# 导入工具类
from utils.health_data_aggregator import get_health_data_aggregator
from utils.user_manager import get_user_manager
from utils.cloud_api import get_cloud_api

# 设置日志
logger = logging.getLogger(__name__)

# 定义KV语言字符串
KV = '''
<DocumentCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(120)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    spacing: dp(4)
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(8)
        
        MDIconButton:
            icon: root.icon
            icon_size: dp(24)
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR
            pos_hint: {"center_y": 0.5}
            
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_x: 0.7
            spacing: dp(2)
            
            MDLabel:
                text: root.title
                font_style: "Body"
                role: "medium"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_PRIMARY
                size_hint_y: None
                height: self.texture_size[1]
                shorten: True
                
            MDLabel:
                text: root.date
                font_style: "Label"
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_SECONDARY
                size_hint_y: None
                height: self.texture_size[1]
        
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_x: 0.3
            spacing: dp(4)
            
            MDIconButton:
                icon: "eye"
                icon_size: dp(20)
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_COLOR
                on_release: root.on_view()
                pos_hint: {"center_x": 0.5}
                
            MDIconButton:
                icon: "delete"
                icon_size: dp(20)
                theme_icon_color: "Custom"
                icon_color: app.theme.ERROR_COLOR
                on_release: root.on_delete()
                pos_hint: {"center_x": 0.5}
    
    MDLabel:
        text: root.summary
        font_style: "Label"
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
        shorten: True
        shorten_from: 'right'

<HealthDocumentScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: root.title
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.show_upload_options()
        
        # 添加Logo组件
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(80)
            logo_size: dp(60), dp(60)
            title_font_size: dp(18)
            subtitle_font_size: dp(14)
        
        # 搜索和筛选区域
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(60)
            padding: [dp(16), dp(8), dp(16), dp(8)]
            spacing: dp(8)
            
            MDTextField:
                id: search_field
                hint_text: "搜索健康资料"
                mode: "outlined"
                on_text: root.filter_documents()
                size_hint_x: 0.7
                
            MDButton:
                style: "outlined"
                size_hint_x: 0.3
                on_release: root.show_filter_options()
                MDButtonText:
                    text: "筛选"
        
        # 过滤器标签区域
        MDBoxLayout:
            id: filter_tags
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(40)
            padding: [dp(16), dp(0), dp(16), dp(0)]
            spacing: dp(8)
        
        # 进度条区域
        MDBoxLayout:
            id: progress_box
            orientation: 'vertical'
            size_hint_y: None
            height: dp(60)
            padding: [dp(16), dp(8), dp(16), dp(8)]
            opacity: 0
            
            MDLabel:
                id: progress_label
                text: "正在上传..."
                font_style: "Label"
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_PRIMARY
                size_hint_y: None
                height: self.texture_size[1]
            
            ProgressBar:
                id: progress_bar
                value: root.upload_progress
                max: 100
                size_hint_y: None
                height: dp(10)
                
        # 文档列表区域
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: documents_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(8), dp(16), dp(8)]
                spacing: dp(16)
                
                # 空状态框
                MDBoxLayout:
                    id: empty_box
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(200)
                    padding: [0, dp(80), 0, 0]
                    opacity: 0
                    
                    MDLabel:
                        text: "暂无健康资料"
                        halign: "center"
                        theme_text_color: "Secondary"
'''

class DocumentCard(MDCard):
    """健康资料卡片组件"""
    title = StringProperty("")
    date = StringProperty("")
    summary = StringProperty("")
    icon = StringProperty("file-document")
    document_id = StringProperty("")
    document_type = StringProperty("")
    document_data = DictProperty({})
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.parent_screen = None
        self.ripple_behavior = True  # 启用涟漪效果
        self.ripple_duration_in_slow = 0.1  # 加快涟漪动画
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)  # 设置涟漪颜色
    
    def on_touch_up(self, touch):
        """处理触摸事件，点击卡片时查看文档"""
        if self.collide_point(*touch.pos):
            # 检查是否点击在按钮上，避免与按钮点击事件冲突
            for child in self.walk(restrict=True):
                if isinstance(child, MDIconButton) and child.collide_point(*touch.pos):
                    return super().on_touch_up(touch)
            # 如果不是点击在按钮上，则调用查看方法
            self.on_view()
        return super().on_touch_up(touch)
    
    def on_view(self):
        """查看文档"""
        logger.info(f"查看文档: {self.document_id}, 类型: {self.document_type}")
        if self.parent_screen:
            self.parent_screen.on_view_document(self, self.document_id, self.document_type)
        else:
            logger.error("未设置父屏幕，无法处理查看事件")
    
    def on_delete(self):
        """删除文档"""
        logger.info(f"删除文档: {self.document_id}, 类型: {self.document_type}")
        if self.parent_screen:
            self.parent_screen.on_delete_document(self, self.document_id, self.document_type)
        else:
            logger.error("未设置父屏幕，无法处理删除事件")
            
    def on_touch_up(self, touch):
        """处理触摸事件，点击卡片时查看文档"""
        if self.collide_point(*touch.pos):
            # 确保不是点击了卡片上的按钮
            for child in self.walk(restrict=True):
                if isinstance(child, MDIconButton) and child.collide_point(*touch.pos):
                    return super().on_touch_up(touch)
            # 如果点击的是卡片本身，调用查看方法
            self.on_view()
        return super().on_touch_up(touch)

# 在类定义后注册Factory
from kivy.factory import Factory
Factory.register('DocumentCard', cls=DocumentCard)

class HealthDocumentScreen(BaseScreen):
    """健康资料管理屏幕"""
    title = StringProperty("健康资料管理")
    document_type = StringProperty("all")  # 当前文档类型，默认显示所有类型
    documents = ListProperty([])  # 文档列表
    filtered_documents = ListProperty([])  # 过滤后的文档列表
    is_loading = BooleanProperty(False)  # 是否正在加载
    upload_progress = NumericProperty(0)  # 上传进度
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.aggregator = get_health_data_aggregator()
        self.file_manager = None
        self.dialog = None
        self.filter_dialog = None
        self.active_filters = {}
        # 恢复文档类型映射定义
        self.document_types = {
            "hospital_records": {
                "title": "住院资料",
                "icon": "hospital-building",
                "description": "住院记录和病历资料"
            },
            "outpatient_records": {
                "title": "门诊资料",
                "icon": "doctor",
                "description": "门诊就诊记录和处方信息"
            },
            "lab_reports": {
                "title": "检验报告",
                "icon": "flask",
                "description": "各类检验检查报告"
            },
            "tech_diagnosis_reports": {
                "title": "技诊报告",
                "icon": "stethoscope",
                "description": "超声、CT、核磁等技术诊断报告"
            },
            "physical_exams": {
                "title": "体检报告",
                "icon": "clipboard-pulse-outline",
                "description": "体检报告及趋势分析"
            },
            "medications": {
                "title": "用药记录",
                "icon": "pill",
                "description": "药物处方和用药记录"
            },
            "questionnaire": {
                "title": "问卷调查",
                "icon": "clipboard-text",
                "description": "调查问卷和评估量表"
            },
            "assessment": {
                "title": "评估量表",
                "icon": "clipboard-check",
                "description": "健康评估和量表测试"
            }
        }
        # 延迟初始化UI
        Clock.schedule_once(self.init_ui, 0.1)
        
    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            # ids有效性判断
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("ids未初始化，跳过UI初始化")
                return
            
            # 设置标题
            if self.document_type != "all" and self.document_type in self.document_types:
                self.title = self.document_types[self.document_type]["title"]
            else:
                self.title = "健康资料管理"
            
            # 确保空状态框存在
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("ids未初始化，延迟初始化UI")
                Clock.schedule_once(self.init_ui, 0.5)
                return
            
            # 创建空状态框
            if 'documents_container' in self.ids:
                # 检查是否已经有empty_box
                has_empty_box = False
                for child in self.ids.documents_container.children:
                    if hasattr(child, 'id') and child.id == 'empty_box':
                        has_empty_box = True
                        break
                    
                # 如果没有empty_box，创建一个
                if not has_empty_box:
                    logger.info("创建空状态框")
                    empty_box = MDBoxLayout(
                        orientation='vertical',
                        size_hint_y=None,
                        height=dp(200),
                        padding=[0, dp(80), 0, 0],
                        opacity=0
                    )
                    empty_box.id = 'empty_box'
                    
                    empty_label = MDLabel(
                        text="暂无健康资料",
                        halign="center",
                        theme_text_color="Secondary"
                    )
                    
                    empty_box.add_widget(empty_label)
                    self.ids.documents_container.add_widget(empty_box)
                    self.ids.empty_box = empty_box
            
            # 设置文件管理器
            self.setup_file_manager()
            
            # 加载文档数据
            self.load_documents()
        except Exception as e:
            logger.error(f"init_ui异常: {e}")
    
    def on_enter(self):
        """屏幕进入时调用"""
        # 刷新文档数据
        self.load_documents()
    
    def on_leave(self):
        """屏幕离开时调用"""
        # 关闭文件管理器和对话框
        if self.file_manager:
            try:
                self.file_manager.close()
            except Exception as e:
                logger.warning(f"关闭文件管理器异常: {e}")
        if self.dialog:
            try:
                self.dialog.dismiss()
            except Exception as e:
                logger.warning(f"关闭对话框异常: {e}")
        if self.filter_dialog:
            try:
                self.filter_dialog.dismiss()
            except Exception as e:
                logger.warning(f"关闭筛选对话框异常: {e}")
    
    def setup_file_manager(self):
        """设置文件管理器"""
        self.file_manager = MDFileManager(
            exit_manager=self.exit_file_manager,
            select_path=self.select_file,
            preview=True
        )
    
    def load_documents(self, force_refresh=False):
        """加载文档数据"""
        # 显示加载状态
        self.is_loading = True
        
        # 检查ids是否已经初始化
        if not hasattr(self, 'ids') or not self.ids:
            # 延迟加载
            Clock.schedule_once(lambda dt: self.load_documents(force_refresh), 0.5)
            return
            
        # 检查empty_box是否存在
        if 'empty_box' in self.ids:
            self.ids.empty_box.opacity = 0
        
        # 清空文档容器
        if 'documents_container' in self.ids:
            self.ids.documents_container.clear_widgets()
            if 'empty_box' in self.ids:
                self.ids.documents_container.add_widget(self.ids.empty_box)
        
        # 启动后台线程加载数据
        threading.Thread(target=self._load_documents_thread, args=(force_refresh,)).start()
    
    def _load_documents_thread(self, force_refresh):
        """后台线程加载文档"""
        try:
            logger.info(f"开始加载健康资料文档，类型: {self.document_type}, 强制刷新: {force_refresh}")
            cloud_api = get_cloud_api()
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            if not custom_id:
                logger.warning("未获取到有效的custom_id，无法加载文档")
                self.documents = []
                Clock.schedule_once(self._show_empty_state)
                return
            # 加载特定类型
            if self.document_type != "all" and self.document_type in self.document_types:
                logger.info(f"加载特定类型文档: {self.document_type}")
                result = cloud_api.get_documents(document_type=self.document_type, custom_id=custom_id)
                logger.info(f"获取到 {self.document_type} 类型文档结果: {result}")
                documents = result.get("documents", []) if result else []
                logger.info(f"成功获取 {len(documents)} 个 {self.document_type} 类型文档")
                
                # 去重处理，防止重复显示相同标题的文档
                unique_titles = {}
                unique_documents = []
                for doc in documents:
                    title = doc.get("title", "")
                    # 如果标题为空，使用文件名
                    if not title and "file_name" in doc:
                        title = doc["file_name"]
                    # 如果还是空，使用ID
                    if not title:
                        title = str(doc.get("id", ""))
                    
                    # 只保留每个标题的最新文档
                    if title not in unique_titles:
                        unique_titles[title] = doc
                        unique_documents.append(doc)
                
                logger.info(f"去重后剩余 {len(unique_documents)}/{len(documents)} 个 {self.document_type} 类型文档")
                self.documents = unique_documents
            else:
                # 加载所有类型
                logger.info("加载所有类型文档")
                all_documents = []
                for doc_type in self.document_types.keys():
                    logger.info(f"加载 {doc_type} 类型文档")
                    result = cloud_api.get_documents(document_type=doc_type, custom_id=custom_id)
                    documents = result.get("documents", []) if result else []
                    logger.info(f"成功获取 {len(documents)} 个 {doc_type} 类型文档")
                    for doc in documents:
                        doc["document_type"] = doc_type
                    all_documents.extend(documents)
                
                # 去重处理，防止重复显示相同标题的文档
                unique_titles = {}
                unique_documents = []
                for doc in all_documents:
                    title = doc.get("title", "")
                    # 如果标题为空，使用文件名
                    if not title and "file_name" in doc:
                        title = doc["file_name"]
                    # 如果还是空，使用ID
                    if not title:
                        title = str(doc.get("id", ""))
                    
                    # 只保留每个标题的最新文档
                    if title not in unique_titles:
                        unique_titles[title] = doc
                        unique_documents.append(doc)
                
                logger.info(f"总共获取到 {len(all_documents)} 个文档，去重后剩余 {len(unique_documents)} 个")
                self.documents = unique_documents
                
            self.apply_filters()
            Clock.schedule_once(self._update_documents_ui)
            if not self.documents or len(self.documents) == 0:
                logger.info("没有获取到文档，显示空状态")
                Clock.schedule_once(self._show_empty_state)
        except Exception as e:
            logger.error(f"加载文档时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.documents = []
            Clock.schedule_once(self._show_empty_state)
    
    def _update_documents_ui(self, dt=None):
        """更新文档UI"""
        try:
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("ids未初始化，跳过UI刷新")
                return
            self.is_loading = False
            if 'documents_container' not in self.ids:
                logger.error("documents_container不存在")
                return
            self.ids.documents_container.clear_widgets()
            if not self.filtered_documents:
                logger.info("没有文档，显示空状态")
                self._show_empty_state()
                return
            logger.info(f"更新文档UI，共 {len(self.filtered_documents)} 个文档")
            for doc in self.filtered_documents:
                doc_type = doc.get("document_type", self.document_type)
                if doc_type not in self.document_types:
                    doc_type = "hospital_records"
                type_info = self.document_types[doc_type]
                # 修正：document_id强制转str，防御None
                doc_id = doc.get("id", "")
                if doc_id is None:
                    doc_id = ""
                card = DocumentCard(
                    title=doc.get("title", type_info["title"]),
                    date=self._format_date(doc.get("date", "")),
                    summary=doc.get("summary", type_info["description"]),
                    icon=type_info["icon"],
                    document_id=str(doc_id),
                    document_type=doc_type,
                    document_data=doc
                )
                card.parent_screen = self
                self.ids.documents_container.add_widget(card)
            logger.info(f"已添加 {len(self.filtered_documents)} 个文档卡片")
        except Exception as e:
            logger.error(f"_update_documents_ui异常: {e}")
    
    def _show_empty_state(self, dt=None):
        """显示空状态"""
        try:
            if not hasattr(self, 'ids') or not self.ids:
                return
            
            # 检查必要的组件是否存在
            if 'documents_container' in self.ids:
                logger.info("显示空状态")
                self.ids.documents_container.clear_widgets()
                
                # 如果empty_box不存在，创建一个
                if 'empty_box' not in self.ids:
                    logger.info("创建空状态框")
                    empty_box = MDBoxLayout(
                        id="empty_box",
                        orientation='vertical',
                        size_hint_y=None,
                        height=dp(200),
                        padding=[0, dp(80), 0, 0],
                        opacity=1
                    )
                    
                    empty_label = MDLabel(
                        text="暂无健康资料",
                        halign="center",
                        theme_text_color="Secondary"
                    )
                    
                    empty_box.add_widget(empty_label)
                    self.ids.documents_container.add_widget(empty_box)
                else:
                    # 使用现有的empty_box
                    logger.info("使用现有空状态框")
                    self.ids.empty_box.opacity = 1
                    if self.ids.empty_box not in self.ids.documents_container.children:
                        self.ids.documents_container.add_widget(self.ids.empty_box)
        except Exception as e:
            logger.error(f"_show_empty_state异常: {e}")
    
    def _format_date(self, date_str):
        """格式化日期"""
        if not date_str:
            return "未知日期"
        
        try:
            # 尝试解析日期
            date_formats = [
                "%Y-%m-%dT%H:%M:%S.%fZ",  # ISO格式带毫秒
                "%Y-%m-%dT%H:%M:%SZ",      # ISO格式
                "%Y-%m-%dT%H:%M:%S",       # ISO格式无Z
                "%Y-%m-%d %H:%M:%S",       # 标准格式
                "%Y-%m-%d"                  # 仅日期
            ]
            
            for fmt in date_formats:
                try:
                    if "Z" in fmt and "Z" not in date_str:
                        continue
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime("%Y年%m月%d日")
                except ValueError:
                    continue
            
            # 如果所有格式都失败，返回原始字符串
            return date_str
            
        except Exception:
            return date_str
    
    def filter_documents(self, dt=None):
        """过滤文档"""
        self.apply_filters()
        Clock.schedule_once(self._update_documents_ui)
    
    def apply_filters(self):
        """应用过滤器"""
        # 获取搜索文本
        search_text = ""
        if hasattr(self, 'ids') and 'search_field' in self.ids:
            search_text = self.ids.search_field.text.strip().lower()
        
        # 应用过滤器
        filtered = []
        for doc in self.documents:
            # 检查搜索文本
            if search_text:
                # 在标题、摘要和内容中搜索
                title = doc.get("title", "").lower()
                summary = doc.get("summary", "").lower()
                content = doc.get("content", "").lower()
                
                if (search_text in title or 
                    search_text in summary or 
                    search_text in content):
                    # 搜索匹配
                    pass
                else:
                    # 搜索不匹配，跳过
                    continue
            
            # 检查文档类型过滤器
            if self.document_type != "all" and doc.get("document_type") != self.document_type:
                continue
            
            # 检查活动过滤器
            skip = False
            for filter_key, filter_value in self.active_filters.items():
                if filter_key in doc and doc[filter_key] != filter_value:
                    skip = True
                    break
            
            if not skip:
                filtered.append(doc)
        
        # 更新过滤后的文档列表
        self.filtered_documents = filtered
    
    def show_filter_options(self):
        """显示过滤选项"""
        if self.filter_dialog:
            self.filter_dialog.dismiss()
        # 创建过滤选项按钮
        def make_button(text, filter_dict):
            return MDButton(text=text, on_release=lambda *a: (self.filter_dialog.dismiss(), self.apply_filter(filter_dict)))
        # 过滤选项
        filter_items = [
            ("全部文档", {"type": "all"})
        ]
        for doc_type, info in self.document_types.items():
            filter_items.append((info["title"], {"document_type": doc_type}))
        filter_items.extend([
            ("最近7天", {"date_range": "7days"}),
            ("最近30天", {"date_range": "30days"})
        ])
        # 兼容KivyMD 2.0.1 dev0，使用text+buttons弹窗
        self.filter_dialog = MDDialog(
            text="请选择过滤方式",
            buttons=[make_button(text, fdict) for text, fdict in filter_items]
        )
        self.filter_dialog.open()
    
    def on_filter_selected(self, filter_item):
        """过滤器选项被选中"""
        # 关闭对话框
        if self.filter_dialog:
            self.filter_dialog.dismiss()
        
        # 应用过滤器
        filter_data = filter_item["filter"]
        if "type" in filter_data and filter_data["type"] == "all":
            # 清除文档类型过滤器
            self.document_type = "all"
            self.title = "健康资料管理"
        elif "document_type" in filter_data:
            # 设置文档类型过滤器
            self.document_type = filter_data["document_type"]
            self.title = self.document_types[self.document_type]["title"]
        
        # 更新活动过滤器
        for key, value in filter_data.items():
            if key not in ["type", "document_type"]:
                self.active_filters[key] = value
        
        # 重新加载文档
        self.load_documents()
    
    def show_upload_options(self):
        """显示上传选项，100%兼容KivyMD 2.0.1dev0，使用自定义Popup+MDButton"""
        from kivy.uix.popup import Popup
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.button import MDButton, MDButtonText
        from kivy.metrics import dp
        layout = MDBoxLayout(orientation='vertical', spacing=dp(12), padding=dp(16))
        def make_btn(text, callback):
            btn = MDButton(MDButtonText(text=text), style="filled", size_hint_y=None, height=dp(48))
            btn.bind(on_release=lambda *a: (popup.dismiss(), callback(None)))
            return btn
        layout.add_widget(make_btn("文件上传", self.open_file_manager))
        layout.add_widget(make_btn("拍照上传", self.open_camera))
        layout.add_widget(make_btn("扫描二维码", self.scan_qr_code))
        popup = Popup(title="请选择上传方式", content=layout, size_hint=(.8, None), height=dp(300), auto_dismiss=True)
        popup.open()
    
    def open_file_manager(self, *args):
        """打开文件管理器"""
        # 关闭对话框
        if self.dialog:
            self.dialog.dismiss()
        # 初始化file_manager（兼容KivyMD 2.0.1 dev0）
        if not self.file_manager:
            self.file_manager = MDFileManager(
                exit_manager=self.exit_file_manager,
                select_path=self.select_file,
                preview=True
            )
        # 设置文件管理器路径
        self.file_manager.show(os.path.expanduser("~"))
    
    def select_file(self, path, *args):
        """选择文件回调"""
        self.file_manager.close()
        self.upload_file(path)
    
    def exit_file_manager(self, *args):
        """退出文件管理器"""
        logger.info("退出文件管理器")
        if hasattr(self, 'file_manager') and self.file_manager:
            try:
                # 检查file_manager是否有close方法
                if hasattr(self.file_manager, 'close'):
                    self.file_manager.close()
                # 检查file_manager是否有dismiss方法
                elif hasattr(self.file_manager, 'dismiss'):
                    self.file_manager.dismiss()
            except Exception as e:
                logger.warning(f"关闭文件管理器异常: {e}")
            finally:
                # 确保即使出错也将file_manager设为None
                self.file_manager = None
        else:
            logger.info("文件管理器已为空或不存在")
    
    def open_camera(self, *args):
        """打开相机"""
        if self.dialog:
            self.dialog.dismiss()
        self.show_info("相机功能待实现")
    
    def scan_qr_code(self, *args):
        """扫描二维码"""
        if self.dialog:
            self.dialog.dismiss()
        self.show_info("二维码扫描功能待实现")
    
    def upload_file(self, file_path, document_type=None):
        """上传文件，走cloud_api高层API，带custom_id，异常捕获和日志"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                self.show_error("文件不存在")
                return
                
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            logger.info(f"开始上传文件: {file_path}, 类型: {document_type}, 大小: {file_size} 字节")
            
            # 获取文件扩展名
            _, ext = os.path.splitext(file_path)
            logger.info(f"文件扩展名: {ext}")
            
            # 获取云API实例
            cloud_api = get_cloud_api()
            
            # 检查认证状态
            if not cloud_api.is_authenticated():
                logger.error("云API未认证，无法上传文件")
                self.show_error("未登录，无法上传文件")
                return
                
            # 获取用户ID
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            
            if not custom_id:
                logger.warning("未获取到有效的custom_id，无法上传文件")
                self.show_error("未获取到用户ID，无法上传")
                return
                
            # 组装元数据
            metadata = {'custom_id': custom_id}
            if document_type:
                metadata['document_type'] = document_type
                
            logger.info(f"上传文件元数据: {metadata}")
            
            # 走cloud_api高层API
            result = cloud_api.upload_file(file_path, metadata=metadata, document_type=document_type)
            logger.info(f"上传结果: {result}")
            
            if result and (result.get('success') or result.get('status') == 'success'):
                logger.info("文件上传成功")
                self.show_success("上传成功")
                self.load_documents(force_refresh=True)
            else:
                # 提取详细错误信息
                error_msg = None
                if isinstance(result, dict):
                    error_msg = result.get('message')
                    error_detail = result.get('detail')
                    if error_detail:
                        logger.error(f"上传失败详情: {error_detail}")
                        error_msg = f"{error_msg}: {error_detail}" if error_msg else f"错误: {error_detail}"
                
                if not error_msg:
                    error_msg = '上传失败，未知错误'
                
                logger.error(f"上传失败: {error_msg}")
                self.show_error(error_msg)
                
                # 检查是否需要添加到上传队列
                if cloud_api.is_in_local_mode() or "网络" in str(error_msg) or "连接" in str(error_msg):
                    logger.info("尝试添加到上传队列")
                    if cloud_api.add_to_upload_queue(file_path, metadata):
                        self.show_info("文件已添加到上传队列，将在网络恢复后自动上传")
        except Exception as e:
            logger.error(f"上传文件异常: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"上传失败: {e}")
            
    def show_success(self, message):
        """显示成功信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme.SUCCESS_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示成功提示失败: {e}")
            # 降级为普通提示
            self.show_info(message)
    
    def on_view_document(self, instance, document_id, document_type):
        """查看文档"""
        # 强制document_id为str
        document_id = str(document_id) if document_id is not None else ""
        logger.info(f"查看文档: {document_id}, 类型: {document_type}")
        
        try:
            # 获取文档数据
            document_data = None
            for doc in self.documents:
                if str(doc.get('id', '')) == document_id:
                    document_data = doc
                    break
            
            if not document_data:
                self.show_error("未找到文档数据")
                return
                
            # 显示加载对话框
            self.show_loading_dialog("正在加载文档...")
            
            # 在后台线程中下载文档
            threading.Thread(target=self._download_document_thread, args=(document_data,)).start()
        except Exception as e:
            logger.error(f"查看文档时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("查看文档时出错")
    
    def _download_document_thread(self, document_data):
        """后台线程下载文档"""
        try:
            # 获取云API实例
            cloud_api = get_cloud_api()
            
            # 获取文档ID
            document_id = document_data.get('id')
            if not document_id:
                Clock.schedule_once(lambda dt: self.show_error("文档ID无效"))
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
                return
            
            # 获取文档类型和文件名
            document_type = document_data.get('document_type', '')
            file_name = document_data.get('file_name', f'document_{document_id}')
            
            # 创建临时文件夹
            temp_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成临时文件路径
            temp_file_path = os.path.join(temp_dir, file_name)
            
            # 下载文档
            logger.info(f"开始下载文档: {document_id}, 保存到: {temp_file_path}")
            
            # 尝试下载，最多重试3次
            max_retries = 3
            retry_count = 0
            download_result = False
            last_error = None
            
            while retry_count < max_retries and not download_result:
                try:
                    logger.info(f"尝试下载文档 (尝试 {retry_count + 1}/{max_retries})")
                    download_result = cloud_api.download_document(document_id, temp_file_path)
                    if download_result:
                        break
                    
                    last_error = cloud_api.last_error
                    logger.warning(f"下载失败，错误: {last_error}")
                    
                    # 如果是502错误，等待一秒后重试
                    if "502" in str(last_error):
                        logger.info("检测到502错误，等待后重试")
                        time.sleep(1)
                    else:
                        # 其他错误不重试
                        break
                        
                except Exception as e:
                    logger.error(f"下载过程中出错: {e}")
                    last_error = str(e)
                    time.sleep(1)
                    
                retry_count += 1
            
            if not download_result or not os.path.exists(temp_file_path):
                error_msg = last_error if last_error else "未知错误"
                logger.error(f"下载文档失败: {document_id}, 错误: {error_msg}")
                
                # 显示更详细的错误信息
                if "502" in str(error_msg):
                    Clock.schedule_once(lambda dt: self.show_error("下载文档失败: 服务器暂时不可用 (502)，请稍后重试"))
                else:
                    Clock.schedule_once(lambda dt: self.show_error(f"下载文档失败: {error_msg}"))
                    
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
                return
            
            logger.info(f"文档下载成功: {temp_file_path}")
            
            # 根据文件类型选择不同的查看方式
            file_ext = os.path.splitext(file_name)[1].lower()
            
            # 在主线程中打开文档查看器
            Clock.schedule_once(lambda dt: self._open_document_viewer(temp_file_path, file_ext, document_data))
        except Exception as e:
            logger.error(f"下载文档线程出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            Clock.schedule_once(lambda dt: self.show_error(f"下载文档时出错: {str(e)}"))
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
    
    def _open_document_viewer(self, file_path, file_ext, document_data):
        """打开文档查看器"""
        try:
            # 关闭加载对话框
            self.dismiss_loading_dialog()
            
            app = MDApp.get_running_app()
            
            # 根据文件类型选择不同的查看器
            if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                # 图像文件
                if not app.root.has_screen('image_viewer_screen'):
                    from screens.image_viewer_screen import ImageViewerScreen
                    image_viewer = ImageViewerScreen(name='image_viewer_screen')
                    app.root.add_widget(image_viewer)
                else:
                    # 确保图像查看器被重新初始化
                    image_viewer = app.root.get_screen('image_viewer_screen')
                    Clock.schedule_once(image_viewer.init_ui, 0)
                
                # 设置图像路径
                app.image_to_view = file_path
                app.image_title = document_data.get('file_name', '图像文件')
                
                # 切换到图像查看器
                app.root.transition.direction = 'left'
                app.root.current = 'image_viewer_screen'
            elif file_ext in ['.pdf']:
                # PDF文件 - 使用系统默认应用打开
                import subprocess
                import platform
                
                system = platform.system()
                
                if system == 'Windows':
                    os.startfile(file_path)
                elif system == 'Darwin':  # macOS
                    subprocess.call(['open', file_path])
                else:  # Linux
                    subprocess.call(['xdg-open', file_path])
                
                self.show_info(f"已使用系统默认应用打开 {os.path.basename(file_path)}")
            elif file_ext in ['.txt', '.md', '.csv', '.json', '.xml', '.html', '.htm']:
                # 文本文件
                if not app.root.has_screen('text_viewer_screen'):
                    from screens.text_viewer_screen import TextViewerScreen
                    text_viewer = TextViewerScreen(name='text_viewer_screen')
                    app.root.add_widget(text_viewer)
                else:
                    # 确保文本查看器被重新初始化
                    text_viewer = app.root.get_screen('text_viewer_screen')
                    Clock.schedule_once(text_viewer.init_ui, 0)
                
                # 设置文本文件路径
                app.text_to_view = file_path
                app.text_title = document_data.get('file_name', '文本文件')
                
                # 切换到文本查看器
                app.root.transition.direction = 'left'
                app.root.current = 'text_viewer_screen'
            else:
                # 其他类型文件，尝试使用系统默认应用打开
                import subprocess
                import platform
                
                system = platform.system()
                
                try:
                    if system == 'Windows':
                        os.startfile(file_path)
                    elif system == 'Darwin':  # macOS
                        subprocess.call(['open', file_path])
                    else:  # Linux
                        subprocess.call(['xdg-open', file_path])
                    
                    self.show_info(f"已使用系统默认应用打开 {os.path.basename(file_path)}")
                except Exception as e:
                    logger.error(f"无法打开文件: {e}")
                    self.show_error(f"无法打开此类型的文件: {file_ext}")
        except Exception as e:
            logger.error(f"打开文档查看器时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开文档查看器时出错: {str(e)}")
            
    def show_loading_dialog(self, message="加载中..."):
        """显示加载对话框"""
        # 如果已经有对话框在显示，先关闭它
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            try:
                self.loading_dialog.dismiss()
            except:
                pass
            self.loading_dialog = None
        
        # 使用简单的对话框，避免复杂的参数问题
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.button import MDButton, MDButtonText
            
            self.loading_dialog = MDDialog(
                auto_dismiss=False,
                size_hint=(.8, None),
                height=dp(150)
            )
            
            # 手动添加内容
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            
            content_box = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                padding=dp(20),
                size_hint_y=None,
                height=dp(100)
            )
            
            content_box.add_widget(MDLabel(
                text=message,
                halign='center',
                theme_text_color="Primary"
            ))
            
            # 添加简单的加载指示
            content_box.add_widget(MDLabel(
                text="⏳ 请稍候...",
                halign='center',
                theme_text_color="Secondary"
            ))
            
            self.loading_dialog.add_widget(content_box)
            self.loading_dialog.open()
            
        except Exception as e:
            logger.error(f"创建加载对话框失败: {e}")
            # 如果对话框创建失败，至少显示一个简单的提示
            self.show_info(message)

    def dismiss_loading_dialog(self):
        """关闭加载对话框"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.dismiss()
            self.loading_dialog = None

    def exit_file_manager(self, *args):
        """退出文件管理器"""
        logger.info("退出文件管理器")
        if hasattr(self, 'file_manager') and self.file_manager:
            try:
                # 检查file_manager是否有close方法
                if hasattr(self.file_manager, 'close'):
                    self.file_manager.close()
                # 检查file_manager是否有dismiss方法
                elif hasattr(self.file_manager, 'dismiss'):
                    self.file_manager.dismiss()
            except Exception as e:
                logger.warning(f"关闭文件管理器异常: {e}")
            finally:
                # 确保即使出错也将file_manager设为None
                self.file_manager = None
        else:
            logger.info("文件管理器已为空或不存在")
    
    def on_delete_document(self, instance, document_id, document_type):
        """删除文档"""
        document_id = str(document_id) if document_id is not None else ""
        if self.dialog:
            self.dialog.dismiss()
        self.dialog = MDDialog(
            title="确认删除",
            text="确定要删除这个文档吗？此操作不可撤销。",
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="删除"),
                    style="text",
                    theme_text_color="Custom",
                    text_color=self.app.theme.ERROR_COLOR,
                    on_release=lambda x: self.confirm_delete_document(document_id, document_type)
                )
            ]
        )
        self.dialog.open()
    
    def confirm_delete_document(self, document_id, document_type):
        document_id = str(document_id) if document_id is not None else ""
        if self.dialog:
            self.dialog.dismiss()
        threading.Thread(target=self._delete_document_thread, args=(document_id, document_type)).start()
    
    def _delete_document_thread(self, document_id, document_type):
        document_id = str(document_id) if document_id is not None else ""
        try:
            cloud_api = get_cloud_api()
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            if not custom_id:
                Clock.schedule_once(lambda dt: self.show_error("未获取到有效的custom_id，无法删除"))
                return
            result = cloud_api.delete_document(document_id=document_id, document_type=document_type, custom_id=custom_id)
            if result and result.get("status") == "success":
                Clock.schedule_once(lambda dt: self.show_info("文档已删除"))
                Clock.schedule_once(lambda dt: self.load_documents(force_refresh=True), 1)
            else:
                error_msg = result.get("message", "未知错误") if result else "删除失败"
                Clock.schedule_once(lambda dt: self.show_error(f"删除文档失败: {error_msg}"))
        except Exception as e:
            logger.error(f"删除文档出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            Clock.schedule_once(lambda dt: self.show_error(f"删除文档失败: {str(e)}"))
    
    def navigate_to_document_type(self, document_type):
        """导航到特定类型的文档页面"""
        # 设置文档类型
        self.document_type = document_type
        
        # 设置标题
        if document_type in self.document_types:
            self.title = self.document_types[document_type]["title"]
        else:
            self.title = "健康资料管理"
        
        # 重新加载文档
        self.load_documents()

    def show_error(self, message):
        """显示错误信息
        
        Args:
            message (str): 错误信息
        """
        logger.error(message)
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")
            # 降级处理，使用简单的对话框
            try:
                from kivymd.uix.dialog import MDDialog
                
                dialog = MDDialog(
                    title="错误",
                    text=message,
                )
                dialog.open()
            except Exception as e2:
                logger.error(f"显示错误对话框也失败: {e2}")

    def show_info(self, message):
        """显示信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
            ).open()
        except Exception as e:
            logger.error(f"显示信息提示失败: {e}")
            # 降级处理，使用简单的对话框
            try:
                from kivymd.uix.dialog import MDDialog
                dialog = MDDialog(
                    title="提示",
                    text=message,
                )
                dialog.open()
            except Exception as e2:
                logger.error(f"显示信息对话框也失败: {e2}")

    def go_back(self, *args):
        """兼容KV和按钮事件的返回方法"""
        self.on_back()

# 在类定义后注册Factory和加载KV，顺序与其它页面一致
Factory.register('HealthDocumentScreen', cls=HealthDocumentScreen)
from kivy.lang import Builder
Builder.load_string(KV)
