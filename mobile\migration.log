2025-07-02 23:05:41,880 - INFO - 备份目录创建: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230541
2025-07-02 23:05:41,916 - INFO - 开始完整迁移流程...
2025-07-02 23:05:41,927 - INFO - 执行步骤: 备份原始文件
2025-07-02 23:05:41,943 - INFO - 开始备份原始文件...
2025-07-02 23:05:42,090 - INFO - ✓ 备份文件: main.py
2025-07-02 23:05:42,129 - INFO - ✓ 备份文件: theme.py
2025-07-02 23:05:42,132 - WARNING - ⚠ 备份文件: api/api_config.py 不存在
2025-07-02 23:05:42,136 - INFO - ✓ 备份文件: api/api_client.py
2025-07-02 23:05:42,177 - INFO - ✓ 备份文件: screens/homepage_screen.py
2025-07-02 23:05:42,186 - WARNING - ⚠ 备份文件: config.json 不存在
2025-07-02 23:05:43,931 - INFO - ✓ 备份目录: data/
2025-07-02 23:05:44,327 - INFO - ✓ 备份目录: logs/
2025-07-02 23:05:44,349 - INFO - ✓ 备份完成: 共备份 6 个文件/目录
2025-07-02 23:05:44,423 - INFO - ✓ 备份原始文件 完成
2025-07-02 23:05:44,498 - INFO - 执行步骤: 迁移配置文件
2025-07-02 23:05:44,608 - INFO - 开始迁移配置文件...
2025-07-02 23:05:44,650 - INFO - ✓ 配置迁移: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json
2025-07-02 23:05:44,684 - INFO - ✓ 迁移配置文件 完成
2025-07-02 23:05:44,782 - INFO - 执行步骤: 迁移数据库
2025-07-02 23:05:44,826 - INFO - 开始迁移数据库...
2025-07-02 23:05:44,868 - WARNING - ⚠ 数据库迁移: 未找到可迁移的数据库文件
2025-07-02 23:05:44,901 - INFO - ✓ 迁移数据库 完成
2025-07-02 23:05:44,952 - INFO - 执行步骤: 迁移用户数据
2025-07-02 23:05:45,010 - INFO - 开始迁移用户数据...
2025-07-02 23:05:45,058 - INFO - ✓ 用户数据迁移: data/users/
2025-07-02 23:05:45,233 - INFO - ✓ 用户数据迁移: cache/
2025-07-02 23:05:45,332 - INFO - ✓ 用户数据迁移完成: 共迁移 0 个文件
2025-07-02 23:05:45,333 - INFO - ✓ 迁移用户数据 完成
2025-07-02 23:05:45,374 - INFO - 执行步骤: 更新导入语句
2025-07-02 23:05:45,374 - INFO - 开始更新导入语句...
2025-07-02 23:05:48,324 - INFO - ✓ 更新导入: fix_kivymd_compatibility.py
2025-07-02 23:05:48,425 - INFO - ✓ 更新导入: main.py
2025-07-02 23:05:48,575 - INFO - ✓ 更新导入: migration_tool.py
2025-07-02 23:05:48,619 - INFO - ✓ 更新导入: run_tests.py
2025-07-02 23:05:48,648 - INFO - ✓ 更新导入: test_management_log.py
2025-07-02 23:05:48,665 - INFO - ✓ 更新导入: test_mobile_fixes.py
2025-07-02 23:05:48,835 - INFO - ✓ 更新导入: assessment_form_screen.py
2025-07-02 23:05:49,097 - INFO - ✓ 更新导入: assessment_screen.py
2025-07-02 23:05:49,308 - INFO - ✓ 更新导入: basic_health_info_screen.py
2025-07-02 23:05:49,552 - INFO - ✓ 更新导入: companion_service_screen.py
2025-07-02 23:05:49,633 - INFO - ✓ 更新导入: consultant_screen.py
2025-07-02 23:05:49,677 - INFO - ✓ 更新导入: document_list_screen.py
2025-07-02 23:05:49,681 - INFO - ✓ 更新导入: health_diary_screen.py
2025-07-02 23:05:49,685 - INFO - ✓ 更新导入: health_document_screen.py
2025-07-02 23:05:49,692 - INFO - ✓ 更新导入: health_overview_screen.py
2025-07-02 23:05:49,830 - INFO - ✓ 更新导入: homepage_screen.py
2025-07-02 23:05:49,896 - INFO - ✓ 更新导入: hospital_records_screen.py
2025-07-02 23:05:49,995 - INFO - ✓ 更新导入: lab_report_screen.py
2025-07-02 23:05:50,081 - INFO - ✓ 更新导入: login_screen.py
2025-07-02 23:05:50,148 - INFO - ✓ 更新导入: log_screen.py
2025-07-02 23:05:50,261 - INFO - ✓ 更新导入: management_log_screen.py
2025-07-02 23:05:50,362 - INFO - ✓ 更新导入: medical_records_screen.py
2025-07-02 23:05:50,582 - INFO - ✓ 更新导入: medication_management_screen.py
2025-07-02 23:05:50,692 - INFO - ✓ 更新导入: other_records_screen.py
2025-07-02 23:05:50,762 - INFO - ✓ 更新导入: outpatient_records_screen.py
2025-07-02 23:05:50,808 - INFO - ✓ 更新导入: personalized_checkup_screen.py
2025-07-02 23:05:50,927 - INFO - ✓ 更新导入: physical_exam_screen.py
2025-07-02 23:05:50,985 - INFO - ✓ 更新导入: profile_page.py
2025-07-02 23:05:51,101 - INFO - ✓ 更新导入: public_screen.py
2025-07-02 23:05:51,301 - INFO - ✓ 更新导入: questionnaire_form_screen.py
2025-07-02 23:05:51,414 - INFO - ✓ 更新导入: register_screen.py
2025-07-02 23:05:51,541 - INFO - ✓ 更新导入: supermanager_screen.py
2025-07-02 23:05:51,662 - INFO - ✓ 更新导入: survey_screen.py
2025-07-02 23:05:51,759 - INFO - ✓ 更新导入: tech_diagnosis_report_screen.py
2025-07-02 23:05:51,809 - INFO - ✓ 更新导入: unit_screen.py
2025-07-02 23:05:51,827 - INFO - ✓ 更新导入: voice_triage_screen.py
2025-07-02 23:05:51,841 - INFO - ✓ 更新导入: app_metrics.py
2025-07-02 23:05:51,863 - INFO - ✓ 更新导入: common_components.py
2025-07-02 23:05:51,878 - INFO - ✓ 更新导入: health_data_aggregator.py
2025-07-02 23:05:51,906 - INFO - ✓ 更新导入: triage_manager.py
2025-07-02 23:05:51,913 - INFO - ✓ 更新导入: camera_view.py
2025-07-02 23:05:51,926 - INFO - ✓ 更新导入: logo.py
2025-07-02 23:05:52,760 - INFO - ✓ 更新导入: font_definitions.py
2025-07-02 23:05:52,943 - INFO - ✓ 更新导入: theming.py
2025-07-02 23:05:57,579 - INFO - ✓ 更新导入: label.py
2025-07-02 23:05:57,812 - INFO - ✓ 更新导入: textfield.py
2025-07-02 23:05:59,894 - INFO - ✓ 导入更新完成: 共更新 46 个文件
2025-07-02 23:05:59,965 - INFO - ✓ 更新导入语句 完成
2025-07-02 23:06:00,030 - INFO - 执行步骤: 创建迁移脚本
2025-07-02 23:06:00,062 - ERROR - ✗ 创建迁移脚本: name 'missing_files' is not defined
2025-07-02 23:06:00,089 - WARNING - ⚠ 创建迁移脚本 部分失败
2025-07-02 23:06:00,131 - INFO - 迁移报告已保存: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230541\migration_report.md
2025-07-02 23:06:00,144 - INFO - 
迁移完成: 5/6 个步骤成功
2025-07-02 23:06:00,151 - INFO - 迁移报告: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230541\migration_report.md
2025-07-02 23:06:00,161 - INFO - 备份目录: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230541
2025-07-02 23:06:00,188 - WARNING - ⚠️ 迁移部分完成，请检查报告
2025-07-02 23:06:15,645 - INFO - 备份目录创建: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230615
2025-07-02 23:06:15,645 - INFO - 开始完整迁移流程...
2025-07-02 23:06:15,646 - INFO - 执行步骤: 备份原始文件
2025-07-02 23:06:15,646 - INFO - 开始备份原始文件...
2025-07-02 23:06:15,655 - INFO - ✓ 备份文件: main.py
2025-07-02 23:06:15,658 - INFO - ✓ 备份文件: theme.py
2025-07-02 23:06:15,659 - WARNING - ⚠ 备份文件: api/api_config.py 不存在
2025-07-02 23:06:15,663 - INFO - ✓ 备份文件: api/api_client.py
2025-07-02 23:06:15,669 - INFO - ✓ 备份文件: screens/homepage_screen.py
2025-07-02 23:06:15,671 - WARNING - ⚠ 备份文件: config.json 不存在
2025-07-02 23:06:15,938 - INFO - ✓ 备份目录: data/
2025-07-02 23:06:16,019 - INFO - ✓ 备份目录: logs/
2025-07-02 23:06:16,020 - INFO - ✓ 备份完成: 共备份 6 个文件/目录
2025-07-02 23:06:16,021 - INFO - ✓ 备份原始文件 完成
2025-07-02 23:06:16,021 - INFO - 执行步骤: 迁移配置文件
2025-07-02 23:06:16,021 - INFO - 开始迁移配置文件...
2025-07-02 23:06:16,024 - INFO - ✓ 配置迁移: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json
2025-07-02 23:06:16,025 - INFO - ✓ 迁移配置文件 完成
2025-07-02 23:06:16,025 - INFO - 执行步骤: 迁移数据库
2025-07-02 23:06:16,026 - INFO - 开始迁移数据库...
2025-07-02 23:06:16,027 - WARNING - ⚠ 数据库迁移: 未找到可迁移的数据库文件
2025-07-02 23:06:16,028 - INFO - ✓ 迁移数据库 完成
2025-07-02 23:06:16,033 - INFO - 执行步骤: 迁移用户数据
2025-07-02 23:06:16,039 - INFO - 开始迁移用户数据...
2025-07-02 23:06:16,041 - INFO - ✓ 用户数据迁移: data/users/
2025-07-02 23:06:16,044 - INFO - ✓ 用户数据迁移: cache/
2025-07-02 23:06:16,045 - INFO - ✓ 用户数据迁移完成: 共迁移 0 个文件
2025-07-02 23:06:16,051 - INFO - ✓ 迁移用户数据 完成
2025-07-02 23:06:16,052 - INFO - 执行步骤: 更新导入语句
2025-07-02 23:06:16,052 - INFO - 开始更新导入语句...
2025-07-02 23:06:17,420 - INFO - ✓ 更新导入: migration_tool.py
2025-07-02 23:06:17,789 - INFO - ✓ 更新导入: run_tests.py
2025-07-02 23:06:18,063 - INFO - ✓ 更新导入: test_mobile_fixes.py
2025-07-02 23:06:18,225 - INFO - ✓ 更新导入: logo.py
2025-07-02 23:06:18,297 - INFO - ✓ 更新导入: font_definitions.py
2025-07-02 23:06:18,326 - INFO - ✓ 更新导入: theming.py
2025-07-02 23:06:19,654 - INFO - ✓ 更新导入: label.py
2025-07-02 23:06:19,672 - INFO - ✓ 更新导入: textfield.py
2025-07-02 23:06:20,515 - INFO - ✓ 导入更新完成: 共更新 8 个文件
2025-07-02 23:06:20,523 - INFO - ✓ 更新导入语句 完成
2025-07-02 23:06:20,523 - INFO - 执行步骤: 创建迁移脚本
2025-07-02 23:06:20,579 - ERROR - ✗ 创建迁移脚本: name 'missing_files' is not defined
2025-07-02 23:06:20,580 - WARNING - ⚠ 创建迁移脚本 部分失败
2025-07-02 23:06:20,582 - INFO - 迁移报告已保存: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230615\migration_report.md
2025-07-02 23:06:20,595 - INFO - 
迁移完成: 5/6 个步骤成功
2025-07-02 23:06:20,607 - INFO - 迁移报告: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230615\migration_report.md
2025-07-02 23:06:20,663 - INFO - 备份目录: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230615
2025-07-02 23:06:20,715 - WARNING - ⚠️ 迁移部分完成，请检查报告
2025-07-02 23:07:46,999 - INFO - 备份目录创建: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230746
2025-07-02 23:07:47,022 - INFO - 开始完整迁移流程...
2025-07-02 23:07:47,025 - INFO - 执行步骤: 备份原始文件
2025-07-02 23:07:47,031 - INFO - 开始备份原始文件...
2025-07-02 23:07:47,050 - INFO - ✓ 备份文件: main.py
2025-07-02 23:07:47,067 - INFO - ✓ 备份文件: theme.py
2025-07-02 23:07:47,070 - WARNING - ⚠ 备份文件: api/api_config.py 不存在
2025-07-02 23:07:47,094 - INFO - ✓ 备份文件: api/api_client.py
2025-07-02 23:07:47,118 - INFO - ✓ 备份文件: screens/homepage_screen.py
2025-07-02 23:07:47,123 - WARNING - ⚠ 备份文件: config.json 不存在
2025-07-02 23:07:47,501 - INFO - ✓ 备份目录: data/
2025-07-02 23:07:47,585 - INFO - ✓ 备份目录: logs/
2025-07-02 23:07:47,585 - INFO - ✓ 备份完成: 共备份 6 个文件/目录
2025-07-02 23:07:47,586 - INFO - ✓ 备份原始文件 完成
2025-07-02 23:07:47,586 - INFO - 执行步骤: 迁移配置文件
2025-07-02 23:07:47,587 - INFO - 开始迁移配置文件...
2025-07-02 23:07:47,590 - INFO - ✓ 配置迁移: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json
2025-07-02 23:07:47,591 - INFO - ✓ 迁移配置文件 完成
2025-07-02 23:07:47,591 - INFO - 执行步骤: 迁移数据库
2025-07-02 23:07:47,596 - INFO - 开始迁移数据库...
2025-07-02 23:07:47,597 - WARNING - ⚠ 数据库迁移: 未找到可迁移的数据库文件
2025-07-02 23:07:47,598 - INFO - ✓ 迁移数据库 完成
2025-07-02 23:07:47,605 - INFO - 执行步骤: 迁移用户数据
2025-07-02 23:07:47,620 - INFO - 开始迁移用户数据...
2025-07-02 23:07:47,622 - INFO - ✓ 用户数据迁移: data/users/
2025-07-02 23:07:47,630 - INFO - ✓ 用户数据迁移: cache/
2025-07-02 23:07:47,633 - INFO - ✓ 用户数据迁移完成: 共迁移 0 个文件
2025-07-02 23:07:47,635 - INFO - ✓ 迁移用户数据 完成
2025-07-02 23:07:47,636 - INFO - 执行步骤: 更新导入语句
2025-07-02 23:07:47,637 - INFO - 开始更新导入语句...
2025-07-02 23:07:49,356 - INFO - ✓ 更新导入: migration_tool.py
2025-07-02 23:07:49,362 - INFO - ✓ 更新导入: run_tests.py
2025-07-02 23:07:49,378 - INFO - ✓ 更新导入: test_mobile_fixes.py
2025-07-02 23:07:49,461 - INFO - ✓ 更新导入: logo.py
2025-07-02 23:07:49,697 - INFO - ✓ 更新导入: font_definitions.py
2025-07-02 23:07:49,819 - INFO - ✓ 更新导入: theming.py
2025-07-02 23:07:51,780 - INFO - ✓ 更新导入: label.py
2025-07-02 23:07:51,953 - INFO - ✓ 更新导入: textfield.py
2025-07-02 23:07:53,241 - INFO - ✓ 导入更新完成: 共更新 8 个文件
2025-07-02 23:07:53,315 - INFO - ✓ 更新导入语句 完成
2025-07-02 23:07:53,338 - INFO - 执行步骤: 创建迁移脚本
2025-07-02 23:07:53,349 - ERROR - ✗ 创建迁移脚本: name 'missing_files' is not defined
2025-07-02 23:07:53,355 - WARNING - ⚠ 创建迁移脚本 部分失败
2025-07-02 23:07:53,366 - INFO - 迁移报告已保存: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230746\migration_report.md
2025-07-02 23:07:53,377 - INFO - 
迁移完成: 5/6 个步骤成功
2025-07-02 23:07:53,398 - INFO - 迁移报告: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230746\migration_report.md
2025-07-02 23:07:53,404 - INFO - 备份目录: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230746
2025-07-02 23:07:53,407 - WARNING - ⚠️ 迁移部分完成，请检查报告
2025-07-02 23:08:51,231 - INFO - 备份目录创建: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230851
2025-07-02 23:08:51,237 - INFO - 开始完整迁移流程...
2025-07-02 23:08:51,244 - INFO - 执行步骤: 备份原始文件
2025-07-02 23:08:51,245 - INFO - 开始备份原始文件...
2025-07-02 23:08:51,253 - INFO - ✓ 备份文件: main.py
2025-07-02 23:08:51,312 - INFO - ✓ 备份文件: theme.py
2025-07-02 23:08:51,315 - WARNING - ⚠ 备份文件: api/api_config.py 不存在
2025-07-02 23:08:51,322 - INFO - ✓ 备份文件: api/api_client.py
2025-07-02 23:08:51,347 - INFO - ✓ 备份文件: screens/homepage_screen.py
2025-07-02 23:08:51,350 - WARNING - ⚠ 备份文件: config.json 不存在
2025-07-02 23:08:51,638 - INFO - ✓ 备份目录: data/
2025-07-02 23:08:51,666 - INFO - ✓ 备份目录: logs/
2025-07-02 23:08:51,667 - INFO - ✓ 备份完成: 共备份 6 个文件/目录
2025-07-02 23:08:51,668 - INFO - ✓ 备份原始文件 完成
2025-07-02 23:08:51,669 - INFO - 执行步骤: 迁移配置文件
2025-07-02 23:08:51,669 - INFO - 开始迁移配置文件...
2025-07-02 23:08:51,674 - INFO - ✓ 配置迁移: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json
2025-07-02 23:08:51,676 - INFO - ✓ 迁移配置文件 完成
2025-07-02 23:08:51,677 - INFO - 执行步骤: 迁移数据库
2025-07-02 23:08:51,678 - INFO - 开始迁移数据库...
2025-07-02 23:08:51,679 - WARNING - ⚠ 数据库迁移: 未找到可迁移的数据库文件
2025-07-02 23:08:51,680 - INFO - ✓ 迁移数据库 完成
2025-07-02 23:08:51,681 - INFO - 执行步骤: 迁移用户数据
2025-07-02 23:08:51,681 - INFO - 开始迁移用户数据...
2025-07-02 23:08:51,684 - INFO - ✓ 用户数据迁移: data/users/
2025-07-02 23:08:51,686 - INFO - ✓ 用户数据迁移: cache/
2025-07-02 23:08:51,687 - INFO - ✓ 用户数据迁移完成: 共迁移 0 个文件
2025-07-02 23:08:51,693 - INFO - ✓ 迁移用户数据 完成
2025-07-02 23:08:51,698 - INFO - 执行步骤: 更新导入语句
2025-07-02 23:08:51,699 - INFO - 开始更新导入语句...
2025-07-02 23:08:53,948 - INFO - ✓ 更新导入: migration_tool.py
2025-07-02 23:08:53,954 - INFO - ✓ 更新导入: run_tests.py
2025-07-02 23:08:53,968 - INFO - ✓ 更新导入: test_mobile_fixes.py
2025-07-02 23:08:54,050 - INFO - ✓ 更新导入: logo.py
2025-07-02 23:08:54,118 - INFO - ✓ 更新导入: font_definitions.py
2025-07-02 23:08:54,132 - INFO - ✓ 更新导入: theming.py
2025-07-02 23:08:55,432 - INFO - ✓ 更新导入: label.py
2025-07-02 23:08:55,600 - INFO - ✓ 更新导入: textfield.py
2025-07-02 23:08:56,424 - INFO - ✓ 导入更新完成: 共更新 8 个文件
2025-07-02 23:08:56,514 - INFO - ✓ 更新导入语句 完成
2025-07-02 23:08:56,739 - INFO - 执行步骤: 创建迁移脚本
2025-07-02 23:08:56,911 - INFO - ✓ 创建迁移脚本: C:\Users\<USER>\Desktop\health-Trea\mobile\check_migration.py
2025-07-02 23:08:57,102 - INFO - ✓ 创建迁移脚本 完成
2025-07-02 23:08:57,197 - INFO - 迁移报告已保存: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230851\migration_report.md
2025-07-02 23:08:57,284 - INFO - 
迁移完成: 6/6 个步骤成功
2025-07-02 23:08:57,511 - INFO - 迁移报告: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230851\migration_report.md
2025-07-02 23:08:57,597 - INFO - 备份目录: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230851
2025-07-02 23:08:57,699 - INFO - 🎉 迁移成功完成！
