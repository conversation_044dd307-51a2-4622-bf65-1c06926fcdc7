#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用药管理屏幕模块

提供完整的用药记录管理功能，包括：
1. 当前用药管理 - 查看、添加、编辑正在使用的药物
2. 既往用药管理 - 查看历史用药记录和停药原因
3. 用药提醒设置 - 为药物设置定时提醒
4. 数据导出分享 - 支持用药记录的导出和分享
5. 数据持久化 - 本地数据库存储 + 云端同步

技术特性：
- 基于KivyMD 2.0.1框架开发
- 支持本地SQLite数据库存储
- 集成云端API同步
- 实现推送通知提醒
- 支持数据导出和分享
- 优化性能，支持分页加载
- 敏感数据加密存储
"""

import os
import json
import logging
import sqlite3
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# Kivy/KivyMD imports
from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.factory import Factory
from kivy.utils import platform

# KivyMD imports
from kivy.app import App
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldTrailingIcon
from kivymd.uix.selectioncontrol import MDCheckbox, MDSwitch
from kivymd.uix.progressindicator import MDCircularProgressIndicator

# 项目内部imports
from screens.base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import AppTheme, AppMetrics, FontStyles
from utils.cloud_api import get_cloud_api
from utils.storage import UserStorage
from utils.common_components import BaseFormField, BaseButton
from utils.date_picker_utils import get_date_picker_manager

# 尝试导入通知模块
try:
    if platform == 'android':
        from plyer import notification
        NOTIFICATION_AVAILABLE = True
    else:
        NOTIFICATION_AVAILABLE = False
except ImportError:
    NOTIFICATION_AVAILABLE = False
    KivyLogger.warning("[MedicationManagement] 通知模块不可用")

# 配置日志
logger = logging.getLogger(__name__)

# 数据库配置

MEDICATIONS_TABLE = "medications"
REMINDERS_TABLE = "medication_reminders"

# 分页配置已移除 - 直接加载所有数据

class MedicationDatabaseManager:
    """用药数据库管理器"""

    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        if db_path is None:
            # 使用应用数据目录
            app_data_dir = Path.home() / ".health_management" / "data"
            app_data_dir.mkdir(parents=True, exist_ok=True)
            db_path = app_data_dir / "medications.db"

        self.db_path = str(db_path)
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建用药记录表
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {MEDICATIONS_TABLE} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        custom_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        dosage TEXT NOT NULL,
                        frequency TEXT NOT NULL,
                        start_date TEXT NOT NULL,
                        end_date TEXT,
                        reason TEXT,
                        notes TEXT,
                        status TEXT DEFAULT 'active',
                        stop_reason TEXT,
                        stop_date TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL,
                        encrypted_data TEXT
                    )
                ''')

                # 创建用药提醒表
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {REMINDERS_TABLE} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        medication_id INTEGER NOT NULL,
                        custom_id TEXT NOT NULL,
                        reminder_time TEXT NOT NULL,
                        reminder_type TEXT DEFAULT 'daily',
                        is_active BOOLEAN DEFAULT 1,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (medication_id) REFERENCES {MEDICATIONS_TABLE} (id)
                    )
                ''')

                # 创建索引
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_medications_custom_id ON {MEDICATIONS_TABLE} (custom_id)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_medications_status ON {MEDICATIONS_TABLE} (status)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_reminders_custom_id ON {REMINDERS_TABLE} (custom_id)')

                conn.commit()
                KivyLogger.info("[MedicationDB] 数据库初始化完成")

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 数据库初始化失败: {e}")
            raise

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        try:
            # 简单的哈希加密，实际应用中应使用更强的加密算法
            return hashlib.sha256(data.encode()).hexdigest()
        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 数据加密失败: {e}")
            return data

    def save_medication(self, custom_id: str, medication_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """保存用药记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now().isoformat()

                # 加密敏感数据
                encrypted_notes = self.encrypt_sensitive_data(medication_data.get('notes', ''))

                cursor.execute(f'''
                    INSERT INTO {MEDICATIONS_TABLE}
                    (custom_id, name, dosage, frequency, start_date, end_date, reason, notes,
                     status, created_at, updated_at, encrypted_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    custom_id,
                    medication_data['name'],
                    medication_data['dosage'],
                    medication_data['frequency'],
                    medication_data['start_date'],
                    medication_data.get('end_date'),
                    medication_data.get('reason'),
                    medication_data.get('notes'),
                    'active',
                    now,
                    now,
                    encrypted_notes
                ))

                # 获取插入的记录ID
                medication_id = cursor.lastrowid

                # 查询刚插入的完整记录
                cursor.execute(f'''
                    SELECT id, custom_id, name, dosage, frequency, start_date, end_date,
                           reason, notes, status, created_at, updated_at
                    FROM {MEDICATIONS_TABLE}
                    WHERE id = ?
                ''', (medication_id,))

                row = cursor.fetchone()
                if row:
                    # 构建返回的药物数据
                    saved_medication = {
                        'id': row[0],
                        'custom_id': row[1],
                        'name': row[2],
                        'dosage': row[3],
                        'frequency': row[4],
                        'start_date': row[5],
                        'end_date': row[6],
                        'reason': row[7],
                        'notes': row[8],
                        'status': row[9],
                        'created_at': row[10],
                        'updated_at': row[11]
                    }

                conn.commit()
                KivyLogger.info(f"[MedicationDB] 保存用药记录成功: {medication_data['name']}, ID: {medication_id}")
                return saved_medication

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 保存用药记录失败: {e}")
            return None

    def update_medication_status(self, medication_id: int, status: str, stop_date: str = None, stop_reason: str = None) -> bool:
        """更新药物状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now().isoformat()

                if status == 'stopped':
                    # 停药状态，更新停药日期和停药原因
                    formatted_stop_date = stop_date
                    if stop_date and 'T' in stop_date:
                        # 如果传入的是完整日期时间，只取日期部分
                        formatted_stop_date = stop_date.split('T')[0]
                    elif not stop_date:
                        # 如果没有提供停药日期，使用当前日期
                        formatted_stop_date = datetime.now().strftime("%Y-%m-%d")

                    cursor.execute(f'''
                        UPDATE {MEDICATIONS_TABLE}
                        SET status = ?, stop_date = ?, stop_reason = ?, updated_at = ?
                        WHERE id = ?
                    ''', (status, formatted_stop_date, stop_reason, now, medication_id))
                else:
                    # 其他状态更新
                    cursor.execute(f'''
                        UPDATE {MEDICATIONS_TABLE}
                        SET status = ?, updated_at = ?
                        WHERE id = ?
                    ''', (status, now, medication_id))

                conn.commit()

                if cursor.rowcount > 0:
                    KivyLogger.info(f"[MedicationDB] 药物状态更新成功: ID {medication_id}, 状态: {status}")
                    return True
                else:
                    KivyLogger.warning(f"[MedicationDB] 未找到要更新的药物: ID {medication_id}")
                    return False

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 更新药物状态失败: {e}")
            return False

    def delete_medication(self, medication_id: int) -> bool:
        """删除药物记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(f'''
                    DELETE FROM {MEDICATIONS_TABLE}
                    WHERE id = ?
                ''', (medication_id,))

                conn.commit()

                if cursor.rowcount > 0:
                    KivyLogger.info(f"[MedicationDB] 药物删除成功: ID {medication_id}")
                    return True
                else:
                    KivyLogger.warning(f"[MedicationDB] 未找到要删除的药物: ID {medication_id}")
                    return False

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 删除药物失败: {e}")
            return False

    def get_medications(self, custom_id: str, status: str = 'active') -> List[Dict[str, Any]]:
        """获取用药记录（包含提醒设置）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(f'''
                    SELECT * FROM {MEDICATIONS_TABLE}
                    WHERE custom_id = ? AND status = ?
                    ORDER BY created_at DESC
                ''', (custom_id, status))

                columns = [description[0] for description in cursor.description]
                medications = []

                for row in cursor.fetchall():
                    medication = dict(zip(columns, row))
                    
                    # 获取该药物的提醒设置
                    medication_id = medication.get('id')
                    if medication_id:
                        reminder_settings = self._get_medication_reminders(medication_id)
                        medication['reminder_settings'] = reminder_settings
                    else:
                        medication['reminder_settings'] = {}
                    
                    medications.append(medication)

                KivyLogger.info(f"[MedicationDB] 获取用药记录成功: {len(medications)} 条")
                return medications

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 获取用药记录失败: {e}")
            return []
    
    def _get_medication_reminders(self, medication_id: int) -> Dict[str, Any]:
        """获取指定药物的提醒设置"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute(f'''
                    SELECT reminder_time, reminder_type FROM {REMINDERS_TABLE}
                    WHERE medication_id = ? AND is_active = 1
                    ORDER BY created_at DESC
                ''', (medication_id,))
                
                reminders = cursor.fetchall()
                if not reminders:
                    return {}
                
                # 组装提醒设置数据
                reminder_settings = {
                    'med_reminder': {'enabled': False, 'times': []},
                    'review_reminder': {'enabled': False}
                }
                
                for reminder_time, reminder_type in reminders:
                    try:
                        # 解析提醒数据（假设是JSON格式）
                        import json
                        reminder_data = json.loads(reminder_time) if reminder_time.startswith('{') else {'time': reminder_time}
                        
                        if reminder_type == 'medication' or reminder_type == 'daily':
                            # 服药提醒
                            reminder_settings['med_reminder']['enabled'] = True
                            if 'times' in reminder_data:
                                # 新格式：包含多个时间段的数组
                                reminder_settings['med_reminder']['times'] = reminder_data['times']
                            elif 'time' in reminder_data:
                                # 单个时间段格式：转换为数组格式
                                time_setting = {
                                    'time': reminder_data['time'],
                                    'advance_minutes': reminder_data.get('advance_minutes', '15')
                                }
                                reminder_settings['med_reminder']['times'].append(time_setting)
                            else:
                                # 兼容旧格式：直接保存时间和提前分钟
                                reminder_settings['med_reminder']['time'] = reminder_data.get('time', '')
                                reminder_settings['med_reminder']['advance_minutes'] = reminder_data.get('advance_minutes', '15')
                        elif reminder_type == 'review':
                            # 复查提醒
                            reminder_settings['review_reminder']['enabled'] = True
                            reminder_settings['review_reminder']['date'] = reminder_data.get('date', '')
                            reminder_settings['review_reminder']['advance_days'] = reminder_data.get('advance_days', '3')
                    except Exception as parse_error:
                        KivyLogger.error(f"[MedicationDB] 解析提醒数据失败: {parse_error}")
                        continue
                
                return reminder_settings
                
        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 获取药物提醒设置失败: {e}")
            return {}

    def stop_medication(self, medication_id: int, stop_reason: str) -> bool:
        """停用药物"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now()
                stop_date = now.strftime("%Y-%m-%d")  # 只保存日期部分
                updated_at = now.isoformat()  # 更新时间保持完整格式

                cursor.execute(f'''
                    UPDATE {MEDICATIONS_TABLE}
                    SET status = 'stopped', stop_reason = ?, stop_date = ?, updated_at = ?
                    WHERE id = ?
                ''', (stop_reason, stop_date, updated_at, medication_id))

                conn.commit()
                KivyLogger.info(f"[MedicationDB] 停用药物成功: ID {medication_id}, 停药日期: {stop_date}, 停药原因: {stop_reason}")
                return True

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 停用药物失败: {e}")
            return False

    def save_reminder(self, medication_id: int, custom_id: str, reminder_data: Dict[str, Any]) -> bool:
        """保存用药提醒 - 按提醒类型更新现有记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now().isoformat()
                reminder_type = reminder_data.get('reminder_type', 'daily')
                
                # 首先删除该药物的同类型现有提醒记录
                cursor.execute(f'''
                    DELETE FROM {REMINDERS_TABLE}
                    WHERE medication_id = ? AND custom_id = ? AND reminder_type = ?
                ''', (medication_id, custom_id, reminder_type))
                
                # 然后插入新的提醒记录
                cursor.execute(f'''
                    INSERT INTO {REMINDERS_TABLE}
                    (medication_id, custom_id, reminder_time, reminder_type, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    medication_id,
                    custom_id,
                    reminder_data['reminder_time'],
                    reminder_type,
                    True,
                    now
                ))

                conn.commit()
                KivyLogger.info(f"[MedicationDB] 更新{reminder_type}提醒设置成功: 药物ID {medication_id}")
                return True

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 保存提醒设置失败: {e}")
            return False

    def set_medication_reminder(self, medication_id: int, custom_id: str, reminder_time: str, reminder_type: str = 'daily') -> bool:
        """设置用药提醒（兼容性方法）"""
        try:
            reminder_data = {
                'reminder_time': reminder_time,
                'reminder_type': reminder_type
            }
            return self.save_reminder(medication_id, custom_id, reminder_data)
        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 设置用药提醒失败: {e}")
            return False

    def export_data(self, custom_id: str, export_path: str) -> bool:
        """导出用药数据"""
        try:
            medications = self.get_medications(custom_id, status='active')
            history_medications = self.get_medications(custom_id, status='stopped')

            export_data = {
                'export_time': datetime.now().isoformat(),
                'custom_id': custom_id,
                'current_medications': medications,
                'history_medications': history_medications,
                'total_count': len(medications) + len(history_medications)
            }

            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            KivyLogger.info(f"[MedicationDB] 数据导出成功: {export_path}")
            return True

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 数据导出失败: {e}")
            return False

class NotificationManager:
    """通知管理器"""

    def __init__(self):
        self.is_available = NOTIFICATION_AVAILABLE

    def schedule_medication_reminder(self, medication_name: str, reminder_time: str) -> bool:
        """安排用药提醒"""
        if not self.is_available:
            KivyLogger.warning("[NotificationManager] 通知功能不可用")
            return False

        try:
            # 这里应该实现实际的通知调度逻辑
            # 由于Kivy的限制，这里只是模拟
            KivyLogger.info(f"[NotificationManager] 已安排提醒: {medication_name} at {reminder_time}")
            return True

        except Exception as e:
            KivyLogger.error(f"[NotificationManager] 安排提醒失败: {e}")
            return False

    def send_immediate_notification(self, title: str, message: str) -> bool:
        """发送即时通知"""
        if not self.is_available:
            return False

        try:
            notification.notify(
                title=title,
                message=message,
                app_name="健康管理",
                timeout=10
            )
            return True

        except Exception as e:
            KivyLogger.error(f"[NotificationManager] 发送通知失败: {e}")
            return False

# 全局实例
_db_manager = None
_notification_manager = None

# 全局工具函数
def safe_str(value, default=""):
    """安全的字符串转换函数"""
    return str(value) if value is not None else default

def get_medication_db_manager() -> MedicationDatabaseManager:
    """获取用药数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = MedicationDatabaseManager()
    return _db_manager

def get_notification_manager() -> NotificationManager:
    """获取通知管理器实例"""
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager()
    return _notification_manager

KV = '''
<MedicationManagementScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'
        spacing: 0
        
        # 顶部应用栏 - 移到最上方与健康资料管理页面保持一致
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "用药管理"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.refresh_data()
        
        # Logo区域 - 移到应用栏下方
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(60)
            pos_hint: {"center_x": 0.5}

        # 主要内容区域
        MDBoxLayout:
            orientation: 'vertical'
            padding: [dp(16), dp(8), dp(16), dp(100)]  # 减少顶部padding，避免遮挡Logo
            spacing: dp(16)
            size_hint_y: 1  # 让主要内容区域填充剩余空间
                
            # Tab切换区域 - 固定在顶部
            MDBoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: dp(48)
                spacing: dp(16)
                pos_hint: {"center_x": 0.5}
                size_hint_x: None
                width: dp(280)

                MDButton:
                    id: current_tab_btn
                    style: "filled" if root.current_tab == 'current' else "outlined"
                    md_bg_color: app.theme_cls.primaryColor if root.current_tab == 'current' else app.theme_cls.surfaceContainerHighColor
                    size_hint_x: None
                    width: dp(132)
                    height: dp(40)
                    radius: [dp(20)]
                    on_release: root.switch_tab('current')

                    MDButtonIcon:
                        icon: "pill"
                        theme_icon_color: "Custom"
                        icon_color: app.theme_cls.onPrimaryColor if root.current_tab == 'current' else app.theme_cls.onSurfaceVariantColor

                    MDButtonText:
                        text: "目前用药"
                        font_size: sp(13)
                        theme_text_color: "Custom"
                        text_color: app.theme_cls.onPrimaryColor if root.current_tab == 'current' else app.theme_cls.onSurfaceVariantColor

                MDButton:
                    id: history_tab_btn
                    style: "filled" if root.current_tab == 'history' else "outlined"
                    md_bg_color: app.theme_cls.primaryColor if root.current_tab == 'history' else app.theme_cls.surfaceContainerHighColor
                    size_hint_x: None
                    width: dp(132)
                    height: dp(40)
                    radius: [dp(20)]
                    on_release: root.switch_tab('history')

                    MDButtonIcon:
                        icon: "history"
                        theme_icon_color: "Custom"
                        icon_color: app.theme_cls.onPrimaryColor if root.current_tab == 'history' else app.theme_cls.onSurfaceVariantColor

                    MDButtonText:
                        text: "既往用药"
                        font_size: sp(13)
                        theme_text_color: "Custom"
                        text_color: app.theme_cls.onPrimaryColor if root.current_tab == 'history' else app.theme_cls.onSurfaceVariantColor

            # 目前用药内容
            MDBoxLayout:
                id: current_content
                orientation: 'vertical'
                size_hint_y: 1 if root.current_tab == 'current' else None
                height: 0 if root.current_tab != 'current' else dp(400)
                opacity: 1 if root.current_tab == 'current' else 0
                spacing: dp(12)

                # 操作按钮区域 - 横向排列，优化布局
                MDCard:
                    size_hint_y: None
                    height: dp(48)
                    md_bg_color: app.theme_cls.surfaceContainerColor
                    radius: [dp(12)]
                    elevation: 1
                    padding: [dp(8), dp(8), dp(8), dp(8)]

                    MDBoxLayout:
                        orientation: 'horizontal'
                        spacing: dp(8)
                        size_hint_y: None
                        height: dp(32)

                        MDButton:
                            style: "filled"
                            md_bg_color: app.theme_cls.primaryColor
                            size_hint_x: 0.33
                            height: dp(32)
                            radius: [dp(16)]
                            on_release: root.show_delete_dialog()

                            MDButtonIcon:
                                icon: "delete"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.onPrimaryColor
                                icon_size: dp(16)

                            MDButtonText:
                                text: "删除"
                                font_size: sp(12)
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onPrimaryColor

                        MDButton:
                            style: "filled"
                            md_bg_color: app.theme_cls.primaryColor
                            size_hint_x: 0.33
                            height: dp(32)
                            radius: [dp(16)]
                            on_release: root.show_stop_dialog()

                            MDButtonIcon:
                                icon: "stop"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.onPrimaryColor
                                icon_size: dp(16)

                            MDButtonText:
                                text: "停用"
                                font_size: sp(12)
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onPrimaryColor

                        MDButton:
                            style: "filled"
                            md_bg_color: app.theme_cls.primaryColor
                            size_hint_x: 0.33
                            height: dp(32)
                            radius: [dp(16)]
                            on_release: root.show_batch_reminder_dialog()

                            MDButtonIcon:
                                icon: "bell"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.onPrimaryColor
                                icon_size: dp(16)

                            MDButtonText:
                                text: "提醒"
                                font_size: sp(12)
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onPrimaryColor

                # 添加药物卡片（可伸缩显示）- 紧贴功能按钮下方
                MDCard:
                    id: add_medication_card
                    size_hint_y: None
                    height: self.minimum_height
                    md_bg_color: app.theme_cls.surfaceContainerHighColor
                    radius: [dp(12)]
                    elevation: 3
                    padding: [0, dp(4), 0, 0]  # 顶部小间隔

                # 添加新药物表单区域
                MDBoxLayout:
                    orientation: 'vertical'
                    padding: [dp(16), dp(16), dp(16), dp(16)]
                    spacing: dp(12)
                    size_hint_y: None
                    height: self.minimum_height

                    # 标题区域（可点击展开/收起）
                    MDCard:
                        size_hint_y: None
                        height: dp(40)
                        md_bg_color: 0, 0, 0, 0
                        elevation: 0
                        ripple_behavior: True
                        on_release: root.toggle_add_medication_form()

                        MDBoxLayout:
                            orientation: 'horizontal'
                            size_hint_y: None
                            height: dp(32)
                            spacing: dp(8)
                            padding: [dp(8), dp(4), dp(8), dp(4)]

                            MDIconButton:
                                icon: "pill"
                                size_hint_x: None
                                width: dp(24)
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.primaryColor
                                icon_size: dp(24)

                            MDLabel:
                                text: "添加新药物"
                                font_size: sp(14)
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.primaryColor
                                valign: "center"

                            Widget:
                                # 占位符，推动箭头到右侧

                            MDIconButton:
                                id: expand_arrow
                                icon: "chevron-down"
                                size_hint_x: None
                                width: dp(24)
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.primaryColor
                                icon_size: dp(20)

                        # 表单内容容器（可控制显示/隐藏）
                        MDBoxLayout:
                            id: add_form_container
                            orientation: 'vertical'
                            size_hint_y: None
                            height: 0  # 初始隐藏
                            opacity: 0  # 初始透明
                            spacing: dp(12)

                            # 第一行：药物名称和剂量
                            MDBoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: dp(56)
                                spacing: dp(8)

                                MDTextField:
                                    id: medication_name_field
                                    mode: "outlined"
                                    size_hint_x: 0.5

                                    MDTextFieldHintText:
                                        text: "药物名称"

                                MDTextField:
                                    id: dosage_field
                                    mode: "outlined"
                                    size_hint_x: 0.5

                                    MDTextFieldHintText:
                                        text: "剂量"

                            # 第二行：使用频次和开始日期
                            MDBoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: dp(56)
                                spacing: dp(8)

                                MDTextField:
                                    id: frequency_field
                                    mode: "outlined"
                                    size_hint_x: 0.5
                                    readonly: True
                                    on_focus: if self.focus: root.show_frequency_menu(self)

                                    MDTextFieldHintText:
                                        text: "使用频次"

                                    MDTextFieldTrailingIcon:
                                        icon: "chevron-down"

                                MDCard:
                                    size_hint_x: 0.5
                                    md_bg_color: 0, 0, 0, 0
                                    elevation: 0
                                    ripple_behavior: True
                                    on_release: root.show_date_picker(root.ids.start_date_field)

                                    MDTextField:
                                        id: start_date_field
                                        mode: "outlined"
                                        readonly: True

                                        MDTextFieldHintText:
                                            text: "开始日期"

                                        MDTextFieldTrailingIcon:
                                            icon: "calendar"

                            # 第三行：用药原因和注意事项
                            MDBoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: dp(56)
                                spacing: dp(8)

                                MDTextField:
                                    id: reason_field
                                    mode: "outlined"
                                    size_hint_x: 0.5
                                    readonly: True
                                    on_focus: if self.focus: root.show_reason_menu(self)

                                    MDTextFieldHintText:
                                        text: "用药原因"

                                    MDTextFieldTrailingIcon:
                                        icon: "chevron-down"

                                MDTextField:
                                    id: notes_field
                                    mode: "outlined"
                                    size_hint_x: 0.5
                                    readonly: True
                                    on_focus: if self.focus: root.show_notes_menu(self)

                                    MDTextFieldHintText:
                                        text: "注意事项"

                                    MDTextFieldTrailingIcon:
                                        icon: "chevron-down"

                            # 添加按钮
                            MDButton:
                                style: "filled"
                                md_bg_color: app.theme_cls.primaryColor
                                size_hint_y: None
                                height: dp(40)
                                radius: [dp(20)]
                                on_release: root.add_medication_to_list()

                                MDButtonIcon:
                                    icon: "plus"
                                    theme_icon_color: "Custom"
                                    icon_color: app.theme_cls.onPrimaryColor

                                MDButtonText:
                                    text: "确认添加"
                                    theme_text_color: "Custom"
                                    text_color: app.theme_cls.onPrimaryColor

                # 当前用药列表滚动容器
                MDScrollView:
                    do_scroll_x: False
                    do_scroll_y: True
                    size_hint_y: 1  # 填充剩余空间

                    MDBoxLayout:
                        id: current_medications_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        adaptive_height: True
                        spacing: dp(12)
                        padding: [dp(8), dp(8), dp(8), dp(8)]

            # 既往用药内容
            MDBoxLayout:
                id: history_content
                orientation: 'vertical'
                size_hint_y: 1 if root.current_tab == 'history' else None
                height: 0 if root.current_tab != 'history' else dp(400)
                opacity: 1 if root.current_tab == 'history' else 0
                spacing: dp(12)

                # 搜索功能区域 - 固定在顶部
                MDBoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: dp(56)
                    spacing: dp(12)
                    padding: [dp(4), dp(8), dp(4), dp(8)]

                    MDTextField:
                        id: history_search_field
                        mode: "outlined"
                        size_hint_x: 0.7

                        MDTextFieldHintText:
                            text: "输入药物名称"

                    MDButton:
                        style: "filled"
                        md_bg_color: app.theme_cls.primaryColor
                        size_hint_x: 0.15
                        on_release: root.search_history_medications()

                        MDButtonText:
                            text: "搜索"
                            theme_text_color: "Custom"
                            text_color: app.theme_cls.onPrimaryColor

                    MDButton:
                        style: "outlined"
                        size_hint_x: 0.15
                        on_release: root.clear_history_search()

                        MDButtonText:
                            text: "清空"
                            theme_text_color: "Custom"
                            text_color: app.theme_cls.primaryColor

                # 既往用药列表滚动容器
                MDScrollView:
                    do_scroll_x: False
                    do_scroll_y: True
                    size_hint_y: 1  # 填充剩余空间

                    MDBoxLayout:
                        id: history_medications_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        adaptive_height: True
                        spacing: dp(12)
                        padding: [dp(8), dp(8), dp(8), dp(8)]

# CurrentMedicationCard - 当前用药卡片，继承BaseMedicationCard并添加特有功能
<CurrentMedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(180)
    adaptive_height: False
    md_bg_color: app.theme_cls.surfaceColor
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    ripple_behavior: True
    on_release: self.on_card_click()

    # 卡片头部 - 复选框、序号和药物名称
    MDBoxLayout:
        id: header_layout
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)

        # 复选框
        MDCheckbox:
            id: selection_checkbox
            size_hint: None, None
            size: dp(24), dp(24)
            active: root.is_selected
            on_active: root.on_checkbox_active(self.active)
            theme_icon_color: "Custom"
            icon_color: app.theme_cls.primaryColor
            pos_hint: {"center_y": 0.5}

        # 序号标签
        MDLabel:
            text: f"#{root.row_index + 1}"
            font_style: "Label"
            role: "large"
            bold: True
            theme_text_color: "Custom"
            text_color: root.get_sequence_color()
            size_hint_x: None
            width: dp(60)
            halign: "left"
            valign: "center"

        # 药物名称标签
        MDLabel:
            text: root.name
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"
            text_size: self.width, None

    # 基础信息 - 剂量和频次
    MDBoxLayout:
        id: basic_info_layout
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(24)
        spacing: dp(16)

        MDLabel:
            text: f"剂量: {root.dosage}"
            font_style: "Body"
            role: "medium"
            theme_text_color: "Secondary"
            size_hint_x: 0.5
            halign: "left"
            valign: "center"
            text_size: self.width, None

        MDLabel:
            text: f"频次: {root.frequency}"
            font_style: "Body"
            role: "medium"
            theme_text_color: "Secondary"
            size_hint_x: 0.5
            halign: "left"
            valign: "center"
            text_size: self.width, None

    # 扩展内容 - 当前用药特有信息
    MDBoxLayout:
        id: current_extended_content
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        adaptive_height: True
        spacing: dp(4)

        # 用药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24) if root.reason else 0
            opacity: 1 if root.reason else 0
            spacing: dp(8)

            MDIconButton:
                icon: "heart-pulse"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.primaryColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"用药原因: {root.reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 开始时间
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24) if root.start_date else 0
            opacity: 1 if root.start_date else 0
            spacing: dp(8)

            MDIconButton:
                icon: "calendar-start"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.primaryColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"开始时间: {root.start_date}"
                font_style: "Body"
                role: "small"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 注意事项
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24) if root.notes else 0
            opacity: 1 if root.notes else 0
            spacing: dp(8)

            MDIconButton:
                icon: "note-text"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.primaryColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"注意事项: {root.notes}"
                font_style: "Body"
                role: "small"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 提醒设置
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24) if root.get_reminder_text() else 0
            opacity: 1 if root.get_reminder_text() else 0
            spacing: dp(8)

            MDIconButton:
                icon: "bell"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.primaryColor
                size_hint: None, None
                size: dp(16), dp(16)
                pos_hint: {"center_y": 0.5}

            MDLabel:
                text: root.get_reminder_text()
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme_cls.primaryColor
                halign: "left"
                valign: "center"
                text_size: self.width, None

# HistoryMedicationCard - 既往用药卡片，继承BaseMedicationCard并添加特有功能
<HistoryMedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    adaptive_height: True
    md_bg_color: app.theme_cls.surfaceContainerColor
    radius: [dp(12)]
    elevation: 1
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    ripple_behavior: True
    on_release: self.on_card_click()

    # 卡片头部 - 序号和药物名称
    MDBoxLayout:
        id: header_layout
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)

        # 序号标签
        MDLabel:
            text: f"#{root.row_index + 1}"
            font_style: "Label"
            role: "large"
            bold: True
            theme_text_color: "Custom"
            text_color: root.get_sequence_color()
            size_hint_x: None
            width: dp(60)
            halign: "left"
            valign: "center"

        # 药物名称标签
        MDLabel:
            text: root.name
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"
            text_size: self.width, None

    # 基础信息 - 剂量和频次
    MDBoxLayout:
        id: basic_info_layout
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(24)
        spacing: dp(16)

        MDLabel:
            text: f"剂量: {root.dosage}"
            font_style: "Body"
            role: "medium"
            theme_text_color: "Secondary"
            size_hint_x: 0.5
            halign: "left"
            valign: "center"
            text_size: self.width, None

        MDLabel:
            text: f"频次: {root.frequency}"
            font_style: "Body"
            role: "medium"
            theme_text_color: "Secondary"
            size_hint_x: 0.5
            halign: "left"
            valign: "center"
            text_size: self.width, None

    # 扩展内容 - 既往用药特有信息
    MDBoxLayout:
        id: extended_content
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        adaptive_height: True
        spacing: dp(8)

        # 用药日期
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)

            MDIconButton:
                icon: "calendar-range"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"用药日期: {root.start_date}" + (f" 至 {root.get_formatted_stop_date()}" if root.get_formatted_stop_date() else "")
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 用药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)

            MDIconButton:
                icon: "heart-pulse"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"用药原因: {root.reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 停药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)

            MDIconButton:
                icon: "alert-circle-outline"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.errorColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"停药原因: {root.get_stop_reason_text()}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Custom"
                text_color: app.theme_cls.errorColor
                halign: "left"
                valign: "center"
                text_size: self.width, None
'''

class BaseMedicationCard(MDCard):
    """基础用药卡片组件 - 提供通用属性和方法"""

    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    reason = StringProperty("")
    notes = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ripple_behavior = True
        self.ripple_duration_in_slow = 0.1
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)

    def get_sequence_color(self):
        """获取序号标签颜色 - 子类可重写"""
        from kivy.app import App
        return App.get_running_app().theme_cls.primaryColor

    def on_card_click(self):
        """卡片点击事件 - 子类可重写"""
        pass

class CurrentMedicationCard(BaseMedicationCard):
    """当前用药卡片组件 - 继承基础卡片"""

    is_selected = BooleanProperty(False)
    reason = StringProperty("")  # 确保有用药原因属性

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 绑定选中状态变化事件
        self.bind(is_selected=self.on_selection_changed)

    def on_selection_changed(self, instance, value):
        """处理选中状态变化，更新背景色"""
        try:
            from kivy.app import App
            app = App.get_running_app()
            if value:  # 选中状态
                # 使用主色调的浅色版本作为选中背景
                primary_color = app.theme_cls.primaryColor
                # 创建半透明的主色调背景
                self.md_bg_color = [primary_color[0], primary_color[1], primary_color[2], 0.1]
            else:  # 未选中状态
                self.md_bg_color = app.theme_cls.surfaceColor
        except Exception as e:
            KivyLogger.error(f"Error updating selection background: {e}")

    def get_sequence_color(self):
        """当前用药卡片使用主色调"""
        from kivy.app import App
        return App.get_running_app().theme_cls.primaryColor

    def get_reminder_text(self):
        """获取提醒设置文本"""
        try:
            if not self.medication_data:
                return ""

            reminder_settings = self.medication_data.get('reminder_settings', {})
            if not reminder_settings:
                return ""

            reminder_texts = []

            # 服药提醒 - 支持多个时间段
            med_reminder = reminder_settings.get('med_reminder', {})
            if med_reminder.get('enabled'):
                times = med_reminder.get('times', [])
                if times:
                    # 如果有多个时间段，显示所有时间
                    time_strs = []
                    for time_setting in times:
                        time_text = time_setting.get('time', '')
                        advance_text = time_setting.get('advance_minutes', '15')
                        if time_text:  # 只有时间不为空才添加
                            time_strs.append(f"{time_text}(提前{advance_text}分钟)")
                    
                    if len(time_strs) == 1:
                        reminder_texts.append(f"服药: {time_strs[0]}")
                    elif len(time_strs) > 1:
                        reminder_texts.append(f"服药({len(time_strs)}次): {', '.join(time_strs)}")
                else:
                    # 兼容旧格式
                    time_text = med_reminder.get('time', '')
                    advance_text = med_reminder.get('advance_minutes', '15')
                    if time_text:
                        reminder_texts.append(f"服药: {time_text} (提前{advance_text}分钟)")

            # 复查提醒
            review_reminder = reminder_settings.get('review_reminder', {})
            if review_reminder.get('enabled'):
                date_text = review_reminder.get('date', '')
                advance_text = review_reminder.get('advance_days', '3')
                if date_text:  # 只有日期不为空才显示
                    reminder_texts.append(f"复查: {date_text} (提前{advance_text}天)")

            return " | ".join(reminder_texts) if reminder_texts else ""

        except Exception as e:
            return ""

    def on_checkbox_active(self, active):
        """处理checkbox状态变化"""
        try:
            # 避免循环调用，只在状态真正改变时更新
            if self.is_selected != active:
                self.is_selected = active
                # 通知父屏幕更新选择列表
                current = self.parent
                while current and not isinstance(current, MedicationManagementScreen):
                    current = current.parent

                if current and hasattr(current, 'update_selection'):
                    current.update_selection(self, self.is_selected)
        except Exception as e:
            KivyLogger.error(f"Error in on_checkbox_active: {e}")

    def toggle_selection(self):
        """切换选择状态（程序调用）"""
        try:
            new_state = not self.is_selected
            self.is_selected = new_state
            # 通知父屏幕更新选择列表
            current = self.parent
            while current and not isinstance(current, MedicationManagementScreen):
                current = current.parent

            if current and hasattr(current, 'update_selection'):
                current.update_selection(self, self.is_selected)
        except Exception as e:
            KivyLogger.error(f"Error in toggle_selection: {e}")

    def on_card_click(self):
        """处理卡片点击事件"""
        if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'parent'):
            screen = self.parent.parent.parent
            if hasattr(screen, 'show_medication_detail'):
                screen.show_medication_detail(self.medication_data)

class HistoryMedicationCard(BaseMedicationCard):
    """既往用药卡片组件 - 继承基础卡片"""

    stop_date = StringProperty("")
    stop_reason = StringProperty("")
    reason = StringProperty("")

    def get_sequence_color(self):
        """既往用药卡片使用次要颜色"""
        from kivy.app import App
        return App.get_running_app().theme_cls.onSurfaceVariantColor

    def get_formatted_stop_date(self):
        """获取格式化的停药日期"""
        try:
            if not self.stop_date or not self.stop_date.strip():
                return ""

            # 如果是ISO格式的日期时间，只取日期部分
            if 'T' in self.stop_date:
                return self.stop_date.split('T')[0]
            return self.stop_date
        except Exception as e:
            KivyLogger.error(f"[HistoryMedicationCard] 格式化停药日期失败: {e}")
            return self.stop_date

    def get_stop_reason_text(self):
        """获取停药原因显示文本"""
        try:
            if not self.stop_reason or not self.stop_reason.strip():
                return "未记录停药原因"
            return self.stop_reason.strip()
        except Exception as e:
            KivyLogger.error(f"[HistoryMedicationCard] 获取停药原因失败: {e}")
            return "未记录停药原因"

class MedicationManagementScreen(BaseScreen):
    """用药管理屏幕"""
    medications = ListProperty([])
    history_medications = ListProperty([])
    current_medications = ListProperty([])
    current_tab = StringProperty('current')
    dialog = None
    editing_medication = None
    is_loading = BooleanProperty(False)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()

        # 初始化数据管理器
        self.db_manager = get_medication_db_manager()
        self.notification_manager = get_notification_manager()

        # 初始化数据列表
        self.medications = []
        self.history_medications = []
        self.current_medications = []
        self.selected_medications = []

        # 初始化对话框变量
        self.dialog = None
        self.unified_dialog = None
        self.delete_dialog = None
        self.detail_dialog = None

        # 初始化选择状态
        self.selected_reminder_type = "daily"

        KivyLogger.info("[MedicationManagement] 初始化完成")
        Clock.schedule_once(self.init_ui, 0.2)

    def on_enter(self):
        """进入屏幕时调用"""
        try:
            super().on_enter()
            KivyLogger.info("[MedicationManagement] 进入屏幕")
            self.init_ui()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 进入屏幕失败: {e}")

    def init_ui(self, dt=0):
        """初始化UI"""
        try:
            KivyLogger.info("[MedicationManagement] 开始初始化UI")
            self.load_current_medications()
            self.load_history_medications()
            self.set_default_values()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 初始化UI失败: {e}")

    def set_default_values(self):
        """设置默认值"""
        try:
            from datetime import datetime

            # 延迟设置默认值，确保界面已完全加载
            def _set_defaults(dt):
                try:
                    # 设置默认开始日期为今天
                    if hasattr(self, 'ids') and 'start_date_field' in self.ids:
                        start_date_field = self.ids.get('start_date_field')
                        if start_date_field and hasattr(start_date_field, 'text'):
                            if not start_date_field.text.strip():
                                start_date_field.text = datetime.now().strftime("%Y-%m-%d")
                                KivyLogger.info(f"[MedicationManagement] 设置默认开始日期: {start_date_field.text}")
                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 延迟设置默认值失败: {e}")

            Clock.schedule_once(_set_defaults, 0.5)

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置默认值失败: {e}")

    def get_current_user_id(self) -> Optional[str]:
        """获取当前用户ID"""
        try:
            # 首先尝试从app.user_data获取
            if hasattr(self.app, 'user_data') and self.app.user_data:
                custom_id = self.app.user_data.get('custom_id')
                if custom_id:
                    return custom_id

            # 如果没有获取到，尝试从认证管理器获取
            try:
                from utils.auth_manager import get_auth_manager
                auth_manager = get_auth_manager()
                user_info = auth_manager.get_current_user_info()
                if user_info:
                    return user_info.get('custom_id')
            except Exception as auth_error:
                KivyLogger.warning(f"[MedicationManagement] 获取认证信息失败: {auth_error}")

            return None

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 获取用户ID失败: {e}")
            return None

    def toggle_add_medication_form(self):
        """切换添加用药表单的显示状态"""
        try:
            # 检查是否存在添加用药表单容器
            if hasattr(self, 'ids') and 'add_form_container' in self.ids:
                form_container = self.ids.add_form_container
                expand_arrow = self.ids.get('expand_arrow')
                
                # 切换表单的可见性
                if hasattr(form_container, 'opacity'):
                    if form_container.opacity == 0:
                        # 显示表单 - 向下展开
                        form_container.opacity = 1
                        form_container.size_hint_y = None
                        # 计算表单高度：3行输入框(56*3) + 间距(12*4) + 按钮(40) + 内边距(16)
                        form_container.height = dp(56 * 3 + 12 * 4 + 40 + 16)  # 约dp(272)
                        if expand_arrow:
                            expand_arrow.icon = "chevron-up"
                        KivyLogger.info("[MedicationManagement] 显示添加用药表单")
                    else:
                        # 隐藏表单
                        form_container.opacity = 0
                        form_container.size_hint_y = None
                        form_container.height = 0
                        if expand_arrow:
                            expand_arrow.icon = "chevron-down"
                        KivyLogger.info("[MedicationManagement] 隐藏添加用药表单")
                else:
                    KivyLogger.warning("[MedicationManagement] 表单容器没有opacity属性")
            else:
                KivyLogger.warning("[MedicationManagement] 未找到添加用药表单容器")
                
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 切换添加用药表单失败: {e}")

    def load_medications(self):
        """加载用药记录"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                KivyLogger.info("[MedicationManagement] 未找到用户信息，显示登录提示")
                self.show_empty_state("暂无用药记录\n请先登录后查看")
                return

            if not hasattr(self, 'ids') or 'current_medications_container' not in self.ids:
                return

            try:
                self.ids.current_medications_container.clear_widgets()
            except ReferenceError:
                return

            # 从数据库加载用药记录
            self.medications = self.db_manager.get_medications(
                custom_id=user_id,
                status='active'
            )

            if not self.medications:
                # 显示空状态
                self.show_empty_state("暂无用药记录\n点击右上角的 + 按钮添加记录")
                return

            for med in self.medications:
                card = CurrentMedicationCard(
                    name=med["name"],
                    dosage=med['dosage'],
                    frequency=med['frequency'],
                    start_date=med.get('start_date', ''),
                    status_text="正在使用",
                    medication_data=med
                )
                self.ids.current_medications_container.add_widget(card)

            KivyLogger.info(f"[MedicationManagement] 成功加载 {len(self.medications)} 条用药记录")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载用药记录时出错: {e}")
            self.show_error(f"加载用药记录失败: {str(e)}")

    def show_empty_state(self, message: str):
        """显示空状态"""
        try:
            if hasattr(self, 'ids') and 'current_medications_container' in self.ids:
                self.ids.current_medications_container.clear_widgets()
                empty_label = MDLabel(
                    text=message,
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium"
                )
                self.ids.current_medications_container.add_widget(empty_label)
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示空状态失败: {e}")

    def load_current_medications(self):
        """加载当前用药记录"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                KivyLogger.info("[MedicationManagement] 未找到用户信息，无法加载当前用药")
                return

            # 从数据库加载当前用药记录
            self.current_medications = self.db_manager.get_medications(
                custom_id=user_id,
                status='active'
            )

            KivyLogger.info(f"[MedicationManagement] 加载到 {len(self.current_medications)} 条当前用药记录")

            # 刷新当前用药显示
            self.refresh_current_medications()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载当前用药失败: {e}")

    def refresh_current_medications(self):
        """刷新当前用药显示"""
        try:
            KivyLogger.info(f"[MedicationManagement] 开始刷新当前用药显示，数量: {len(self.current_medications)}")

            # 清空现有容器中的药物卡片
            if hasattr(self, 'ids') and 'current_medications_container' in self.ids:
                self.ids.current_medications_container.clear_widgets()
            else:
                KivyLogger.warning("[MedicationManagement] 未找到current_medications_container")
                return

            # 如果没有当前用药记录，显示空状态
            if not self.current_medications:
                empty_label = MDLabel(
                    text="暂无当前用药记录\n请使用上方表单添加药物",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    size_hint_y=None,
                    height=dp(80)
                )
                self.ids.current_medications_container.add_widget(empty_label)
                KivyLogger.info("[MedicationManagement] 显示当前用药空状态")
                return

            # 添加当前用药卡片
            for index, med in enumerate(self.current_medications):
                try:
                    # 使用全局safe_str函数

                    # 调试日志：检查提醒设置
                    reminder_settings = med.get('reminder_settings', {})
                    KivyLogger.info(f"[MedicationManagement] 创建当前用药卡片 - 药物: {med.get('name')}, 提醒设置: {reminder_settings}")

                    card = CurrentMedicationCard(
                        name=safe_str(med.get('name'), '未知药物'),
                        dosage=safe_str(med.get('dosage')),
                        frequency=safe_str(med.get('frequency')),
                        start_date=safe_str(med.get('start_date')),
                        reason=safe_str(med.get('reason')),
                        notes=safe_str(med.get('notes')),
                        row_index=index,
                        medication_data=med
                    )

                    # 恢复选择状态（如果之前被选中）
                    if hasattr(self, 'selected_medications'):
                        for selected_card in self.selected_medications:
                            if (hasattr(selected_card, 'medication_data') and
                                selected_card.medication_data.get('id') == med.get('id')):
                                card.is_selected = True
                                # 更新选择列表中的引用
                                selected_index = self.selected_medications.index(selected_card)
                                self.selected_medications[selected_index] = card
                                break

                    self.ids.current_medications_container.add_widget(card)

                    # 调试日志：检查提醒文本
                    try:
                        reminder_text = card.get_reminder_text()
                        KivyLogger.info(f"[MedicationManagement] 添加当前用药卡片: {med['name']}, 提醒文本: '{reminder_text}'")

                        # 强制刷新卡片显示
                        if hasattr(card, 'ids'):
                            card.canvas.ask_update()
                    except Exception as reminder_error:
                        KivyLogger.error(f"[MedicationManagement] 获取提醒文本失败: {reminder_error}")
                except Exception as card_error:
                    KivyLogger.error(f"[MedicationManagement] 创建当前用药卡片失败: {card_error}")
                    # 添加详细的错误信息
                    KivyLogger.error(f"[MedicationManagement] 药物数据: {med}")
                    import traceback
                    KivyLogger.error(f"[MedicationManagement] 错误详情: {traceback.format_exc()}")
                    continue

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新当前用药显示失败: {e}")

    def load_history_medications(self):
        """加载既往用药记录"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                return

            # 从数据库加载既往用药记录
            self.history_medications = self.db_manager.get_medications(
                custom_id=user_id,
                status='stopped'
            )

            self.refresh_history_display()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载既往用药失败: {e}")

    def refresh_history_medications(self):
        """刷新既往用药记录（不重新从数据库加载）"""
        try:
            KivyLogger.info(f"[MedicationManagement] 刷新既往用药记录，数量: {len(self.history_medications)}")
            self.refresh_history_display()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新既往用药记录失败: {e}")

    def refresh_history_display(self):
        """刷新既往用药显示"""
        try:
            KivyLogger.info(f"[MedicationManagement] 开始刷新既往用药显示，数量: {len(self.history_medications)}")

            if not hasattr(self, 'ids') or 'history_medications_container' not in self.ids:
                KivyLogger.warning("[MedicationManagement] 未找到history_medications_container")
                return

            container = self.ids.history_medications_container
            container.clear_widgets()

            if not self.history_medications:
                empty_label = MDLabel(
                    text="暂无既往用药记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium",
                    size_hint_y=None,
                    height=dp(100)
                )
                container.add_widget(empty_label)
                KivyLogger.info("[MedicationManagement] 显示空状态")
                return

            # 按停药日期排序（最新的在前）
            def safe_sort_key(med):
                """安全的排序键，处理None值"""
                stop_date = med.get('stop_date')
                if stop_date is None:
                    return ''  # None值排到最后
                return str(stop_date)

            sorted_medications = sorted(
                self.history_medications,
                key=safe_sort_key,
                reverse=True
            )

            for index, med in enumerate(sorted_medications):
                try:
                    # 使用全局safe_str函数

                    # 格式化停药日期
                    raw_stop_date = med.get("stop_date")
                    formatted_stop_date = ""
                    if raw_stop_date:
                        try:
                            # 如果是ISO格式的日期时间，只取日期部分
                            if 'T' in str(raw_stop_date):
                                formatted_stop_date = str(raw_stop_date).split('T')[0]
                            else:
                                formatted_stop_date = safe_str(raw_stop_date)
                        except Exception as e:
                            KivyLogger.error(f"[MedicationManagement] 格式化停药日期失败: {e}")
                            formatted_stop_date = safe_str(raw_stop_date)

                    # 格式化停药原因
                    stop_reason_value = safe_str(med.get("stop_reason"))

                    # 调试日志：检查停药信息
                    KivyLogger.info(f"[MedicationManagement] 创建既往用药卡片 - 药物: {med.get('name')}")
                    KivyLogger.info(f"[MedicationManagement] 原始停药日期: '{raw_stop_date}' -> 格式化: '{formatted_stop_date}'")
                    KivyLogger.info(f"[MedicationManagement] 停药原因: '{stop_reason_value}'")

                    card = HistoryMedicationCard(
                        name=safe_str(med.get("name"), "未知药物"),
                        dosage=safe_str(med.get('dosage')),
                        frequency=safe_str(med.get('frequency')),
                        start_date=safe_str(med.get("start_date")),
                        stop_date=formatted_stop_date,
                        reason=safe_str(med.get("reason")),
                        stop_reason=stop_reason_value,
                        notes=safe_str(med.get("notes")),
                        row_index=index,
                        medication_data=med
                    )

                    # 验证卡片属性
                    KivyLogger.info(f"[MedicationManagement] 卡片属性 - stop_date: '{card.stop_date}', stop_reason: '{card.stop_reason}'")
                    container.add_widget(card)
                    KivyLogger.info(f"[MedicationManagement] 添加既往用药卡片: {med['name']}")
                except Exception as card_error:
                    KivyLogger.error(f"[MedicationManagement] 创建既往用药卡片失败: {card_error}")
                    # 添加详细的错误信息
                    KivyLogger.error(f"[MedicationManagement] 药物数据: {med}")
                    import traceback
                    KivyLogger.error(f"[MedicationManagement] 错误详情: {traceback.format_exc()}")

            KivyLogger.info(f"[MedicationManagement] 既往用药显示刷新完成，共 {len(sorted_medications)} 条记录")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新既往用药显示失败: {e}")

    def go_back(self):
        """返回上一页"""
        try:
            app = MDApp.get_running_app()
            app.root.current = 'health_data_management_screen'
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'

    def switch_tab(self, tab_name):
        """切换Tab"""
        try:
            KivyLogger.info(f"[MedicationManagement] 切换到Tab: {tab_name}")
            self.current_tab = tab_name

            # 更新内容显示
            if hasattr(self, 'ids'):
                current_content = self.ids.get('current_content')
                history_content = self.ids.get('history_content')
                current_tab_btn = self.ids.get('current_tab_btn')
                history_tab_btn = self.ids.get('history_tab_btn')

                if current_content and history_content:
                    if tab_name == 'current':
                        # 显示目前用药
                        current_content.height = dp(560)
                        current_content.opacity = 1
                        current_content.disabled = False

                        # 隐藏既往用药
                        history_content.height = 0
                        history_content.opacity = 0
                        history_content.disabled = True

                        # 更新Tab按钮状态
                        if current_tab_btn:
                            current_tab_btn.md_bg_color = App.get_running_app().theme_cls.primaryColor
                        if history_tab_btn:
                            history_tab_btn.md_bg_color = App.get_running_app().theme_cls.surfaceContainerColor

                        # 加载数据
                        Clock.schedule_once(lambda dt: self.load_current_medications(), 0.1)
                        KivyLogger.info("[MedicationManagement] 显示目前用药Tab")

                    else:  # history
                        # 隐藏目前用药
                        current_content.height = 0
                        current_content.opacity = 0
                        current_content.disabled = True

                        # 显示既往用药
                        history_content.height = dp(560)
                        history_content.opacity = 1
                        history_content.disabled = False

                        # 更新Tab按钮状态
                        if history_tab_btn:
                            history_tab_btn.md_bg_color = App.get_running_app().theme_cls.primaryColor
                        if current_tab_btn:
                            current_tab_btn.md_bg_color = App.get_running_app().theme_cls.surfaceContainerColor

                        # 加载数据
                        Clock.schedule_once(lambda dt: self.load_history_medications(), 0.1)
                        KivyLogger.info("[MedicationManagement] 显示既往用药Tab")

                else:
                    KivyLogger.warning("[MedicationManagement] 未找到Tab内容容器")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 切换Tab失败: {e}")

    def search_history_medications(self):
        """搜索既往用药记录"""
        try:
            if not hasattr(self, 'ids') or 'history_search_field' not in self.ids:
                return

            search_text = self.ids.history_search_field.text.strip().lower()

            if not search_text:
                self.refresh_history_display()
                return

            # 过滤既往用药记录
            filtered_medications = []
            for med in self.history_medications:
                try:
                    # 安全地获取字符串值
                    name = med.get("name", "") or ""
                    stop_reason = med.get("stop_reason", "") or ""

                    # 检查是否匹配搜索条件
                    if (search_text in name.lower() or
                        search_text in stop_reason.lower()):
                        filtered_medications.append(med)
                except Exception as filter_error:
                    KivyLogger.error(f"[MedicationManagement] 过滤药物失败: {filter_error}")
                    continue

            # 临时更新显示
            original_history = self.history_medications
            self.history_medications = filtered_medications
            self.refresh_history_display()
            self.history_medications = original_history

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 搜索既往用药失败: {e}")

    def _create_unified_dialog(self, dialog_type, title_text, content_widget=None, 
                              confirm_callback=None, confirm_text="确认", 
                              confirm_icon="check", size_hint=(0.85, None), height=dp(320)):
        """统一对话框创建方法 - 封装重复的对话框创建逻辑
        
        Args:
            dialog_type (str): 对话框类型 ('delete', 'stop', 'reminder')
            title_text (str): 对话框标题文本
            content_widget (Widget): 对话框内容组件
            confirm_callback (callable): 确认按钮回调函数
            confirm_text (str): 确认按钮文本
            confirm_icon (str): 确认按钮图标
            size_hint (tuple): 对话框大小提示
            height (int): 对话框高度
        
        Returns:
            MDDialog: 创建的对话框实例
        """
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon
            from kivymd.uix.boxlayout import MDBoxLayout

            # 根据对话框类型设置样式
            if dialog_type == 'delete':
                title_color = self.app.theme_cls.errorColor
                confirm_bg_color = self.app.theme_cls.errorColor
            elif dialog_type == 'stop':
                title_color = (1, 0, 0, 1)  # 红色
                confirm_bg_color = self.app.theme_cls.errorColor
            else:  # reminder
                title_color = (1, 0, 0, 1)  # 红色
                confirm_bg_color = self.app.theme_cls.primaryColor

            # 创建对话框组件
            dialog_components = [
                MDDialogHeadlineText(
                    text=title_text,
                    halign="center",
                    theme_text_color="Custom",
                    text_color=title_color
                )
            ]

            # 如果有内容组件，添加到对话框
            if content_widget:
                dialog_components.append(content_widget)

            # 添加按钮容器
            if confirm_callback:
                dialog_components.append(
                    MDDialogButtonContainer(
                        MDButton(
                            MDButtonIcon(icon="close"),
                            MDButtonText(text="取消"),
                            style="outlined",
                            on_release=lambda *x: self._close_unified_dialog()
                        ),
                        MDButton(
                            MDButtonIcon(icon=confirm_icon),
                            MDButtonText(text=confirm_text),
                            style="filled",
                            md_bg_color=confirm_bg_color,
                            on_release=lambda *x: confirm_callback()
                        )
                    )
                )

            # 创建对话框
            dialog = MDDialog(
                *dialog_components,
                size_hint=size_hint,
                height=height,
                auto_dismiss=False
            )

            return dialog

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 创建统一对话框失败: {e}")
            return None

    def _close_unified_dialog(self):
        """关闭统一对话框"""
        try:
            if hasattr(self, 'delete_dialog') and self.delete_dialog:
                self.delete_dialog.dismiss()
                self.delete_dialog = None
            if hasattr(self, 'unified_dialog') and self.unified_dialog:
                self.unified_dialog.dismiss()
                self.unified_dialog = None
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 关闭对话框失败: {e}")

    def show_delete_dialog(self):
        """显示批量删除确认对话框"""
        try:
            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("请先选择要删除的药物")
                return

            # 获取选中药物名称
            selected_names = [med.name for med in self.selected_medications]
            
            # 构建确认文本
            if len(selected_names) == 1:
                confirm_text = f"确定要删除药物 {selected_names[0]} 吗？\n\n⚠️ 此操作不可撤销！"
            else:
                # 最多显示5个药物名称
                display_names = selected_names[:5]
                if len(selected_names) > 5:
                    display_names.append(f"等{len(selected_names)}个药物")
                confirm_text = f"确定要删除以下药物吗？\n\n{', '.join(display_names)}\n\n⚠️ 此操作不可撤销！"

            # 创建对话框
            self.delete_dialog = MDDialog(
                MDDialogHeadlineText(text="删除确认"),
                MDDialogSupportingText(text=confirm_text),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda x: self.delete_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确认删除"),
                        style="filled",
                        md_bg_color=self.app.theme_cls.errorColor,
                        on_release=lambda x: self._confirm_batch_delete()
                    )
                )
            )

            self.delete_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示删除对话框失败: {e}")
            self.show_error("显示删除对话框失败")

    def _confirm_batch_delete(self):
        """确认批量删除"""
        try:
            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("没有选中的药物")
                return

            deleted_count = 0
            custom_id = self.get_current_user_id()
            medications_to_delete = [card.medication_data for card in self.selected_medications if hasattr(card, 'medication_data')]

            for medication in medications_to_delete:
                try:
                    # 从数据库中删除
                    if custom_id and medication.get('id'):
                        try:
                            success = self.db_manager.delete_medication(medication['id'])
                            if success:
                                KivyLogger.info(f"[MedicationManagement] 药物已从数据库删除: {medication['name']}")
                            else:
                                KivyLogger.warning(f"[MedicationManagement] 数据库删除失败: {medication['name']}")
                        except Exception as db_error:
                            KivyLogger.error(f"[MedicationManagement] 数据库删除异常: {db_error}")

                    # 从当前用药列表中删除
                    if medication in self.current_medications:
                        self.current_medications.remove(medication)
                    if medication in self.medications:
                        self.medications.remove(medication)

                    deleted_count += 1

                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 删除单个药物失败: {e}")

            # 刷新界面（不重新从数据库加载）
            self.refresh_current_medications()

            # 清空选择
            if hasattr(self, 'selected_medications'):
                self.selected_medications.clear()

            # 关闭对话框
            if self.delete_dialog:
                self.delete_dialog.dismiss()

            # 显示成功消息
            if deleted_count > 0:
                self.show_success(f"成功删除 {deleted_count} 个药物")
            else:
                self.show_error("没有药物被删除")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 批量删除失败: {e}")
            self.show_error("删除操作失败")

    def show_stop_dialog(self):
        """显示停药对话框（使用新的截图布局设计）"""
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.boxlayout import MDBoxLayout

            # 调试信息
            selected_count = len(getattr(self, 'selected_medications', []))
            KivyLogger.info(f"[MedicationManagement] 停药对话框 - 已选择药物数量: {selected_count}")

            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("请先选择要停用的药物")
                return

            # 创建对话框内容容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(0),
                size_hint_y=None,
                adaptive_height=True
            )

            # 添加停药字段（使用新的截图布局）
            self._add_stop_fields(content)

            # 创建对话框标题
            if len(self.selected_medications) > 1:
                # 多个药物时显示所有药物名称
                medication_names = [med.name for med in self.selected_medications]
                title_text = f"药物 {', '.join(medication_names)} 将被停用"
            else:
                # 单个药物时显示该药物名称
                medication_name = self.selected_medications[0].name
                title_text = f"药物 {medication_name} 将被停用"
            
            # 创建对话框，使用KivyMD 2.0.1dev0规范
            self.unified_dialog = MDDialog(
                MDDialogHeadlineText(
                    text=title_text,
                    halign="center",
                    theme_text_color="Custom",
                    text_color=(1, 0, 0, 1)  # 红色字体
                ),
                size_hint=(0.85, None),
                height=dp(320),
                auto_dismiss=False
            )
            # 添加内容到对话框容器
            self.unified_dialog.ids.container.add_widget(content)

            self.unified_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示停药对话框失败: {e}")
            self.show_error("显示停药对话框失败")

    def show_batch_reminder_dialog(self):
        """显示批量设置提醒对话框 - 取消滚动设计，使用固定高度"""
        try:
            # 检查是否选中了药物
            selected_count = len(getattr(self, 'selected_medications', []))
            KivyLogger.info(f"[MedicationManagement] 提醒设置对话框 - 已选择药物数量: {selected_count}")

            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("请先选择要设置提醒的药物")
                return

            # 创建对话框内容容器 - 不使用滚动容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(0),
                size_hint_y=None,
                adaptive_height=True
            )

            # 添加提醒设置字段
            self._add_reminder_fields(content)

            # 获取选中药物名称
            selected_names = [med.name for med in self.selected_medications]
            if len(selected_names) == 1:
                title_text = f"设置提醒: {selected_names[0]}"
            else:
                title_text = f"批量设置提醒: {'、'.join(selected_names)}"

            # 创建对话框 - 使用更大的固定高度确保内容完整显示
            self.reminder_dialog = MDDialog(
                MDDialogHeadlineText(
                    text=title_text,
                    theme_text_color="Custom",
                    text_color=(1, 0, 0, 1)  # 红色标题
                ),

                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda x: self.reminder_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确认设置"),
                        style="filled",
                        on_release=lambda x: self._confirm_batch_reminder()
                    ),
                ),
                size_hint=(0.9, None),  # 使用固定高度
                height=dp(750),  # 增加高度以容纳所有内容
                auto_dismiss=False
            )

            # 直接添加内容到对话框
            self.reminder_dialog.ids.container.add_widget(content)
            self.reminder_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示提醒设置对话框失败: {e}")
            self.show_error("显示提醒对话框失败")

    def _confirm_stop_medications(self):
        """确认停用选中的药物"""
        try:
            if hasattr(self, 'unified_dialog') and self.unified_dialog:
                self.unified_dialog.dismiss()

            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("没有选中的药物")
                return

            # 获取停药原因
            stop_reason = ""
            if hasattr(self, 'stop_reason_field') and self.stop_reason_field:
                stop_reason = self.stop_reason_field.text.strip()

            if not stop_reason:
                self.show_error("请输入停药原因")
                return

            # 批量停用药物
            success_count = 0
            total_count = len(self.selected_medications)

            for card in self.selected_medications:
                try:
                    # 获取药物数据
                    medication_data = getattr(card, 'medication_data', None)
                    if medication_data and medication_data.get('id'):
                        medication_id = medication_data.get('id')
                        # 更新药物状态为停用
                        success = self.db_manager.stop_medication(medication_id, stop_reason)
                        if success:
                            success_count += 1
                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 停用药物失败: {e}")
                    continue

            # 清空选择
            self.selected_medications.clear()
            # 更新UI状态（清空选择后不需要特定卡片参数）
            try:
                self.refresh_selection_ui()
            except AttributeError:
                # 如果没有refresh_selection_ui方法，跳过UI更新
                pass

            # 刷新列表
            self.load_current_medications()

            # 显示结果
            if success_count == total_count:
                self.show_info(f"成功停用 {success_count} 个药物")
            elif success_count > 0:
                self.show_info(f"成功停用 {success_count}/{total_count} 个药物")
            else:
                self.show_error("停用药物失败")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认停用药物失败: {e}")
            self.show_error("停用药物失败")

    def _confirm_batch_reminder(self):
        """确认批量设置提醒"""
        try:
            if hasattr(self, 'reminder_dialog') and self.reminder_dialog:
                self.reminder_dialog.dismiss()

            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("没有选中的药物")
                return

            # 获取提醒设置
            reminder_data = self._get_reminder_data()
            if not reminder_data:
                self.show_error("请完善提醒设置")
                return

            # 批量设置提醒
            success_count = 0
            total_count = len(self.selected_medications)

            for card in self.selected_medications:
                try:
                    # 获取药物数据
                    medication_data = getattr(card, 'medication_data', None)
                    if medication_data and medication_data.get('id'):
                        medication_id = medication_data.get('id')
                        custom_id = self.get_current_user_id() or 'default_user'
                        
                        # 处理服药提醒
                        if reminder_data.get('med_reminder', {}).get('enabled'):
                            med_times = reminder_data['med_reminder'].get('times', [])
                            for time_setting in med_times:
                                import json
                                reminder_time_data = json.dumps(time_setting)
                                db_reminder_data = {
                                    'reminder_time': reminder_time_data,
                                    'reminder_type': 'medication'
                                }
                                success = self.db_manager.save_reminder(medication_id, custom_id, db_reminder_data)
                                if success:
                                    success_count += 1
                        
                        # 处理复查提醒
                        if reminder_data.get('review_reminder', {}).get('enabled'):
                            review_data = {
                                'date': reminder_data['review_reminder'].get('date', ''),
                                'advance_days': reminder_data['review_reminder'].get('advance_days', '3')
                            }
                            import json
                            reminder_time_data = json.dumps(review_data)
                            db_reminder_data = {
                                'reminder_time': reminder_time_data,
                                'reminder_type': 'review'
                            }
                            success = self.db_manager.save_reminder(medication_id, custom_id, db_reminder_data)
                            if success:
                                success_count += 1
                                
                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 设置提醒失败: {e}")
                    continue

            # 清空选择
            self.selected_medications.clear()
            # 更新UI状态（清空选择后不需要特定卡片参数）
            try:
                self.refresh_selection_ui()
            except AttributeError:
                # 如果没有refresh_selection_ui方法，跳过UI更新
                pass

            # 刷新列表
            self.load_current_medications()

            # 显示结果
            if success_count == total_count:
                self.show_info(f"成功为 {success_count} 个药物设置提醒")
            elif success_count > 0:
                self.show_info(f"成功为 {success_count}/{total_count} 个药物设置提醒")
            else:
                self.show_error("设置提醒失败")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认设置提醒失败: {e}")
            self.show_error("设置提醒失败")

    def _get_reminder_data(self):
        """获取提醒设置数据"""
        try:
            reminder_data = {}
            has_valid_data = False

            # 添加调试日志
            KivyLogger.info(f"[MedicationManagement] 开始获取提醒数据")
            
            # 获取服药提醒设置
            if hasattr(self, 'unified_med_reminder_checkbox'):
                KivyLogger.info(f"[MedicationManagement] 服药提醒复选框存在: {self.unified_med_reminder_checkbox is not None}")
                if self.unified_med_reminder_checkbox and self.unified_med_reminder_checkbox.active:
                    KivyLogger.info(f"[MedicationManagement] 服药提醒已启用")
                    # 收集所有时间段的设置
                    med_times = []
                    time_slot_count = getattr(self, 'time_slot_count', 1)
                    KivyLogger.info(f"[MedicationManagement] 时间段数量: {time_slot_count}")
                    
                    for i in range(1, time_slot_count + 1):
                        time_field = getattr(self, f'unified_med_time{i}_field', None)
                        advance_field = getattr(self, f'unified_med_advance{i}_field', None)
                        
                        KivyLogger.info(f"[MedicationManagement] 时间段{i}: 时间字段存在={time_field is not None}, 提前字段存在={advance_field is not None}")
                        
                        if time_field:
                            time_text = time_field.text.strip() if time_field.text else ""
                            advance_text = advance_field.text.strip() if advance_field and advance_field.text else ""
                            KivyLogger.info(f"[MedicationManagement] 时间段{i}: 时间='{time_text}', 提前分钟='{advance_text}'")
                            
                            if time_text:
                                time_setting = {
                                    'time': time_text,
                                    'advance_minutes': advance_text if advance_text else '15'
                                }
                                med_times.append(time_setting)
                                KivyLogger.info(f"[MedicationManagement] 添加有效时间段{i}: {time_setting}")
                    
                    if med_times:
                        reminder_data['med_reminder'] = {
                            'enabled': True,
                            'times': med_times
                        }
                        has_valid_data = True
                        KivyLogger.info(f"[MedicationManagement] 服药提醒数据有效，共{len(med_times)}个时间段")
                    else:
                        KivyLogger.warning(f"[MedicationManagement] 服药提醒已启用但没有有效时间段")
                else:
                    KivyLogger.info(f"[MedicationManagement] 服药提醒未启用")
            else:
                KivyLogger.warning(f"[MedicationManagement] 服药提醒复选框不存在")

            # 获取复查提醒设置
            if hasattr(self, 'unified_review_reminder_checkbox'):
                KivyLogger.info(f"[MedicationManagement] 复查提醒复选框存在: {self.unified_review_reminder_checkbox is not None}")
                if self.unified_review_reminder_checkbox and self.unified_review_reminder_checkbox.active:
                    KivyLogger.info(f"[MedicationManagement] 复查提醒已启用")
                    review_date_field = getattr(self, 'unified_review_date_field', None)
                    review_advance_field = getattr(self, 'unified_review_advance_field', None)
                    
                    KivyLogger.info(f"[MedicationManagement] 复查字段存在: 日期={review_date_field is not None}, 提前天数={review_advance_field is not None}")
                    
                    if review_date_field:
                        date_text = review_date_field.text.strip() if review_date_field.text else ""
                        advance_text = review_advance_field.text.strip() if review_advance_field and review_advance_field.text else ""
                        KivyLogger.info(f"[MedicationManagement] 复查数据: 日期='{date_text}', 提前天数='{advance_text}'")
                        
                        if date_text:
                            reminder_data['review_reminder'] = {
                                'enabled': True,
                                'date': date_text,
                                'advance_days': advance_text if advance_text else '3'
                            }
                            has_valid_data = True
                            KivyLogger.info(f"[MedicationManagement] 复查提醒数据有效")
                        else:
                            KivyLogger.warning(f"[MedicationManagement] 复查提醒已启用但日期为空")
                    else:
                        KivyLogger.warning(f"[MedicationManagement] 复查日期字段不存在")
                else:
                    KivyLogger.info(f"[MedicationManagement] 复查提醒未启用")
            else:
                KivyLogger.warning(f"[MedicationManagement] 复查提醒复选框不存在")

            # 至少需要一个有效的提醒设置
            KivyLogger.info(f"[MedicationManagement] 最终结果: 有效数据={has_valid_data}, 提醒数据={reminder_data}")
            if not has_valid_data:
                KivyLogger.warning(f"[MedicationManagement] 没有有效的提醒设置，返回None")
                return None

            return reminder_data

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 获取提醒数据失败: {e}")
            import traceback
            KivyLogger.error(f"[MedicationManagement] 错误详情: {traceback.format_exc()}")
            return None

    def show_delete_confirmation(self, medication):
        """显示删除确认对话框 - 使用统一对话框方法"""
        try:
            from kivymd.uix.dialog import MDDialogSupportingText
            
            # 创建支持文本组件
            support_text = MDDialogSupportingText(
                text=f"确定要永久删除 {medication.get('name', '此药品')} 的用药记录吗？\n\n⚠️ 此操作不可撤销！"
            )

            self.delete_dialog = self._create_unified_dialog(
                dialog_type='delete',
                title_text="删除用药记录",
                content_widget=support_text,
                confirm_callback=lambda: self.confirm_delete_medication(medication),
                confirm_text="永久删除",
                confirm_icon="delete",
                size_hint=(0.9, None)
            )

            if self.delete_dialog:
                self.delete_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示删除对话框失败: {e}")
            self.show_error("显示删除对话框失败")

    def confirm_delete_medication(self, medication):
        """确认删除用药记录"""
        try:
            if self.delete_dialog:
                self.delete_dialog.dismiss()

            medication_id = medication.get('id')
            if not medication_id:
                self.show_error("药物ID缺失")
                return

            # 删除用药记录
            success = self.db_manager.delete_medication(medication_id)

            if success:
                self.show_info(f"已删除 {medication.get('name', '药物')} 的用药记录")
                self.load_current_medications()
            else:
                self.show_error("删除用药记录失败")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 删除用药记录失败: {e}")
            self.show_error("删除用药记录失败")

    def _show_time_picker(self, *args):
        """显示时间选择器"""
        try:
            try:
                from utils.time_picker_utils import show_time_picker

                def on_time_selected(selected_time):
                    """时间选择回调"""
                    try:
                        self.med_time_field.text = selected_time.strftime("%H:%M")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置服药时间失败: {e}")

                show_time_picker(on_time_selected)

            except ImportError:
                # 备用方案：使用简单的输入对话框
                self._show_simple_time_picker(self.med_time_field, "选择服药时间")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示时间选择器失败: {e}")

    def _show_review_date_picker(self, *args):
        """显示复查日期选择器"""
        try:
            try:
                from utils.date_picker_utils import show_review_date_picker

                def on_date_selected(selected_date):
                    """日期选择回调"""
                    try:
                        self.review_date_field.text = selected_date.strftime("%Y-%m-%d")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置复查日期失败: {e}")

                show_review_date_picker(on_date_selected)

            except ImportError:
                # 备用方案：使用简单的输入对话框
                from datetime import datetime, timedelta
                default_date = datetime.now() + timedelta(days=30)
                self.review_date_field.text = default_date.strftime("%Y-%m-%d")
                self._show_simple_date_picker(self.review_date_field, "选择复查日期")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示复查日期选择器失败: {e}")

    def _show_simple_time_picker(self, text_field, title="选择时间"):
        """显示简单的时间输入对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText

            time_input = MDTextField(
                mode="outlined",
                text=text_field.text,
                size_hint_y=None,
                height=dp(56)
            )
            time_input.add_widget(MDTextFieldHintText(text="时间 (HH:MM)"))

            def confirm_time(*args):
                text_field.text = time_input.text
                time_dialog.dismiss()

            time_dialog = MDDialog(
                MDDialogHeadlineText(text=title),
                time_input,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda x: time_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确认"),
                        style="filled",
                        on_release=confirm_time
                    )
                ),
                size_hint=(0.8, None),
                auto_dismiss=False
            )
            time_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示简单时间选择器失败: {e}")

    def show_medication_detail(self, medication):
        """显示药物详情对话框"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )

            # 药物基本信息
            info_items = [
                ("药物名称", medication.get('name', '')),
                ("剂量", medication.get('dosage', '')),
                ("用法", medication.get('frequency', '')),
                ("开始时间", medication.get('start_date', '')),
                ("结束时间", medication.get('end_date', '')),
                ("备注", medication.get('notes', '无'))
            ]

            for label_text, value_text in info_items:
                item_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(32),
                    spacing=dp(8)
                )

                label = MDLabel(
                    text=f"{label_text}:",
                    theme_text_color="Primary",
                    size_hint_x=0.3,
                    halign="left",
                    valign="center"
                )
                item_layout.add_widget(label)

                value = MDLabel(
                    text=str(value_text),
                    theme_text_color="Secondary",
                    size_hint_x=0.7,
                    halign="left",
                    valign="center"
                )
                item_layout.add_widget(value)

                content.add_widget(item_layout)

            self.detail_dialog = MDDialog(
                MDDialogHeadlineText(text="药物详情"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="filled",
                        on_release=lambda x: self.detail_dialog.dismiss()
                    )
                )
            )

            self.detail_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示详情对话框失败: {e}")
            self.show_error("显示详情对话框失败")

    def export_medication_data(self):
        """导出用药数据"""
        try:
            custom_id = self.get_current_user_id()
            if not custom_id:
                self.show_error("用户未登录，无法导出数据")
                return

            # 创建导出目录
            export_dir = Path.home() / "Downloads" / "health_management"
            export_dir.mkdir(parents=True, exist_ok=True)

            # 生成导出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_path = export_dir / f"medication_data_{timestamp}.json"

            # 导出数据
            success = self.db_manager.export_data(custom_id, str(export_path))

            if success:
                self.show_info(f"数据导出成功\n文件保存至: {export_path}")
            else:
                self.show_error("数据导出失败")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 导出数据失败: {e}")
            self.show_error("导出数据失败")

    def show_info(self, message):
        """显示信息提示"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示信息失败: {e}")

    def show_error(self, message):
        """显示错误提示"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=f"错误: {message}"),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=(0.8, 0.2, 0.2, 1),  # 红色背景
            )
            snackbar.open()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示错误失败: {e}")

    def show_success(self, message):
        """显示成功消息"""
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9,
                md_bg_color=self.app.theme_cls.primaryColor,
                duration=3
            )
            snackbar.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示成功消息失败: {e}")

    def show_frequency_menu(self, text_field):
        """显示使用频次下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            frequency_options = [
                "每日一次", "每日两次", "每日三次", "每日四次",
                "每周一次", "每周两次", "每周三次",
                "每月一次", "按需服用", "其他"
            ]

            menu_items = []
            for option in frequency_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_frequency(x, text_field)
                })

            self.frequency_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),  # 使用width替代width_mult
                max_height=dp(200),
            )
            self.frequency_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示频次菜单失败: {e}")

    def set_frequency(self, frequency, text_field):
        """设置使用频次"""
        try:
            text_field.text = frequency
            if hasattr(self, 'frequency_menu') and self.frequency_menu:
                self.frequency_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置频次失败: {e}")

    def show_reason_menu(self, text_field):
        """显示用药原因下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            reason_options = [
                "高血压", "糖尿病", "高血脂", "冠心病",
                "心律不齐", "慢性肾病", "甲状腺疾病",
                "关节炎", "骨质疏松", "抑郁症", "其他原因"
            ]

            menu_items = []
            for option in reason_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_reason(x, text_field)
                })

            self.reason_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),  # 使用width替代width_mult
                max_height=dp(200),
            )
            self.reason_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示原因菜单失败: {e}")

    def set_reason(self, reason, text_field):
        """设置用药原因"""
        try:
            text_field.text = reason
            if hasattr(self, 'reason_menu') and self.reason_menu:
                self.reason_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置原因失败: {e}")

    def show_notes_menu(self, text_field):
        """显示注意事项下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            notes_options = [
                "空腹服用", "餐前服用", "餐中服用", "餐后服用",
                "睡前服用", "避免饮酒", "多饮水", "无特殊要求"
            ]

            menu_items = []
            for option in notes_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_notes(x, text_field)
                })

            self.notes_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),  # 使用width替代width_mult
                max_height=dp(200),
            )
            self.notes_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示注意事项菜单失败: {e}")

    def set_notes(self, notes, text_field):
        """设置注意事项"""
        try:
            text_field.text = notes
            if hasattr(self, 'notes_menu') and self.notes_menu:
                self.notes_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置注意事项失败: {e}")

    def show_date_picker(self, text_field):
        """显示日期选择器"""
        try:
            try:
                from utils.date_picker_utils import show_medication_start_date_picker

                def on_date_selected(selected_date):
                    """日期选择回调"""
                    try:
                        text_field.text = selected_date.strftime("%Y-%m-%d")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置日期失败: {e}")

                show_medication_start_date_picker(on_date_selected)

            except ImportError:
                # 备用方案：使用简单的输入对话框
                self._show_simple_date_picker(text_field, "选择用药开始日期")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示日期选择器失败: {e}")
            # 最后的备用方案：直接设置当前日期
            from datetime import datetime
            text_field.text = datetime.now().strftime("%Y-%m-%d")

    def _show_simple_date_picker(self, text_field, title="选择日期"):
        """显示简单的日期输入对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            from kivymd.uix.boxlayout import MDBoxLayout
            from datetime import datetime

            # 创建输入框
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )

            self.date_input = MDTextField(
                mode="outlined",
                text=datetime.now().strftime("%Y-%m-%d"),
                size_hint_y=None,
                height=dp(56)
            )
            self.date_input.add_widget(MDTextFieldHintText(text="请输入日期 (YYYY-MM-DD)"))
            content.add_widget(self.date_input)

            # 创建对话框
            self.date_dialog = MDDialog(
                MDDialogHeadlineText(text=title),
                MDDialogSupportingText(text="请输入日期，格式：YYYY-MM-DD"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda *x: self.date_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确定"),
                        style="filled",
                        on_release=lambda *x: self._confirm_simple_date(text_field)
                    )
                ),
                size_hint=(0.8, None),
                auto_dismiss=False
            )

            self.date_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示简单日期选择器失败: {e}")
            # 最终备用方案
            from datetime import datetime
            text_field.text = datetime.now().strftime("%Y-%m-%d")

    def _confirm_simple_date(self, text_field):
        """确认简单日期输入"""
        try:
            date_str = self.date_input.text.strip()
            # 验证日期格式
            from datetime import datetime
            datetime.strptime(date_str, "%Y-%m-%d")
            text_field.text = date_str

        except ValueError:
            KivyLogger.error(f"[MedicationManagement] 无效的日期格式: {date_str}")
            self.show_error("请输入正确的日期格式 (YYYY-MM-DD)")
            return
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认日期失败: {e}")
        finally:
            if hasattr(self, 'date_dialog') and self.date_dialog:
                self.date_dialog.dismiss()

    def add_medication_to_list(self):
        """添加药物到列表"""
        try:
            # 获取表单数据
            if not hasattr(self, 'ids'):
                self.show_error("界面未初始化")
                return

            # 获取各个字段的值
            name = getattr(self.ids.get('medication_name_field'), 'text', '').strip()
            dosage = getattr(self.ids.get('dosage_field'), 'text', '').strip()
            frequency = getattr(self.ids.get('frequency_field'), 'text', '').strip()
            start_date = getattr(self.ids.get('start_date_field'), 'text', '').strip()
            reason = getattr(self.ids.get('reason_field'), 'text', '').strip()
            notes = getattr(self.ids.get('notes_field'), 'text', '').strip()

            # 验证必填字段
            if not name:
                self.show_error("请输入药物名称")
                return
            if not dosage:
                self.show_error("请输入用药剂量")
                return
            if not frequency:
                self.show_error("请选择使用频次")
                return
            if not start_date:
                self.show_error("请选择开始日期")
                return

            # 检查是否存在重复药物
            for existing_med in self.current_medications:
                if (existing_med.get('name', '').lower() == name.lower() and
                    existing_med.get('dosage', '') == dosage):
                    self.show_error(f"药物 '{name} {dosage}' 已存在，请勿重复添加")
                    return

            # 获取用户ID
            custom_id = self.get_current_user_id()
            if not custom_id:
                self.show_error("用户未登录，无法保存药物信息")
                return

            # 创建药物数据
            medication_data = {
                'name': name,
                'dosage': dosage,
                'frequency': frequency,
                'start_date': start_date,
                'reason': reason,
                'notes': notes,
                'status': 'active',  # 数据库中使用 'active' 状态
                'custom_id': custom_id
            }

            # 保存到数据库
            try:
                saved_medication = self.db_manager.save_medication(custom_id, medication_data)
                if saved_medication:
                    # 使用数据库返回的数据（包含ID等信息）
                    medication_data = saved_medication
                    KivyLogger.info(f"[MedicationManagement] 药物已保存到数据库: {medication_data.get('id')}")
                else:
                    self.show_error("保存药物到数据库失败")
                    return
            except Exception as db_error:
                KivyLogger.error(f"[MedicationManagement] 数据库保存失败: {db_error}")
                self.show_error("保存药物失败，请重试")
                return

            # 添加到当前用药列表
            self.current_medications.append(medication_data)

            # 刷新界面显示
            self.refresh_current_medications()

            # 清空表单
            self.clear_add_form()

            # 切换到当前用药标签页
            self.switch_tab('current')

            self.show_success("药物添加成功")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加药物失败: {e}")
            self.show_error("添加药物失败")

    def clear_add_form(self):
        """清空添加表单"""
        try:
            if hasattr(self, 'ids'):
                fields = ['medication_name_field', 'dosage_field', 'frequency_field',
                         'start_date_field', 'reason_field', 'notes_field']
                for field_id in fields:
                    field = self.ids.get(field_id)
                    if field and hasattr(field, 'text'):
                        field.text = ''
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 清空表单失败: {e}")

    def clear_history_search(self):
        """清空历史搜索"""
        try:
            if hasattr(self, 'ids') and 'history_search_field' in self.ids:
                self.ids.history_search_field.text = ''
                # 重新加载所有历史记录
                self.load_history_medications()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 清空历史搜索失败: {e}")

    def unified_stop_medication(self, medication_data=None, stop_reason="", stop_date="", is_single=False):
        """统一停药方法"""
        try:
            from datetime import datetime

            # 如果没有提供停药日期，使用当前日期
            if not stop_date:
                stop_date = datetime.now().strftime("%Y-%m-%d")

            # 如果没有提供停药原因，使用默认原因
            if not stop_reason:
                stop_reason = "用户手动停药"

            if is_single and medication_data:
                # 单个药物停药
                medications_to_stop = [medication_data]
            else:
                # 批量停药（从选中的药物列表）
                medications_to_stop = getattr(self, 'selected_medications', [])
                if hasattr(self, 'selected_medications'):
                    medications_to_stop = [card.medication_data for card in self.selected_medications if hasattr(card, 'medication_data')]

            if not medications_to_stop:
                self.show_error("没有要停用的药物")
                return

            stopped_count = 0
            custom_id = self.get_current_user_id()

            for medication in medications_to_stop:
                try:
                    # 更新药物状态
                    medication['status'] = 'stopped'  # 数据库中使用 'stopped' 状态
                    medication['stop_date'] = stop_date
                    medication['stop_reason'] = stop_reason
                    medication['end_date'] = stop_date  # 设置结束日期

                    # 保存到数据库
                    if custom_id and medication.get('id'):
                        try:
                            success = self.db_manager.update_medication_status(
                                medication['id'],
                                'stopped',
                                stop_date,
                                stop_reason
                            )
                            if success:
                                KivyLogger.info(f"[MedicationManagement] 药物状态已更新到数据库: {medication['name']}")
                            else:
                                KivyLogger.warning(f"[MedicationManagement] 数据库更新失败: {medication['name']}")
                        except Exception as db_error:
                            KivyLogger.error(f"[MedicationManagement] 数据库更新异常: {db_error}")

                    # 如果停药原因是过敏，更新过敏记录
                    if stop_reason and '过敏' in stop_reason:
                        try:
                            self.update_allergy_record(custom_id, medication, stop_date)
                        except Exception as allergy_error:
                            KivyLogger.error(f"[MedicationManagement] 更新过敏记录失败: {allergy_error}")

                    # 从当前用药列表中移除
                    if medication in self.medications:
                        self.medications.remove(medication)
                    if medication in self.current_medications:
                        self.current_medications.remove(medication)

                    # 添加到历史用药列表
                    if medication not in self.history_medications:
                        self.history_medications.append(medication)

                    stopped_count += 1

                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 停用单个药物失败: {e}")

            # 刷新界面显示（不重新从数据库加载）
            self.refresh_current_medications()
            self.refresh_history_medications()

            # 清空选择
            if hasattr(self, 'selected_medications'):
                self.selected_medications.clear()

            # 显示成功消息
            if stopped_count > 0:
                self.show_success(f"成功停用 {stopped_count} 个药物")
            else:
                self.show_error("没有药物被停用")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 统一停药方法失败: {e}")
            self.show_error("停药操作失败")

    def confirm_single_delete(self, medication_data):
        """确认删除单个药物"""
        try:
            # 从当前用药列表中删除
            if medication_data in self.medications:
                self.medications.remove(medication_data)

            # 刷新界面
            self.load_current_medications()

            self.show_success("药物删除成功")

            if self.dialog:
                self.dialog.dismiss()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认删除单个药物失败: {e}")
            self.show_error("删除药物失败")

    def update_selection(self, medication_card, is_selected):
        """更新药物选择状态"""
        try:
            if is_selected:
                if medication_card not in self.selected_medications:
                    self.selected_medications.append(medication_card)
            else:
                if medication_card in self.selected_medications:
                    self.selected_medications.remove(medication_card)

            KivyLogger.info(f"MedicationManagement: 已选择 {len(self.selected_medications)} 个药物")
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 更新选择状态失败: {e}")

    def update_selection_ui(self, medication_card, is_selected):
        """更新药物选择UI状态（兼容性方法）"""
        try:
            # 调用主要的选择更新方法
            self.update_selection(medication_card, is_selected)
            
            # 可以在这里添加额外的UI更新逻辑
            # 例如更新选择计数显示、按钮状态等
            
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 更新选择UI状态失败: {e}")

    # 重复的show_review_date_picker方法已删除，使用_show_review_date_picker

    def update_allergy_record(self, custom_id: str, medication: dict, allergy_date: str):
        """更新过敏记录到基本健康信息"""
        try:
            from datetime import datetime

            # 构建过敏记录数据
            allergy_data = {
                'custom_id': custom_id,
                'allergen_type': 'medication',  # 过敏原类型：药物
                'allergen_name': medication.get('name', '未知药物'),
                'allergen_details': f"药物: {medication.get('name', '未知药物')}, 剂量: {medication.get('dosage', '')}, 用法: {medication.get('frequency', '')}",
                'reaction_type': 'drug_allergy',  # 反应类型：药物过敏
                'reaction_severity': 'unknown',  # 严重程度：未知（可以后续完善）
                'reaction_symptoms': '药物过敏反应',  # 过敏症状
                'occurrence_date': allergy_date,
                'notes': f"因药物过敏停用 {medication.get('name', '未知药物')}",
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            # 检查是否已存在相同的过敏记录
            existing_allergies = self.get_existing_allergies(custom_id, medication.get('name', ''))
            if existing_allergies:
                KivyLogger.info(f"[MedicationManagement] 过敏记录已存在: {medication.get('name', '')}")
                return

            # 保存过敏记录到数据库
            success = self.save_allergy_record(allergy_data)
            if success:
                KivyLogger.info(f"[MedicationManagement] 过敏记录已保存: {medication.get('name', '')}")
                self.show_success(f"已将 {medication.get('name', '')} 添加到过敏记录")
            else:
                KivyLogger.error(f"[MedicationManagement] 过敏记录保存失败: {medication.get('name', '')}")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 更新过敏记录异常: {e}")

    def get_existing_allergies(self, custom_id: str, medication_name: str) -> list:
        """检查是否已存在相同的过敏记录"""
        try:
            # 这里应该查询过敏记录数据库表
            # 暂时返回空列表，表示没有重复记录
            # TODO: 实现实际的数据库查询
            return []
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 查询过敏记录失败: {e}")
            return []

    def save_allergy_record(self, allergy_data: dict) -> bool:
        """保存过敏记录到数据库"""
        try:
            # 这里应该调用过敏记录数据库管理器
            # 暂时返回True表示保存成功
            # TODO: 实现实际的数据库保存逻辑

            # 模拟数据库保存
            KivyLogger.info(f"[MedicationManagement] 模拟保存过敏记录: {allergy_data}")

            # 实际实现时应该类似这样：
            # from database.allergy_manager import AllergyDatabaseManager
            # allergy_db = AllergyDatabaseManager()
            # return allergy_db.save_allergy_record(allergy_data)

            return True
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 保存过敏记录异常: {e}")
            return False

    def refresh_data(self):
        """刷新数据 - 供UI刷新按钮调用"""
        try:
            KivyLogger.info("[MedicationManagement] 开始刷新数据")

            # 清空选择状态
            if hasattr(self, 'selected_medications'):
                self.selected_medications.clear()

            # 重新加载药物数据
            self.load_current_medications()

            # 刷新界面显示
            self.refresh_current_medications()
            self.refresh_history_display()

            self.show_success("数据刷新完成")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新数据失败: {e}")
            self.show_error("刷新数据失败")

    def _add_stop_fields(self, content):
        """添加停药相关字段"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            # 根据截图设计的停药对话框布局
            # 主容器 - 圆角背景
            main_container = MDCard(
                md_bg_color=AppTheme.PRIMARY_LIGHT,  # 浅蓝色背景
                radius=[dp(20)],
                elevation=3,
                size_hint_y=None,
                height=dp(260),  # 调整高度以适应对话框
                padding=[dp(16), dp(16), dp(16), dp(16)]  # 减少内边距
            )

            main_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )

            # 顶部提示信息 - 白色圆角卡片
            info_card = MDCard(
                md_bg_color=AppTheme.CARD_BACKGROUND,
                radius=[dp(12)],
                elevation=1,
                size_hint_y=None,
                height=dp(48),
                padding=[dp(16), dp(12), dp(16), dp(12)]
            )

            # 获取第一个选中药物的名称
            medication_name = "XXX"
            if hasattr(self, 'selected_medications') and self.selected_medications:
                first_med = self.selected_medications[0]
                if hasattr(first_med, 'medication_data'):
                    medication_name = first_med.medication_data.get('name', 'XXX')

            info_label = MDLabel(
                text=f"药物：{medication_name}，将被停用！",
                font_style="Body",
                role="medium",
                theme_text_color="Primary",
                halign="center",
                valign="center"
            )
            info_card.add_widget(info_label)
            main_layout.add_widget(info_card)

            # 显示默认停用日期按钮 - 绿色
            self.unified_stop_date_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_y=None,
                height=dp(48),
                radius=[dp(12)],
                on_release=self._on_unified_stop_date_click
            )

            self.unified_stop_date_button_text = MDButtonText(
                text="显示默认停用日期",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT
            )
            self.unified_stop_date_button.add_widget(self.unified_stop_date_button_text)
            main_layout.add_widget(self.unified_stop_date_button)

            # 停药原因选择按钮 - 绿色
            self.unified_stop_reason_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_y=None,
                height=dp(48),
                radius=[dp(12)],
                on_release=self._show_unified_stop_reason_menu
            )

            self.unified_stop_reason_button_text = MDButtonText(
                text="请选择停药原因",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT
            )
            self.unified_stop_reason_button.add_widget(self.unified_stop_reason_button_text)
            main_layout.add_widget(self.unified_stop_reason_button)

            # 按钮区域
            button_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(12)
            )

            # 取消按钮
            cancel_button = MDButton(
                style="outlined",
                md_bg_color=AppTheme.CARD_BACKGROUND,
                size_hint_x=0.4,
                radius=[dp(12)],
                on_release=lambda *x: self.unified_dialog.dismiss() if hasattr(self, 'unified_dialog') else None
            )

            cancel_text = MDButtonText(
                text="取消",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY
            )
            cancel_button.add_widget(cancel_text)
            button_layout.add_widget(cancel_button)

            # 确认按钮 - 绿色渐变效果
            confirm_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_x=0.6,
                radius=[dp(12)],
                on_release=lambda *x: self._confirm_unified_action('stop')
            )

            confirm_text = MDButtonText(
                text="确认",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.ERROR_COLOR  # 红色文字
            )
            confirm_button.add_widget(confirm_text)
            button_layout.add_widget(confirm_button)

            main_layout.add_widget(button_layout)

            main_container.add_widget(main_layout)
            content.add_widget(main_container)

            # 初始化停药原因选择
            self.unified_selected_stop_reason = ""

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加停药字段失败: {e}")

    def _add_reminder_fields(self, content):
        """添加提醒设置相关字段"""
        try:
            # 根据截图设计的提醒设置对话框布局
            # 主容器 - 绿色圆角背景
            main_container = MDCard(
                md_bg_color=AppTheme.HEALTH_GREEN,  # 绿色背景
                radius=[dp(20)],
                elevation=3,
                size_hint_y=None,
                height=dp(360),  # 调整高度以适应对话框
                padding=[dp(16), dp(16), dp(16), dp(16)]  # 减少内边距
            )

            main_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                size_hint_y=None,
                adaptive_height=True
            )

            # 标题卡片 - 深蓝色
            title_card = MDCard(
                md_bg_color=AppTheme.PRIMARY_DARK,  # 深蓝色背景
                radius=[dp(12)],
                elevation=2,
                size_hint_y=None,
                height=dp(40),
                padding=[dp(16), dp(8), dp(16), dp(8)]
            )

            title_label = MDLabel(
                text="提醒设置",
                font_style="Body",
                role="large",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT,
                halign="center",
                valign="center"
            )
            title_card.add_widget(title_label)
            main_layout.add_widget(title_card)

            # 服药提醒卡片 - 深蓝色
            med_reminder_card = MDCard(
                md_bg_color=AppTheme.PRIMARY_DARK,  # 深蓝色背景
                radius=[dp(12)],
                elevation=2,
                size_hint_y=None,
                height=dp(80),
                padding=[dp(16), dp(12), dp(16), dp(12)]
            )

            med_reminder_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(12),
                size_hint_y=None,
                height=dp(56),
                padding=[dp(0), dp(8), dp(0), dp(8)]
            )

            # 服药提醒复选框
            self.unified_med_reminder_checkbox = MDCheckbox(
                size_hint_x=None,
                width=dp(24),
                active=True
            )
            med_reminder_layout.add_widget(self.unified_med_reminder_checkbox)

            # 服药提醒标签
            med_reminder_label = MDLabel(
                text="服药提醒",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT,
                halign="left",
                valign="center"
            )
            med_reminder_layout.add_widget(med_reminder_label)

            # 时间输入框
            self.unified_med_time_field = MDTextField(
                mode="outlined",
                hint_text="时间",
                text="08:00",
                size_hint_x=0.3,
                height=dp(40)
            )
            med_reminder_layout.add_widget(self.unified_med_time_field)

            # 分钟输入框
            self.unified_med_advance_field = MDTextField(
                mode="outlined",
                hint_text="分钟",
                text="30",
                size_hint_x=0.3,
                height=dp(40)
            )
            med_reminder_layout.add_widget(self.unified_med_advance_field)

            med_reminder_card.add_widget(med_reminder_layout)
            main_layout.add_widget(med_reminder_card)

            # 复查提醒卡片 - 深蓝色
            review_reminder_card = MDCard(
                md_bg_color=AppTheme.PRIMARY_DARK,  # 深蓝色背景
                radius=[dp(12)],
                elevation=2,
                size_hint_y=None,
                height=dp(80),
                padding=[dp(16), dp(12), dp(16), dp(12)]
            )

            review_reminder_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(12),
                size_hint_y=None,
                height=dp(56),
                padding=[dp(0), dp(8), dp(0), dp(8)]
            )

            # 复查提醒复选框
            self.unified_review_reminder_checkbox = MDCheckbox(
                size_hint_x=None,
                width=dp(24),
                active=True
            )
            review_reminder_layout.add_widget(self.unified_review_reminder_checkbox)

            # 复查提醒标签
            review_reminder_label = MDLabel(
                text="复查提醒",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT,
                halign="left",
                valign="center"
            )
            review_reminder_layout.add_widget(review_reminder_label)

            # 复查日期输入框
            self.unified_review_date_field = MDTextField(
                mode="outlined",
                hint_text="日期",
                text="2024-02-01",
                size_hint_x=0.4,
                height=dp(40)
            )
            review_reminder_layout.add_widget(self.unified_review_date_field)

            # 提前天数输入框
            self.unified_review_advance_field = MDTextField(
                mode="outlined",
                hint_text="天数",
                text="3",
                size_hint_x=0.2,
                height=dp(40)
            )
            review_reminder_layout.add_widget(self.unified_review_advance_field)

            review_reminder_card.add_widget(review_reminder_layout)
            main_layout.add_widget(review_reminder_card)

            # 按钮区域
            button_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(12)
            )

            # 取消按钮
            cancel_button = MDButton(
                style="outlined",
                md_bg_color=AppTheme.CARD_BACKGROUND,
                size_hint_x=0.4,
                radius=[dp(12)],
                on_release=lambda *x: self.unified_dialog.dismiss() if hasattr(self, 'unified_dialog') else None
            )

            cancel_text = MDButtonText(
                text="取消",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY
            )
            cancel_button.add_widget(cancel_text)
            button_layout.add_widget(cancel_button)

            # 保存按钮 - 绿色
            save_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_x=0.6,
                radius=[dp(12)],
                on_release=lambda *x: self._confirm_unified_action('reminder')
            )

            save_text = MDButtonText(
                text="保存",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT
            )
            save_button.add_widget(save_text)
            button_layout.add_widget(save_button)

            main_layout.add_widget(button_layout)

            main_container.add_widget(main_layout)
            content.add_widget(main_container)

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加提醒字段失败: {e}")
            self.show_error("添加提醒字段失败")

    def _create_time_slot(self, times_layout, time_num):
        """创建时间段UI组件 - 简化设计"""
        try:
            app = MDApp.get_running_app()
            theme = app.theme_cls
            
            time_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(36),
                spacing=dp(8)
            )

            time_layout.add_widget(MDLabel(
                text=f"时间{time_num}:",
                font_style="Body",
                role="small",
                size_hint_x=None,
                width=dp(40),
                halign="left"
            ))

            time_field = MDTextField(
                mode="outlined",
                text="08:00" if time_num == 1 else "20:00",
                size_hint_x=None,
                width=dp(80),
                height=dp(20),  # 减小高度从24到20
                font_size=dp(10)  # 减小字体大小
            )
            time_field.add_widget(MDTextFieldHintText(
                text="时:分",
                font_size=dp(8)  # 减小提示词字体
            ))
            time_layout.add_widget(time_field)

            time_layout.add_widget(MDLabel(
                text="提前:",
                font_style="Body",
                role="small",
                size_hint_x=None,
                width=dp(40),
                halign="center"
            ))

            advance_field = MDTextField(
                mode="outlined",
                text="15",
                size_hint_x=None,
                width=dp(40),
                height=dp(20),  # 减小高度从24到20
                font_size=dp(10)  # 减小字体大小
            )
            advance_field.add_widget(MDTextFieldHintText(
                text="分钟",
                font_size=dp(8)  # 减小提示词字体
            ))
            time_layout.add_widget(advance_field)

            time_layout.add_widget(MDLabel(
                text="分钟",
                font_style="Body",
                role="small",
                size_hint_x=None,
                width=dp(40),
                halign="left"
            ))

            times_layout.add_widget(time_layout)

            # 存储字段引用
            setattr(self, f'unified_med_time{time_num}_field', time_field)
            setattr(self, f'unified_med_advance{time_num}_field', advance_field)
            
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 创建时间段失败: {e}")

    def _add_time_slot(self, times_layout):
        """添加新的时间段 - 最多4个时间段"""
        try:
            if self.time_slot_count >= 4:  # 最多4个时间段
                self.show_info("最多只能设置4个时间段")
                return

            self.time_slot_count += 1
            
            # 使用统一的创建方法
            self._create_time_slot(times_layout, self.time_slot_count)

            # 更新times_layout的高度，但保持对话框总高度固定
            times_layout.height = dp(32 * self.time_slot_count + 3 * (self.time_slot_count - 1))  # 考虑间距
            
            # 对话框高度保持固定，不随时间段增加而变化
            # 这样可以防止按钮位置移动

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加时间段失败: {e}")
            self.show_error("添加时间段失败")

    def _remove_time_slot(self, times_layout):
        """删除最后一个时间段 - 至少保留一个时间段"""
        try:
            if self.time_slot_count <= 1:  # 至少保留一个时间段
                self.show_info("至少需要保留一个时间段")
                return

            # 删除最后一个时间段的布局
            if times_layout.children:
                times_layout.remove_widget(times_layout.children[0])

            # 删除字段引用
            time_num = self.time_slot_count
            if hasattr(self, f'unified_med_time{time_num}_field'):
                delattr(self, f'unified_med_time{time_num}_field')
            if hasattr(self, f'unified_med_advance{time_num}_field'):
                delattr(self, f'unified_med_advance{time_num}_field')

            self.time_slot_count -= 1
            
            # 更新times_layout的高度
            times_layout.height = dp(32 * self.time_slot_count + 3 * (self.time_slot_count - 1))  # 考虑间距

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 删除时间段失败: {e}")
            self.show_error("删除时间段失败")

    def _confirm_unified_action(self, action_type):
        """确认统一操作"""
        try:
            if action_type == 'stop':
                self._confirm_unified_stop()
            else:
                self._confirm_unified_reminder()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认统一操作失败: {e}")

    def _confirm_unified_stop(self):
        """确认统一停药操作"""
        try:
            stop_reason = getattr(self, 'unified_selected_stop_reason', '')
            stop_date = getattr(self, 'unified_selected_stop_date', datetime.now().strftime("%Y-%m-%d"))

            if not stop_reason:
                self.show_error("请选择停药原因")
                return

            # 调用统一停药方法
            self.unified_stop_medication(
                medication_data=None,
                stop_reason=stop_reason,
                stop_date=stop_date,
                is_single=False
            )

            # 关闭对话框
            if hasattr(self, 'unified_dialog') and self.unified_dialog:
                self.unified_dialog.dismiss()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认统一停药失败: {e}")

    def _confirm_unified_reminder(self):
        """确认统一提醒设置"""
        try:
            # 获取服药提醒设置
            med_reminder_enabled = getattr(self, 'unified_med_reminder_checkbox', None)
            
            # 获取复查提醒设置
            review_reminder_enabled = getattr(self, 'unified_review_reminder_checkbox', None)
            review_date = getattr(self, 'unified_review_date_field', None)
            review_advance = getattr(self, 'unified_review_advance_field', None)

            reminder_settings = {}
            settings_saved = False

            # 处理服药提醒 - 支持多个时间段
            if med_reminder_enabled and med_reminder_enabled.active:
                med_times = []
                
                # 收集所有时间段的设置
                for i in range(1, self.time_slot_count + 1):
                    time_field = getattr(self, f'unified_med_time{i}_field', None)
                    advance_field = getattr(self, f'unified_med_advance{i}_field', None)
                    
                    if time_field and time_field.text.strip():
                        time_setting = {
                            'time': time_field.text.strip(),
                            'advance_minutes': advance_field.text.strip() if advance_field and advance_field.text.strip() else '15'
                        }
                        med_times.append(time_setting)
                
                if med_times:
                    reminder_settings['med_reminder'] = {
                        'enabled': True,
                        'times': med_times  # 支持多个时间段
                    }
                    settings_saved = True

            # 处理复查提醒
            if (review_reminder_enabled and review_reminder_enabled.active and
                review_date and review_date.text.strip()):
                reminder_settings['review_reminder'] = {
                    'enabled': True,
                    'date': review_date.text.strip(),
                    'advance_days': review_advance.text.strip() if review_advance and review_advance.text.strip() else '3'
                }
                settings_saved = True

            if settings_saved:
                # 为所有选中的药物设置提醒
                for card in self.selected_medications:
                    if hasattr(card, 'medication_data'):
                        medication = card.medication_data
                        medication['reminder_settings'] = reminder_settings

                        # 保存到数据库
                        medication_id = medication.get('id')
                        if medication_id:
                            # 获取当前用户的custom_id
                            current_user_id = self.get_current_user_id()
                            if not current_user_id:
                                KivyLogger.warning("[MedicationManagement] 无法获取用户ID，跳过提醒设置")
                                return
                            
                            # 保存服药提醒到数据库
                            if reminder_settings.get('med_reminder', {}).get('enabled'):
                                for time_setting in reminder_settings['med_reminder']['times']:
                                    reminder_data = {
                                        'reminder_time': time_setting['time'],
                                        'reminder_type': 'medication',
                                        'advance_minutes': time_setting['advance_minutes']
                                    }
                                    self.db_manager.save_reminder(medication_id, current_custom_id, reminder_data)
                            
                            # 保存复查提醒到数据库
                            if reminder_settings.get('review_reminder', {}).get('enabled'):
                                reminder_data = {
                                    'reminder_time': reminder_settings['review_reminder']['date'],
                                    'reminder_type': 'review',
                                    'advance_days': reminder_settings['review_reminder']['advance_days']
                                }
                                self.db_manager.save_reminder(medication_id, current_custom_id, reminder_data)

                        # 更新当前用药列表中的对应药物
                        for i, med in enumerate(self.current_medications):
                            if med.get('id') == medication.get('id') or med.get('name') == medication.get('name'):
                                self.current_medications[i] = medication
                                break

                # 刷新界面显示
                self.refresh_current_medications()

                # 显示成功消息
                reminder_types = []
                if reminder_settings.get('med_reminder', {}).get('enabled'):
                    time_count = len(reminder_settings['med_reminder']['times'])
                    reminder_types.append(f"服药提醒({time_count}个时间段)")
                if reminder_settings.get('review_reminder', {}).get('enabled'):
                    reminder_types.append("复查提醒")

                reminder_text = "、".join(reminder_types)
                self.show_success(f"已为 {len(self.selected_medications)} 个药物设置{reminder_text}")
            else:
                self.show_error("请至少启用一种提醒并填写相关信息")

            # 关闭对话框
            if hasattr(self, 'unified_dialog') and self.unified_dialog:
                self.unified_dialog.dismiss()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认统一提醒设置失败: {e}")

    def _on_unified_stop_date_click(self, *args):
        """统一停药日期按钮点击时弹出日期选择器"""
        try:
            # 弹出日期选择器让用户选择停药日期
            self._show_unified_stop_date_picker()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示停药日期选择器失败: {e}")
    
    def _show_unified_stop_date_picker(self, *args):
        """显示统一停药日期选择器"""
        try:
            # 使用DatePickerManager来显示日期选择器
            date_picker_manager = get_date_picker_manager()
            date_picker_manager.show_date_picker(
                callback_function=self._on_unified_stop_date_selected,
                default_date=datetime.now(),
                title="选择停药日期"
            )
            
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 创建停药日期选择器失败: {e}")
            # 如果日期选择器创建失败，则使用当前日期作为默认值
            current_date = datetime.now().strftime("%Y-%m-%d")
            if hasattr(self, 'unified_stop_date_button_text'):
                self.unified_stop_date_button_text.text = f"停药日期: {current_date}"
                self.unified_selected_stop_date = current_date
    
    def _on_unified_stop_date_selected(self, selected_date):
        """处理选择的停药日期"""
        try:
            date_str = selected_date.strftime("%Y-%m-%d")
            if hasattr(self, 'unified_stop_date_button_text'):
                self.unified_stop_date_button_text.text = f"停药日期: {date_str}"
                self.unified_selected_stop_date = date_str
                KivyLogger.info(f"[MedicationManagement] 统一停药日期已设置: {date_str}")
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 保存停药日期失败: {e}")

    def _show_unified_stop_reason_menu(self, *args):
        """显示统一停药原因下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            stop_reason_options = [
                "治疗完成",
                "药物不良反应",
                "疗效不佳",
                "医生建议停药",
                "患者自主停药",
                "药物过敏",
                "其他原因"
            ]

            menu_items = []
            for reason in stop_reason_options:
                menu_items.append({
                    "text": reason,
                    "on_release": lambda x=reason: self._select_unified_stop_reason(x)
                })

            # 确保菜单有正确的调用者
            if hasattr(self, 'unified_stop_reason_button'):
                self.unified_stop_reason_menu = MDDropdownMenu(
                    caller=self.unified_stop_reason_button,
                    items=menu_items,
                    width=dp(240),  # 使用width替代width_mult
                    max_height=dp(200),
                    position="bottom"
                )
                self.unified_stop_reason_menu.open()
            else:
                KivyLogger.error("[MedicationManagement] 停药原因按钮不存在")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示统一停药原因菜单失败: {e}")

    def _select_unified_stop_reason(self, reason):
        """选择统一停药原因"""
        try:
            self.unified_selected_stop_reason = reason
            if hasattr(self, 'unified_stop_reason_button_text'):
                self.unified_stop_reason_button_text.text = reason
                KivyLogger.info(f"[MedicationManagement] 停药原因按钮文本已更新: {reason}")
            if hasattr(self, 'unified_stop_reason_menu'):
                self.unified_stop_reason_menu.dismiss()
            KivyLogger.info(f"[MedicationManagement] 统一停药原因已选择: {reason}")
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 选择统一停药原因失败: {e}")

    def _show_unified_time_picker(self, *args):
        """显示统一时间选择器"""
        try:
            try:
                from utils.date_picker_utils import show_time_picker

                def on_time_selected(selected_time):
                    try:
                        if hasattr(self, 'unified_med_time_field'):
                            self.unified_med_time_field.text = selected_time
                        KivyLogger.info(f"[MedicationManagement] 统一服药时间已设置: {selected_time}")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置统一服药时间失败: {e}")

                show_time_picker(on_time_selected)

            except ImportError:
                # 备用方案：使用简单的时间选择器
                self._show_simple_time_picker(self.unified_med_time_field, "选择服药时间")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示统一时间选择器失败: {e}")

    def _show_unified_review_date_picker(self, *args):
        """显示统一复查日期选择器"""
        try:
            try:
                from utils.date_picker_utils import show_review_date_picker

                def on_date_selected(selected_date):
                    try:
                        if hasattr(self, 'unified_review_date_field'):
                            self.unified_review_date_field.text = selected_date.strftime("%Y-%m-%d")
                        KivyLogger.info(f"[MedicationManagement] 统一复查日期已设置: {selected_date}")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置统一复查日期失败: {e}")

                show_review_date_picker(on_date_selected)

            except ImportError:
                # 备用方案：使用简单的日期选择器
                self._show_simple_date_picker(self.unified_review_date_field, "选择复查日期")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示统一复查日期选择器失败: {e}")

# 注册Factory
Factory.register('MedicationManagementScreen', cls=MedicationManagementScreen)
Factory.register('MedicationCard', cls=BaseMedicationCard)
Factory.register('CurrentMedicationCard', cls=CurrentMedicationCard)
Factory.register('HistoryMedicationCard', cls=HistoryMedicationCard)
Builder.load_string(KV)
