# -*- coding: utf-8 -*-
import json
import logging
import threading
from datetime import datetime

from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.uix.scrollview import ScrollView

from kivymd.app import MD<PERSON>pp
from kivymd.uix.boxlayout import MDBox<PERSON>ayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.slider import MD<PERSON>lider

from screens.base_screen import BaseScreen
from utils.cloud_api import get_cloud_api
from utils.user_manager import get_user_manager
from kivy.factory import Factory
from theme import AppTheme, AppMetrics

# 获取日志记录器
logger = logging.getLogger(__name__)

class QuestionnaireFormScreen(BaseScreen):
    """问卷表单屏幕"""
    title = StringProperty("问卷调查")
    questions = ListProperty([])
    answers = ListProperty([])
    current_questionnaire = ObjectProperty(None)
    is_submitting = BooleanProperty(False)
    progress = NumericProperty(0)  # 完成进度，0-100

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.answers = []
        Clock.schedule_once(self.init_ui)

    def on_enter(self, *args):
        """每次进入屏幕时重新初始化UI"""
        Clock.schedule_once(self.init_ui, 0.1)

    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            logger.info("初始化问卷表单UI")
            # 获取当前问卷数据
            self.current_questionnaire = getattr(self.app, 'questionnaire_to_fill', None)
            
            if not self.current_questionnaire:
                logger.error("未找到问卷数据")
                self.show_error("未找到问卷数据")
                # 延迟执行返回操作，避免在UI初始化过程中立即跳转
                Clock.schedule_once(lambda dt: self.go_back(), 1.0)
                return
            
            # 设置标题
            questionnaire_title = self.current_questionnaire.get('title', '')
            if not questionnaire_title and 'template' in self.current_questionnaire and 'name' in self.current_questionnaire['template']:
                questionnaire_title = self.current_questionnaire['template']['name']
            
            if questionnaire_title:
                self.title = questionnaire_title
                logger.info(f"设置问卷标题: {questionnaire_title}")
            
            # 获取问题列表
            self.questions = []
            if 'template' in self.current_questionnaire and isinstance(self.current_questionnaire['template'], dict) and 'questions' in self.current_questionnaire['template']:
                self.questions = self.current_questionnaire['template']['questions']
                logger.info(f"从模板获取问题列表，共 {len(self.questions)} 个问题")
            elif 'questions' in self.current_questionnaire and self.current_questionnaire['questions']:
                self.questions = self.current_questionnaire['questions']
                logger.info(f"从问卷获取问题列表，共 {len(self.questions)} 个问题")
            
            # 如果仍然没有问题列表，强制从API获取
            if not self.questions:
                try:
                    questionnaire_id = self.current_questionnaire.get('id')
                    if questionnaire_id:
                        logger.info(f"强制从API获取问卷问题，ID: {questionnaire_id}")
                        cloud_api = get_cloud_api()
                        if cloud_api and cloud_api.is_authenticated():
                            # 直接调用get_questionnaire_questions方法
                            result = cloud_api.get_questionnaire_questions(questionnaire_id)
                            if result and 'questions' in result and result['questions']:
                                self.questions = result['questions']
                                # 更新问卷数据中的问题列表，以便后续使用
                                self.current_questionnaire['questions'] = self.questions
                                logger.info(f"从API获取问题列表成功，共 {len(self.questions)} 个问题")
                            else:
                                # 如果get_questionnaire_questions方法失败，尝试get_questionnaire_detail方法
                                result = cloud_api.get_questionnaire_detail(questionnaire_id)
                                if result and isinstance(result, dict):
                                    # 检查详情中的问题
                                    if 'questions' in result:
                                        self.questions = result['questions']
                                        self.current_questionnaire['questions'] = self.questions
                                        logger.info(f"从问卷详情获取问题，共 {len(self.questions)} 个问题")
                                    # 检查详情中模板的问题
                                    elif 'template' in result and isinstance(result['template'], dict) and 'questions' in result['template']:
                                        self.questions = result['template']['questions']
                                        self.current_questionnaire['questions'] = self.questions
                                        logger.info(f"从问卷详情模板获取问题，共 {len(self.questions)} 个问题")
                                    # 检查data字段
                                    elif 'data' in result and isinstance(result['data'], dict):
                                        if 'questions' in result['data']:
                                            self.questions = result['data']['questions']
                                            self.current_questionnaire['questions'] = self.questions
                                            logger.info(f"从问卷详情data获取问题，共 {len(self.questions)} 个问题")
                                        elif 'template' in result['data'] and isinstance(result['data']['template'], dict) and 'questions' in result['data']['template']:
                                            self.questions = result['data']['template']['questions']
                                            self.current_questionnaire['questions'] = self.questions
                                            logger.info(f"从问卷详情data模板获取问题，共 {len(self.questions)} 个问题")
                except Exception as e:
                    logger.error(f"从API获取问题列表失败: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
            
            # 如果仍然没有问题列表，创建默认问题
            if not self.questions:
                logger.warning("未找到问题列表，创建默认问题")
                # 创建默认问题，避免空白表单
                question_count = 5  # 默认创建5个问题
                if self.current_questionnaire.get('question_count', 0) > 0:
                    question_count = self.current_questionnaire.get('question_count')
                
                default_questions = []
                for i in range(question_count):
                    default_questions.append({
                        'id': i + 1,
                        'question_id': f'q_{i+1}',
                        'text': f'问题 {i + 1}',
                        'question_text': f'问题 {i + 1}',
                        'type': 'radio',
                        'question_type': 'radio',
                        'options': [
                            {'value': '1', 'text': '选项1', 'label': '选项1'},
                            {'value': '2', 'text': '选项2', 'label': '选项2'},
                            {'value': '3', 'text': '选项3', 'label': '选项3'},
                            {'value': '4', 'text': '选项4', 'label': '选项4'},
                            {'value': '5', 'text': '选项5', 'label': '选项5'}
                        ],
                        'order': i,
                        'is_required': True
                    })
                self.questions = default_questions
                # 更新问卷数据中的问题列表
                self.current_questionnaire['questions'] = self.questions
                logger.info(f"创建了 {len(default_questions)} 个默认问题")
            
            logger.info(f"最终获得的问题数量: {len(self.questions)}")
            for i, q in enumerate(self.questions[:3]):  # 只显示前3个问题的详情
                logger.info(f"问题 {i+1}: {q}")
            
            # 初始化答案列表
            self.answers = [None] * len(self.questions)
            
            # 创建问题表单
            logger.info("开始创建问题表单")
            self.create_question_form()
            logger.info("问题表单创建完成")
        except Exception as e:
            logger.error(f"初始化问卷表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"初始化表单时出错: {str(e)}")

    def create_question_form(self):
        """创建问题表单"""
        try:
            logger.info(f"开始创建问题表单，问题数量: {len(self.questions)}")
            
            # 检查form_container是否存在
            if not hasattr(self, 'ids') or 'form_container' not in self.ids:
                logger.error("form_container不存在，UI可能未正确初始化")
                logger.info(f"当前ids: {list(self.ids.keys()) if hasattr(self, 'ids') else 'None'}")
                # 延迟重试
                Clock.schedule_once(lambda dt: self.create_question_form(), 0.5)
                return
            
            # 清空现有内容
            try:
                self.ids.form_container.clear_widgets()
                logger.info("已清空现有表单内容")
            except Exception as clear_error:
                logger.error(f"清空表单内容时出错: {clear_error}")
                # 如果清空失败，尝试重新获取form_container
                Clock.schedule_once(lambda dt: self.create_question_form(), 0.5)
                return
            
            if not self.questions:
                logger.warning("问题列表为空，显示空状态")
                # 显示空状态
                empty_label = MDLabel(
                    text="暂无问题数据",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                self.ids.form_container.add_widget(empty_label)
                return
            
            # 直接使用KV文件中定义的form_container
            form_container = self.ids.form_container
            logger.info("使用KV文件中定义的表单容器")
            
            # 添加问卷描述
            self.add_questionnaire_description(form_container)
            
            # 进度指示器已在KV文件中定义，无需重复添加
            
            # 添加问题卡片
            for i, question in enumerate(self.questions):
                question_card = self.create_question_card(i, question)
                form_container.add_widget(question_card)
                
                # 在问题之间添加间距（除了最后一个问题）
                if i < len(self.questions) - 1:
                    spacer = MDBoxLayout(
                        size_hint_y=None,
                        height=dp(16)  # 增加间距使布局更舒适
                    )
                    form_container.add_widget(spacer)
            
            # 添加提交按钮
            self.add_submit_button(form_container)
            
            logger.info("问题表单创建完成")
            
        except Exception as e:
            logger.error(f"创建问题表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"创建表单时出错: {str(e)}")

    def add_progress_indicator(self, container):
        """添加进度指示器"""
        progress_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            height=dp(80),
            padding=[dp(20), dp(16)],
            radius=[dp(12)],
            elevation=1,
            md_bg_color=self.app.theme.PRIMARY_LIGHT,
            shadow_softness=4
        )
        
        progress_label = MDLabel(
            text=f"完成进度: {self.progress:.0f}%",
            theme_text_color="Custom",
            text_color=self.app.theme.PRIMARY_COLOR,
            font_size=AppMetrics.FONT_SIZE_SMALL,
            halign="center"
        )
        
        progress_card.add_widget(progress_label)
        container.add_widget(progress_card)
        
        # 添加间距
        spacer = MDBoxLayout(
            size_hint_y=None,
            height=dp(16)
        )
        container.add_widget(spacer)

    def create_question_card(self, index, question):
        """创建问题卡片"""
        try:
            # 获取问题数据（兼容多种字段名）
            question_text = question.get('text') or question.get('question_text', f'问题 {index + 1}')
            question_type = question.get('type') or question.get('question_type', 'text')
            options = question.get('options', [])
            required = question.get('required') or question.get('is_required', True)
            
            # 创建卡片 - 使用自适应高度
            # 在KivyMD 2.0.1中不允许height=None，使用最小高度并在添加内容后更新
            card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(100),  # 设置初始最小高度，后续会根据内容调整
                padding=[dp(16), dp(12)],  # 增加内边距避免紧凑
                radius=[dp(6)],  # 更小的圆角
                elevation=0.5,  # 更轻的阴影
                md_bg_color=self.app.theme.CARD_BACKGROUND,
                shadow_softness=2,
                line_color=self.app.theme.BORDER_COLOR,
                line_width=0.5
            )
            
            # 问题标题 - 采用更紧凑的布局
            title_container = MDBoxLayout(
                orientation="horizontal",
                size_hint_y=None,
                height=dp(36),  # 增加高度避免重叠
                spacing=dp(8)  # 增加间距
            )
            
            # 问题序号 - 更突出的样式
            number_label = MDLabel(
                text=f"{index + 1}.",
                theme_text_color="Custom",
                text_color=self.app.theme.PRIMARY_COLOR,
                size_hint_x=None,
                width=dp(28),  # 减小宽度
                font_size=AppMetrics.FONT_SIZE_MEDIUM,  # 稍大的字体
                bold=True,
                halign="left"
            )
            title_container.add_widget(number_label)
            
            # 问题文本 - 简化布局
            question_label = MDLabel(
                text=question_text + (" *" if required else ""),
                theme_text_color="Custom",
                text_color=[0.13, 0.13, 0.13, 1],  # 深灰色文字，确保在白色背景上清晰可见
                font_size=dp(12),  # 减小字体大小优化排版
                text_size=(None, None),
                markup=True,
                valign="center"
            )
            title_container.add_widget(question_label)
            
            card.add_widget(title_container)
            
            # 添加分隔线
            separator = MDBoxLayout(
                size_hint_y=None,
                height=dp(1),
                md_bg_color=self.app.theme.BORDER_COLOR
            )
            card.add_widget(separator)
            
            # 添加间距
            spacer = MDBoxLayout(
                size_hint_y=None,
                height=dp(12)  # 增加间距避免紧凑
            )
            card.add_widget(spacer)
            
            # 根据问题类型添加不同的输入控件
            input_container = self.create_input_widget(index, question_type, options, question)
            card.add_widget(input_container)
            
            # 添加完所有内容后，计算并更新卡片高度
            def update_card_height(dt):
                # 首先更新输入容器的高度
                input_height = 0
                for child in input_container.children:
                    if hasattr(child, 'height'):
                        input_height += child.height
                    if hasattr(child, 'padding'):
                        if isinstance(child.padding, list) and len(child.padding) >= 2:
                            input_height += child.padding[1] * 2
                
                # 添加间距
                input_height += input_container.spacing * (len(input_container.children) - 1 if len(input_container.children) > 0 else 0)
                
                # 设置输入容器的高度
                input_container.height = max(dp(50), input_height + dp(10))
                
                # 然后计算卡片的总高度
                total_height = 0
                for child in card.children:
                    if hasattr(child, 'height'):
                        total_height += child.height
                    if hasattr(child, 'padding'):
                        if isinstance(child.padding, list) and len(child.padding) >= 2:
                            total_height += child.padding[1] * 2
                
                # 添加卡片间距
                total_height += card.padding[1] * 2 if isinstance(card.padding, list) and len(card.padding) >= 2 else 0
                
                # 设置最小高度
                card.height = max(dp(100), total_height + dp(20))
            
            # 使用Clock延迟调用，确保子组件已完全加载
            from kivy.clock import Clock
            Clock.schedule_once(update_card_height, 0.1)
            
            return card
            
        except Exception as e:
            logger.error(f"创建问题卡片时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 返回一个错误卡片
            error_card = MDCard(
                orientation="vertical",
                size_hint_y=None,
                height=dp(80),
                padding=[dp(16), dp(12)],
                radius=[dp(8)],
                elevation=1,
                md_bg_color=self.app.theme.ERROR_COLOR
            )
            
            error_label = MDLabel(
                text=f"问题 {index + 1} 加载失败",
                theme_text_color="Custom",
                text_color=self.app.theme.TEXT_LIGHT,
                halign="center",
                font_size=AppMetrics.FONT_SIZE_SMALL
            )
            error_card.add_widget(error_label)
            return error_card

    def create_input_widget(self, index, question_type, options, question):
        container = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(50)
        )
        is_active = question.get('active', True)
        container.disabled = not is_active
        container.opacity = 1.0 if is_active else 0.3
        
        # 兼容多种问题类型名称
        if question_type == 'single_choice':
            question_type = 'radio'
        elif question_type == 'multiple_choice':
            question_type = 'checkbox'
        
        # 兼容多种选项格式
        formatted_options = []
        if options:
            if isinstance(options, list):
                if options and isinstance(options[0], dict):
                    # 已经是字典格式
                    formatted_options = options
                else:
                    # 字符串列表，转换为字典
                    formatted_options = [{'value': str(i), 'text': opt, 'label': opt} for i, opt in enumerate(options)]
            elif isinstance(options, dict):
                # 如果是字典，尝试提取选项
                if 'choices' in options:
                    formatted_options = options['choices']
                    if isinstance(formatted_options[0], str):
                        formatted_options = [{'value': str(i), 'text': opt, 'label': opt} for i, opt in enumerate(formatted_options)]
        
        # 如果仍然没有选项，创建默认选项
        if not formatted_options and (question_type in ['radio', 'checkbox', 'single_choice', 'multiple_choice']):
            formatted_options = [
                {'value': '1', 'text': '选项1', 'label': '选项1'},
                {'value': '2', 'text': '选项2', 'label': '选项2'},
                {'value': '3', 'text': '选项3', 'label': '选项3'},
                {'value': '4', 'text': '选项4', 'label': '选项4'},
                {'value': '5', 'text': '选项5', 'label': '选项5'}
            ]
            
        # 更新选项
        options = formatted_options
        if question_type in ['likert']:
            from kivymd.uix.button import MDButton
            likert_row = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48))
            for i, option in enumerate(options):
                btn = MDButton(text=option.get('label', str(option)), md_bg_color="#0D47A1", theme_text_color="Custom", text_color="#fff", size_hint=(None, None), width=dp(60), height=dp(36), style="elevated")
                btn.question_index = index
                btn.option_index = i
                btn.option_value = option.get('value', i)
                btn.bind(on_release=lambda x: self.on_radio_answer(x, True))
                likert_row.add_widget(btn)
            container.add_widget(likert_row)
        elif question_type in ['semantic_differential']:
            from kivymd.uix.slider import MDSlider
            sd_row = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48))
            left = options[0].get('label', '极低') if options else '极低'
            right = options[-1].get('label', '极高') if options else '极高'
            sd_row.add_widget(MDLabel(text=left, size_hint_x=None, width=dp(40)))
            slider = MDSlider(min=0, max=len(options)-1, value=0, step=1, size_hint_x=1)
            slider.question_index = index
            slider.bind(value=lambda inst, val: self.on_slider_answer(inst, int(val)))
            sd_row.add_widget(slider)
            sd_row.add_widget(MDLabel(text=right, size_hint_x=None, width=dp(40)))
            container.add_widget(sd_row)
        elif question_type in ['ranking']:
            for i, option in enumerate(options):
                row = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(36))
                row.add_widget(MDLabel(text=option.get('label', str(option)), size_hint_x=0.7))
                tf = MDTextField(hint_text="排序", input_filter="int", size_hint_x=0.3)
                tf.question_index = index
                tf.option_index = i
                tf.bind(text=lambda x, t, idx=index, opt=i: self.on_ranking_answer(idx, opt, t))
                row.add_widget(tf)
                container.add_widget(row)
        elif question_type in ['time']:
            # 兼容KivyMD 2.0.1，无MDTimePicker，改为文本输入
            tf = MDTextField(hint_text="请输入时间（如 12:30）", size_hint_y=None, height=dp(48))
            tf.question_index = index
            tf.bind(text=lambda x, t: self.on_text_answer(x, t))
            container.add_widget(tf)
        elif question_type in ['short_answer', 'long_answer']:
            tf = MDTextField(hint_text="请输入答案", multiline=(question_type=='long_answer'), size_hint_y=None, height=dp(80) if question_type=='long_answer' else dp(48))
            tf.question_index = index
            tf.bind(text=lambda x, t: self.on_text_answer(x, t))
            container.add_widget(tf)
        elif question_type in ['matrix']:
            from kivymd.uix.datatables import MDDataTable
            cols = [(col.get('label', str(col)), dp(60)) for col in question.get('columns', [])]
            rows = [[row.get('label', str(row))] + ['' for _ in cols] for row in question.get('rows', [])]
            table = MDDataTable(column_data=cols, row_data=rows, size_hint_y=None, height=dp(120))
            container.add_widget(table)
        elif question_type in ['branching']:
            radio_container = MDBoxLayout(orientation="vertical", spacing=dp(2), size_hint_y=None)
            for i, option in enumerate(options):
                row = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(36), spacing=dp(12))
                label = MDLabel(text=option.get('label', str(option)), size_hint_x=0.7)
                radio_btn = MDCheckbox(group=f"question_{index}", size_hint=(None, None), size=(dp(24), dp(24)), pos_hint={'center_y': 0.5}, active=False)
                radio_btn.question_index = index
                radio_btn.option_index = i
                radio_btn.option_value = option.get('value', i)
                radio_btn.branch_targets = option.get('branch_targets', None)
                radio_btn.bind(active=self.on_branching_answer)
                row.add_widget(label)
                row.add_widget(radio_btn)
                radio_container.add_widget(row)
            container.add_widget(radio_container)
        elif question_type in ['numeric_rating']:
            from kivymd.uix.slider import MDSlider
            slider = MDSlider(min=0, max=10, value=5, step=1, color="#0D47A1", size_hint_y=None, height=dp(32))
            slider.question_index = index
            slider.bind(value=lambda inst, val: self.on_slider_answer(inst, int(val)))
            container.add_widget(slider)
        elif question_type in ['fill_blank']:
            tf = MDTextField(hint_text="请填写", size_hint_y=None, height=dp(48))
            tf.question_index = index
            tf.bind(text=lambda x, t: self.on_text_answer(x, t))
            container.add_widget(tf)
        elif question_type in ['text', 'textarea']:
            text_field = MDTextField(
                hint_text="请输入答案",
                mode="outlined",
                size_hint_y=None,
                height=dp(80) if question_type == 'textarea' else dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self.app.theme.BORDER_COLOR,
                line_color_focus=self.app.theme.PRIMARY_COLOR,
                multiline=(question_type == 'textarea')
            )
            text_field.question_index = index
            text_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = text_field.height
            container.add_widget(text_field)
        # 数字输入
        elif question_type == 'number':
            number_field = MDTextField(
                hint_text="请输入数字",
                mode="outlined",
                input_filter="int",
                size_hint_y=None,
                height=dp(48),
                font_size=AppMetrics.FONT_SIZE_SMALL,
                line_color_normal=self.app.theme.BORDER_COLOR,
                line_color_focus=self.app.theme.PRIMARY_COLOR,
                multiline=False
            )
            number_field.question_index = index
            number_field.bind(text=lambda x, text: self.on_text_answer(x, text))
            container.height = number_field.height
            container.add_widget(number_field)
        elif question_type in ['radio', 'single_choice']:
            radio_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(2),
                size_hint_y=None
            )
            if options:
                table_container = MDBoxLayout(
                    orientation="vertical",
                    spacing=dp(1),
                    size_hint_y=None,
                    padding=[dp(8), dp(4)]
                )
                for i, option in enumerate(options):
                    option_row = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_y=None,
                        height=dp(36),
                        spacing=dp(12),
                        padding=[dp(4), dp(2)]
                    )
                    option_value = option.get('value', str(i)) if isinstance(option, dict) else str(i)
                    option_id = MDLabel(
                        text=f"({option_value})",
                        theme_text_color="Custom",
                        text_color=self.app.theme.PRIMARY_COLOR,
                        font_size=dp(12),
                        size_hint_x=None,
                        width=dp(40),
                        halign="center",
                        valign="center",
                        bold=True
                    )
                    option_row.add_widget(option_id)
                    if isinstance(option, dict):
                        option_text = option.get('label', option.get('text', option.get('name', f'选项 {i+1}')))
                    elif isinstance(option, str):
                        option_text = option
                    else:
                        option_text = str(option)
                    if not option_text or option_text.strip() == '':
                        option_text = f'选项 {i+1}'
                    option_label = MDLabel(
                        text=option_text,
                        theme_text_color="Custom",
                        text_color=[0.13, 0.13, 0.13, 1],
                        font_size=dp(12),
                        text_size=(None, None),
                        valign="center"
                    )
                    option_label.bind(width=lambda instance, width: setattr(instance, 'text_size', (width - dp(16), None)))
                    option_row.add_widget(option_label)
                    radio_btn = MDCheckbox(
                        group=f"question_{index}",
                        size_hint=(None, None),
                        size=(dp(24), dp(24)),
                        pos_hint={'center_y': 0.5},
                        active=False,
                        color_inactive=self.app.theme.BORDER_COLOR,
                        color_active=self.app.theme.PRIMARY_COLOR,
                        disabled_color=self.app.theme.BORDER_COLOR
                    )
                    radio_btn.question_index = index
                    radio_btn.option_index = i
                    radio_btn.option_value = option.get('value', i)
                    radio_btn.bind(active=self.on_radio_answer)
                    radio_container_widget = MDBoxLayout(
                        size_hint_x=None,
                        width=dp(40),
                        orientation="horizontal"
                    )
                    radio_container_widget.add_widget(radio_btn)
                    option_row.add_widget(radio_container_widget)
                    if i < len(options) - 1:
                        option_row.md_bg_color = self.app.theme.SURFACE_COLOR if i % 2 == 0 else self.app.theme.CARD_BACKGROUND
                    table_container.add_widget(option_row)
                border_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=len(options) * dp(36) + dp(16),
                    padding=[dp(4), dp(4)],
                    radius=[dp(6)],
                    elevation=0,
                    md_bg_color=self.app.theme.SURFACE_COLOR,
                    line_color=self.app.theme.BORDER_COLOR,
                    line_width=1
                )
                table_container.height = len(options) * dp(36)
                border_card.add_widget(table_container)
                radio_container.add_widget(border_card)
                container_height = len(options) * dp(36) + dp(24)
            else:
                no_options_label = MDLabel(
                    text="暂无选项",
                    theme_text_color="Secondary",
                    font_size=AppMetrics.FONT_SIZE_SMALL,
                    halign="center",
                    size_hint_y=None,
                    height=dp(32)
                )
                radio_container.add_widget(no_options_label)
                container_height = dp(32)
            radio_container.height = container_height
            container.height = container_height
            container.add_widget(radio_container)
        elif question_type in ['checkbox', 'multiple_choice']:
            checkbox_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(6),
                size_hint_y=None
            )
            if options:
                for i, option in enumerate(options):
                    option_container = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_y=None,
                        height=dp(36),
                        spacing=dp(8)
                    )
                    checkbox = MDCheckbox(
                        size_hint=(None, None),
                        size=(dp(20), dp(20)),
                        pos_hint={'center_y': 0.5},
                        active=False,
                        color_inactive=self.app.theme.BORDER_COLOR,
                        color_active=self.app.theme.PRIMARY_COLOR,
                        disabled_color=self.app.theme.BORDER_COLOR
                    )
                    checkbox.question_index = index
                    checkbox.option_index = i
                    checkbox.option_value = option.get('value', i) if isinstance(option, dict) else i
                    checkbox.bind(active=lambda x, active, opt_idx=i: self.on_checkbox_answer(x, active))
                    option_container.add_widget(checkbox)
                    option_text = option.get('label', option.get('text', f'选项 {i+1}')) if isinstance(option, dict) else str(option)
                    option_label = MDLabel(
                        text=option_text,
                        theme_text_color="Custom",
                        text_color=[0.13, 0.13, 0.13, 1],
                        font_size=dp(12),
                        size_hint_y=None,
                        height=dp(36),
                        text_size=(None, None),
                        valign="center"
                    )
                    option_container.add_widget(option_label)
                    checkbox_container.add_widget(option_container)
                container_height = len(options) * dp(36) + (len(options) - 1) * dp(6)
            else:
                no_options_label = MDLabel(
                    text="暂无选项",
                    theme_text_color="Secondary",
                    font_size=AppMetrics.FONT_SIZE_SMALL,
                    halign="center",
                    size_hint_y=None,
                    height=dp(32)
                )
                checkbox_container.add_widget(no_options_label)
                container_height = dp(32)
            checkbox_container.height = container_height
            container.height = container_height
            container.add_widget(checkbox_container)
        elif question_type == 'rating':
            from kivymd.uix.slider import MDSlider
            rating_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(4),
                size_hint_y=None,
                height=dp(60)
            )
            label = MDLabel(
                text="请选择评分",
                theme_text_color="Secondary",
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="left"
            )
            rating_slider = MDSlider(
                min=1,
                max=5,
                value=1,
                step=1,
                size_hint_y=None,
                height=dp(32)
            )
            rating_slider.question_index = index
            def on_rating_value(instance, value):
                self.on_slider_answer(instance, int(value))
            rating_slider.bind(value=on_rating_value)
            rating_container.add_widget(label)
            rating_container.add_widget(rating_slider)
            container.height = dp(60)
            container.add_widget(rating_container)
        else:
            unknown_label = MDLabel(
                text=f"不支持的问题类型: {question_type}",
                theme_text_color="Error",
                font_size=AppMetrics.FONT_SIZE_SMALL,
                halign="center",
                size_hint_y=None,
                height=dp(32)
            )
            container.height = dp(32)
            container.add_widget(unknown_label)
        return container

    def add_questionnaire_description(self, container):
        """添加问卷描述和填写说明（instructions）"""
        try:
            # 获取描述和填写说明，优先级：current_questionnaire > template
            description = None
            instructions = None
            
            if self.current_questionnaire:
                description = self.current_questionnaire.get('description')
                instructions = self.current_questionnaire.get('instructions')
                template = self.current_questionnaire.get('template')
                if not description and template:
                    description = template.get('description')
                if not instructions and template:
                    instructions = template.get('instructions')
            elif hasattr(self, 'template') and self.template:
                description = self.template.get('description')
                instructions = self.template.get('instructions')
            
            # 如果都没有描述，降级为模板名称
            if not description:
                if hasattr(self, 'template') and self.template:
                    description = self.template.get('name')
                elif self.current_questionnaire and self.current_questionnaire.get('template'):
                    description = self.current_questionnaire.get('template', {}).get('name', '')
                else:
                    description = ""
            
            logger.debug(f"问卷描述: {description}")
            logger.debug(f"填写说明: {instructions}")
            
            # 渲染描述
            if description:
                desc_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=dp(60),  # 初始高度
                    padding=[dp(12), dp(8)],
                    radius=[dp(6)],
                    elevation=0.5,
                    md_bg_color=[0.96, 0.96, 1, 1],  # 浅蓝色背景
                    shadow_softness=2
                )
                desc_label = MDLabel(
                    text=description,
                    theme_text_color="Primary",
                    font_style="Body1",
                    text_size=(None, None),
                    halign="left",
                    valign="top",
                    markup=True,
                    size_hint_y=None
                )
                def update_desc_size(instance, value):
                    instance.text_size = (instance.width - dp(20), None)
                    instance.texture_update()
                    if instance.texture_size[1] > 0:
                        instance.height = instance.texture_size[1] + dp(8)
                        desc_card.height = max(dp(60), instance.height + dp(20))
                desc_label.bind(width=update_desc_size)
                desc_label.bind(texture_size=lambda instance, size: setattr(instance, 'height', size[1] + dp(8)))
                desc_card.add_widget(desc_label)
                container.add_widget(desc_card)
                spacer = MDBoxLayout(size_hint_y=None, height=dp(10))
                container.add_widget(spacer)
            
            # 渲染填写说明
            if instructions:
                instr_card = MDCard(
                    orientation="vertical",
                    size_hint_y=None,
                    height=dp(50),  # 初始高度
                    padding=[dp(12), dp(6)],
                    radius=[dp(6)],
                    elevation=0.3,
                    md_bg_color=[0.98, 0.98, 1, 1],  # 更浅的背景
                    shadow_softness=1
                )
                instr_label = MDLabel(
                    text="填写说明：" + instructions,
                    theme_text_color="Secondary",
                    font_style="Caption",
                    text_size=(None, None),
                    halign="left",
                    valign="top",
                    markup=True,
                    size_hint_y=None
                )
                def update_instr_size(instance, value):
                    instance.text_size = (instance.width - dp(20), None)
                    instance.texture_update()
                    if instance.texture_size[1] > 0:
                        instance.height = instance.texture_size[1] + dp(6)
                        instr_card.height = max(dp(50), instance.height + dp(16))
                instr_label.bind(width=update_instr_size)
                instr_label.bind(texture_size=lambda instance, size: setattr(instance, 'height', size[1] + dp(6)))
                instr_card.add_widget(instr_label)
                container.add_widget(instr_card)
                spacer = MDBoxLayout(size_hint_y=None, height=dp(8))
                container.add_widget(spacer)
                
        except Exception as e:
            logger.error(f"添加问卷描述和填写说明时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def add_submit_button(self, container):
        try:
            spacer = MDBoxLayout(size_hint_y=None, height=dp(24))
            container.add_widget(spacer)
            button_container = MDBoxLayout(
                orientation="horizontal",
                size_hint_y=None,
                height=dp(56),
                padding=[dp(24), 0],
                spacing=dp(16)
            )
            submit_button = MDButton(
                MDButtonText(
                    text="提交问卷",
                    font_size=dp(14),
                    theme_text_color="Custom",
                    text_color="#FFFFFF"
                ),
                style="elevated",
                size_hint=(1, 1),
                md_bg_color="#0D47A1",
                elevation=3,
                on_release=self.on_submit
            )
            button_container.add_widget(submit_button)
            container.add_widget(button_container)
            bottom_spacer = MDBoxLayout(size_hint_y=None, height=dp(32))
            container.add_widget(bottom_spacer)
        except Exception as e:
            logger.error(f"添加提交按钮时出错: {e}")

    def on_text_answer(self, instance, text):
        """处理文本答案"""
        try:
            question_index = getattr(instance, 'question_index', None)
            if question_index is not None and 0 <= question_index < len(self.answers):
                self.answers[question_index] = text
                self.update_progress()
                logger.debug(f"更新问题 {question_index + 1} 的文本答案: {text}")
        except Exception as e:
            logger.error(f"处理文本答案时出错: {e}")

    def on_radio_answer(self, instance, value):
        """处理单选答案"""
        try:
            if value:  # 只处理选中状态
                question_index = getattr(instance, 'question_index', None)
                option_index = getattr(instance, 'option_index', None)
                option_value = getattr(instance, 'option_value', option_index)
                
                if question_index is not None and 0 <= question_index < len(self.answers):
                    self.answers[question_index] = option_value
                    self.update_progress()
                    logger.debug(f"更新问题 {question_index + 1} 的单选答案: {option_value}")
        except Exception as e:
            logger.error(f"处理单选答案时出错: {e}")

    def on_checkbox_answer(self, instance, value):
        """处理多选答案"""
        try:
            question_index = getattr(instance, 'question_index', None)
            option_index = getattr(instance, 'option_index', None)
            option_value = getattr(instance, 'option_value', option_index)
            
            if question_index is not None and 0 <= question_index < len(self.answers):
                # 初始化多选答案为列表
                if not isinstance(self.answers[question_index], list):
                    self.answers[question_index] = []
                
                if value:  # 选中
                    if option_value not in self.answers[question_index]:
                        self.answers[question_index].append(option_value)
                else:  # 取消选中
                    if option_value in self.answers[question_index]:
                        self.answers[question_index].remove(option_value)
                
                self.update_progress()
                logger.debug(f"更新问题 {question_index + 1} 的多选答案: {self.answers[question_index]}")
        except Exception as e:
            logger.error(f"处理多选答案时出错: {e}")

    def on_slider_answer(self, instance, value):
        """处理滑块答案"""
        try:
            question_index = getattr(instance, 'question_index', None)
            if question_index is not None and 0 <= question_index < len(self.answers):
                self.answers[question_index] = int(value)
                self.update_progress()
                logger.debug(f"更新问题 {question_index + 1} 的滑块答案: {int(value)}")
        except Exception as e:
            logger.error(f"处理滑块答案时出错: {e}")

    def on_branching_answer(self, instance, value):
        if value:
            question_index = getattr(instance, 'question_index', None)
            option_index = getattr(instance, 'option_index', None)
            branch_targets = getattr(instance, 'branch_targets', None)
            self.answers[question_index] = {'option_index': option_index, 'type': 'branching'}
            if branch_targets:
                for idx, active in branch_targets.items():
                    if 0 <= idx < len(self.questions):
                        self.questions[idx]['active'] = active
            self.create_question_form()
            self.update_progress()

    def on_ranking_answer(self, question_index, option_index, value):
        if not isinstance(self.answers[question_index], dict):
            self.answers[question_index] = {}
        self.answers[question_index][option_index] = value
        self.update_progress()

    def is_answer_valid(self, answer):
        """检查答案是否有效"""
        if answer is None:
            return False
        
        if isinstance(answer, str):
            return answer.strip() != ""
        
        if isinstance(answer, dict):
            if answer.get('type') == 'checkbox' or answer.get('type') == 'multiple_choice':
                return bool(answer.get('selected_options'))
            else:
                return True
        
        if isinstance(answer, list):
            return bool(answer)
        
        return True

    def update_progress(self):
        try:
            if not self.questions:
                self.progress = 0
                return
            answered_count = 0
            active_count = 0
            for i, question in enumerate(self.questions):
                if not question.get('active', True):
                    continue
                active_count += 1
                answer = self.answers[i] if i < len(self.answers) else None
                if self.is_answer_valid(answer):
                    answered_count += 1
            self.progress = (answered_count / active_count) * 100 if active_count else 0
            try:
                form_container = self.ids.form_container
                if form_container.children:
                    for child in form_container.children:
                        if hasattr(child, 'children'):
                            for card_child in child.children:
                                if hasattr(card_child, 'text') and "完成进度" in card_child.text:
                                    card_child.text = f"完成进度: {self.progress:.1f}%"
                                    break
            except Exception as e:
                logger.debug(f"更新进度显示时出错: {e}")
            logger.debug(f"进度更新: {answered_count}/{len(self.questions)} = {self.progress:.1f}%")
        except Exception as e:
            logger.error(f"更新进度时出错: {e}")

    def on_submit(self, instance=None):
        try:
            if self.is_submitting:
                logger.warning("问卷正在提交中，请勿重复提交")
                return
            logger.info("开始提交问卷")
            missing_required = []
            for i, question in enumerate(self.questions):
                if question.get('required', True) and question.get('active', True):
                    answer = self.answers[i] if i < len(self.answers) else None
                    if not self.is_answer_valid(answer):
                        missing_required.append(i + 1)
            if missing_required:
                missing_text = "、".join(map(str, missing_required))
                self.show_error(f"请完成必填问题: {missing_text}")
                return
            self.is_submitting = True
            submission_data = self.prepare_submission_data()
            threading.Thread(
                target=self.submit_questionnaire_thread,
                args=(submission_data,),
                daemon=True
            ).start()
        except Exception as e:
            logger.error(f"提交问卷时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"提交失败: {str(e)}")
            self.is_submitting = False

    def submit_questionnaire_thread(self, submission_data):
        try:
            logger.info("开始后台提交问卷")
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                Clock.schedule_once(lambda dt: self.show_error("用户未登录，请先登录"))
                return
            result = cloud_api.submit_mobile_questionnaire(
                questionnaire_id=submission_data.get('questionnaire_id'),
                answers=submission_data.get('answers'),
                custom_id=submission_data.get('custom_id'),
                template_id=submission_data.get('template_id')
            )
            if result and result.get('status') == 'success':
                logger.info("问卷提交成功")
                Clock.schedule_once(lambda dt: self.on_submit_success(result))
            else:
                error_msg = result.get('message', '提交失败') if result else '提交失败'
                logger.error(f"问卷提交失败: {error_msg}")
                Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_msg}"))
        except Exception as ex:
            logger.error(f"后台提交问卷时出错: {ex}")
            import traceback
            logger.error(traceback.format_exc())
            error_message = str(ex)
            Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_message}"))
        finally:
            Clock.schedule_once(lambda dt: setattr(self, 'is_submitting', False))

    def on_submit_success(self, result):
        try:
            self.is_submitting = False
            self.show_success("问卷提交成功！")
            app = MDApp.get_running_app()
            if hasattr(app, 'root') and hasattr(app.root, 'get_screen'):
                try:
                    survey_screen = app.root.get_screen('survey_screen')
                    if survey_screen:
                        survey_screen.load_response_history()
                        survey_screen.load_questionnaires()
                except Exception as e:
                    logger.error(f"刷新SurveyScreen历史记录和问卷列表失败: {e}")
            Clock.schedule_once(lambda dt: self.go_back(), 1.5)
        except Exception as e:
            logger.error(f"处理提交成功时出错: {e}")

    def show_confirm_dialog(self):
        """显示确认对话框"""
        from kivymd.uix.dialog import MDDialog
        
        self.dialog = MDDialog(
            title="确认提交",
            text="您确定要提交这份问卷吗？提交后将无法修改。",
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="提交"),
                    style="text",
                    theme_text_color="Custom",
                    text_color=self.app.theme.PRIMARY_COLOR,
                    on_release=self.submit_form
                )
            ]
        )
        self.dialog.open()

    def submit_form(self, *args):
        """提交表单到服务器"""
        try:
            # 关闭对话框
            if hasattr(self, 'dialog') and self.dialog:
                self.dialog.dismiss()
            
            # 显示加载对话框
            self.show_loading_dialog("正在提交...")
            
            # 设置提交状态
            self.is_submitting = True
            
            # 在后台线程中提交
            threading.Thread(target=self._submit_form_thread).start()
        except Exception as ex:
            logger.error(f"提交表单时出错: {ex}")
            import traceback
            logger.error(traceback.format_exc())
            error_message = str(ex)
            self.show_error(f"提交表单时出错: {error_message}")
            self.is_submitting = False

    def _submit_form_thread(self):
        """后台线程提交表单"""
        try:
            # 获取云API实例
            cloud_api = get_cloud_api()
            
            # 获取用户ID
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            
            if not custom_id:
                logger.warning("未获取到有效的custom_id，无法提交表单")
                Clock.schedule_once(lambda dt: self.show_error("未获取到用户ID，无法提交表单"))
                Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
                self.is_submitting = False
                return
            
            # 准备提交数据
            submission_data = self.prepare_submission_data()
            
            # 提交数据
            result = cloud_api.submit_mobile_questionnaire(submission_data['questionnaire_id'], submission_data['answers'], custom_id, submission_data['template_id'])
            
            # 处理结果
            if result and (result.get('success') or result.get('status') == 'success'):
                logger.info("问卷提交成功")
                Clock.schedule_once(lambda dt: self.show_success("提交成功"))
                Clock.schedule_once(lambda dt: self.go_back(), 1)
            else:
                error_msg = "提交失败，请稍后重试"
                if isinstance(result, dict) and result.get('message'):
                    error_msg = result.get('message')
                logger.error(f"问卷提交失败: {error_msg}")
                Clock.schedule_once(lambda dt: self.show_error(f"提交失败: {error_msg}"))
        except Exception as ex:
            logger.error(f"提交表单线程出错: {ex}")
            import traceback
            logger.error(traceback.format_exc())
            error_message = str(ex)
            Clock.schedule_once(lambda dt: self.show_error(f"提交表单时出错: {error_message}"))
        finally:
            # 关闭加载对话框
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog())
            self.is_submitting = False

    def show_loading_dialog(self, message="加载中..."):
        """显示加载对话框"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.dismiss()
            
        from kivymd.uix.dialog import MDDialog
        from kivy.uix.progressbar import ProgressBar
        
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(10),
            padding=dp(20),
            size_hint_y=None,
            height=dp(100)
        )
        
        content.add_widget(MDLabel(
            text=message,
            halign='center'
        ))
        
        progress = ProgressBar(
            value=50
        )
        content.add_widget(progress)
        
        from kivymd.uix.dialog import MDDialogHeadlineText
        self.loading_dialog = MDDialog(
            MDDialogHeadlineText(
                text="请稍候",
                halign="center",
            ),
            content=content,
            size_hint=(.8, None),
            height=dp(200),
        )
        self.loading_dialog.open()
    
    def dismiss_loading_dialog(self):
        """关闭加载对话框"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.dismiss()
            self.loading_dialog = None

    def show_error(self, message):
        """显示错误信息"""
        logger.error(message)
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            # 确保message不为空
            if not message:
                message = "发生未知错误"
            
            # 确保app和theme存在
            error_color = "#F44336"  # 默认红色
            if hasattr(self, 'app') and self.app and hasattr(self.app, 'theme'):
                if hasattr(self.app.theme, 'ERROR_COLOR'):
                    error_color = self.app.theme.ERROR_COLOR
            
            MDSnackbar(
                MDSnackbarText(
                    text=str(message),
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=error_color,
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")
            # 备用错误显示方法
            try:
                from kivymd.toast import toast
                toast(str(message))
            except:
                print(f"ERROR: {message}")

    def show_success(self, message):
        """显示成功信息"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme.SUCCESS_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示成功信息失败: {e}")

    def show_info(self, message):
        """显示信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2,
                md_bg_color=self.app.theme.INFO_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示信息提示失败: {e}")

    def go_back(self, *args):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'survey_screen'

    def prepare_submission_data(self):
        try:
            user_manager = get_user_manager()
            user_info = user_manager.get_user_info()
            submission_data = {
                'questionnaire_id': self.current_questionnaire.get('id'),
                'template_id': self.current_questionnaire.get('template', {}).get('id'),
                'user_id': user_info.get('id') if user_info else None,
                'submitted_at': datetime.now().isoformat(),
                'answers': []
            }
            logger.info(f"准备提交数据，问卷ID: {submission_data['questionnaire_id']}, 模板ID: {submission_data['template_id']}")
            for i, answer in enumerate(self.answers):
                if i < len(self.questions):
                    question = self.questions[i]
                    question_id = question.get('question_id') or question.get('id')
                    if not question_id:
                        logger.warning(f"问题 {i+1} 缺少question_id，将跳过此问题")
                        continue
                    # 后端要求question_id为字符串
                    question_id = str(question_id)
                    answer_data = {'question_id': question_id}
                    # 只允许answer字段
                    if answer is not None:
                        if isinstance(answer, str):
                            answer_data['answer'] = answer
                        elif isinstance(answer, dict):
                            if answer.get('type') == 'radio':
                                answer_data['answer'] = answer.get('option_value')
                            elif answer.get('type') == 'checkbox':
                                selected_options = answer.get('selected_options', [])
                                answer_data['answer'] = [opt.get('option_value') for opt in selected_options]
                            elif answer.get('type') == 'slider':
                                answer_data['answer'] = answer.get('value')
                            else:
                                answer_data['answer'] = answer.get('answer') or answer.get('value')
                        else:
                            answer_data['answer'] = answer
                    submission_data['answers'].append(answer_data)
            logger.info(f"准备提交数据完成，共 {len(submission_data['answers'])} 个答案")
            logger.debug(f"完整提交数据: {json.dumps(submission_data)}")
            return submission_data
        except Exception as e:
            logger.error(f"准备提交数据时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise

# 注册屏幕
Factory.register('QuestionnaireFormScreen', cls=QuestionnaireFormScreen)

# 定义KV字符串
KV = '''
<QuestionnaireFormScreen>:
    canvas.before:
        Color:
            rgba: [0.98, 0.98, 0.98, 1]  # 非常浅的灰色背景，确保与白色卡片有对比
        Rectangle:
            pos: self.pos
            size: self.size
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: root.title
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
                font_style: "Title"
            
            MDIconButton:
                icon: "help-circle"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.show_info("请回答所有问题后点击提交按钮。")
        
        # 进度条
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_y: None
            height: dp(48)
            padding: [dp(16), dp(8)]
            
            MDLabel:
                text: f"完成进度: {root.progress}%"
                theme_text_color: "Secondary"
                size_hint_y: None
                height: dp(20)
            
            ProgressBar:
                id: progress_bar
                value: root.progress
                size_hint_y: None
                height: dp(4)
        
        # 表单容器
        ScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: form_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16)]
                spacing: dp(16)
'''

# 加载KV字符串
from kivy.lang import Builder
Builder.load_string(KV)
print("QuestionnaireFormScreen KV loaded")
