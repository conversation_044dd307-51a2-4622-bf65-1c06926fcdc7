#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复批量处理adaptive_height兼容性时引入的布局高度设置错误
恢复自适应高度设置
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"已备份: {backup_path}")
    return backup_path

def fix_layout_heights(file_path):
    """修复文件中的布局高度设置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复模式1: 将固定的 height: dp(32) 替换为自适应高度
        # 对于MDLabel等文本组件，保持合理的固定高度
        # 对于容器组件，使用自适应高度
        
        # 检查上下文来决定替换策略
        lines = content.split('\n')
        modified_lines = []
        
        for i, line in enumerate(lines):
            if 'height: dp(32)' in line:
                # 检查前面几行的上下文
                context_lines = lines[max(0, i-10):i]
                context = '\n'.join(context_lines)
                
                # 如果是在MDLabel、MDButtonText等文本组件中，保持固定高度但调整为合适的值
                if any(component in context for component in ['MDLabel:', 'MDButtonText:', 'MDIconButton:', 'MDButton:']):
                    # 对于文本组件，使用合适的固定高度
                    if 'MDIconButton:' in context:
                        modified_lines.append(line.replace('height: dp(32)', 'height: dp(48)'))
                    else:
                        modified_lines.append(line.replace('height: dp(32)', 'height: dp(40)'))
                # 如果是在容器组件中，使用自适应高度
                elif any(container in context for container in ['MDBoxLayout:', 'MDCard:', 'MDScrollView:']):
                    # 检查是否有size_hint_y: None
                    if 'size_hint_y: None' in context:
                        modified_lines.append(line.replace('height: dp(32)', 'height: self.minimum_height'))
                    else:
                        modified_lines.append(line)
                else:
                    # 默认情况，使用自适应高度
                    modified_lines.append(line.replace('height: dp(32)', 'height: self.minimum_height'))
            else:
                modified_lines.append(line)
        
        content = '\n'.join(modified_lines)
        
        # 如果内容有变化，写入文件
        if content != original_content:
            # 备份原文件
            backup_file(file_path)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已修复: {file_path}")
            return True
        else:
            print(f"⏭️  无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    # 需要处理的目录
    mobile_dir = os.path.dirname(os.path.abspath(__file__))
    screens_dir = os.path.join(mobile_dir, 'screens')
    
    # 跳过的目录
    skip_dirs = {
        'mobile_env', 'venv', '__pycache__', '.git', 
        'screens_bak', 'backup', 'node_modules'
    }
    
    fixed_files = []
    
    print("开始修复布局高度设置...")
    print(f"处理目录: {screens_dir}")
    
    # 遍历screens目录下的所有Python文件
    for root, dirs, files in os.walk(screens_dir):
        # 跳过备份目录
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                if fix_layout_heights(file_path):
                    fixed_files.append(file_path)
    
    print(f"\n修复完成! 共修复了 {len(fixed_files)} 个文件:")
    for file_path in fixed_files:
        print(f"  - {os.path.relpath(file_path, mobile_dir)}")
    
    if fixed_files:
        print("\n建议重新启动应用程序以查看修复效果。")
    else:
        print("\n没有发现需要修复的文件。")

if __name__ == '__main__':
    main()