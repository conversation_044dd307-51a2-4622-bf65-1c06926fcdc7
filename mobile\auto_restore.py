#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动恢复工具
自动从最早的备份恢复项目到原始状态
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_restore.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def auto_restore():
    """自动恢复到原始状态"""
    project_root = Path(__file__).parent
    backup_root = project_root / 'backup'
    
    logger.info("健康管理应用 - 自动恢复工具")
    logger.info("=" * 40)
    
    # 检查备份目录
    if not backup_root.exists():
        logger.error("❌ 备份目录不存在")
        return False
    
    # 获取所有备份
    backups = []
    for backup_dir in backup_root.iterdir():
        if backup_dir.is_dir() and backup_dir.name.startswith('202'):
            backups.append(backup_dir)
    
    if not backups:
        logger.error("❌ 没有找到可用的备份")
        return False
    
    # 按时间排序，选择最早的备份
    backups.sort(key=lambda x: x.name)
    selected_backup = backups[0]
    
    logger.info(f"找到 {len(backups)} 个备份")
    logger.info(f"自动选择最早的备份: {selected_backup.name}")
    
    # 显示备份信息
    logger.info(f"\n备份目录: {selected_backup.name}")
    logger.info(f"备份路径: {selected_backup}")
    
    # 统计备份内容
    file_count = 0
    dir_count = 0
    
    for item in selected_backup.rglob('*'):
        if item.is_file():
            file_count += 1
        elif item.is_dir():
            dir_count += 1
    
    logger.info(f"包含文件: {file_count} 个")
    logger.info(f"包含目录: {dir_count} 个")
    
    # 开始恢复
    logger.info(f"\n开始从备份恢复: {selected_backup.name}")
    
    restored_files = []
    errors = []
    
    try:
        # 遍历备份目录中的所有文件和目录
        for item in selected_backup.iterdir():
            if item.name == 'migration_report.md':
                continue  # 跳过迁移报告
            
            target_path = project_root / item.name
            
            if item.is_file():
                # 恢复文件
                if target_path.exists():
                    # 备份当前文件
                    backup_current = target_path.with_suffix(target_path.suffix + '.bak')
                    shutil.copy2(target_path, backup_current)
                    logger.info(f"当前文件已备份: {backup_current.name}")
                
                shutil.copy2(item, target_path)
                restored_files.append(str(target_path))
                logger.info(f"✓ 恢复文件: {item.name}")
            
            elif item.is_dir():
                # 恢复目录
                if target_path.exists():
                    # 备份当前目录
                    backup_current = target_path.with_name(target_path.name + '_bak')
                    if backup_current.exists():
                        shutil.rmtree(backup_current)
                    shutil.copytree(target_path, backup_current)
                    logger.info(f"当前目录已备份: {backup_current.name}")
                    
                    # 删除当前目录
                    shutil.rmtree(target_path)
                
                shutil.copytree(item, target_path)
                restored_files.append(str(target_path))
                logger.info(f"✓ 恢复目录: {item.name}")
        
        logger.info(f"\n恢复完成！共恢复 {len(restored_files)} 个文件/目录")
        
    except Exception as e:
        logger.error(f"恢复过程中出错: {e}")
        errors.append(str(e))
        return False
    
    # 清理优化版本文件
    logger.info("\n清理优化版本文件...")
    
    optimized_files = [
        'main_optimized.py',
        'theme.py',
        'api/api_config_optimized.py',
        'api/api_client_optimized.py',
        'check_migration.py'
    ]
    
    cleaned_count = 0
    
    for file_path in optimized_files:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                full_path.unlink()
                cleaned_count += 1
                logger.info(f"✓ 删除优化文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除文件失败 {file_path}: {e}")
    
    logger.info(f"清理完成，共删除 {cleaned_count} 个优化文件")
    
    # 生成恢复报告
    report_content = f"""# 自动恢复报告

**恢复时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**使用备份**: {selected_backup.name}
**项目目录**: {project_root}

## 恢复摘要

- **恢复文件数**: {len(restored_files)}
- **清理优化文件数**: {cleaned_count}
- **错误数**: {len(errors)}

## 恢复的文件列表

"""
    
    for file_path in restored_files:
        report_content += f"- {Path(file_path).name}\n"
    
    if errors:
        report_content += "\n## 错误信息\n\n"
        for error in errors:
            report_content += f"- {error}\n"
    
    report_content += "\n## 后续步骤\n\n"
    report_content += "1. 检查恢复的文件是否正常\n"
    report_content += "2. 运行 `python main.py` 测试应用\n"
    report_content += "3. 如有问题，检查备份文件\n"
    report_content += "\n✅ 项目已恢复到原始状态！\n"
    
    # 保存报告
    report_path = project_root / 'auto_restore_report.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"\n恢复报告已保存: {report_path}")
    
    return True


def main():
    """主函数"""
    if auto_restore():
        print("\n🎉 自动恢复成功完成！")
        print("\n下一步:")
        print("1. 运行 'python main.py' 测试应用")
        print("2. 检查功能是否正常")
        print("3. 查看 'auto_restore_report.md' 了解详情")
        return 0
    else:
        print("\n❌ 自动恢复过程中遇到问题")
        print("请查看日志文件 'auto_restore.log' 了解详情")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)