import os
import logging
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from kivy.uix.image import AsyncImage

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MD<PERSON>conButton
from kivymd.uix.label import MDLabel

from screens.base_screen import BaseScreen
from kivy.factory import Factory

# 获取日志记录器
logger = logging.getLogger(__name__)

class ImageViewerScreen(BaseScreen):
    """图片查看器屏幕"""
    title = StringProperty("图片查看器")
    image_source = StringProperty("")
    is_loading = BooleanProperty(False)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        Clock.schedule_once(self.init_ui)
    
    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            # 获取图片源
            self.image_source = getattr(self.app, 'image_to_view', "")
            
            # 获取标题
            image_title = getattr(self.app, 'image_title', "")
            if image_title:
                self.title = image_title
            
            # 加载图片
            if self.image_source:
                self.load_image()
            else:
                self.show_error("未找到图片源")
        except Exception as e:
            logger.error(f"初始化图片查看器时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"初始化图片查看器时出错: {str(e)}")
    
    def load_image(self):
        """加载图片"""
        try:
            # 清空图片容器
            self.ids.image_container.clear_widgets()
            
            # 重置状态
            self.loading_label = None
            self.async_image = None
            
            # 显示加载提示
            self.loading_label = MDLabel(
                text="正在加载图片...",
                halign="center",
                valign="center",
                theme_text_color="Primary"
            )
            self.ids.image_container.add_widget(self.loading_label)
            
            self.is_loading = True
            logger.info(f"开始加载图片: {self.image_source}")
            
            # 检查文件是否存在
            if self.image_source and os.path.exists(self.image_source):
                # 延迟创建AsyncImage，确保UI更新完成
                Clock.schedule_once(lambda dt: self._create_async_image(), 0.1)
                
                # 设置超时机制，10秒后如果还在加载则强制完成
                Clock.schedule_once(lambda dt: self._check_loading_timeout(), 10.0)
            else:
                # 文件不存在，直接显示错误
                logger.error(f"图片文件不存在: {self.image_source}")
                Clock.schedule_once(lambda dt: self._on_image_error(None, None), 0.1)
            
        except Exception as e:
            logger.error(f"加载图片时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"加载图片时出错: {str(e)}")
            self.is_loading = False
    
    def _create_async_image(self):
        """创建AsyncImage控件"""
        try:
            if not self.is_loading:
                return
                
            logger.info(f"创建AsyncImage控件: {self.image_source}")
            
            # 创建AsyncImage控件
            self.async_image = AsyncImage(
                source=self.image_source,
                allow_stretch=True,
                keep_ratio=True
            )
            
            # 绑定加载完成和失败的回调
            self.async_image.bind(on_load=self._on_image_loaded)
            self.async_image.bind(on_error=self._on_image_error)
            
            # 添加图片到容器
            self.ids.image_container.add_widget(self.async_image)
            
            logger.info("AsyncImage控件已添加到容器")
            
        except Exception as e:
            logger.error(f"创建AsyncImage控件时出错: {e}")
            self._on_image_error(None, None)
    
    def _check_loading_timeout(self):
        """检查加载超时"""
        if self.is_loading:
            logger.warning(f"图片加载超时: {self.image_source}")
            self._force_complete_loading()
    

    
    def _on_image_loaded(self, instance):
        """图片加载完成回调"""
        try:
            logger.info(f"图片加载完成回调触发: {self.image_source}")
            self._force_complete_loading()
        except Exception as e:
            logger.error(f"图片加载完成回调时出错: {e}")
            self.is_loading = False
    
    def _force_complete_loading(self):
        """强制完成加载过程"""
        try:
            # 移除所有加载提示
            if hasattr(self, 'loading_label') and self.loading_label:
                if self.loading_label in self.ids.image_container.children:
                    self.ids.image_container.remove_widget(self.loading_label)
                self.loading_label = None
            
            # 查找并移除所有包含"正在加载"的标签
            for child in list(self.ids.image_container.children):
                if hasattr(child, 'text') and isinstance(child.text, str) and '正在加载' in child.text:
                    self.ids.image_container.remove_widget(child)
            
            self.is_loading = False
            logger.info(f"图片加载已完成: {self.image_source}")
        except Exception as e:
            logger.error(f"强制完成加载时出错: {e}")
            self.is_loading = False
    
    def _on_image_error(self, instance, error=None):
        """图片加载失败回调"""
        try:
            logger.error(f"图片加载失败回调触发: {self.image_source}, 错误: {error}")
            
            # 强制完成加载过程
            self._force_complete_loading()
            
            # 清空容器，确保移除所有控件
            self.ids.image_container.clear_widgets()
            
            # 显示错误信息
            error_label = MDLabel(
                text=f"图片加载失败\n文件路径: {self.image_source}\n请检查文件是否存在或格式是否正确",
                halign="center",
                valign="center",
                theme_text_color="Error"
            )
            self.ids.image_container.add_widget(error_label)
            
            logger.error(f"图片加载失败: {self.image_source}")
            self.show_error(f"图片加载失败: {os.path.basename(self.image_source) if self.image_source else '未知文件'}")
        except Exception as e:
            logger.error(f"图片加载失败回调时出错: {e}")
            self.is_loading = False
    
    def go_back(self, *args):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'health_document_screen'
    
    def show_error(self, message):
        """显示错误信息"""
        logger.error(message)
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=self.app.theme.ERROR_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")

# 注册屏幕已移除，避免与Builder冲突

# 定义KV字符串
KV = '''
<ImageViewerScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: root.title
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
                font_size: dp(20)
                bold: True
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.load_image()
        
        # 图片容器
        MDBoxLayout:
            id: image_container
            orientation: 'vertical'
            padding: dp(16)
'''

# 加载KV字符串
from kivy.lang import Builder

# 检查是否已经加载过，避免重复加载
if not hasattr(Builder, '_image_viewer_kv_loaded'):
    Builder.load_string(KV)
    Builder._image_viewer_kv_loaded = True
    print("ImageViewerScreen KV loaded")
