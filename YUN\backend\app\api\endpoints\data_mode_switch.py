#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模式切换API

提供前端切换模拟数据和生产数据模式的接口
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from app.core.auth import get_current_active_user
from app.core.mock_data_manager import backend_mock_manager
from app.models.user import User

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix='/api/data-mode', tags=['数据模式切换'])

# 数据模式状态存储（在实际生产中应该使用数据库或Redis）
data_mode_state = {
    'mode': 'auto',  # auto, mock, production
    'last_updated': datetime.utcnow().isoformat(),
    'updated_by': 'system'
}

# Pydantic模型
class DataModeSwitch(BaseModel):
    mode: str
    reason: Optional[str] = '用户手动切换'

class DataModeResponse(BaseModel):
    status: str
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

@router.get('/status', summary='获取数据模式状态')
async def get_data_mode_status():
    """
    获取当前数据模式状态
    
    Returns:
        JSON: 当前数据模式信息
    """
    try:
        # 获取环境变量状态
        env_mock_enabled = os.getenv('ENABLE_MOCK_DATA', 'false').lower() in ['true', '1', 'yes', 'on']
        manager_enabled = backend_mock_manager.is_enabled()
        
        return {
            'status': 'success',
            'data': {
                'current_mode': data_mode_state['mode'],
                'mock_enabled': manager_enabled,
                'env_mock_enabled': env_mock_enabled,
                'last_updated': data_mode_state['last_updated'],
                'updated_by': data_mode_state['updated_by'],
                'available_modes': [
                    {'value': 'auto', 'label': '自动模式', 'description': '根据环境变量自动选择'},
                    {'value': 'mock', 'label': '模拟数据模式', 'description': '强制使用模拟数据'},
                    {'value': 'production', 'label': '生产数据模式', 'description': '强制使用真实数据'}
                ]
            }
        }
    except Exception as e:
        logger.error(f"获取数据模式状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'获取数据模式状态失败: {str(e)}'
        )

@router.post('/switch', summary='切换数据模式')
async def switch_data_mode(
    switch_request: DataModeSwitch
):
    """
    切换数据模式
    
    Request Body:
        {
            "mode": "auto|mock|production",
            "reason": "切换原因（可选）"
        }
    
    Returns:
        JSON: 切换结果
    """
    try:
        new_mode = switch_request.mode
        reason = switch_request.reason
        
        # 验证模式
        valid_modes = ['auto', 'mock', 'production']
        if new_mode not in valid_modes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f'无效的数据模式: {new_mode}，有效模式: {", ".join(valid_modes)}'
            )
        
        # 获取用户信息（暂时设为系统用户）
        user_id = 'system'
        
        # 更新数据模式状态
        old_mode = data_mode_state['mode']
        data_mode_state.update({
            'mode': new_mode,
            'last_updated': datetime.utcnow().isoformat(),
            'updated_by': f'user_{user_id}'
        })
        
        # 根据模式设置环境变量
        if new_mode == 'mock':
            os.environ['ENABLE_MOCK_DATA'] = 'true'
            os.environ['FORCE_MOCK_MODE'] = 'true'
        elif new_mode == 'production':
            os.environ['ENABLE_MOCK_DATA'] = 'false'
            os.environ['FORCE_MOCK_MODE'] = 'false'
        else:  # auto mode
            # 移除强制模式，恢复自动检测
            os.environ.pop('FORCE_MOCK_MODE', None)
        
        # 重新初始化模拟数据管理器
        backend_mock_manager.__init__()
        
        # 记录切换日志
        logger.info(f"数据模式切换: {old_mode} -> {new_mode}, 用户: {user_id}, 原因: {reason}")
        
        return {
            'status': 'success',
            'message': f'数据模式已切换为: {new_mode}',
            'data': {
                'old_mode': old_mode,
                'new_mode': new_mode,
                'mock_enabled': backend_mock_manager.is_enabled(),
                'switched_at': data_mode_state['last_updated'],
                'reason': reason
            }
        }
        
    except Exception as e:
        logger.error(f"切换数据模式失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'切换数据模式失败: {str(e)}'
        )

@router.get('/test', summary='测试数据模式')
async def test_data_mode():
    """
    测试当前数据模式
    
    Returns:
        JSON: 测试结果，包含示例数据
    """
    try:
        # 测试各种数据源
        mock_enabled = backend_mock_manager.is_enabled()
        
        # 获取示例数据
        sample_data = {}
        if mock_enabled:
            sample_data = {
                'dashboard_stats': backend_mock_manager.generate_mock_dashboard_stats(),
                'system_metrics': backend_mock_manager.generate_mock_system_metrics(),
                'service_stats': backend_mock_manager.generate_mock_service_stats()
            }
            data_source = 'mock'
        else:
            sample_data = {
                'message': '当前使用生产数据模式，无法提供示例模拟数据'
            }
            data_source = 'production'
        
        # 返回前端期望的格式
        test_results = {
            'current_mode': data_mode_state['mode'],
            'mock_enabled': mock_enabled,
            'data_source': data_source,
            'test_time': datetime.now().isoformat(),
            'env_mock_enabled': os.getenv('ENABLE_MOCK_DATA', 'false').lower() in ['true', '1', 'yes', 'on'],
            'sample_data': sample_data
        }
        
        return {
            'status': 'success',
            'data': test_results
        }
        
    except Exception as e:
        logger.error(f"测试数据模式失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'测试数据模式失败: {str(e)}'
        )

@router.get('/history', summary='获取切换历史')
async def get_switch_history():
    """
    获取数据模式切换历史
    
    Returns:
        JSON: 切换历史记录
    """
    try:
        # 在实际应用中，这里应该从数据库获取历史记录
        # 目前返回当前状态作为示例
        history = [
            {
                'timestamp': data_mode_state['last_updated'],
                'mode': data_mode_state['mode'],
                'updated_by': data_mode_state['updated_by'],
                'reason': '当前状态'
            }
        ]
        
        return {
            'status': 'success',
            'data': {
                'history': history,
                'total_count': len(history)
            }
        }
        
    except Exception as e:
        logger.error(f"获取切换历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'获取切换历史失败: {str(e)}'
        )

# 导出路由器
__all__ = ['router']