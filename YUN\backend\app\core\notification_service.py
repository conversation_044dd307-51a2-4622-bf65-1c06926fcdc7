#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知服务模块
提供邮件、短信、推送通知、站内消息等多种通知方式
"""

import asyncio
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, field, asdict
from enum import Enum
import json
import uuid
from pathlib import Path
import aiohttp
import asyncio
from jinja2 import Template, Environment, FileSystemLoader
import re
from urllib.parse import urljoin

from .env_config import env_config
from .logging_utils import get_logger
from .monitoring_utils import get_metrics_collector, increment_counter, record_timer
from .cache_utils import get_cache_manager

class NotificationType(str, Enum):
    """通知类型枚举"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    IN_APP = "in_app"
    WEBHOOK = "webhook"
    SLACK = "slack"
    DINGTALK = "dingtalk"
    WECHAT = "wechat"

class NotificationPriority(int, Enum):
    """通知优先级枚举"""
    LOW = 1
    NORMAL = 5
    HIGH = 10
    URGENT = 20

class NotificationStatus(str, Enum):
    """通知状态枚举"""
    PENDING = "pending"
    SENDING = "sending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    CANCELLED = "cancelled"
    READ = "read"

class TemplateType(str, Enum):
    """模板类型枚举"""
    PLAIN_TEXT = "plain_text"
    HTML = "html"
    MARKDOWN = "markdown"
    JSON = "json"

@dataclass
class NotificationTemplate:
    """通知模板"""
    id: str
    name: str
    template_type: TemplateType
    subject_template: Optional[str] = None
    content_template: str = ""
    variables: List[str] = field(default_factory=list)
    description: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def render(self, variables: Dict[str, Any]) -> Dict[str, str]:
        """渲染模板"""
        env = Environment()
        
        result = {}
        
        if self.subject_template:
            subject_tmpl = env.from_string(self.subject_template)
            result["subject"] = subject_tmpl.render(**variables)
        
        content_tmpl = env.from_string(self.content_template)
        result["content"] = content_tmpl.render(**variables)
        
        return result

@dataclass
class NotificationRecipient:
    """通知接收者"""
    id: str
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    push_token: Optional[str] = None
    user_id: Optional[str] = None
    preferences: Dict[str, Any] = field(default_factory=dict)
    
    def supports_type(self, notification_type: NotificationType) -> bool:
        """检查是否支持指定通知类型"""
        if notification_type == NotificationType.EMAIL:
            return bool(self.email)
        elif notification_type == NotificationType.SMS:
            return bool(self.phone)
        elif notification_type == NotificationType.PUSH:
            return bool(self.push_token)
        elif notification_type == NotificationType.IN_APP:
            return bool(self.user_id)
        else:
            return True

@dataclass
class NotificationAttachment:
    """通知附件"""
    filename: str
    content: bytes
    content_type: str
    size: int

@dataclass
class Notification:
    """通知消息"""
    id: str
    notification_type: NotificationType
    recipient: NotificationRecipient
    subject: Optional[str] = None
    content: str = ""
    template_id: Optional[str] = None
    template_variables: Dict[str, Any] = field(default_factory=dict)
    priority: NotificationPriority = NotificationPriority.NORMAL
    scheduled_time: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    attachments: List[NotificationAttachment] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    status: NotificationStatus = NotificationStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "notification_type": self.notification_type.value,
            "recipient": asdict(self.recipient),
            "subject": self.subject,
            "content": self.content,
            "template_id": self.template_id,
            "template_variables": self.template_variables,
            "priority": self.priority.value,
            "scheduled_time": self.scheduled_time.isoformat() if self.scheduled_time else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "metadata": self.metadata,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
            "delivered_at": self.delivered_at.isoformat() if self.delivered_at else None,
            "read_at": self.read_at.isoformat() if self.read_at else None,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries
        }

class EmailProvider:
    """邮件服务提供者"""
    
    def __init__(self, smtp_host: str, smtp_port: int, username: str, password: str, use_tls: bool = True):
        self.smtp_host = smtp_host
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.logger = get_logger()
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        content: str,
        from_email: Optional[str] = None,
        from_name: Optional[str] = None,
        is_html: bool = False,
        attachments: Optional[List[NotificationAttachment]] = None
    ) -> bool:
        """发送邮件"""
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = f"{from_name} <{from_email or self.username}>" if from_name else (from_email or self.username)
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # 添加邮件内容
            if is_html:
                msg.attach(MIMEText(content, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 添加附件
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.content)
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment.filename}'
                    )
                    msg.attach(part)
            
            # 发送邮件
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._send_smtp, msg, to_email)
            
            self.logger.info(f"Email sent successfully to {to_email}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def _send_smtp(self, msg: MIMEMultipart, to_email: str):
        """发送SMTP邮件（同步）"""
        if self.use_tls:
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.username, self.password)
                server.send_message(msg, to_addrs=[to_email])
        else:
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.login(self.username, self.password)
                server.send_message(msg, to_addrs=[to_email])

class SMSProvider:
    """短信服务提供者"""
    
    def __init__(self, api_url: str, api_key: str, api_secret: Optional[str] = None):
        self.api_url = api_url
        self.api_key = api_key
        self.api_secret = api_secret
        self.logger = get_logger()
    
    async def send_sms(self, phone: str, content: str, template_id: Optional[str] = None) -> bool:
        """发送短信"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "phone": phone,
                    "content": content,
                    "api_key": self.api_key
                }
                
                if template_id:
                    payload["template_id"] = template_id
                
                if self.api_secret:
                    payload["api_secret"] = self.api_secret
                
                async with session.post(self.api_url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("success", False):
                            self.logger.info(f"SMS sent successfully to {phone}")
                            return True
                        else:
                            self.logger.error(f"SMS API error: {result.get('message', 'Unknown error')}")
                            return False
                    else:
                        self.logger.error(f"SMS API returned status {response.status}")
                        return False
        
        except Exception as e:
            self.logger.error(f"Failed to send SMS to {phone}: {e}")
            return False

class PushProvider:
    """推送通知服务提供者"""
    
    def __init__(self, fcm_server_key: Optional[str] = None, apns_key_file: Optional[str] = None):
        self.fcm_server_key = fcm_server_key
        self.apns_key_file = apns_key_file
        self.logger = get_logger()
    
    async def send_push(
        self,
        token: str,
        title: str,
        body: str,
        data: Optional[Dict[str, Any]] = None,
        platform: str = "android"
    ) -> bool:
        """发送推送通知"""
        try:
            if platform.lower() == "android" and self.fcm_server_key:
                return await self._send_fcm(token, title, body, data)
            elif platform.lower() == "ios" and self.apns_key_file:
                return await self._send_apns(token, title, body, data)
            else:
                self.logger.error(f"Unsupported push platform: {platform}")
                return False
        
        except Exception as e:
            self.logger.error(f"Failed to send push notification: {e}")
            return False
    
    async def _send_fcm(
        self,
        token: str,
        title: str,
        body: str,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """发送FCM推送"""
        fcm_url = "https://fcm.googleapis.com/fcm/send"
        
        headers = {
            "Authorization": f"key={self.fcm_server_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "to": token,
            "notification": {
                "title": title,
                "body": body
            }
        }
        
        if data:
            payload["data"] = data
        
        async with aiohttp.ClientSession() as session:
            async with session.post(fcm_url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success", 0) > 0:
                        self.logger.info(f"FCM push sent successfully to {token}")
                        return True
                    else:
                        self.logger.error(f"FCM push failed: {result}")
                        return False
                else:
                    self.logger.error(f"FCM API returned status {response.status}")
                    return False
    
    async def _send_apns(
        self,
        token: str,
        title: str,
        body: str,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """发送APNS推送"""
        # 这里需要实现APNS推送逻辑
        # 可以使用aioapns库
        self.logger.warning("APNS push not implemented yet")
        return False

class WebhookProvider:
    """Webhook通知提供者"""
    
    def __init__(self, webhook_url: str, secret: Optional[str] = None):
        self.webhook_url = webhook_url
        self.secret = secret
        self.logger = get_logger()
    
    async def send_webhook(
        self,
        payload: Dict[str, Any],
        headers: Optional[Dict[str, str]] = None
    ) -> bool:
        """发送Webhook通知"""
        try:
            request_headers = headers or {}
            request_headers["Content-Type"] = "application/json"
            
            if self.secret:
                import hmac
                import hashlib
                
                payload_str = json.dumps(payload, sort_keys=True)
                signature = hmac.new(
                    self.secret.encode(),
                    payload_str.encode(),
                    hashlib.sha256
                ).hexdigest()
                request_headers["X-Signature"] = f"sha256={signature}"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    headers=request_headers,
                    json=payload
                ) as response:
                    if 200 <= response.status < 300:
                        self.logger.info(f"Webhook sent successfully to {self.webhook_url}")
                        return True
                    else:
                        self.logger.error(f"Webhook failed with status {response.status}")
                        return False
        
        except Exception as e:
            self.logger.error(f"Failed to send webhook: {e}")
            return False

class NotificationService:
    """通知服务"""
    
    def __init__(self):
        self.templates: Dict[str, NotificationTemplate] = {}
        self.providers: Dict[NotificationType, Any] = {}
        self.notifications: Dict[str, Notification] = {}
        self.pending_notifications: List[Notification] = []
        
        self.logger = get_logger()
        self.metrics = get_metrics_collector()
        self.cache = get_cache_manager()
        
        self._running = False
        self._worker_task: Optional[asyncio.Task] = None
        
        # 初始化提供者
        self._init_providers()
    
    def _init_providers(self):
        """初始化通知提供者"""
        config = env_config
        
        # 邮件提供者
        if hasattr(config, 'SMTP_HOST'):
            self.providers[NotificationType.EMAIL] = EmailProvider(
                smtp_host=config.SMTP_HOST,
                smtp_port=getattr(config, 'SMTP_PORT', 587),
                username=config.SMTP_USERNAME,
                password=config.SMTP_PASSWORD,
                use_tls=getattr(config, 'SMTP_USE_TLS', True)
            )
        
        # 短信提供者
        if hasattr(config, 'SMS_API_URL'):
            self.providers[NotificationType.SMS] = SMSProvider(
                api_url=config.SMS_API_URL,
                api_key=config.SMS_API_KEY,
                api_secret=getattr(config, 'SMS_API_SECRET', None)
            )
        
        # 推送提供者
        if hasattr(config, 'FCM_SERVER_KEY'):
            self.providers[NotificationType.PUSH] = PushProvider(
                fcm_server_key=config.FCM_SERVER_KEY,
                apns_key_file=getattr(config, 'APNS_KEY_FILE', None)
            )
    
    def register_template(self, template: NotificationTemplate):
        """注册通知模板"""
        self.templates[template.id] = template
        self.logger.debug(f"Notification template registered: {template.name}")
    
    def get_template(self, template_id: str) -> Optional[NotificationTemplate]:
        """获取通知模板"""
        return self.templates.get(template_id)
    
    async def send_notification(
        self,
        notification_type: NotificationType,
        recipient: NotificationRecipient,
        subject: Optional[str] = None,
        content: str = "",
        template_id: Optional[str] = None,
        template_variables: Optional[Dict[str, Any]] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        scheduled_time: Optional[datetime] = None,
        expires_at: Optional[datetime] = None,
        attachments: Optional[List[NotificationAttachment]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        max_retries: int = 3
    ) -> str:
        """发送通知"""
        notification_id = str(uuid.uuid4())
        
        # 检查接收者是否支持该通知类型
        if not recipient.supports_type(notification_type):
            raise ValueError(f"Recipient does not support {notification_type.value} notifications")
        
        # 使用模板渲染内容
        if template_id:
            template = self.get_template(template_id)
            if template:
                rendered = template.render(template_variables or {})
                if not subject and "subject" in rendered:
                    subject = rendered["subject"]
                if not content and "content" in rendered:
                    content = rendered["content"]
        
        notification = Notification(
            id=notification_id,
            notification_type=notification_type,
            recipient=recipient,
            subject=subject,
            content=content,
            template_id=template_id,
            template_variables=template_variables or {},
            priority=priority,
            scheduled_time=scheduled_time,
            expires_at=expires_at,
            attachments=attachments or [],
            metadata=metadata or {},
            max_retries=max_retries
        )
        
        self.notifications[notification_id] = notification
        
        if scheduled_time and scheduled_time > datetime.now():
            # 定时通知
            self.pending_notifications.append(notification)
            self.logger.debug(f"Notification scheduled: {notification_id}")
        else:
            # 立即发送
            await self._send_notification(notification)
        
        increment_counter("notifications_created", tags={"type": notification_type.value})
        return notification_id
    
    async def _send_notification(self, notification: Notification) -> bool:
        """发送单个通知"""
        if notification.expires_at and notification.expires_at < datetime.now():
            notification.status = NotificationStatus.CANCELLED
            self.logger.warning(f"Notification expired: {notification.id}")
            return False
        
        notification.status = NotificationStatus.SENDING
        notification.sent_at = datetime.now()
        
        try:
            provider = self.providers.get(notification.notification_type)
            if not provider:
                raise ValueError(f"No provider configured for {notification.notification_type.value}")
            
            success = False
            
            if notification.notification_type == NotificationType.EMAIL:
                success = await provider.send_email(
                    to_email=notification.recipient.email,
                    subject=notification.subject or "",
                    content=notification.content,
                    is_html=True,
                    attachments=notification.attachments
                )
            
            elif notification.notification_type == NotificationType.SMS:
                success = await provider.send_sms(
                    phone=notification.recipient.phone,
                    content=notification.content
                )
            
            elif notification.notification_type == NotificationType.PUSH:
                success = await provider.send_push(
                    token=notification.recipient.push_token,
                    title=notification.subject or "",
                    body=notification.content,
                    data=notification.metadata
                )
            
            elif notification.notification_type == NotificationType.WEBHOOK:
                webhook_provider = self.providers.get(NotificationType.WEBHOOK)
                if webhook_provider:
                    payload = {
                        "notification_id": notification.id,
                        "type": notification.notification_type.value,
                        "recipient": asdict(notification.recipient),
                        "subject": notification.subject,
                        "content": notification.content,
                        "metadata": notification.metadata,
                        "timestamp": datetime.now().isoformat()
                    }
                    success = await webhook_provider.send_webhook(payload)
            
            elif notification.notification_type == NotificationType.IN_APP:
                # 站内消息存储到缓存或数据库
                await self._store_in_app_notification(notification)
                success = True
            
            if success:
                notification.status = NotificationStatus.SENT
                self.logger.info(f"Notification sent successfully: {notification.id}")
                increment_counter("notifications_sent", tags={"type": notification.notification_type.value})
            else:
                notification.status = NotificationStatus.FAILED
                notification.error_message = "Provider returned failure"
                increment_counter("notifications_failed", tags={"type": notification.notification_type.value})
            
            return success
        
        except Exception as e:
            notification.status = NotificationStatus.FAILED
            notification.error_message = str(e)
            self.logger.error(f"Failed to send notification {notification.id}: {e}")
            increment_counter("notifications_failed", tags={"type": notification.notification_type.value})
            return False
    
    async def _store_in_app_notification(self, notification: Notification):
        """存储站内通知"""
        cache_key = f"in_app_notifications:{notification.recipient.user_id}"
        
        # 获取用户的站内通知列表
        notifications = await self.cache.get(cache_key) or []
        
        # 添加新通知
        notifications.append({
            "id": notification.id,
            "subject": notification.subject,
            "content": notification.content,
            "metadata": notification.metadata,
            "created_at": notification.created_at.isoformat(),
            "read": False
        })
        
        # 限制通知数量
        if len(notifications) > 100:
            notifications = notifications[-100:]
        
        # 存储到缓存
        await self.cache.set(cache_key, notifications, ttl=86400 * 30)  # 30天
    
    async def retry_failed_notification(self, notification_id: str) -> bool:
        """重试失败的通知"""
        notification = self.notifications.get(notification_id)
        if not notification:
            return False
        
        if notification.status != NotificationStatus.FAILED:
            return False
        
        if notification.retry_count >= notification.max_retries:
            self.logger.warning(f"Notification {notification_id} exceeded max retries")
            return False
        
        notification.retry_count += 1
        notification.status = NotificationStatus.PENDING
        notification.error_message = None
        
        return await self._send_notification(notification)
    
    async def cancel_notification(self, notification_id: str) -> bool:
        """取消通知"""
        notification = self.notifications.get(notification_id)
        if not notification:
            return False
        
        if notification.status in [NotificationStatus.SENT, NotificationStatus.DELIVERED]:
            return False
        
        notification.status = NotificationStatus.CANCELLED
        
        # 从待发送列表中移除
        self.pending_notifications = [
            n for n in self.pending_notifications if n.id != notification_id
        ]
        
        self.logger.info(f"Notification cancelled: {notification_id}")
        increment_counter("notifications_cancelled")
        return True
    
    async def get_notification(self, notification_id: str) -> Optional[Notification]:
        """获取通知"""
        return self.notifications.get(notification_id)
    
    async def get_user_notifications(
        self,
        user_id: str,
        notification_type: Optional[NotificationType] = None,
        status: Optional[NotificationStatus] = None,
        limit: int = 50
    ) -> List[Notification]:
        """获取用户通知"""
        user_notifications = []
        
        for notification in self.notifications.values():
            if notification.recipient.user_id == user_id:
                if notification_type and notification.notification_type != notification_type:
                    continue
                if status and notification.status != status:
                    continue
                user_notifications.append(notification)
        
        # 按创建时间倒序排列
        user_notifications.sort(key=lambda n: n.created_at, reverse=True)
        
        return user_notifications[:limit]
    
    async def mark_notification_read(self, notification_id: str, user_id: str) -> bool:
        """标记通知为已读"""
        notification = self.notifications.get(notification_id)
        if not notification:
            return False
        
        if notification.recipient.user_id != user_id:
            return False
        
        notification.read_at = datetime.now()
        
        # 如果是站内通知，更新缓存
        if notification.notification_type == NotificationType.IN_APP:
            cache_key = f"in_app_notifications:{user_id}"
            notifications = await self.cache.get(cache_key) or []
            
            for n in notifications:
                if n["id"] == notification_id:
                    n["read"] = True
                    break
            
            await self.cache.set(cache_key, notifications, ttl=86400 * 30)
        
        self.logger.debug(f"Notification marked as read: {notification_id}")
        return True
    
    async def get_notification_statistics(self) -> Dict[str, Any]:
        """获取通知统计"""
        stats = {
            "total_notifications": len(self.notifications),
            "pending_notifications": len(self.pending_notifications),
            "by_type": {},
            "by_status": {},
            "by_priority": {}
        }
        
        for notification in self.notifications.values():
            # 按类型统计
            type_key = notification.notification_type.value
            stats["by_type"][type_key] = stats["by_type"].get(type_key, 0) + 1
            
            # 按状态统计
            status_key = notification.status.value
            stats["by_status"][status_key] = stats["by_status"].get(status_key, 0) + 1
            
            # 按优先级统计
            priority_key = notification.priority.name
            stats["by_priority"][priority_key] = stats["by_priority"].get(priority_key, 0) + 1
        
        return stats
    
    async def start_worker(self):
        """启动通知工作者"""
        if self._running:
            return
        
        self._running = True
        self._worker_task = asyncio.create_task(self._worker_loop())
        self.logger.info("Notification worker started")
    
    async def stop_worker(self):
        """停止通知工作者"""
        if not self._running:
            return
        
        self._running = False
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Notification worker stopped")
    
    async def _worker_loop(self):
        """通知工作者循环"""
        while self._running:
            try:
                await self._process_pending_notifications()
                await asyncio.sleep(10)  # 每10秒检查一次
            except Exception as e:
                self.logger.error(f"Notification worker error: {e}")
                await asyncio.sleep(30)
    
    async def _process_pending_notifications(self):
        """处理待发送通知"""
        now = datetime.now()
        ready_notifications = []
        
        for notification in self.pending_notifications[:]:
            if notification.scheduled_time and notification.scheduled_time <= now:
                ready_notifications.append(notification)
                self.pending_notifications.remove(notification)
        
        for notification in ready_notifications:
            await self._send_notification(notification)

# 全局实例
_notification_service: Optional[NotificationService] = None

def get_notification_service() -> NotificationService:
    """获取通知服务"""
    global _notification_service
    if _notification_service is None:
        _notification_service = NotificationService()
    return _notification_service

# 便捷函数
async def send_email(
    to_email: str,
    subject: str,
    content: str,
    to_name: Optional[str] = None,
    template_id: Optional[str] = None,
    template_variables: Optional[Dict[str, Any]] = None,
    priority: NotificationPriority = NotificationPriority.NORMAL,
    attachments: Optional[List[NotificationAttachment]] = None
) -> str:
    """发送邮件"""
    service = get_notification_service()
    recipient = NotificationRecipient(
        id=str(uuid.uuid4()),
        name=to_name,
        email=to_email
    )
    
    return await service.send_notification(
        notification_type=NotificationType.EMAIL,
        recipient=recipient,
        subject=subject,
        content=content,
        template_id=template_id,
        template_variables=template_variables,
        priority=priority,
        attachments=attachments
    )

async def send_sms(
    phone: str,
    content: str,
    template_id: Optional[str] = None,
    template_variables: Optional[Dict[str, Any]] = None,
    priority: NotificationPriority = NotificationPriority.NORMAL
) -> str:
    """发送短信"""
    service = get_notification_service()
    recipient = NotificationRecipient(
        id=str(uuid.uuid4()),
        phone=phone
    )
    
    return await service.send_notification(
        notification_type=NotificationType.SMS,
        recipient=recipient,
        content=content,
        template_id=template_id,
        template_variables=template_variables,
        priority=priority
    )

async def send_push_notification(
    push_token: str,
    title: str,
    body: str,
    data: Optional[Dict[str, Any]] = None,
    priority: NotificationPriority = NotificationPriority.NORMAL
) -> str:
    """发送推送通知"""
    service = get_notification_service()
    recipient = NotificationRecipient(
        id=str(uuid.uuid4()),
        push_token=push_token
    )
    
    return await service.send_notification(
        notification_type=NotificationType.PUSH,
        recipient=recipient,
        subject=title,
        content=body,
        metadata=data,
        priority=priority
    )

async def send_in_app_notification(
    user_id: str,
    title: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None,
    priority: NotificationPriority = NotificationPriority.NORMAL
) -> str:
    """发送站内通知"""
    service = get_notification_service()
    recipient = NotificationRecipient(
        id=str(uuid.uuid4()),
        user_id=user_id
    )
    
    return await service.send_notification(
        notification_type=NotificationType.IN_APP,
        recipient=recipient,
        subject=title,
        content=content,
        metadata=metadata,
        priority=priority
    )

# 模板管理函数
def register_notification_template(
    template_id: str,
    name: str,
    template_type: TemplateType,
    subject_template: Optional[str] = None,
    content_template: str = "",
    variables: Optional[List[str]] = None,
    description: Optional[str] = None
):
    """注册通知模板"""
    service = get_notification_service()
    template = NotificationTemplate(
        id=template_id,
        name=name,
        template_type=template_type,
        subject_template=subject_template,
        content_template=content_template,
        variables=variables or [],
        description=description
    )
    service.register_template(template)

# 初始化函数
async def init_notification_service():
    """初始化通知服务"""
    service = get_notification_service()
    await service.start_worker()
    
    # 注册默认模板
    register_notification_template(
        template_id="welcome_email",
        name="欢迎邮件",
        template_type=TemplateType.HTML,
        subject_template="欢迎使用 {{ app_name }}",
        content_template="""
        <h1>欢迎 {{ user_name }}！</h1>
        <p>感谢您注册 {{ app_name }}。</p>
        <p>您的账户已成功创建，现在可以开始使用我们的服务了。</p>
        """,
        variables=["app_name", "user_name"],
        description="用户注册成功后的欢迎邮件"
    )
    
    register_notification_template(
        template_id="password_reset",
        name="密码重置",
        template_type=TemplateType.HTML,
        subject_template="密码重置请求",
        content_template="""
        <h1>密码重置</h1>
        <p>您好 {{ user_name }}，</p>
        <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
        <p><a href="{{ reset_url }}">重置密码</a></p>
        <p>如果您没有请求重置密码，请忽略此邮件。</p>
        <p>此链接将在 {{ expires_in }} 小时后失效。</p>
        """,
        variables=["user_name", "reset_url", "expires_in"],
        description="密码重置邮件"
    )
    
    logger = get_logger()
    logger.info("Notification service initialized")

async def shutdown_notification_service():
    """关闭通知服务"""
    service = get_notification_service()
    await service.stop_worker()