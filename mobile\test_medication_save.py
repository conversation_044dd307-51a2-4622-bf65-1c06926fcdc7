#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试药物数据保存功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import DatabaseManager
from utils.user_manager import get_user_manager
from kivy.logger import Lo<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_medication_save():
    """测试药物数据保存功能"""
    try:
        # 获取用户管理器
        user_manager = get_user_manager()
        current_user_id = "SM_008"  # 测试用户ID
        
        # 连接数据库
        db_manager = DatabaseManager()
        db_manager.connect(current_user_id)
        
        # 创建medications表（如果不存在）
        db_manager.execute_query('''
            CREATE TABLE IF NOT EXISTS medications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                custom_id TEXT NOT NULL,
                name TEXT NOT NULL,
                dosage TEXT,
                frequency TEXT,
                start_date TEXT,
                end_date TEXT,
                instructions TEXT,
                prescription_required INTEGER DEFAULT 0,
                notes TEXT,
                medication_type TEXT,
                status TEXT DEFAULT 'active',
                stop_reason TEXT,
                stop_date TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        print("✓ medications表创建成功")
        
        # 测试插入数据
        test_medication = {
            'name': '测试药物',
            'dosage': '100mg',
            'frequency': '每日一次',
            'start_date': '2025-01-20',
            'notes': '测试用药记录'
        }
        
        db_manager.execute_query('''
            INSERT INTO medications (custom_id, name, dosage, frequency, start_date, notes, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        ''', (
            current_user_id,
            test_medication['name'],
            test_medication['dosage'],
            test_medication['frequency'],
            test_medication['start_date'],
            test_medication['notes'],
            'active'
        ))
        
        print("✓ 测试药物数据插入成功")
        
        # 查询验证
        rows = db_manager.execute_query(
            "SELECT id, name, dosage, frequency, start_date, notes, status FROM medications WHERE custom_id = ? ORDER BY created_at DESC",
            (current_user_id,)
        )
        
        if rows:
            print(f"✓ 查询到 {len(rows)} 条药物记录:")
            for row in rows:
                # 处理字典格式的返回结果
                if isinstance(row, dict):
                    print(f"  - ID: {row.get('id', 'N/A')}, 名称: {row.get('name', 'N/A')}, 剂量: {row.get('dosage', 'N/A')}, 频率: {row.get('frequency', 'N/A')}, 开始日期: {row.get('start_date', 'N/A')}, 状态: {row.get('status', 'N/A')}")
                else:
                    # 处理元组格式的返回结果
                    print(f"  - ID: {row[0]}, 名称: {row[1]}, 剂量: {row[2]}, 频率: {row[3]}, 开始日期: {row[4]}, 状态: {row[6]}")
        else:
            print("✗ 未查询到药物记录")
            
        # 测试后端同步（模拟）
        print("\n测试后端同步功能:")
        try:
            import requests
            from utils.app_config import API_CONFIG
            API_BASE_URL = API_CONFIG['BASE_URL']
            
            backend_data = {
                'custom_id': current_user_id,
                'name': test_medication['name'],
                'dosage': test_medication['dosage'],
                'frequency': test_medication['frequency'],
                'start_date': test_medication['start_date'],
                'instructions': test_medication['notes'],
                'notes': test_medication['notes'],
                'status': 'active',
                'medication_type': '',
                'prescription_required': 0
            }
            
            print(f"准备发送到后端的数据: {backend_data}")
            print("✓ 后端同步数据格式正确")
            
        except ImportError as e:
            print(f"✗ 导入模块失败: {e}")
        except Exception as e:
            print(f"✗ 后端同步测试失败: {e}")
            
        print("\n=== 药物数据保存功能测试完成 ===")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_medication_save()