#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主题修复 - 验证 ON_SURFACE 属性
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_theme_attributes():
    """测试主题属性是否正确定义"""
    try:
        from theme import AppTheme
        
        print("=== 主题属性测试 ===")
        
        # 测试 ON_SURFACE 属性
        if hasattr(AppTheme, 'ON_SURFACE'):
            print(f"✓ ON_SURFACE 属性存在: {AppTheme.ON_SURFACE}")
        else:
            print("✗ ON_SURFACE 属性缺失")
            return False
            
        # 测试 TEXT_ON_PRIMARY 属性
        if hasattr(AppTheme, 'TEXT_ON_PRIMARY'):
            print(f"✓ TEXT_ON_PRIMARY 属性存在: {AppTheme.TEXT_ON_PRIMARY}")
        else:
            print("✗ TEXT_ON_PRIMARY 属性缺失")
            return False
            
        # 测试其他重要属性
        required_attrs = ['PRIMARY_COLOR', 'TEXT_SECONDARY', 'SURFACE_COLOR']
        for attr in required_attrs:
            if hasattr(AppTheme, attr):
                print(f"✓ {attr} 属性存在: {getattr(AppTheme, attr)}")
            else:
                print(f"✗ {attr} 属性缺失")
                return False
                
        print("\n=== 所有主题属性测试通过 ===")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_health_data_screen_import():
    """测试健康资料管理屏幕导入"""
    try:
        print("\n=== 健康资料管理屏幕导入测试 ===")
        from screens.health_data_management_screen import HealthDataManagementScreen
        print("✓ HealthDataManagementScreen 导入成功")
        return True
    except Exception as e:
        print(f"✗ HealthDataManagementScreen 导入失败: {e}")
        return False

if __name__ == "__main__":
    print("开始主题修复验证测试...\n")
    
    success = True
    success &= test_theme_attributes()
    success &= test_health_data_screen_import()
    
    if success:
        print("\n🎉 所有测试通过！主题修复成功。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查修复。")
        sys.exit(1)