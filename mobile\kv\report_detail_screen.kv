<ReportDetailScreen>:
    name: 'report_detail'
    
    MDBoxLayout:
        orientation: 'vertical'
        
        # 顶部工具栏
        MDTopAppBar:
            id: toolbar
            title: "报告详情"
            left_action_items: [['arrow-left', lambda x: root.go_back()]]
            elevation: 1
            md_bg_color: app.theme.PRIMARY_COLOR
            specific_text_color: app.theme.ON_PRIMARY_COLOR
            
        # 主要内容区域
        MDBoxLayout:
            id: content_container
            orientation: 'vertical'
            padding: 0
            spacing: 0
            
            # 内容将通过Python代码动态添加