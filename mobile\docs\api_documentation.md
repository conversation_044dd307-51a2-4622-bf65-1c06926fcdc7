# 健康管理系统API文档

## 概述

健康管理系统API提供了一组RESTful接口，用于管理用户、健康数据、问卷和评估量表等功能。

## 基本信息

- **基础URL**: `http://your-server-address:8006`
- **认证方式**: Bearer <PERSON>ken
- **内容类型**: JSON

## 认证

### 登录

#### 直接登录

```
POST /api/direct-login
```

**请求体**:

```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**响应**:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "token_id": "12345678-1234-5678-1234-567812345678",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "full_name": "管理员",
    "role": "ADMIN",
    "is_active": true,
    "requires_2fa": false
  }
}
```

#### JSON登录

```
POST /api/json-login
```

**请求体**:

```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**响应**: 与直接登录相同

### 双因素认证

如果用户启用了双因素认证，登录响应将包含`requires_2fa`字段：

```json
{
  "access_token": "",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "admin",
    "requires_2fa": true
  },
  "requires_2fa": true,
  "message": "需要双因素认证，请输入验证码"
}
```

#### 验证双因素认证验证码

```
POST /api/verify-2fa
```

**请求体**:

```json
{
  "custom_id": "P_00000001",
  "code": "123456"
}
```

**响应**: 成功后与登录响应相同

### 注销

```
POST /api/logout
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "token_id": "12345678-1234-5678-1234-567812345678"
}
```

**响应**:

```json
{
  "status": "success",
  "message": "令牌已撤销"
}
```

## 用户管理

### 获取当前用户信息

```
GET /api/users/me
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "full_name": "管理员",
  "role": "ADMIN",
  "is_active": true
}
```

### 创建用户

```
POST /api/users
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "username": "new_user",
  "password": "StrongP@ssw0rd",
  "email": "<EMAIL>",
  "full_name": "新用户",
  "role": "PERSONAL"
}
```

**响应**:

```json
{
  "id": 2,
  "username": "new_user",
  "email": "<EMAIL>",
  "full_name": "新用户",
  "role": "PERSONAL",
  "is_active": true
}
```

### 更新用户

```
PUT /api/users/{custom_id}
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "email": "<EMAIL>",
  "full_name": "更新的用户"
}
```

**响应**:

```json
{
  "id": 2,
  "username": "new_user",
  "email": "<EMAIL>",
  "full_name": "更新的用户",
  "role": "PERSONAL",
  "is_active": true
}
```

### 删除用户

```
DELETE /api/users/{custom_id}
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "status": "success",
  "message": "用户已删除"
}
```

## 健康数据管理

### 获取健康数据列表

```
GET /api/health-data
```

**请求头**:

```
Authorization: Bearer your_token
```

**查询参数**:

- `page`: 页码（默认为1）
- `limit`: 每页数量（默认为10）
- `search`: 搜索关键词

**响应**:

```json
{
  "items": [
    {
      "id": 1,
      "custom_id": "P_00000002",
      "date": "2023-01-01",
      "data_type": "blood_pressure",
      "data": {
        "systolic": 120,
        "diastolic": 80
      }
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### 获取健康数据详情

```
GET /api/health-data/{data_id}
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "id": 1,
  "custom_id": "P_00000002",
  "date": "2023-01-01",
  "data_type": "blood_pressure",
  "data": {
    "systolic": 120,
    "diastolic": 80
  }
}
```

### 创建健康数据

```
POST /api/health-data
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "custom_id": "P_00000002",
  "date": "2023-01-01",
  "data_type": "blood_pressure",
  "data": {
    "systolic": 120,
    "diastolic": 80
  }
}
```

**响应**:

```json
{
  "id": 1,
  "custom_id": "P_00000002",
  "date": "2023-01-01",
  "data_type": "blood_pressure",
  "data": {
    "systolic": 120,
    "diastolic": 80
  }
}
```

### 更新健康数据

```
PUT /api/health-data/{data_id}
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "data": {
    "systolic": 130,
    "diastolic": 85
  }
}
```

**响应**:

```json
{
  "id": 1,
  "custom_id": "P_00000002",
  "date": "2023-01-01",
  "data_type": "blood_pressure",
  "data": {
    "systolic": 130,
    "diastolic": 85
  }
}
```

### 删除健康数据

```
DELETE /api/health-data/{data_id}
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "status": "success",
  "message": "健康数据已删除"
}
```

## 移动端API（新版分发表驱动，支持多次分发与唯一标识）

### 评估量表接口

#### 获取分发给用户的评估量表列表

```
GET /api/mobile/assessments
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "status": "success",
  "data": {
    "assessments": [
      {
        "id": 1,
        "distribution_id": 1,
        "name": "PHQ-9抑郁症筛查量表",
        "assessment_type": "MENTAL_HEALTH",
        "status": "pending",
        "distribution_status": "distributed",
        "round_number": 1,
        "sequence_number": 1,
        "unique_identifier": "1_P_00000001_1_1",
        "due_date": "2024-01-15T23:59:59",
        "created_at": "2024-01-01T10:00:00",
        "completed_at": null,
        "template": {
          "id": 1,
          "name": "PHQ-9抑郁症筛查量表",
          "assessment_type": "MENTAL_HEALTH",
          "max_score": 27,
          "questions": [ ... ]
        }
      }
    ],
    "total": 1,
    "skip": 0,
    "limit": 20
  }
}
```

- 以 assessment_distributions 为主表，join assessments，返回所有分发给当前用户的量表。
- `unique_identifier` 字段确保每次分发唯一。
- `distribution_status` 反映分发表状态。

#### 提交评估量表结果

```
POST /api/mobile/assessments/{assessment_id}/submit
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "answers": [
    { "question_id": 1, "answer": 2 },
    { "question_id": 2, "answer": 1 }
  ]
}
```

**响应**:

```json
{
  "status": "success",
  "message": "评估量表提交成功",
  "data": {
    "assessment_id": 1,
    "total_score": 15,
    "max_score": 27,
    "result_category": "中度抑郁",
    "conclusion": "建议关注心理健康状况。",
    "completed_at": "2024-01-01T14:30:00",
    "unique_identifier": "1_P_00000001_1_1"
  }
}
```

---

### 问卷接口

#### 获取分发给用户的问卷列表

```
GET /api/mobile/questionnaires
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "status": "success",
  "data": {
    "questionnaires": [
      {
        "id": 1,
        "distribution_id": 1,
        "name": "健康状况调查问卷",
        "questionnaire_type": "health",
        "status": "pending",
        "distribution_status": "distributed",
        "round_number": 1,
        "sequence_number": 1,
        "unique_identifier": "1_P_00000001_1_1",
        "due_date": "2024-01-15T23:59:59",
        "created_at": "2024-01-01T10:00:00",
        "completed_at": null,
        "template": {
          "id": 1,
          "name": "健康状况调查问卷",
          "questionnaire_type": "health",
          "questions": [ ... ]
        }
      }
    ],
    "total": 1,
    "skip": 0,
    "limit": 20
  }
}
```

- 以 questionnaire_distributions 为主表，join questionnaires，返回所有分发给当前用户的问卷。
- `unique_identifier` 字段确保每次分发唯一。
- `distribution_status` 反映分发表状态。

#### 提交问卷结果

```
POST /api/mobile/questionnaires/{questionnaire_id}/submit
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "answers": [
    { "question_id": 1, "answer": "175" },
    { "question_id": 2, "answer": "70" }
  ]
}
```

**响应**:

```json
{
  "status": "success",
  "message": "问卷提交成功",
  "data": {
    "questionnaire_id": 1,
    "response_id": 1,
    "completed_at": "2024-01-01T14:30:00",
    "result_category": "体重正常",
    "conclusion": "建议保持健康的生活方式。"
  }
}
```

---

### 关键字段说明
- `unique_identifier`：每次分发唯一标识，格式为 template_id_custom_id_round_number_sequence_number
- `distribution_status`：分发表状态，支持 distributed/pending/completed
- `round_number`/`sequence_number`：支持多次分发和多轮评估
- `template`：聚合模板和题目信息，便于移动端直接渲染

---

## 问卷和评估量表（Web端）

### 获取问卷列表

```
GET /api/questionnaires
```

**请求头**:

```
Authorization: Bearer your_token
```

**查询参数**:

- `page`: 页码（默认为1）
- `limit`: 每页数量（默认为10）
- `search`: 搜索关键词

**响应**:

```json
{
  "items": [
    {
      "id": 1,
      "title": "健康状况调查",
      "description": "了解您的健康状况",
      "questions": [
        {
          "id": 1,
          "text": "您的身高是多少？",
          "type": "number",
          "required": true
        }
      ]
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### 获取问卷详情

```
GET /api/questionnaires/{questionnaire_id}
```

**请求头**:

```
Authorization: Bearer your_token
```

**响应**:

```json
{
  "id": 1,
  "title": "健康状况调查",
  "description": "了解您的健康状况",
  "questions": [
    {
      "id": 1,
      "text": "您的身高是多少？",
      "type": "number",
      "required": true
    }
  ]
}
```

### 创建问卷

```
POST /api/questionnaires
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "title": "健康状况调查",
  "description": "了解您的健康状况",
  "questions": [
    {
      "text": "您的身高是多少？",
      "type": "number",
      "required": true
    }
  ]
}
```

**响应**:

```json
{
  "id": 1,
  "title": "健康状况调查",
  "description": "了解您的健康状况",
  "questions": [
    {
      "id": 1,
      "text": "您的身高是多少？",
      "type": "number",
      "required": true
    }
  ]
}
```

### 提交问卷回答

```
POST /api/questionnaires/{questionnaire_id}/answers
```

**请求头**:

```
Authorization: Bearer your_token
```

**请求体**:

```json
{
  "custom_id": "P_00000002",
  "answers": [
    {
      "question_id": 1,
      "answer": "175"
    }
  ]
}
```

**响应**:

```json
{
  "id": 1,
  "custom_id": "P_00000002",
  "questionnaire_id": 1,
  "answers": [
    {
      "question_id": 1,
      "answer": "175"
    }
  ],
  "submitted_at": "2023-01-01T12:00:00Z"
}
```

## 错误处理

所有API请求可能返回以下错误：

### 认证错误

```json
{
  "detail": "未提供有效的认证凭据"
}
```

### 权限错误

```json
{
  "detail": "没有足够的权限执行此操作"
}
```

### 资源不存在

```json
{
  "detail": "请求的资源不存在"
}
```

### 验证错误

```json
{
  "detail": [
    {
      "loc": ["body", "username"],
      "msg": "字段不能为空",
      "type": "value_error.missing"
    }
  ]
}
```

## 安全建议

1. 始终使用HTTPS进行API通信
2. 定期轮换访问令牌
3. 使用强密码并启用双因素认证
4. 限制API请求频率，防止暴力破解
5. 定期检查活动会话并撤销可疑会话
