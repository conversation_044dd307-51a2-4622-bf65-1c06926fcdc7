#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理工具模块
提供文件上传、下载、存储、压缩、图片处理等功能
"""

import os
import shutil
import zipfile
import tarfile
import mimetypes
import hashlib
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional, Union, BinaryIO, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import aiofiles
import aiofiles.os
from PIL import Image, ImageOps
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
    magic = None

import asyncio
from urllib.parse import quote, unquote
import json
import tempfile

from .env_config import env_config
from .logging_utils import get_logger
from .monitoring_utils import get_metrics_collector, increment_counter, record_timer
from .security_utils import generate_secure_token

class FileType(str, Enum):
    """文件类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    ARCHIVE = "archive"
    TEXT = "text"
    BINARY = "binary"
    OTHER = "other"

class StorageType(str, Enum):
    """存储类型枚举"""
    LOCAL = "local"
    S3 = "s3"
    OSS = "oss"
    COS = "cos"
    MINIO = "minio"

class CompressionType(str, Enum):
    """压缩类型枚举"""
    ZIP = "zip"
    TAR = "tar"
    TAR_GZ = "tar.gz"
    TAR_BZ2 = "tar.bz2"

class ImageFormat(str, Enum):
    """图片格式枚举"""
    JPEG = "JPEG"
    PNG = "PNG"
    WEBP = "WEBP"
    GIF = "GIF"
    BMP = "BMP"
    TIFF = "TIFF"

@dataclass
class FileInfo:
    """文件信息"""
    id: str
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    file_type: FileType
    mime_type: str
    file_hash: str
    upload_time: datetime = field(default_factory=datetime.now)
    last_access_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    is_public: bool = False
    expires_at: Optional[datetime] = None
    download_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "filename": self.filename,
            "original_filename": self.original_filename,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_type": self.file_type.value,
            "mime_type": self.mime_type,
            "file_hash": self.file_hash,
            "upload_time": self.upload_time.isoformat(),
            "last_access_time": self.last_access_time.isoformat() if self.last_access_time else None,
            "metadata": self.metadata,
            "tags": self.tags,
            "is_public": self.is_public,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "download_count": self.download_count
        }

@dataclass
class UploadConfig:
    """上传配置"""
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_extensions: List[str] = field(default_factory=lambda: [
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',  # 图片
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv',    # 视频
        '.mp3', '.wav', '.flac', '.aac', '.ogg',          # 音频
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',  # 文档
        '.txt', '.csv', '.json', '.xml', '.yaml', '.yml',  # 文本
        '.zip', '.rar', '.7z', '.tar', '.gz'              # 压缩包
    ])
    allowed_mime_types: List[str] = field(default_factory=lambda: [
        'image/*', 'video/*', 'audio/*', 'application/pdf',
        'application/msword', 'application/vnd.ms-excel',
        'text/plain', 'text/csv', 'application/json'
    ])
    upload_path: str = "uploads"
    create_thumbnails: bool = True
    thumbnail_sizes: List[Tuple[int, int]] = field(default_factory=lambda: [
        (150, 150), (300, 300), (800, 600)
    ])
    auto_orient: bool = True
    quality: int = 85

@dataclass
class ThumbnailInfo:
    """缩略图信息"""
    size: Tuple[int, int]
    file_path: str
    file_size: int

class FileValidator:
    """文件验证器"""
    
    def __init__(self, config: UploadConfig):
        self.config = config
        self.logger = get_logger()
    
    def validate_file(
        self,
        filename: str,
        file_size: int,
        content: Optional[bytes] = None
    ) -> Tuple[bool, Optional[str]]:
        """验证文件"""
        # 检查文件大小
        if file_size > self.config.max_file_size:
            return False, f"File size {file_size} exceeds maximum allowed size {self.config.max_file_size}"
        
        # 检查文件扩展名
        file_ext = Path(filename).suffix.lower()
        if file_ext not in self.config.allowed_extensions:
            return False, f"File extension {file_ext} is not allowed"
        
        # 检查MIME类型
        if content and HAS_MAGIC:
            mime_type = magic.from_buffer(content, mime=True)
            if not self._is_mime_type_allowed(mime_type):
                return False, f"MIME type {mime_type} is not allowed"
        
        return True, None
    
    def _is_mime_type_allowed(self, mime_type: str) -> bool:
        """检查MIME类型是否允许"""
        for allowed_pattern in self.config.allowed_mime_types:
            if allowed_pattern.endswith('*'):
                if mime_type.startswith(allowed_pattern[:-1]):
                    return True
            elif mime_type == allowed_pattern:
                return True
        return False
    
    def get_file_type(self, mime_type: str) -> FileType:
        """根据MIME类型获取文件类型"""
        if mime_type.startswith('image/'):
            return FileType.IMAGE
        elif mime_type.startswith('video/'):
            return FileType.VIDEO
        elif mime_type.startswith('audio/'):
            return FileType.AUDIO
        elif mime_type in ['application/pdf', 'application/msword', 
                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return FileType.DOCUMENT
        elif mime_type in ['application/zip', 'application/x-rar-compressed', 
                          'application/x-7z-compressed']:
            return FileType.ARCHIVE
        elif mime_type.startswith('text/'):
            return FileType.TEXT
        else:
            return FileType.OTHER

class ImageProcessor:
    """图片处理器"""
    
    def __init__(self, config: UploadConfig):
        self.config = config
        self.logger = get_logger()
    
    async def process_image(
        self,
        image_path: str,
        output_dir: str,
        create_thumbnails: bool = True
    ) -> List[ThumbnailInfo]:
        """处理图片"""
        thumbnails = []
        
        try:
            # 在线程池中处理图片
            loop = asyncio.get_event_loop()
            thumbnails = await loop.run_in_executor(
                None, self._process_image_sync, image_path, output_dir, create_thumbnails
            )
        except Exception as e:
            self.logger.error(f"Failed to process image {image_path}: {e}")
        
        return thumbnails
    
    def _process_image_sync(
        self,
        image_path: str,
        output_dir: str,
        create_thumbnails: bool
    ) -> List[ThumbnailInfo]:
        """同步处理图片"""
        thumbnails = []
        
        with Image.open(image_path) as img:
            # 自动旋转
            if self.config.auto_orient:
                img = ImageOps.exif_transpose(img)
            
            # 转换为RGB模式（如果需要）
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # 创建缩略图
            if create_thumbnails:
                for size in self.config.thumbnail_sizes:
                    thumbnail_path = self._generate_thumbnail_path(image_path, output_dir, size)
                    
                    # 创建缩略图
                    thumbnail = img.copy()
                    thumbnail.thumbnail(size, Image.Resampling.LANCZOS)
                    
                    # 保存缩略图
                    os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
                    thumbnail.save(thumbnail_path, quality=self.config.quality, optimize=True)
                    
                    # 获取文件大小
                    thumbnail_size = os.path.getsize(thumbnail_path)
                    
                    thumbnails.append(ThumbnailInfo(
                        size=size,
                        file_path=thumbnail_path,
                        file_size=thumbnail_size
                    ))
        
        return thumbnails
    
    def _generate_thumbnail_path(self, original_path: str, output_dir: str, size: Tuple[int, int]) -> str:
        """生成缩略图路径"""
        path = Path(original_path)
        filename = f"{path.stem}_{size[0]}x{size[1]}{path.suffix}"
        return os.path.join(output_dir, "thumbnails", filename)

class FileCompressor:
    """文件压缩器"""
    
    def __init__(self):
        self.logger = get_logger()
    
    async def compress_files(
        self,
        file_paths: List[str],
        output_path: str,
        compression_type: CompressionType = CompressionType.ZIP,
        compression_level: int = 6
    ) -> bool:
        """压缩文件"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, self._compress_files_sync, file_paths, output_path, compression_type, compression_level
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to compress files: {e}")
            return False
    
    def _compress_files_sync(
        self,
        file_paths: List[str],
        output_path: str,
        compression_type: CompressionType,
        compression_level: int
    ):
        """同步压缩文件"""
        if compression_type == CompressionType.ZIP:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=compression_level) as zf:
                for file_path in file_paths:
                    if os.path.isfile(file_path):
                        arcname = os.path.basename(file_path)
                        zf.write(file_path, arcname)
        
        elif compression_type in [CompressionType.TAR, CompressionType.TAR_GZ, CompressionType.TAR_BZ2]:
            mode_map = {
                CompressionType.TAR: 'w',
                CompressionType.TAR_GZ: 'w:gz',
                CompressionType.TAR_BZ2: 'w:bz2'
            }
            
            with tarfile.open(output_path, mode_map[compression_type]) as tf:
                for file_path in file_paths:
                    if os.path.isfile(file_path):
                        arcname = os.path.basename(file_path)
                        tf.add(file_path, arcname)
    
    async def extract_archive(
        self,
        archive_path: str,
        extract_dir: str,
        password: Optional[str] = None
    ) -> List[str]:
        """解压缩文件"""
        try:
            loop = asyncio.get_event_loop()
            extracted_files = await loop.run_in_executor(
                None, self._extract_archive_sync, archive_path, extract_dir, password
            )
            return extracted_files
        except Exception as e:
            self.logger.error(f"Failed to extract archive {archive_path}: {e}")
            return []
    
    def _extract_archive_sync(
        self,
        archive_path: str,
        extract_dir: str,
        password: Optional[str] = None
    ) -> List[str]:
        """同步解压缩文件"""
        extracted_files = []
        
        # 创建解压目录
        os.makedirs(extract_dir, exist_ok=True)
        
        file_ext = Path(archive_path).suffix.lower()
        
        if file_ext == '.zip':
            with zipfile.ZipFile(archive_path, 'r') as zf:
                if password:
                    zf.setpassword(password.encode())
                zf.extractall(extract_dir)
                extracted_files = [os.path.join(extract_dir, name) for name in zf.namelist()]
        
        elif file_ext in ['.tar', '.gz', '.bz2']:
            with tarfile.open(archive_path, 'r:*') as tf:
                tf.extractall(extract_dir)
                extracted_files = [os.path.join(extract_dir, name) for name in tf.getnames()]
        
        return extracted_files

class LocalStorage:
    """本地存储"""
    
    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger()
    
    async def save_file(self, file_content: bytes, file_path: str) -> bool:
        """保存文件"""
        try:
            full_path = self.base_path / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(full_path, 'wb') as f:
                await f.write(file_content)
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to save file {file_path}: {e}")
            return False
    
    async def load_file(self, file_path: str) -> Optional[bytes]:
        """加载文件"""
        try:
            full_path = self.base_path / file_path
            if not full_path.exists():
                return None
            
            async with aiofiles.open(full_path, 'rb') as f:
                return await f.read()
        except Exception as e:
            self.logger.error(f"Failed to load file {file_path}: {e}")
            return None
    
    async def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            full_path = self.base_path / file_path
            if full_path.exists():
                await aiofiles.os.remove(full_path)
            return True
        except Exception as e:
            self.logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    async def file_exists(self, file_path: str) -> bool:
        """检查文件是否存在"""
        full_path = self.base_path / file_path
        return full_path.exists()
    
    async def get_file_size(self, file_path: str) -> Optional[int]:
        """获取文件大小"""
        try:
            full_path = self.base_path / file_path
            if full_path.exists():
                return full_path.stat().st_size
            return None
        except Exception as e:
            self.logger.error(f"Failed to get file size {file_path}: {e}")
            return None

class FileManager:
    """文件管理器"""
    
    def __init__(self, config: Optional[UploadConfig] = None):
        self.config = config or UploadConfig()
        self.validator = FileValidator(self.config)
        self.image_processor = ImageProcessor(self.config)
        self.compressor = FileCompressor()
        
        # 初始化存储
        self.storage = LocalStorage(self.config.upload_path)
        
        # 文件信息存储
        self.files: Dict[str, FileInfo] = {}
        
        self.logger = get_logger()
        self.metrics = get_metrics_collector()
    
    async def upload_file(
        self,
        file_content: bytes,
        filename: str,
        user_id: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        is_public: bool = False,
        expires_in_days: Optional[int] = None
    ) -> Optional[FileInfo]:
        """上传文件"""
        start_time = datetime.now()
        
        try:
            # 验证文件
            is_valid, error_message = self.validator.validate_file(
                filename, len(file_content), file_content
            )
            if not is_valid:
                self.logger.warning(f"File validation failed: {error_message}")
                return None
            
            # 生成文件信息
            file_id = str(uuid.uuid4())
            file_hash = hashlib.sha256(file_content).hexdigest()
            mime_type = magic.from_buffer(file_content, mime=True) if HAS_MAGIC else 'application/octet-stream'
            file_type = self.validator.get_file_type(mime_type)
            
            # 生成文件路径
            file_ext = Path(filename).suffix
            safe_filename = f"{file_id}{file_ext}"
            date_path = datetime.now().strftime("%Y/%m/%d")
            file_path = f"{date_path}/{safe_filename}"
            
            # 保存文件
            if not await self.storage.save_file(file_content, file_path):
                return None
            
            # 计算过期时间
            expires_at = None
            if expires_in_days:
                expires_at = datetime.now() + timedelta(days=expires_in_days)
            
            # 创建文件信息
            file_info = FileInfo(
                id=file_id,
                filename=safe_filename,
                original_filename=filename,
                file_path=file_path,
                file_size=len(file_content),
                file_type=file_type,
                mime_type=mime_type,
                file_hash=file_hash,
                metadata=metadata or {},
                tags=tags or [],
                is_public=is_public,
                expires_at=expires_at
            )
            
            # 添加用户信息到元数据
            if user_id:
                file_info.metadata["user_id"] = user_id
            
            # 处理图片
            if file_type == FileType.IMAGE and self.config.create_thumbnails:
                full_path = self.storage.base_path / file_path
                thumbnail_dir = self.storage.base_path / date_path
                thumbnails = await self.image_processor.process_image(
                    str(full_path), str(thumbnail_dir)
                )
                file_info.metadata["thumbnails"] = [
                    {
                        "size": thumb.size,
                        "path": os.path.relpath(thumb.file_path, self.storage.base_path),
                        "size_bytes": thumb.file_size
                    }
                    for thumb in thumbnails
                ]
            
            # 存储文件信息
            self.files[file_id] = file_info
            
            # 记录指标
            processing_time = (datetime.now() - start_time).total_seconds()
            record_timer("file_upload_duration", processing_time, tags={"type": file_type.value})
            increment_counter("files_uploaded", tags={"type": file_type.value})
            
            self.logger.info(f"File uploaded successfully: {file_id} ({filename})")
            return file_info
        
        except Exception as e:
            self.logger.error(f"Failed to upload file {filename}: {e}")
            increment_counter("file_upload_errors")
            return None
    
    async def download_file(self, file_id: str, user_id: Optional[str] = None) -> Optional[Tuple[bytes, FileInfo]]:
        """下载文件"""
        try:
            file_info = self.files.get(file_id)
            if not file_info:
                return None
            
            # 检查权限
            if not file_info.is_public:
                if not user_id or file_info.metadata.get("user_id") != user_id:
                    self.logger.warning(f"Access denied for file {file_id} by user {user_id}")
                    return None
            
            # 检查是否过期
            if file_info.expires_at and file_info.expires_at < datetime.now():
                self.logger.warning(f"File {file_id} has expired")
                return None
            
            # 加载文件内容
            file_content = await self.storage.load_file(file_info.file_path)
            if file_content is None:
                return None
            
            # 更新访问时间和下载次数
            file_info.last_access_time = datetime.now()
            file_info.download_count += 1
            
            increment_counter("files_downloaded", tags={"type": file_info.file_type.value})
            
            self.logger.debug(f"File downloaded: {file_id}")
            return file_content, file_info
        
        except Exception as e:
            self.logger.error(f"Failed to download file {file_id}: {e}")
            return None
    
    async def delete_file(self, file_id: str, user_id: Optional[str] = None) -> bool:
        """删除文件"""
        try:
            file_info = self.files.get(file_id)
            if not file_info:
                return False
            
            # 检查权限
            if not file_info.is_public:
                if not user_id or file_info.metadata.get("user_id") != user_id:
                    self.logger.warning(f"Access denied for deleting file {file_id} by user {user_id}")
                    return False
            
            # 删除主文件
            await self.storage.delete_file(file_info.file_path)
            
            # 删除缩略图
            if "thumbnails" in file_info.metadata:
                for thumbnail in file_info.metadata["thumbnails"]:
                    await self.storage.delete_file(thumbnail["path"])
            
            # 从内存中移除
            del self.files[file_id]
            
            increment_counter("files_deleted", tags={"type": file_info.file_type.value})
            
            self.logger.info(f"File deleted: {file_id}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to delete file {file_id}: {e}")
            return False
    
    async def get_file_info(self, file_id: str) -> Optional[FileInfo]:
        """获取文件信息"""
        return self.files.get(file_id)
    
    async def list_files(
        self,
        user_id: Optional[str] = None,
        file_type: Optional[FileType] = None,
        tags: Optional[List[str]] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[FileInfo]:
        """列出文件"""
        files = list(self.files.values())
        
        # 过滤条件
        if user_id:
            files = [f for f in files if f.metadata.get("user_id") == user_id or f.is_public]
        
        if file_type:
            files = [f for f in files if f.file_type == file_type]
        
        if tags:
            files = [f for f in files if any(tag in f.tags for tag in tags)]
        
        # 排序（按上传时间倒序）
        files.sort(key=lambda f: f.upload_time, reverse=True)
        
        # 分页
        return files[offset:offset + limit]
    
    async def search_files(
        self,
        query: str,
        user_id: Optional[str] = None,
        file_type: Optional[FileType] = None,
        limit: int = 50
    ) -> List[FileInfo]:
        """搜索文件"""
        files = list(self.files.values())
        
        # 权限过滤
        if user_id:
            files = [f for f in files if f.metadata.get("user_id") == user_id or f.is_public]
        
        # 类型过滤
        if file_type:
            files = [f for f in files if f.file_type == file_type]
        
        # 文本搜索
        query_lower = query.lower()
        matching_files = []
        
        for file_info in files:
            if (
                query_lower in file_info.original_filename.lower() or
                query_lower in file_info.filename.lower() or
                any(query_lower in tag.lower() for tag in file_info.tags) or
                any(query_lower in str(v).lower() for v in file_info.metadata.values())
            ):
                matching_files.append(file_info)
        
        # 排序
        matching_files.sort(key=lambda f: f.upload_time, reverse=True)
        
        return matching_files[:limit]
    
    async def create_archive(
        self,
        file_ids: List[str],
        archive_name: str,
        compression_type: CompressionType = CompressionType.ZIP,
        user_id: Optional[str] = None
    ) -> Optional[str]:
        """创建文件压缩包"""
        try:
            # 收集文件路径
            file_paths = []
            for file_id in file_ids:
                file_info = self.files.get(file_id)
                if not file_info:
                    continue
                
                # 检查权限
                if not file_info.is_public:
                    if not user_id or file_info.metadata.get("user_id") != user_id:
                        continue
                
                full_path = self.storage.base_path / file_info.file_path
                if full_path.exists():
                    file_paths.append(str(full_path))
            
            if not file_paths:
                return None
            
            # 生成压缩包路径
            archive_id = str(uuid.uuid4())
            archive_ext = ".zip" if compression_type == CompressionType.ZIP else ".tar.gz"
            archive_filename = f"{archive_name}_{archive_id}{archive_ext}"
            archive_path = self.storage.base_path / "archives" / archive_filename
            archive_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建压缩包
            success = await self.compressor.compress_files(
                file_paths, str(archive_path), compression_type
            )
            
            if success:
                # 创建压缩包文件信息
                archive_size = archive_path.stat().st_size
                archive_info = FileInfo(
                    id=archive_id,
                    filename=archive_filename,
                    original_filename=archive_name,
                    file_path=f"archives/{archive_filename}",
                    file_size=archive_size,
                    file_type=FileType.ARCHIVE,
                    mime_type="application/zip" if compression_type == CompressionType.ZIP else "application/gzip",
                    file_hash=hashlib.sha256(archive_path.read_bytes()).hexdigest(),
                    metadata={"user_id": user_id, "source_files": file_ids},
                    expires_at=datetime.now() + timedelta(hours=24)  # 24小时后过期
                )
                
                self.files[archive_id] = archive_info
                
                increment_counter("archives_created")
                self.logger.info(f"Archive created: {archive_id} ({archive_name})")
                return archive_id
            
            return None
        
        except Exception as e:
            self.logger.error(f"Failed to create archive: {e}")
            return None
    
    async def cleanup_expired_files(self):
        """清理过期文件"""
        now = datetime.now()
        expired_files = []
        
        for file_id, file_info in self.files.items():
            if file_info.expires_at and file_info.expires_at < now:
                expired_files.append(file_id)
        
        for file_id in expired_files:
            await self.delete_file(file_id)
        
        if expired_files:
            self.logger.info(f"Cleaned up {len(expired_files)} expired files")
    
    async def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计"""
        total_files = len(self.files)
        total_size = sum(f.file_size for f in self.files.values())
        
        type_stats = {}
        for file_info in self.files.values():
            file_type = file_info.file_type.value
            if file_type not in type_stats:
                type_stats[file_type] = {"count": 0, "size": 0}
            type_stats[file_type]["count"] += 1
            type_stats[file_type]["size"] += file_info.file_size
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "by_type": type_stats,
            "upload_path": str(self.storage.base_path)
        }

# 全局实例
_file_manager: Optional[FileManager] = None

def get_file_manager() -> FileManager:
    """获取文件管理器"""
    global _file_manager
    if _file_manager is None:
        config = UploadConfig()
        # 从环境配置中读取设置
        app_config = env_config
        if hasattr(app_config, 'UPLOAD_PATH'):
            config.upload_path = app_config.UPLOAD_PATH
        if hasattr(app_config, 'MAX_FILE_SIZE'):
            config.max_file_size = app_config.MAX_FILE_SIZE
        
        _file_manager = FileManager(config)
    return _file_manager

# 便捷函数
async def upload_file(
    file_content: bytes,
    filename: str,
    user_id: Optional[str] = None,
    tags: Optional[List[str]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    is_public: bool = False,
    expires_in_days: Optional[int] = None
) -> Optional[FileInfo]:
    """上传文件"""
    manager = get_file_manager()
    return await manager.upload_file(
        file_content, filename, user_id, tags, metadata, is_public, expires_in_days
    )

async def download_file(file_id: str, user_id: Optional[str] = None) -> Optional[Tuple[bytes, FileInfo]]:
    """下载文件"""
    manager = get_file_manager()
    return await manager.download_file(file_id, user_id)

async def delete_file(file_id: str, user_id: Optional[str] = None) -> bool:
    """删除文件"""
    manager = get_file_manager()
    return await manager.delete_file(file_id, user_id)

async def get_file_info(file_id: str) -> Optional[FileInfo]:
    """获取文件信息"""
    manager = get_file_manager()
    return await manager.get_file_info(file_id)

async def list_user_files(
    user_id: str,
    file_type: Optional[FileType] = None,
    tags: Optional[List[str]] = None,
    limit: int = 50,
    offset: int = 0
) -> List[FileInfo]:
    """列出用户文件"""
    manager = get_file_manager()
    return await manager.list_files(user_id, file_type, tags, limit, offset)

async def search_files(
    query: str,
    user_id: Optional[str] = None,
    file_type: Optional[FileType] = None,
    limit: int = 50
) -> List[FileInfo]:
    """搜索文件"""
    manager = get_file_manager()
    return await manager.search_files(query, user_id, file_type, limit)

async def create_file_archive(
    file_ids: List[str],
    archive_name: str,
    compression_type: CompressionType = CompressionType.ZIP,
    user_id: Optional[str] = None
) -> Optional[str]:
    """创建文件压缩包"""
    manager = get_file_manager()
    return await manager.create_archive(file_ids, archive_name, compression_type, user_id)

# 初始化函数
async def init_file_manager():
    """初始化文件管理器"""
    manager = get_file_manager()
    
    # 创建必要的目录
    upload_path = Path(manager.config.upload_path)
    upload_path.mkdir(parents=True, exist_ok=True)
    (upload_path / "thumbnails").mkdir(exist_ok=True)
    (upload_path / "archives").mkdir(exist_ok=True)
    
    logger = get_logger()
    logger.info(f"File manager initialized with upload path: {upload_path}")

# 定期清理任务
async def cleanup_expired_files():
    """清理过期文件（定期任务）"""
    manager = get_file_manager()
    await manager.cleanup_expired_files()