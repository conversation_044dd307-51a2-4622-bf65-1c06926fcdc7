# 问卷/量表填写与历史答卷界面
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # mobile目录
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from kivy.uix.screenmanager import Screen
from kivy.properties import ListProperty, ObjectProperty, StringProperty
from kivy.clock import Clock
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import MDList, MDListItem, MDListItemHeadlineText, MDListItemSupportingText, MDListItemTrailingIcon
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from utils.survey_manager import get_survey_manager
from utils.user_manager import get_user_manager
from kivy.lang import Builder
from kivy.uix.scrollview import ScrollView
from functools import partial
import threading
import json
import logging
from datetime import datetime
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.factory import Factory
from screens.base_screen import BaseScreen

# 导入API客户端
from api.api_client import APIClient

# 导入认证管理器
from utils.auth_manager import get_auth_manager, AuthManager

# 导入云端API客户端
from utils.cloud_api import get_cloud_api

# 导入主题
from theme import AppTheme, AppMetrics

# 导入选选项卡组件
from kivymd.uix.tab import MDTabsItem

# 导入Logo组件
from widgets.logo import HealthLogo

# 设置日志
logger = logging.getLogger(__name__)

KV = '''
<SurveyScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "评估量表与问卷"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.refresh_data()

        # Logo区域
        MDBoxLayout:
            id: logo_container_wrap
            orientation: 'vertical'
            size_hint_y: None
            height: dp(120)
            padding: [0, dp(0), 0, dp(0)]

            # Logo主容器
            MDBoxLayout:
                id: logo_container
                orientation: 'vertical'
                size_hint_y: None
                height: dp(110)

                # 使用统一的HealthLogo组件
                HealthLogo:
                    id: health_logo

        # 内容区域
        MDCard:
            orientation: 'vertical'
            size_hint_y: 1
            md_bg_color: app.theme.CARD_BACKGROUND
            radius: [dp(12)]
            elevation: 2
            padding: [dp(16), dp(16), dp(16), dp(16)]

            # 选项卡标题
            MDBoxLayout:
                size_hint_y: None
                height: dp(48)
                spacing: dp(10)

                MDButton:
                    id: assessment_tab
                    text: "评估量表"
                    on_release: root.switch_tab("assessments")
                    size_hint_x: 0.33
                    style: "filled" if root.current_tab == "assessments" else "outlined"
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == "assessments" else [0,0,0,0]
                    
                    MDButtonText:
                        text: "评估量表"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT if root.current_tab == "assessments" else app.theme.PRIMARY_COLOR

                MDButton:
                    id: questionnaire_tab
                    text: "问卷"
                    on_release: root.switch_tab("questionnaires")
                    size_hint_x: 0.33
                    style: "filled" if root.current_tab == "questionnaires" else "outlined"
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == "questionnaires" else [0,0,0,0]
                    
                    MDButtonText:
                        text: "问卷"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT if root.current_tab == "questionnaires" else app.theme.PRIMARY_COLOR

                MDButton:
                    id: history_tab
                    text: "历史记录"
                    on_release: root.switch_tab("history")
                    size_hint_x: 0.33
                    style: "filled" if root.current_tab == "history" else "outlined"
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == "history" else [0,0,0,0]
                    
                    MDButtonText:
                        text: "历史记录"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT if root.current_tab == "history" else app.theme.PRIMARY_COLOR

            # 内容区域
            MDBoxLayout:
                id: content_area
                orientation: 'vertical'

                # 评估量表内容
                MDBoxLayout:
                    id: assessments_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 1 if root.current_tab == "assessments" else 0
                    size_hint_y: 1 if root.current_tab == "assessments" else 0
                    height: dp(400) if root.current_tab == "assessments" else 0

                    # 刷新按钮
                    MDButton:
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        on_release: root.load_assessments()
                        
                        MDButtonText:
                            text: "刷新量表列表"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_LIGHT
                    
                    # 量表列表
                    ScrollView:
                        do_scroll_x: False
                        do_scroll_y: True
                        size_hint_y: 1
                        
                        MDList:
                            id: assessment_list
                            padding: dp(0)
                            spacing: dp(1)
                
                # 问卷内容
                MDBoxLayout:
                    id: questionnaires_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 1 if root.current_tab == "questionnaires" else 0
                    size_hint_y: 1 if root.current_tab == "questionnaires" else 0
                    height: dp(400) if root.current_tab == "questionnaires" else 0
                    
                    # 刷新按钮
                    MDButton:
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        on_release: root.load_questionnaires()
                        
                        MDButtonText:
                            text: "刷新问卷列表"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_LIGHT
                    
                    # 问卷列表
                    ScrollView:
                        do_scroll_x: False
                        do_scroll_y: True
                        size_hint_y: 1
                        
                        MDList:
                            id: questionnaire_list
                            padding: dp(0)
                            spacing: dp(1)
                
                # 历史记录内容
                MDBoxLayout:
                    id: history_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 1 if root.current_tab == "history" else 0
                    size_hint_y: 1 if root.current_tab == "history" else 0
                    height: self.minimum_height if root.current_tab == "history" else 0
                    
                    # 刷新按钮
                    MDButton:
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        on_release: root.load_response_history()
                        
                        MDButtonText:
                            text: "刷新历史记录"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_LIGHT
                    
                    # 历史记录列表
                    ScrollView:
                        do_scroll_x: False
                        do_scroll_y: True
                        size_hint_y: 1
                        bar_width: dp(4)
                        bar_color: app.theme.PRIMARY_LIGHT
                        
                        MDList:
                            id: response_history_list
                            padding: dp(8)
                            spacing: dp(8)
                            size_hint_y: None
                            height: self.minimum_height
                            size_hint_y: None
            height: dp(32)
'''

class SurveyScreen(BaseScreen):
    questionnaire_list = ListProperty([])
    questionnaires = ListProperty([])  # 添加缺失的questionnaires属性
    assessment_list = ListProperty([])
    assessments = ListProperty([])  # 添加缺失的assessments属性
    current_user = ObjectProperty(None)
    selected_questionnaire = ObjectProperty(None)
    selected_assessment = ObjectProperty(None)
    response_history = ListProperty([])
    api_client = None
    current_tab = StringProperty("assessments")
    _is_active = False
    _last_assessments_result = None
    _last_assessment_list = None
    _last_questionnaires_result = None
    _last_questionnaire_list = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.api_client = APIClient()
        self.current_tab = "assessments"
        self.assessment_list = []
        self.assessments = []  # 初始化assessments属性
        self.questionnaire_list = []
        self.questionnaires = []  # 初始化questionnaires属性
        self.response_history = []
        # 初始化认证管理器
        self.auth_manager = get_auth_manager()
        # 添加app属性，确保可以访问应用实例
        self.app = MDApp.get_running_app()
        # 添加活动标志，用于防止在组件销毁后访问
        self._is_active = False
        Clock.schedule_once(self.init_ui, 0.2)
        # 注册和KV加载已经在文件末尾完成，这里不需要重复

    def on_pre_enter(self, *args):
        # 获取当前用户信息
        from utils.user_manager import get_user_manager
        user_manager = get_user_manager()
        self.current_user = user_manager.get_current_user()
        logger.info(f"SurveyScreen: 获取当前用户信息: {self.current_user}")
        if self.current_user:
            logger.info(f"SurveyScreen: 当前用户custom_id: {getattr(self.current_user, 'custom_id', None)}")
        else:
            logger.warning("SurveyScreen: 未获取到当前用户信息")
        
        self.init_ui()

    def init_ui(self, dt=None):
        """初始化UI组件"""
        try:
            logger.info("SurveyScreen: 初始化UI组件")
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("SurveyScreen: ids未初始化，无法初始化UI")
                return
                
            # 确保组件存在
            required_ids = ['assessment_list', 'questionnaire_list']
            for widget_id in required_ids:
                if widget_id not in self.ids:
                    logger.error(f"SurveyScreen: {widget_id}不存在，UI初始化失败")
                    return
                    
            # 加载初始数据
            self.load_assessments()
            
            # 设置当前标签页
            self.switch_tab(self.current_tab)
            
            logger.info("SurveyScreen: UI初始化完成")
        except Exception as e:
            logger.error(f"SurveyScreen: 初始化UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def go_back(self):
        """返回上一个屏幕"""
        self.manager.current = 'health_data_management_screen'

    def switch_tab(self, tab_name):
        """切换标签页

        Args:
            tab_name: 标签页名称，可以是"assessments", "questionnaires", "history"
        """
        try:
            logger.info(f"SurveyScreen: 切换到标签页 {tab_name}")
            
            # 更新当前标签
            self.current_tab = tab_name
            
            # 检查self和self.ids是否有效
            if not self or not hasattr(self, 'ids'):
                logger.warning("SurveyScreen: 页面已被移除或ids不存在，无法切换标签页")
                return
                
            # 根据标签页重新加载对应数据
            if tab_name == "assessments":
                # 评估量表Tab：重新加载待完成的量表数据
                self.load_assessments()
            elif tab_name == "questionnaires":
                # 问卷Tab：重新加载待完成的问卷数据
                self.load_questionnaires()
            elif tab_name == "history":
                # 历史记录Tab：重新加载已完成的数据
                self.load_response_history()
        except Exception as e:
            logger.error(f"SurveyScreen: 切换标签页时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
    def on_enter(self):
        """进入屏幕时的处理"""
        try:
            logger.info("SurveyScreen: 进入页面")
            # 调用父类方法确保正确初始化
            super().on_enter()
            # 标记页面为活跃状态
            self._is_active = True
            
            # 确保UI已初始化
            if not hasattr(self, 'ids') or not self.ids:
                Clock.schedule_once(self.init_ui, 0.1)
                return
                
            # 只在首次进入或数据为空时加载数据，避免频繁加载
            if not hasattr(self, '_data_loaded') or not self._data_loaded:
                # 根据当前标签加载相应数据
                if self.current_tab == "assessments":
                    self.load_assessments()
                elif self.current_tab == "questionnaires":
                    self.load_questionnaires()
                elif self.current_tab == "history":
                    self.load_response_history()
                
                self._data_loaded = True
            else:
                # 如果数据已加载，但切换了标签，需要重新加载对应数据
                if hasattr(self, '_last_tab') and self._last_tab != self.current_tab:
                    if self.current_tab == "assessments":
                        self.load_assessments()
                    elif self.current_tab == "questionnaires":
                        self.load_questionnaires()
                    elif self.current_tab == "history":
                        self.load_response_history()
            
            # 记录当前标签
            self._last_tab = self.current_tab
                
            # 减少分发通知检查频率，只在必要时检查
            if not hasattr(self, '_last_distribution_check') or \
               (datetime.now() - self._last_distribution_check).seconds > 300:  # 5分钟检查一次
                self.check_distributions()
                self._last_distribution_check = datetime.now()
        except Exception as e:
            logger.error(f"SurveyScreen: 进入页面时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def load_assessments(self, dt=None):
        """加载分发给用户的评估量表列表"""
        try:
            logger.info("开始加载分发评估量表列表")
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载评估量表")
                return
            self.assessments = []
            
            # 根据当前标签页调用不同的API端点
            if self.current_tab == "assessments":
                # 评估量表Tab：调用pending-assessments端点获取待完成量表
                result = cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/pending-assessments",
                    headers={
                        'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                        'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                    }
                )
            elif self.current_tab == "history":
                # 历史记录Tab：调用history-assessments端点获取已完成量表
                result = cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/history-assessments",
                    headers={
                        'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                        'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                    }
                )
            else:
                # 其他情况：使用原有的API
                result = cloud_api.get_mobile_assessments()
            
            if result and result.get('status') == 'success':
                data = result.get('data', {})
                assessments_data = data if isinstance(data, list) else data.get('assessments', [])
                logger.info(f"成功获取分发评估量表列表，共 {len(assessments_data)} 条")
                self.assessments = assessments_data
                
                # 统计不同状态的量表数量
                pending_count = sum(1 for a in assessments_data if a.get('status') == 'pending')
                completed_count = sum(1 for a in assessments_data if a.get('status') == 'completed')
                logger.info(f"量表状态统计: pending={pending_count}, completed={completed_count}")
                
                # 添加详细的数据调试信息
                for i, assessment in enumerate(assessments_data[:3]):  # 只显示前3条以避免日志过长
                    logger.debug(f"量表{i+1}: id={assessment.get('id')}, name={assessment.get('name')}, title={assessment.get('title')}, status={assessment.get('status')}")
                    # 添加template信息调试
                    template_info = assessment.get('template', {})
                    logger.debug(f"量表{i+1} template信息: {template_info.keys() if template_info else 'None'}")
                    if template_info:
                        logger.debug(f"量表{i+1} template.description: {repr(template_info.get('description'))}")
                        logger.debug(f"量表{i+1} template.instructions: {repr(template_info.get('instructions'))}")
                
                # 根据当前标签页过滤数据
                self._update_assessment_list_by_tab()
            else:
                logger.warning(f"获取分发评估量表失败: {result.get('message', '') if result else '无响应'}")
            logger.info(f"总评估量表数量: {len(self.assessments)}")
        except Exception as e:
            logger.error(f"加载分发评估量表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _update_assessments_ui(self, dt=None):
        try:
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("ids未初始化，跳过量表UI刷新")
                return
            
            # 移除过于严格的活跃状态检测，只检查基本的页面状态
            if not self.parent and hasattr(self, '_is_active') and not self._is_active:
                logger.info("SurveyScreen: 页面已被移除且标记为不活跃，中断评估量表UI更新")
                return
                
            # 直接使用已经加载的数据，不再重新请求API
            if self.assessment_list:
                # 只在数据变化时刷新UI
                if self.assessment_list != getattr(self, '_last_assessment_list', None):
                    self._last_assessment_list = self.assessment_list
                    # 只刷新UI，不递归调用自身
                    self._refresh_assessment_list_ui()
            else:
                logger.warning("SurveyScreen: 量表数据为空")
                self._refresh_assessment_list_ui()  # 显示空状态
        except ReferenceError:
            logger.error("SurveyScreen: 页面已销毁，跳过UI更新")
        except Exception as e:
            logger.error(f"SurveyScreen: 更新评估量表UI时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _refresh_assessment_list_ui(self):
        try:
            if not hasattr(self, 'ids') or 'assessment_list' not in self.ids:
                logger.error("SurveyScreen: ids不存在或assessment_list不在ids中")
                return
                
            assessment_list_widget = self.ids.get('assessment_list')
            if not assessment_list_widget:
                logger.error("SurveyScreen: assessment_list_widget不存在")
                return
                
            try:
                assessment_list_widget.clear_widgets()
            except ReferenceError:
                logger.error("SurveyScreen: assessment_list_widget已被销毁，跳过UI刷新")
                return
                
            if not self.assessment_list:
                try:
                    assessment_list_widget.add_widget(MDLabel(text='暂无分发的量表', halign='center', theme_text_color='Hint'))
                except ReferenceError:
                    logger.error("SurveyScreen: assessment_list_widget已被销毁，跳过空状态UI")
                return
            
            # 使用集合去重，仅保留不同标题的量表
            unique_titles = set()
            unique_assessments = []
            
            for item in self.assessment_list:
                title = item.get('title') or item.get('name') or '未命名量表'
                if title not in unique_titles:
                    unique_titles.add(title)
                    unique_assessments.append(item)
            
            logger.info(f"SurveyScreen: 量表去重后数量: {len(unique_assessments)}/{len(self.assessment_list)}")
                
            for item in unique_assessments:
                try:
                    list_item = MDListItem(on_release=lambda x, item=item: self.on_assessment_selected(item))
                    list_item.add_widget(MDListItemHeadlineText(text=item.get('title') or item.get('name') or '未命名量表'))
                    # 优先显示template.description
                    description = (
                        (item.get('template', {}) or {}).get('description')
                        or item.get('description')
                        or '无描述'
                    )
                    if len(description) > 100:
                        description = description[:100] + '...'
                    list_item.add_widget(MDListItemSupportingText(text=description))
                    assessment_list_widget.add_widget(list_item)
                except ReferenceError:
                    logger.error("SurveyScreen: 添加量表项时发生ReferenceError，跳过该项")
                    continue
        except ReferenceError:
            logger.error("SurveyScreen: self.ids已被销毁，跳过量表列表UI刷新")
        except Exception as e:
            logger.error(f"SurveyScreen: 刷新量表列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def load_questionnaires(self, dt=None):
        """加载分发给用户的问卷列表"""
        try:
            logger.info("开始加载分发问卷列表")
            cloud_api = get_cloud_api()
            if not cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载问卷")
                return
            self.questionnaires = []
            
            # 根据当前标签页调用不同的API端点
            if self.current_tab == "questionnaires":
                # 问卷Tab：调用pending-questionnaires端点获取待完成问卷
                result = cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/pending-questionnaires",
                    headers={
                        'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                        'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                    }
                )
            elif self.current_tab == "history":
                # 历史记录Tab：调用history-questionnaires端点获取已完成问卷
                result = cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/history-questionnaires",
                    headers={
                        'Authorization': f"Bearer {cloud_api.token}" if cloud_api.token else None,
                        'X-User-ID': cloud_api.custom_id if cloud_api.custom_id else None
                    }
                )
            else:
                # 其他情况：使用原有的API
                result = cloud_api.get_mobile_questionnaires()
            
            if result and result.get('status') == 'success':
                data = result.get('data', {})
                questionnaires_data = data if isinstance(data, list) else data.get('questionnaires', [])
                logger.info(f"成功获取分发问卷列表，共 {len(questionnaires_data)} 条")
                self.questionnaires = questionnaires_data
                
                # 统计不同状态的问卷数量
                pending_count = sum(1 for q in questionnaires_data if q.get('status') == 'pending')
                completed_count = sum(1 for q in questionnaires_data if q.get('status') == 'completed')
                logger.info(f"问卷状态统计: pending={pending_count}, completed={completed_count}")
                
                # 添加详细的数据调试信息
                for i, questionnaire in enumerate(questionnaires_data[:3]):  # 只显示前3条以避免日志过长
                    logger.debug(f"问卷{i+1}: id={questionnaire.get('id')}, name={questionnaire.get('name')}, title={questionnaire.get('title')}, status={questionnaire.get('status')}")
                
                # 根据当前标签页过滤数据
                self._update_questionnaire_list_by_tab()
            else:
                logger.warning(f"获取分发问卷失败: {result.get('message', '') if result else '无响应'}")
            logger.info(f"总问卷数量: {len(self.questionnaires)}")
        except Exception as e:
            logger.error(f"加载分发问卷时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _update_questionnaires_ui(self, dt=None):
        try:
            if not hasattr(self, 'ids') or not self.ids:
                logger.warning("ids未初始化，跳过问卷UI刷新")
                return
            if not hasattr(self, '_is_active') or not self._is_active:
                logger.info("SurveyScreen: 页面已不活跃，中断问卷UI更新")
                return
            if not self.parent:
                logger.info("SurveyScreen: 页面已被移除，中断问卷UI更新")
                return
                
            # 直接使用已经加载的数据，不再重新请求API
            if self.questionnaire_list:
                # 只在数据变化时刷新UI
                if self.questionnaire_list != getattr(self, '_last_questionnaire_list', None):
                    self._last_questionnaire_list = self.questionnaire_list
                    # 只刷新UI，不递归调用自身
                    self._refresh_questionnaire_list_ui()
            else:
                logger.warning("SurveyScreen: 问卷数据为空")
                self._refresh_questionnaire_list_ui()  # 显示空状态
        except ReferenceError:
            logger.error("SurveyScreen: 页面已销毁，跳过UI更新")
        except Exception as e:
            logger.error(f"SurveyScreen: 更新问卷UI时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _refresh_questionnaire_list_ui(self):
        try:
            if not hasattr(self, 'ids') or 'questionnaire_list' not in self.ids:
                logger.error("SurveyScreen: ids不存在或questionnaire_list不在ids中")
                return
                
            questionnaire_list_widget = self.ids.get('questionnaire_list')
            if not questionnaire_list_widget:
                logger.error("SurveyScreen: questionnaire_list_widget不存在")
                return
                
            try:
                questionnaire_list_widget.clear_widgets()
            except ReferenceError:
                logger.error("SurveyScreen: questionnaire_list_widget已被销毁，跳过UI刷新")
                return
                
            if not self.questionnaire_list:
                try:
                    questionnaire_list_widget.add_widget(MDLabel(text='暂无分发的问卷', halign='center', theme_text_color='Hint'))
                except ReferenceError:
                    logger.error("SurveyScreen: questionnaire_list_widget已被销毁，跳过空状态UI")
                return
            
            # 使用集合去重，仅保留不同标题的问卷
            unique_titles = set()
            unique_questionnaires = []
            
            for item in self.questionnaire_list:
                title = item.get('title') or item.get('name') or '未命名问卷'
                if title not in unique_titles:
                    unique_titles.add(title)
                    unique_questionnaires.append(item)
            
            logger.info(f"SurveyScreen: 问卷去重后数量: {len(unique_questionnaires)}/{len(self.questionnaire_list)}")
                
            for item in unique_questionnaires:
                try:
                    list_item = MDListItem(on_release=lambda x, item=item: self.on_questionnaire_selected(item))
                    list_item.add_widget(MDListItemHeadlineText(text=item.get('title') or item.get('name') or '未命名问卷'))
                    # 优先显示template.description
                    description = (
                        (item.get('template', {}) or {}).get('description')
                        or item.get('description')
                        or '无描述'
                    )
                    if len(description) > 100:
                        description = description[:100] + '...'
                    list_item.add_widget(MDListItemSupportingText(text=description))
                    questionnaire_list_widget.add_widget(list_item)
                except ReferenceError:
                    logger.error("SurveyScreen: 添加问卷项时发生ReferenceError，跳过该项")
                    continue
        except ReferenceError:
            logger.error("SurveyScreen: self.ids已被销毁，跳过问卷列表UI刷新")
        except Exception as e:
            logger.error(f"SurveyScreen: 刷新问卷列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _update_assessment_list_by_tab(self):
        """根据当前标签页更新评估量表列表"""
        try:
            if self.current_tab == "assessments":
                # 过滤pending状态的量表，确保有名称
                self.assessment_list = [a for a in self.assessments if a.get('status') == 'pending' and (a.get('name') or a.get('title'))]
                logger.info(f"评估量表tab: 过滤后显示 {len(self.assessment_list)} 条pending状态量表")
                logger.debug(f"pending量表详情: {[{'id': a.get('id'), 'name': a.get('name'), 'status': a.get('status')} for a in self.assessment_list]}")
            elif self.current_tab == "history":
                # 过滤completed状态的量表，确保有名称
                self.assessment_list = [a for a in self.assessments if a.get('status') == 'completed' and (a.get('name') or a.get('title'))]
                logger.info(f"历史记录tab: 过滤后显示 {len(self.assessment_list)} 条completed状态量表")
                logger.debug(f"completed量表详情: {[{'id': a.get('id'), 'name': a.get('name'), 'status': a.get('status')} for a in self.assessment_list]}")
            else:
                self.assessment_list = [a for a in self.assessments if a.get('name') or a.get('title')]
                logger.info(f"其他tab: 显示所有 {len(self.assessment_list)} 条量表")
            
            # 数据过滤完成后，刷新UI显示
            self._refresh_assessment_list_ui()
        except Exception as e:
            logger.error(f"更新评估量表列表时出错: {e}")
            self.assessment_list = []
            # 即使出错也要刷新UI以显示空状态
            self._refresh_assessment_list_ui()
    
    def _update_questionnaire_list_by_tab(self):
        """根据当前标签页更新问卷列表"""
        try:
            if self.current_tab == "questionnaires":
                # 过滤pending状态的问卷，确保有名称
                self.questionnaire_list = [q for q in self.questionnaires if q.get('status') == 'pending' and (q.get('name') or q.get('title'))]
                logger.info(f"问卷tab: 过滤后显示 {len(self.questionnaire_list)} 条pending状态问卷")
                logger.debug(f"pending问卷详情: {[{'id': q.get('id'), 'name': q.get('name'), 'status': q.get('status')} for q in self.questionnaire_list]}")
            elif self.current_tab == "history":
                # 过滤completed状态的问卷，确保有名称
                self.questionnaire_list = [q for q in self.questionnaires if q.get('status') == 'completed' and (q.get('name') or q.get('title'))]
                logger.info(f"历史记录tab: 过滤后显示 {len(self.questionnaire_list)} 条completed状态问卷")
                logger.debug(f"completed问卷详情: {[{'id': q.get('id'), 'name': q.get('name'), 'status': q.get('status')} for q in self.questionnaire_list]}")
            else:
                self.questionnaire_list = [q for q in self.questionnaires if q.get('name') or q.get('title')]
                logger.info(f"其他tab: 显示所有 {len(self.questionnaire_list)} 条问卷")
            
            # 数据过滤完成后，刷新UI显示
            self._refresh_questionnaire_list_ui()
        except Exception as e:
            logger.error(f"更新问卷列表时出错: {e}")
            self.questionnaire_list = []
            # 即使出错也要刷新UI以显示空状态
            self._refresh_questionnaire_list_ui()
    
    def _update_history_list_by_tab(self):
        """根据当前标签页更新历史记录列表"""
        try:
            if self.current_tab == "history":
                # 在历史记录tab中，合并completed状态的量表和问卷
                completed_assessments = [a for a in self.assessments if a.get('status') == 'completed']
                completed_questionnaires = [q for q in self.questionnaires if q.get('status') == 'completed']
                
                # 为了区分类型，给每个项目添加type字段
                for item in completed_assessments:
                    item['item_type'] = 'assessment'
                for item in completed_questionnaires:
                    item['item_type'] = 'questionnaire'
                
                # 合并并按时间排序
                all_completed = completed_assessments + completed_questionnaires
                all_completed.sort(key=lambda x: x.get('created_at', ''), reverse=True)
                
                self.response_history = all_completed
                logger.info(f"历史记录tab: 显示 {len(completed_assessments)} 条completed量表和 {len(completed_questionnaires)} 条completed问卷")
                
                # 数据更新完成后，刷新UI显示
                Clock.schedule_once(self._update_response_history_ui, 0.1)
        except Exception as e:
            logger.error(f"更新历史记录列表时出错: {e}")
            self.response_history = []
            # 即使出错也要刷新UI以显示空状态
            Clock.schedule_once(self._update_response_history_ui, 0.1)

    def load_sample_data(self, data_type='assessments'):
        """加载样例数据，确保即使API无响应也能显示内容"""
        try:
            if data_type == 'assessments':
                sample_data = [
                    {
                        "id": "1",
                        "title": "健康自评量表",
                        "description": "评估个人整体健康状况",
                        "created_at": "2025-05-20",
                        "status": "pending"
                    },
                    {
                        "id": "2",
                        "title": "焦虑自评量表",
                        "description": "评估焦虑程度",
                        "created_at": "2025-05-21",
                        "status": "pending"
                    },
                    {
                        "id": "3",
                        "title": "抑郁自评量表",
                        "description": "评估抑郁风险",
                        "created_at": "2025-05-22",
                        "status": "completed"
                    }
                ]
                self.assessments = sample_data
                self._update_assessment_list_by_tab()
                
                # 安全更新UI，避免弱引用错误
                try:
                    # 检查self和self.ids是否有效
                    if not self or not self.parent or not hasattr(self, 'ids'):
                        logger.warning("SurveyScreen: 页面已被移除或ids不存在，无法更新评估量表UI")
                        return
                    
                    # 尝试安全访问assessment_list控件
                    assessment_list = getattr(self.ids, 'assessment_list', None)
                    if not assessment_list:
                        logger.warning("SurveyScreen: assessment_list控件不存在")
                        return
                    
                    # 清空并添加样例数据项
                    assessment_list.clear_widgets()
                    for item in self.assessment_list:
                        # 创建列表项
                        try:
                            list_item = MDListItem(on_release=lambda x, item=item: self.on_assessment_selected(item))
                            list_item.add_widget(MDListItemHeadlineText(text=item.get('title', '未命名量表')))
                            list_item.add_widget(MDListItemSupportingText(text=item.get('description', '无描述')))
                            assessment_list.add_widget(list_item)
                        except ReferenceError:
                            logger.error("SurveyScreen: 添加评估量表项时发生引用错误")
                            break
                except (ReferenceError, AttributeError) as e:
                    logger.error(f"SurveyScreen: 更新评估量表UI时发生错误: {e}")
                        
            elif data_type == 'questionnaires':
                sample_data = [
                    {
                        "id": "1",
                        "title": "生活习惯调查",
                        "description": "了解日常生活习惯",
                        "created_at": "2025-05-20",
                        "status": "pending"
                    },
                    {
                        "id": "2",
                        "title": "饮食习惯调查",
                        "description": "了解饮食结构和习惯",
                        "created_at": "2025-05-21",
                        "status": "pending"
                    },
                    {
                        "id": "3",
                        "title": "运动习惯调查",
                        "description": "了解日常运动情况",
                        "created_at": "2025-05-22",
                        "status": "completed"
                    }
                ]
                self.questionnaires = sample_data
                self._update_questionnaire_list_by_tab()
                
                # 安全更新UI，避免弱引用错误
                try:
                    # 检查self和self.ids是否有效
                    if not self or not self.parent or not hasattr(self, 'ids'):
                        logger.warning("SurveyScreen: 页面已被移除或ids不存在，无法更新问卷UI")
                        return
                    
                    # 尝试安全访问questionnaire_list控件
                    questionnaire_list = getattr(self.ids, 'questionnaire_list', None)
                    if not questionnaire_list:
                        logger.warning("SurveyScreen: questionnaire_list控件不存在")
                        return
                    
                    # 清空并添加样例数据项
                    questionnaire_list.clear_widgets()
                    for item in self.questionnaire_list:
                        # 创建列表项
                        try:
                            list_item = MDListItem(on_release=lambda x, item=item: self.on_questionnaire_selected(item))
                            list_item.add_widget(MDListItemHeadlineText(text=item.get('title', '未命名问卷')))
                            list_item.add_widget(MDListItemSupportingText(text=item.get('description', '无描述')))
                            questionnaire_list.add_widget(list_item)
                        except ReferenceError:
                            logger.error("SurveyScreen: 添加问卷项时发生引用错误")
                            break
                except (ReferenceError, AttributeError) as e:
                    logger.error(f"SurveyScreen: 更新问卷UI时发生错误: {e}")
        except Exception as e:
            logger.error(f"SurveyScreen: 加载样例数据失败: {e}")

    def load_response_history(self):
        # 取消之前的调度，避免多个回调同时运行
        Clock.unschedule(self._update_response_history_ui)
        
        # 设置当前标签为history，然后加载数据
        original_tab = self.current_tab
        self.current_tab = "history"
        
        # 确保已加载问卷和量表数据，因为历史记录需要合并它们
        if not self.assessments:
            self.load_assessments()
        if not self.questionnaires:
            self.load_questionnaires()
        
        # 恢复原始标签
        self.current_tab = original_tab
            
        # 使用新的回调函数名称，确保unschedule能正确取消
        Clock.schedule_once(self._update_response_history_ui)
    
    def _update_response_history_ui(self, dt):
        """更新回复历史UI的回调"""
        try:
            # 防止在回调过程中对象被销毁
            if not self.parent:
                logger.debug("SurveyScreen: 页面已被移除，中断回复历史UI更新")
                return
                
            # 移除过于严格的活跃状态检测，只检查基本的页面状态
            if hasattr(self, '_is_active') and not self._is_active:
                logger.debug("SurveyScreen: 页面已标记为不活跃，中断回复历史UI更新")
                return
                
            # 确保当前标签是历史记录标签
            if self.current_tab != "history":
                logger.debug(f"SurveyScreen: 当前标签为{self.current_tab}，不是历史记录标签，中断回复历史UI更新")
                return

            # 更安全地访问控件
            response_history_list = None
            try:
                if hasattr(self, 'ids') and self.ids:
                    # 检查response_history_list是否存在
                    if 'response_history_list' not in self.ids:
                        logger.warning("SurveyScreen: response_history_list不存在，可能需要重建UI")
                        # 检查历史记录标签页是否存在
                        if 'history_content' in self.ids:
                            # 重新创建历史记录列表
                            try:
                                history_content = self.ids.history_content
                                # 清空现有内容
                                for child in list(history_content.children):
                                    if isinstance(child, ScrollView):
                                        history_content.remove_widget(child)
                                
                                # 创建新的滚动视图和列表
                                from kivy.uix.scrollview import ScrollView
                                scroll = ScrollView(do_scroll_x=False, do_scroll_y=True, size_hint_y=1)
                                list_widget = MDList(id='response_history_list', padding=dp(0), spacing=dp(1))
                                scroll.add_widget(list_widget)
                                history_content.add_widget(scroll)
                                
                                # 更新ids
                                self.ids.response_history_list = list_widget
                                response_history_list = list_widget
                                logger.info("SurveyScreen: 重新创建response_history_list成功")
                            except Exception as rebuild_error:
                                logger.error(f"SurveyScreen: 重建response_history_list失败: {rebuild_error}")
                                return
                        else:
                            return
                    else:
                        response_history_list = self.ids.response_history_list
                else:
                    logger.warning("SurveyScreen: ids不存在，无法更新历史记录UI")
                    return
            except ReferenceError:
                logger.error("SurveyScreen: 访问ids时发生ReferenceError")
                return
            except Exception as e:
                logger.error(f"SurveyScreen: 获取response_history_list时出错: {e}")
                return
            
            if not response_history_list:
                logger.warning("SurveyScreen: response_history_list无效，无法更新历史记录UI")
                return
                
            # 获取历史数据
            try:
                # 从API获取已完成的量表和问卷报告数据（从assessment_results和questionnaire_results表）
                cloud_api = get_cloud_api()
                
                # 获取当前用户ID
                user_manager = get_user_manager()
                user = user_manager.get_current_user()
                custom_id = getattr(user, 'custom_id', None) if user else None
                
                if not custom_id:
                    logger.warning("未获取到有效的用户ID，无法获取历史报告")
                    self.response_history = []
                    return
                
                logger.info(f"获取用户 {custom_id} 的历史报告（从assessment_results和questionnaire_results表）")
                
                # 获取量表结果报告（从assessment_results表）
                assessment_reports = []
                try:
                    # 使用正确的API端点获取量表结果
                    assessment_reports_result = cloud_api.get(f"assessment-results/user/{custom_id}")
                    logger.info(f"量表报告API响应: {assessment_reports_result}")
                    
                    if assessment_reports_result:
                        # 处理不同的响应格式
                        if isinstance(assessment_reports_result, dict):
                            if assessment_reports_result.get("status") == "success":
                                assessment_reports = assessment_reports_result.get("data", [])
                            elif "data" in assessment_reports_result:
                                assessment_reports = assessment_reports_result["data"]
                            elif "results" in assessment_reports_result:
                                assessment_reports = assessment_reports_result["results"]
                            else:
                                # 可能整个响应就是数据列表
                                assessment_reports = [assessment_reports_result]
                        elif isinstance(assessment_reports_result, list):
                            assessment_reports = assessment_reports_result
                        
                        logger.info(f"从assessment_results获取到 {len(assessment_reports)} 条量表报告")
                        # 为了区分类型，给每个项目添加type字段
                        for item in assessment_reports:
                            item['item_type'] = 'assessment_report'
                            # 确保有title字段
                            if not item.get('title'):
                                item['title'] = item.get('assessment_name', '') or item.get('template_name', '') or item.get('name', '未命名量表报告')
                            # 确保有custom_id字段
                            if not item.get('custom_id'):
                                item['custom_id'] = custom_id
                            # 添加报告ID用于详情查看
                            if not item.get('report_id'):
                                item['report_id'] = item.get('id')
                    else:
                        logger.warning(f"获取量表报告失败: {assessment_reports_result}")
                except Exception as e:
                    logger.error(f"获取量表报告时出错: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                
                # 获取问卷结果报告（从questionnaire_results表）
                questionnaire_reports = []
                try:
                    # 使用正确的API端点获取问卷结果
                    questionnaire_reports_result = cloud_api.get(f"questionnaire-results/user/{custom_id}")
                    logger.info(f"问卷报告API响应: {questionnaire_reports_result}")
                    
                    if questionnaire_reports_result:
                        # 处理不同的响应格式
                        if isinstance(questionnaire_reports_result, dict):
                            if questionnaire_reports_result.get("status") == "success":
                                questionnaire_reports = questionnaire_reports_result.get("data", [])
                            elif "data" in questionnaire_reports_result:
                                questionnaire_reports = questionnaire_reports_result["data"]
                            elif "results" in questionnaire_reports_result:
                                questionnaire_reports = questionnaire_reports_result["results"]
                            else:
                                # 可能整个响应就是数据列表
                                questionnaire_reports = [questionnaire_reports_result]
                        elif isinstance(questionnaire_reports_result, list):
                            questionnaire_reports = questionnaire_reports_result
                        
                        logger.info(f"从questionnaire_results获取到 {len(questionnaire_reports)} 条问卷报告")
                        # 为了区分类型，给每个项目添加type字段
                        for item in questionnaire_reports:
                            item['item_type'] = 'questionnaire_report'
                            # 确保有title字段
                            if not item.get('title'):
                                item['title'] = item.get('questionnaire_name', '') or item.get('template_name', '') or item.get('name', '未命名问卷报告')
                            # 确保有custom_id字段
                            if not item.get('custom_id'):
                                item['custom_id'] = custom_id
                            # 添加报告ID用于详情查看
                            if not item.get('report_id'):
                                item['report_id'] = item.get('id')
                    else:
                        logger.warning(f"获取问卷报告失败: {questionnaire_reports_result}")
                except Exception as e:
                    logger.error(f"获取问卷报告时出错: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
                
                # 合并所有报告记录
                all_reports = assessment_reports + questionnaire_reports
                
                # 按时间排序（如果有时间字段）
                all_reports.sort(
                    key=lambda x: x.get('created_at', '') or x.get('submitted_at', '') or x.get('completed_at', ''), 
                    reverse=True
                )
                
                self.response_history = all_reports
                
                logger.info(f"SurveyScreen: 获取到 {len(all_reports)} 条报告记录（量表报告: {len(assessment_reports)}，问卷报告: {len(questionnaire_reports)}）")
            except Exception as e:
                logger.error(f"SurveyScreen: 获取报告记录数据时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
                self.response_history = []
                
            # 更新UI
            try:
                # 清空现有列表
                response_history_list.clear_widgets()
                
                # 显示数据或空状态
                if not self.response_history:
                    empty_label = MDLabel(
                        text='暂无历史报告记录', 
                        halign='center', 
                        theme_text_color='Hint',
                        font_style="Body",
                        size_hint_y=None,
                        height=dp(60)
                    )
                    response_history_list.add_widget(empty_label)
                else:
                    # 显示历史记录
                    for item in self.response_history:
                        try:
                            # 准备显示数据
                            title = item.get('title') or item.get('name', '未命名记录')
                            
                            # 尝试获取提交时间（支持多种字段名）
                            submitted_at = item.get('created_at', '') or item.get('submitted_at', '') or item.get('completed_at', '')
                            
                            if submitted_at:
                                try:
                                    # 尝试格式化日期
                                    from dateutil import parser
                                    dt = parser.parse(submitted_at)
                                    submitted_at = dt.strftime("%Y-%m-%d %H:%M")
                                except:
                                    # 格式化失败则使用原始字符串
                                    pass
                            
                            # 添加类型标签
                            item_type = item.get('item_type', '')
                            if item_type == 'assessment_report':
                                type_label = '量表报告'
                            elif item_type == 'questionnaire_report':
                                type_label = '问卷报告'
                            else:
                                type_label = '回复'
                            
                            # 创建列表项
                            list_item = MDListItem(
                                on_release=lambda x, item=item: self.show_response_detail(item),
                                size_hint_y=None,
                                height=dp(72),
                                md_bg_color=self.app.theme.ELEVATED_SURFACE if hasattr(self, 'app') else (0.95, 0.95, 0.95, 1),
                                radius=[8, 8, 8, 8]
                            )
                            
                            # 添加标题和副标题
                            list_item.add_widget(MDListItemHeadlineText(
                                text=title,
                                theme_text_color="Primary"
                            ))
                            list_item.add_widget(MDListItemSupportingText(
                                text=f"{type_label} - {submitted_at}" if submitted_at else type_label,
                                theme_text_color="Secondary"
                            ))
                            
                            # 添加查看详情按钮
                            trailing_icon = MDListItemTrailingIcon(
                                icon="eye",
                                theme_icon_color="Primary",
                                on_release=lambda x, item=item: self.show_response_detail(item)
                            )
                            list_item.add_widget(trailing_icon)
                            
                            # 添加到列表
                            response_history_list.add_widget(list_item)
                        except Exception as item_error:
                            logger.error(f"SurveyScreen: 添加历史记录项时出错: {item_error}")
                            continue
            except Exception as ui_error:
                logger.error(f"SurveyScreen: 更新历史记录UI时出错: {ui_error}")
                import traceback
                logger.error(traceback.format_exc())
        except Exception as e:
            logger.error(f"SurveyScreen: 更新历史记录UI时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def show_info(self, msg):
        """显示信息提示"""
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

        MDSnackbar(
            MDSnackbarText(
                text=msg,
                theme_text_color="Custom",
                text_color=self.app.theme.TEXT_LIGHT
            ),
            y=dp(10),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.9,
            md_bg_color=self.app.theme.INFO_COLOR,
            radius=[10, 10, 10, 10]
        ).open()

    def show_error(self, msg):
        """显示错误提示"""
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

        MDSnackbar(
            MDSnackbarText(
                text=msg,
                theme_text_color="Custom",
                text_color=self.app.theme.TEXT_LIGHT
            ),
            y=dp(10),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.9,
            md_bg_color=self.app.theme.ERROR_COLOR,
            radius=[10, 10, 10, 10]
        ).open()
        
    def on_assessment_selected(self, item):
        """评估量表项目被选中"""
        logger.info(f"选中评估量表: {item.get('title', '')}")
        self.selected_assessment = item
        self.open_assessment(item)
        
    def open_assessment(self, assessment_data):
        """打开评估量表
        
        Args:
            assessment_data: 评估量表数据
        """
        try:
            logger.info(f"打开评估量表: {assessment_data}")
            
            # 检查是否有问题列表
            if 'questions' not in assessment_data or not assessment_data.get('questions'):
                # 尝试获取问题列表
                assessment_id = assessment_data.get('id')
                if assessment_id:
                    logger.info(f"评估量表 {assessment_data.get('title')} (ID: {assessment_id}) 没有问题列表，尝试获取完整数据")
                    cloud_api = get_cloud_api()
                    if cloud_api:
                        # 获取完整问题列表
                        result = cloud_api.get_mobile_assessments(
                            custom_id=getattr(assessment_data, 'custom_id', None)
                        )
                        
                        # 在返回的数据中查找对应ID的量表
                        if result and isinstance(result, dict) and 'data' in result:
                            for item in result['data']:
                                if item.get('id') == assessment_id:
                                    # 找到匹配的量表，更新数据
                                    if 'questions' in item and item['questions']:
                                        assessment_data['questions'] = item['questions']
                                        logger.info(f"成功获取评估量表问题，共 {len(item['questions'])} 个问题")
                                        break
                                    
                        # 如果上面的方法没有找到问题，尝试使用get_assessment_questions方法
                        if 'questions' not in assessment_data or not assessment_data.get('questions'):
                            logger.info(f"通过列表未找到问题，尝试单独获取评估量表问题")
                            result = cloud_api.get_assessment_questions(assessment_id)
                            if result and 'questions' in result and result['questions']:
                                assessment_data['questions'] = result['questions']
                                logger.info(f"成功获取评估量表问题，共 {len(result['questions'])} 个问题")
                            else:
                                logger.warning(f"评估量表 {assessment_data.get('title')} (ID: {assessment_id}) 没有问题列表，将在表单页面尝试获取")
                else:
                    logger.warning(f"评估量表 {assessment_data.get('title')} 没有ID，无法获取问题列表")
            
            # 设置当前评估量表
            app = MDApp.get_running_app()
            app.assessment_to_fill = assessment_data
            
            # 导航到评估量表表单页面
            self.manager.current = "assessment_form_screen"
        except Exception as e:
            logger.error(f"打开评估量表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开评估量表时出错: {str(e)}")

    def on_questionnaire_selected(self, item):
        """问卷选择事件处理"""
        logger.info(f"选中问卷: {item.get('title', '')}")
        self.selected_questionnaire = item
        self.open_questionnaire(item)
        
    def navigate_to_assessment_form(self, assessment_data):
        """导航到评估量表表单"""
        try:
            # 保存当前选中的评估量表数据
            app = MDApp.get_running_app()
            app.assessment_to_fill = assessment_data
            
            logger.info(f"准备导航到评估表单，数据ID: {assessment_data.get('id', 'unknown')}")
            
            # 动态导入并创建评估表单屏幕
            if not app.root.has_screen('assessment_form_screen'):
                logger.info("创建新的评估表单屏幕")
                from screens.assessment_form_screen import AssessmentFormScreen
                assessment_form = AssessmentFormScreen(name='assessment_form_screen')
                app.root.add_widget(assessment_form)
            else:
                logger.info("使用已存在的评估表单屏幕")
                # 获取已存在的评估表单屏幕，on_enter方法会自动处理初始化
                assessment_form = app.root.get_screen('assessment_form_screen')
            
            # 切换到评估表单屏幕
            logger.info("切换到评估表单屏幕")
            app.root.transition.direction = 'left'
            app.root.current = 'assessment_form_screen'
        except Exception as e:
            logger.error(f"导航到评估表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"导航到评估表单时出错: {str(e)}")

    def show_questionnaire_detail(self, questionnaire, *args):
        """显示问卷详情"""
        try:
            logger.info(f"显示问卷详情: {questionnaire}")
            
            # 准备问卷数据
            questionnaire_id = questionnaire.get('id')
            
            # 获取问卷标题
            title = ""
            if 'questionnaire_info' in questionnaire and 'title' in questionnaire['questionnaire_info']:
                title = questionnaire['questionnaire_info']['title']
            else:
                title = questionnaire.get('title', '未命名问卷')
            
            # 获取问题列表
            questions = []
            if 'questions' in questionnaire:
                questions = questionnaire['questions']
            
            # 如果没有问题列表，先不报错，让表单页面尝试从API获取
            if not questions:
                logger.warning(f"问卷 {title} (ID: {questionnaire_id}) 没有问题列表，将在表单页面尝试获取")
            
            # 显示确认对话框
            self.show_dialog(
                f"{title}",
                f"准备开始填写问卷" + (f"，共 {len(questions)} 个问题" if questions else ""),
                actions=[
                    ["取消", lambda *args: None],
                    ["开始", lambda *args: self.navigate_to_questionnaire_form(questionnaire)]
                ]
            )
        except Exception as e:
            logger.error(f"显示问卷详情时出错: {e}")
            self.show_error("显示问卷详情时出错，请稍后重试")

    def navigate_to_questionnaire_form(self, questionnaire_data):
        """导航到问卷表单"""
        try:
            # 保存当前选中的问卷数据
            app = MDApp.get_running_app()
            app.questionnaire_to_fill = questionnaire_data
            
            logger.info(f"准备导航到问卷表单，数据ID: {questionnaire_data.get('id', 'unknown')}")
            
            # 动态导入并创建问卷表单屏幕
            if not app.root.has_screen('questionnaire_form_screen'):
                logger.info("创建新的问卷表单屏幕")
                from screens.questionnaire_form_screen import QuestionnaireFormScreen
                questionnaire_form = QuestionnaireFormScreen(name='questionnaire_form_screen')
                app.root.add_widget(questionnaire_form)
            else:
                logger.info("使用已存在的问卷表单屏幕")
                # 确保问卷表单屏幕被重新初始化
                questionnaire_form = app.root.get_screen('questionnaire_form_screen')
                # 立即初始化UI，不使用Clock延迟
                questionnaire_form.init_ui()
            
            # 切换到问卷表单屏幕
            logger.info("切换到问卷表单屏幕")
            app.root.transition.direction = 'left'
            app.root.current = 'questionnaire_form_screen'
        except Exception as e:
            logger.error(f"导航到问卷表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"导航到问卷表单时出错: {str(e)}")

    def show_response_detail(self, response, *args):
        """显示历史记录详情"""
        try:
            logger.info(f"显示回复详情: {response}")
            
            # 直接调用navigate_to_response_detail方法显示详情
            self.navigate_to_response_detail(response)
        except Exception as e:
            logger.error(f"显示历史记录详情时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("显示历史记录详情时出错，请稍后重试")

    def navigate_to_response_detail(self, response):
        """导航到报告详情页面"""
        try:
            logger.info(f"导航到报告详情: {response}")
            
            # 确定报告类型
            item_type = response.get('item_type', '')
            report_type = "assessment" if "assessment" in item_type else "questionnaire"
            
            # 获取报告ID
            report_id = response.get('id')
            if not report_id:
                self.show_error("无效的报告ID")
                return
            
            # 确保custom_id存在于报告数据中
            custom_id = response.get('custom_id') or self.current_user.custom_id if self.current_user else None
            if custom_id and 'custom_id' not in response:
                response['custom_id'] = custom_id
            
            # 获取报告详情
            cloud_api = get_cloud_api()
            report_result = cloud_api.get_report_detail(report_id, report_type)
            
            if report_result.get("status") != "success":
                # 如果获取详情失败，尝试使用列表中的数据作为报告数据
                logger.warning(f"获取报告详情失败，使用列表数据作为报告: {report_result.get('message', '未知错误')}")
                report_data = response
            else:
                report_data = report_result.get("data", {})
                
                # 确保custom_id存在于报告数据中
                if custom_id and 'custom_id' not in report_data:
                    report_data['custom_id'] = custom_id
            
            # 懒加载报告详情页面
            from utils.screen_loader import get_screen_loader
            screen_loader = get_screen_loader()
            
            if screen_loader.load_screen('report_detail'):
                # 设置报告数据到屏幕管理器
                self.manager.current_report_data = report_data
                self.manager.current_report_type = report_type
                self.manager.current_report_id = report_id
                self.manager.current_user_id = custom_id
                
                # 跳转到报告详情页面
                self.manager.current = 'report_detail'
                logger.info("成功跳转到报告详情页面")
            else:
                self.show_error("无法加载报告详情页面")
                
        except Exception as e:
            logger.error(f"导航到报告详情时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开报告详情时出错: {str(e)}")

    def check_distributions(self, dt=None):
        """检查是否有新的量表或问卷分发"""
        try:
            if not self._is_active:
                return
            
            # 获取用户ID
            user_manager = get_user_manager()
            user = user_manager.get_current_user()
            custom_id = getattr(user, 'custom_id', None) if user else None
            
            if not custom_id:
                logger.warning("未获取到有效的用户ID，无法检查分发通知")
                return
            
            # 获取认证令牌 - 修改为使用cloud_api
            cloud_api = get_cloud_api()
            auth_token = cloud_api.get_auth_token()
            
            if not auth_token:
                logger.warning("未获取到有效的认证令牌，无法检查分发通知")
                return
            
            # 创建线程检查分发
            def check_distributions_thread():
                try:
                    # 使用样例数据进行测试
                    # 实际应用中应该调用API检查分发
                    sample_distribution = {
                        "id": "sample_dist_001",
                        "type": "distribution",
                        "content_type": "assessment",
                        "title": "样例评估量表分发",
                        "message": "您有一个新的评估量表需要完成",
                        "created_at": datetime.now().isoformat(),
                        "expires_at": (datetime.now().replace(hour=23, minute=59, second=59)).isoformat(),
                        "read_status": False
                    }
                    
                    # 模拟展示通知
                    def show_sample_notification():
                        if not self._is_active:
                            return
                            
                        # 显示通知
                        self.show_info("您有新的评估量表需要填写")
                        
                        # 如果当前不在量表标签页，提供切换选项
                        if self.current_tab != "assessments":
                            # 显示确认对话框
                            self.show_dialog(
                                "新的评估量表",
                                "您有新的评估量表需要填写，是否立即查看？",
                                actions=[
                                    ["取消", lambda *args: None],
                                    ["查看", lambda *args: self.switch_tab("assessments")]
                                ]
                            )
                    
                    # 调度到主线程展示通知
                    # 实际应用中，应该根据API返回结果决定是否展示
                    # Clock.schedule_once(lambda dt: show_sample_notification())
                        
                except Exception as e:
                    logger.error(f"检查分发通知时出错: {e}")
                    
            # 启动线程
            threading.Thread(target=check_distributions_thread, daemon=True).start()
        except Exception as e:
            logger.error(f"SurveyScreen: 检查分发通知时出错: {e}")

    def show_dialog(self, title, message, actions=None):
        """显示对话框"""
        try:
            # 使用简单的Kivy弹窗，避免KivyMD的Dialog兼容性问题
            from kivy.uix.popup import Popup
            from kivy.uix.boxlayout import BoxLayout
            from kivy.uix.label import Label
            from kivy.uix.button import Button
            from kivy.uix.scrollview import ScrollView
            from kivy.metrics import dp
            
            # 创建内容容器
            content = BoxLayout(
                orientation="vertical",
                spacing=dp(12),
                padding=[dp(20), dp(20), dp(20), dp(20)]
            )
            
            # 添加标题
            title_label = Label(
                text=title,
                size_hint_y=None,
                height=dp(40),
                font_size=dp(18),
                halign='left',
                valign='middle',
                text_size=(None, dp(40))
            )
            content.add_widget(title_label)
            
            # 创建滚动视图显示消息
            scroll = ScrollView(
                size_hint=(1, 1),
                do_scroll_x=False,
                do_scroll_y=True
            )
            
            # 添加消息文本
            message_label = Label(
                text=message,
                size_hint_y=None,
                halign='left',
                valign='top',
                text_size=(None, None),
                padding=[0, 0]
            )
            # 绑定文本尺寸
            message_label.bind(texture_size=message_label.setter('size'))
            scroll.add_widget(message_label)
            content.add_widget(scroll)
            
            # 创建按钮容器
            buttons_box = BoxLayout(
                orientation="horizontal",
                spacing=dp(10),
                size_hint_y=None,
                height=dp(50),
                padding=[0, dp(10), 0, 0]
            )
            
            # 创建弹窗
            popup = Popup(
                title="",
                content=content,
                size_hint=(0.9, None),
                height=dp(400),
                auto_dismiss=True
            )
            
            # 添加按钮
            if actions:
                for action_text, action_handler in actions:
                    btn = Button(
                        text=action_text,
                        size_hint_y=None,
                        height=dp(40),
                        background_color=(0.2, 0.6, 0.8, 1)
                    )
                    # 创建闭包以捕获正确的处理函数和弹窗引用
                    def create_handler(handler, popup_ref):
                        def handler_func(instance):
                            popup_ref.dismiss()
                            if handler:
                                handler(instance)
                        return handler_func
                    
                    # 绑定事件处理函数
                    btn.bind(on_release=create_handler(action_handler, popup))
                    buttons_box.add_widget(btn)
            else:
                # 默认添加一个确定按钮
                close_btn = Button(
                    text="确定",
                    size_hint_y=None,
                    height=dp(40),
                    background_color=(0.2, 0.6, 0.8, 1)
                )
                close_btn.bind(on_release=lambda btn: popup.dismiss())
                buttons_box.add_widget(close_btn)
            
            # 将按钮容器添加到内容容器
            content.add_widget(buttons_box)
            
            # 显示弹窗
            popup.open()
            return popup
        except Exception as e:
            logger.error(f"显示对话框时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def on_leave(self, *args):
        """离开屏幕时的处理"""
        try:
            logger.info("SurveyScreen: 离开页面")
            # 取消所有可能的回调
            Clock.unschedule(self._update_assessments_ui)
            Clock.unschedule(self._update_questionnaires_ui)
            Clock.unschedule(self._update_response_history_ui)
            Clock.unschedule(self.check_distributions)
            
            # 标记页面为非活跃状态
            self._is_active = False
            
            # 保持数据加载状态，避免重新进入时重复加载
            # self._data_loaded 保持不变
        except Exception as e:
            logger.error(f"SurveyScreen: 离开页面时出错: {e}")
    
    def refresh_data(self):
        """刷新所有数据"""
        try:
            logger.info(f"SurveyScreen: 手动刷新数据，当前标签: {self.current_tab}")
            
            # 清除数据加载标记，强制重新加载
            self._data_loaded = False
            
            if self.current_tab == "assessments":
                self.assessment_list = []  # 清空现有数据
                self.load_assessments()
            elif self.current_tab == "questionnaires":
                self.questionnaire_list = []  # 清空现有数据
                self.load_questionnaires()
            elif self.current_tab == "history":
                self.response_history = []  # 清空现有数据
                self.load_response_history()
                
            # 重置分发通知检查时间，允许立即检查
            if hasattr(self, '_last_distribution_check'):
                delattr(self, '_last_distribution_check')
            self.check_distributions()
        except Exception as e:
            logger.error(f"SurveyScreen: 刷新数据时出错: {e}")

    def open_questionnaire(self, questionnaire_data):
        """打开问卷
        
        Args:
            questionnaire_data: 问卷数据
        """
        try:
            logger.info(f"打开问卷: {questionnaire_data}")
            
            # 检查问卷数据结构并获取问题列表
            questions = []
            
            # 尝试从多个可能的位置获取问题列表
            if 'questions' in questionnaire_data and questionnaire_data['questions']:
                questions = questionnaire_data['questions']
                logger.info(f"从问卷直接获取问题列表，共 {len(questions)} 个问题")
            elif 'template' in questionnaire_data and isinstance(questionnaire_data['template'], dict):
                if 'questions' in questionnaire_data['template']:
                    questions = questionnaire_data['template']['questions']
                    logger.info(f"从问卷模板获取问题列表，共 {len(questions)} 个问题")
            
            # 如果仍然没有问题列表，强制从API获取
            questionnaire_id = questionnaire_data.get('id')
            if questionnaire_id:
                logger.info(f"问卷ID: {questionnaire_id}，强制尝试获取问题列表")
                cloud_api = get_cloud_api()
                if cloud_api and cloud_api.is_authenticated():
                    try:
                        # 直接使用get_questionnaire_questions方法获取问题
                        logger.info(f"直接调用get_questionnaire_questions获取问题")
                        result = cloud_api.get_questionnaire_questions(questionnaire_id)
                        if result and 'questions' in result and result['questions']:
                            questions = result['questions']
                            questionnaire_data['questions'] = questions
                            logger.info(f"成功从API获取问卷问题，共 {len(questions)} 个问题")
                        else:
                            # 如果上面的方法没有找到问题，尝试使用get_mobile_questionnaires方法
                            logger.info(f"通过get_questionnaire_questions未找到问题，尝试获取完整数据")
                            result = cloud_api.get_mobile_questionnaires(
                                custom_id=getattr(questionnaire_data, 'custom_id', None)
                            )
                            
                            # 在返回的数据中查找对应ID的问卷
                            if result and isinstance(result, dict) and 'data' in result:
                                for item in result['data']:
                                    if item.get('id') == questionnaire_id:
                                        # 找到匹配的问卷，更新数据
                                        if 'questions' in item and item['questions']:
                                            questions = item['questions']
                                            questionnaire_data['questions'] = questions
                                            logger.info(f"成功获取问卷问题，共 {len(questions)} 个问题")
                                            break
                                        # 检查模板中的问题
                                        elif 'template' in item and isinstance(item['template'], dict) and 'questions' in item['template']:
                                            questions = item['template']['questions']
                                            questionnaire_data['questions'] = questions
                                            logger.info(f"成功从模板获取问卷问题，共 {len(questions)} 个问题")
                                            break
                            
                            if not questions:
                                # 尝试获取问卷详情
                                logger.info(f"尝试获取问卷详情")
                                result = cloud_api.get_questionnaire_detail(questionnaire_id)
                                if result and isinstance(result, dict):
                                    # 检查详情中的问题
                                    if 'questions' in result:
                                        questions = result['questions']
                                        questionnaire_data['questions'] = questions
                                        logger.info(f"从问卷详情获取问题，共 {len(questions)} 个问题")
                                    # 检查详情中模板的问题
                                    elif 'template' in result and isinstance(result['template'], dict) and 'questions' in result['template']:
                                        questions = result['template']['questions']
                                        questionnaire_data['questions'] = questions
                                        logger.info(f"从问卷详情模板获取问题，共 {len(questions)} 个问题")
                                    # 检查data字段
                                    elif 'data' in result and isinstance(result['data'], dict):
                                        if 'questions' in result['data']:
                                            questions = result['data']['questions']
                                            questionnaire_data['questions'] = questions
                                            logger.info(f"从问卷详情data获取问题，共 {len(questions)} 个问题")
                                        elif 'template' in result['data'] and isinstance(result['data']['template'], dict) and 'questions' in result['data']['template']:
                                            questions = result['data']['template']['questions']
                                            questionnaire_data['questions'] = questions
                                            logger.info(f"从问卷详情data模板获取问题，共 {len(questions)} 个问题")
                    except Exception as api_error:
                        logger.error(f"从API获取问题列表失败: {api_error}")
                        import traceback
                        logger.error(traceback.format_exc())
                else:
                    logger.warning("云API未认证或不可用")
            else:
                logger.warning(f"问卷 {questionnaire_data.get('title')} 没有ID，无法从API获取问题列表")
            
            # 确保问卷数据包含问题列表
            if questions:
                questionnaire_data['questions'] = questions
                logger.info(f"最终问卷包含 {len(questions)} 个问题")
            else:
                logger.warning("未能获取到问卷问题列表，将在表单页面显示空状态")
                # 创建默认问题，避免空白表单
                if questionnaire_data.get('question_count', 0) > 0:
                    count = questionnaire_data.get('question_count')
                    default_questions = []
                    for i in range(count):
                        default_questions.append({
                            'id': i + 1,
                            'question_id': f'q_{i+1}',
                            'text': f'问题 {i + 1}',
                            'question_text': f'问题 {i + 1}',
                            'type': 'radio',
                            'question_type': 'radio',
                            'options': [
                                {'value': '1', 'text': '选项1', 'label': '选项1'},
                                {'value': '2', 'text': '选项2', 'label': '选项2'},
                                {'value': '3', 'text': '选项3', 'label': '选项3'},
                                {'value': '4', 'text': '选项4', 'label': '选项4'},
                                {'value': '5', 'text': '选项5', 'label': '选项5'}
                            ],
                            'order': i,
                            'is_required': True
                        })
                    questionnaire_data['questions'] = default_questions
                    logger.info(f"创建了 {len(default_questions)} 个默认问题")
            
            # 设置当前问卷
            app = MDApp.get_running_app()
            app.questionnaire_to_fill = questionnaire_data
            
            # 导航到问卷表单页面
            self.manager.current = "questionnaire_form_screen"
        except Exception as e:
            logger.error(f"打开问卷时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开问卷时出错: {str(e)}")

    def update_assessment_list(self):
        """更新评估量表列表UI"""
        try:
            logger.info(f"更新评估量表列表UI，数量: {len(self.assessments)}")
            
            # 清空现有列表
            # 尝试查找不同的ID命名
            if hasattr(self.ids, 'assessment_list_container'):
                self.ids.assessment_list_container.clear_widgets()
            elif hasattr(self.ids, 'assessment_list'):
                self.ids.assessment_list.clear_widgets()
            else:
                logger.error("找不到assessment_list_container或assessment_list控件")
                return
            
            # 如果没有评估量表，显示空状态
            if not self.assessments:
                empty_label = MDLabel(
                    text="暂无评估量表",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                # 尝试找到正确的容器
                if hasattr(self.ids, 'assessment_list_container'):
                    self.ids.assessment_list_container.add_widget(empty_label)
                elif hasattr(self.ids, 'assessment_list'):
                    self.ids.assessment_list.add_widget(empty_label)
                return
            
            # 添加评估量表卡片
            for assessment in self.assessments:
                # 获取评估量表信息
                assessment_id = assessment.get('id')
                title = assessment.get('title') or assessment.get('name') or "未命名量表"
                description = assessment.get('description') or assessment.get('notes') or ""
                
                # 获取模板信息（如果有）
                template = assessment.get('template', {})
                if template:
                    if not title or title == "未命名量表":
                        title = template.get('name', "未命名量表")
                    if not description:
                        description = template.get('description', "")
                
                # 创建卡片
                card = self.create_assessment_card(assessment_id, title, description, assessment)
                # 尝试找到正确的容器
                if hasattr(self.ids, 'assessment_list_container'):
                    self.ids.assessment_list_container.add_widget(card)
                elif hasattr(self.ids, 'assessment_list'):
                    self.ids.assessment_list.add_widget(card)
            
            logger.info(f"评估量表列表UI更新完成，添加了 {len(self.assessments)} 个卡片")
        except Exception as e:
            logger.error(f"更新评估量表列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def update_questionnaire_list(self):
        """更新问卷列表UI"""
        try:
            logger.info(f"更新问卷列表UI，数量: {len(self.questionnaires)}")
            
            # 清空现有列表
            # 尝试查找不同的ID命名
            if hasattr(self.ids, 'questionnaire_list_container'):
                self.ids.questionnaire_list_container.clear_widgets()
            elif hasattr(self.ids, 'questionnaire_list'):
                self.ids.questionnaire_list.clear_widgets()
            else:
                logger.error("找不到questionnaire_list_container或questionnaire_list控件")
                return
            
            # 如果没有问卷，显示空状态
            if not self.questionnaires:
                empty_label = MDLabel(
                    text="暂无问卷",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                # 尝试找到正确的容器
                if hasattr(self.ids, 'questionnaire_list_container'):
                    self.ids.questionnaire_list_container.add_widget(empty_label)
                elif hasattr(self.ids, 'questionnaire_list'):
                    self.ids.questionnaire_list.add_widget(empty_label)
                return
            
            # 添加问卷卡片
            for questionnaire in self.questionnaires:
                # 获取问卷信息
                questionnaire_id = questionnaire.get('id')
                title = questionnaire.get('title') or questionnaire.get('name') or "未命名问卷"
                description = questionnaire.get('description') or questionnaire.get('notes') or ""
                
                # 获取模板信息（如果有）
                template = questionnaire.get('template', {})
                if template:
                    if not title or title == "未命名问卷":
                        title = template.get('name', "未命名问卷")
                    if not description:
                        description = template.get('description', "")
                
                # 创建卡片
                card = self.create_questionnaire_card(questionnaire_id, title, description, questionnaire)
                # 尝试找到正确的容器
                if hasattr(self.ids, 'questionnaire_list_container'):
                    self.ids.questionnaire_list_container.add_widget(card)
                elif hasattr(self.ids, 'questionnaire_list'):
                    self.ids.questionnaire_list.add_widget(card)
            
            logger.info(f"问卷列表UI更新完成，添加了 {len(self.questionnaires)} 个卡片")
        except Exception as e:
            logger.error(f"更新问卷列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

# 在类定义后注册Factory
Factory.register('SurveyScreen', cls=SurveyScreen)
Builder.load_string(KV)