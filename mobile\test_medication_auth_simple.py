#!/usr/bin/env python3
"""
简单测试用药管理屏幕的认证逻辑

这个脚本测试修复后的medication_management_screen的认证处理逻辑，
不涉及UI组件的初始化。
"""

import os
import sys
import logging

# 添加mobile目录到Python路径
mobile_dir = os.path.dirname(os.path.abspath(__file__))
if mobile_dir not in sys.path:
    sys.path.insert(0, mobile_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_medication_auth_logic():
    """测试用药管理的认证逻辑"""
    print("\n=== 测试用药管理认证逻辑 ===")
    
    try:
        # 导入medication_management_screen模块
        from screens_bak import medication_management_screen
        
        # 创建一个简化的测试类
        class TestMedicationScreen:
            def __init__(self):
                # 模拟app对象
                self.app = type('MockApp', (), {
                    'user_data': {
                        'custom_id': 'SM_008',
                        'username': 'markey',
                        'access_token': 'test_token_123'
                    },
                    'font_styles': {
                        'BODY_MEDIUM': {
                            'font_size': '14sp',
                            'font_name': 'Roboto'
                        }
                    }
                })()
                
                # 模拟medications列表
                self.medications = []
                self.editing_medication = None
                
                # 模拟ids
                self.ids = type('MockIds', (), {
                    'medications_container': type('MockContainer', (), {
                        'clear_widgets': lambda: None,
                        'add_widget': lambda widget: None
                    })()
                })()
            
            def show_error(self, message):
                print(f"ERROR: {message}")
            
            def show_info(self, message):
                print(f"INFO: {message}")
        
        # 创建测试实例
        test_screen = TestMedicationScreen()
        
        # 将medication_management_screen的方法绑定到测试实例
        test_screen.load_medications = medication_management_screen.MedicationManagementScreen.load_medications.__get__(test_screen)
        test_screen.save_medication = medication_management_screen.MedicationManagementScreen.save_medication.__get__(test_screen)
        test_screen.delete_medication = medication_management_screen.MedicationManagementScreen.delete_medication.__get__(test_screen)
        
        print("✓ 成功创建测试实例")
        
        # 测试load_medications方法
        print("\n测试load_medications方法...")
        test_screen.load_medications()
        print("✓ load_medications方法执行成功")
        print(f"  加载了 {len(test_screen.medications)} 条用药记录")
        
        # 测试save_medication方法
        print("\n测试save_medication方法...")
        # 模拟dialog
        test_screen.dialog = type('MockDialog', (), {'dismiss': lambda: None})()
        
        test_screen.save_medication(
            name="测试药物",
            dosage="100mg",
            schedule="每日1次",
            start_date="2024-01-01",
            end_date="2024-12-31",
            notes="测试备注"
        )
        print("✓ save_medication方法执行成功")
        print(f"  当前用药记录数: {len(test_screen.medications)}")
        
        # 测试delete_medication方法
        print("\n测试delete_medication方法...")
        if test_screen.medications:
            test_medication = test_screen.medications[0]
            test_screen.delete_medication(test_medication)
            print("✓ delete_medication方法执行成功")
            print(f"  删除后用药记录数: {len(test_screen.medications)}")
        
        print("\n🎉 所有认证逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_info_retrieval():
    """测试认证信息获取逻辑"""
    print("\n=== 测试认证信息获取逻辑 ===")
    
    try:
        # 模拟app.user_data
        mock_app = type('MockApp', (), {
            'user_data': {
                'custom_id': 'SM_008',
                'username': 'markey',
                'access_token': 'test_token_123'
            }
        })()
        
        # 测试从app.user_data获取认证信息
        custom_id = mock_app.user_data.get('custom_id')
        access_token = mock_app.user_data.get('access_token')
        
        print(f"✓ 从app.user_data获取custom_id: {custom_id}")
        print(f"✓ 从app.user_data获取access_token: {access_token[:10]}...")
        
        # 测试认证管理器
        from utils.auth_manager import get_auth_manager
        auth_manager = get_auth_manager()
        user_info = auth_manager.get_current_user_info()
        
        if user_info:
            print(f"✓ 认证管理器获取custom_id: {user_info.get('custom_id')}")
            print(f"✓ 认证管理器获取token: {'有' if user_info.get('access_token') else '无'}")
        else:
            print("ℹ 认证管理器未获取到用户信息（正常情况）")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证信息获取测试失败: {str(e)}")
        return False

def test_cloud_api_setup():
    """测试云API设置逻辑"""
    print("\n=== 测试云API设置逻辑 ===")
    
    try:
        from utils.cloud_api import get_cloud_api
        
        # 获取云API实例
        cloud_api = get_cloud_api()
        
        # 测试静默设置认证信息
        original_token = cloud_api.token
        original_custom_id = getattr(cloud_api, 'custom_id', None)
        
        # 设置测试认证信息
        cloud_api.token = "test_token_123"
        cloud_api.custom_id = "SM_008"
        
        print(f"✓ 设置token: {cloud_api.token[:10]}...")
        print(f"✓ 设置custom_id: {cloud_api.custom_id}")
        
        # 测试认证状态
        is_authenticated = cloud_api.is_authenticated()
        print(f"✓ 认证状态: {is_authenticated}")
        
        # 恢复原始状态
        cloud_api.token = original_token
        cloud_api.custom_id = original_custom_id
        
        return True
        
    except Exception as e:
        print(f"❌ 云API设置测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试用药管理屏幕认证修复（简化版）...")
    
    # 运行所有测试
    tests = [
        test_auth_info_retrieval,
        test_cloud_api_setup,
        test_medication_auth_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {str(e)}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！用药管理屏幕token认证问题已修复")
        print("\n修复总结:")
        print("1. ✅ 优化了认证信息获取逻辑，优先从app.user_data获取")
        print("2. ✅ 实现了静默认证设置，避免不必要的警告")
        print("3. ✅ 改进了错误日志级别，减少误导性警告")
        print("4. ✅ 保持了向后兼容性，支持多种认证方式")
        print("\n现在medication_management_screen应该不再出现token认证警告！")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
