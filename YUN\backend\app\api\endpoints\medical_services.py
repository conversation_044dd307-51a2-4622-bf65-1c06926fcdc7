"""医疗服务API路由
"""
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.user import User
from app.core.auth import get_current_active_user_custom

router = APIRouter()

@router.get("/services", response_model=Dict[str, Any])
def get_medical_services(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取医疗服务列表
    """
    try:
        # 返回医疗服务列表
        services = [
            {
                "id": 1,
                "name": "语音分诊",
                "description": "通过语音交互进行初步健康评估和分诊",
                "icon": "voice",
                "category": "智能诊疗",
                "status": "active",
                "route": "voice_triage"
            },
            {
                "id": 2,
                "name": "陪诊服务",
                "description": "专业陪诊人员提供就医陪同服务",
                "icon": "companion",
                "category": "就医服务",
                "status": "active",
                "route": "companion_service"
            },
            {
                "id": 3,
                "name": "健康咨询",
                "description": "专业医生在线健康咨询服务",
                "icon": "consultation",
                "category": "在线咨询",
                "status": "active",
                "route": "consultant"
            },
            {
                "id": 4,
                "name": "体检预约",
                "description": "便捷的体检项目预约服务",
                "icon": "checkup",
                "category": "预约服务",
                "status": "active",
                "route": "physical_exam"
            },
            {
                "id": 5,
                "name": "药品配送",
                "description": "处方药品快速配送到家服务",
                "icon": "delivery",
                "category": "药品服务",
                "status": "active",
                "route": "medication_delivery"
            }
        ]
        
        return {
            "status": "success",
            "services": services,
            "total": len(services),
            "message": "获取医疗服务列表成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取医疗服务列表失败: {str(e)}"
        )

@router.get("/services/{service_id}", response_model=Dict[str, Any])
def get_medical_service_detail(
    service_id: int = Path(..., description="服务ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取医疗服务详情
    """
    try:
        # 模拟服务详情数据
        service_details = {
            1: {
                "id": 1,
                "name": "语音分诊",
                "description": "通过语音交互进行初步健康评估和分诊",
                "icon": "voice",
                "category": "智能诊疗",
                "status": "active",
                "route": "voice_triage",
                "features": [
                    "智能语音识别",
                    "症状分析",
                    "科室推荐",
                    "就医建议"
                ],
                "price": "免费",
                "duration": "5-10分钟"
            },
            2: {
                "id": 2,
                "name": "陪诊服务",
                "description": "专业陪诊人员提供就医陪同服务",
                "icon": "companion",
                "category": "就医服务",
                "status": "active",
                "route": "companion_service",
                "features": [
                    "专业陪诊员",
                    "全程陪同",
                    "医疗翻译",
                    "报告解读"
                ],
                "price": "200元/次",
                "duration": "2-4小时"
            }
        }
        
        if service_id not in service_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="服务不存在"
            )
        
        return {
            "status": "success",
            "data": service_details[service_id],
            "message": "获取服务详情成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务详情失败: {str(e)}"
        )

@router.post("/services/{service_id}/book", response_model=Dict[str, Any])
def book_medical_service(
    service_id: int = Path(..., description="服务ID"),
    booking_data: Dict[str, Any] = Body(..., description="预约数据"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    预约医疗服务
    """
    try:
        # 这里可以添加实际的预约逻辑
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}{service_id}"
        
        return {
            "status": "success",
            "data": {
                "booking_id": booking_id,
                "service_id": service_id,
                "user_id": current_user.custom_id,
                "booking_time": datetime.now().isoformat(),
                "status": "confirmed"
            },
            "message": "预约成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预约服务失败: {str(e)}"
        )