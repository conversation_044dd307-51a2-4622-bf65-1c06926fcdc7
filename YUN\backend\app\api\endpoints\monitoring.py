from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from typing import Dict, Any, Optional
import subprocess
import os
import json
import re
from datetime import datetime
import logging
from fastapi import APIRouter, Depends, HTTPException, status
from app.db.session import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.assessment import Assessment
from app.models.questionnaire import Questionnaire
from sqlalchemy.orm import Session
from sqlalchemy import func, case

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取脚本路径
SCRIPTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "scripts")
MONITOR_SCRIPT = os.path.join(SCRIPTS_DIR, "assessment_questionnaire_monitor.py")
FIX_SCRIPT = os.path.join(SCRIPTS_DIR, "fix_monitoring_issues.py")

@router.get("/assessment-questionnaire", response_model=Dict[str, Any])
async def get_assessment_questionnaire_monitoring(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取量表问卷监控数据"""
    try:
        # 运行监控脚本，增加 --json 参数
        result = subprocess.run(
            ["python", MONITOR_SCRIPT, "--json"],
            capture_output=True,
            text=True,
            cwd=SCRIPTS_DIR,
            encoding='utf-8',
            errors='replace'  # 处理编码错误，用替换字符代替无法解码的字节
        )
        
        # 检查是否有JSON输出，不管退出码是否为0
        matches = re.findall(r'\{[\s\S]*\}', result.stdout)
        if matches:
            try:
                json_str = matches[-1]
                raw_data = json.loads(json_str)
                
                # 转换数据格式以匹配前端期望
                results = raw_data.get('results', [])
                
                # 统计各状态的数量
                healthy_count = sum(1 for r in results if r.get('status') == 'HEALTHY')
                warning_count = sum(1 for r in results if r.get('status') == 'WARNING')
                error_count = sum(1 for r in results if r.get('status') == 'ERROR')
                
                # 按阶段组织数据
                stages_data = {}
                for item in results:
                    stage_name = item.get('stage', '未知阶段')
                    if stage_name not in stages_data:
                        stages_data[stage_name] = {
                            'name': stage_name,
                            'display_name': stage_name,
                            'status': 'HEALTHY',
                            'description': item.get('message', ''),
                            'issues': [],
                            'metrics': {}
                        }
                    
                    # 更新阶段状态（优先级：ERROR > WARNING > HEALTHY）
                    current_status = stages_data[stage_name]['status']
                    item_status = item.get('status', 'HEALTHY')
                    if item_status == 'ERROR' or (item_status == 'WARNING' and current_status == 'HEALTHY'):
                        stages_data[stage_name]['status'] = item_status
                    
                    # 添加问题描述
                    if item.get('message') and item_status in ['WARNING', 'ERROR']:
                        stages_data[stage_name]['issues'].append(item.get('message'))
                    
                    # 添加详细信息
                    if item.get('details'):
                        stages_data[stage_name]['metrics'].update(item.get('details', {}))
                
                # 生成日志数据
                logs = []
                for item in results[-50:]:  # 最近50条
                    logs.append({
                        'timestamp': item.get('timestamp', datetime.now().isoformat()),
                        'stage': item.get('stage', '未知阶段'),
                        'status': item.get('status', 'UNKNOWN'),
                        'message': item.get('message', '检查完成')
                    })
                
                # 获取真实的24小时趋势数据
                from app.models.assessment import Assessment
                from app.models.questionnaire import Questionnaire
                from sqlalchemy import func, and_
                from datetime import timedelta
                
                # 24小时趋势数据
                now = datetime.now()
                trend_labels = []
                assessment_hourly = []
                questionnaire_hourly = []
                
                for i in range(24):
                    hour_start = now.replace(minute=0, second=0, microsecond=0) - timedelta(hours=23-i)
                    hour_end = hour_start + timedelta(hours=1)
                    trend_labels.append(hour_start.strftime("%H:%M"))
                    
                    # 查询该小时内完成的评估数量
                    assessment_count = db.query(func.count(Assessment.id)).filter(
                        and_(
                            Assessment.status == 'completed',
                            Assessment.updated_at >= hour_start,
                            Assessment.updated_at < hour_end
                        )
                    ).scalar() or 0
                    assessment_hourly.append(assessment_count)
                    
                    # 查询该小时内完成的问卷数量
                    questionnaire_count = db.query(func.count(Questionnaire.id)).filter(
                        and_(
                            Questionnaire.status == 'completed',
                            Questionnaire.updated_at >= hour_start,
                            Questionnaire.updated_at < hour_end
                        )
                    ).scalar() or 0
                    questionnaire_hourly.append(questionnaire_count)
                
                trend_data = {
                    "labels": trend_labels,
                    "datasets": [
                        {
                            "label": "评估完成数",
                            "data": assessment_hourly
                        },
                        {
                            "label": "问卷完成数", 
                            "data": questionnaire_hourly
                        }
                    ]
                }
                
                # 获取真实的7天分发趋势数据
                distribution_labels = []
                assessment_daily = []
                questionnaire_daily = []
                
                for i in range(7):
                    day_start = (now - timedelta(days=6-i)).replace(hour=0, minute=0, second=0, microsecond=0)
                    day_end = day_start + timedelta(days=1)
                    distribution_labels.append(day_start.strftime("%m-%d"))
                    
                    # 查询该天分发的评估数量
                    assessment_count = db.query(func.count(Assessment.id)).filter(
                        and_(
                            Assessment.created_at >= day_start,
                            Assessment.created_at < day_end
                        )
                    ).scalar() or 0
                    assessment_daily.append(assessment_count)
                    
                    # 查询该天分发的问卷数量
                    questionnaire_count = db.query(func.count(Questionnaire.id)).filter(
                        and_(
                            Questionnaire.created_at >= day_start,
                            Questionnaire.created_at < day_end
                        )
                    ).scalar() or 0
                    questionnaire_daily.append(questionnaire_count)
                
                distribution_trend = {
                    "labels": distribution_labels,
                    "datasets": [
                        {
                            "label": "评估分发数",
                            "data": assessment_daily
                        },
                        {
                            "label": "问卷分发数",
                            "data": questionnaire_daily
                        }
                    ]
                }
                
                # 返回前端期望的格式
                data = {
                    'total_checks': raw_data.get('total_checks', len(results)),
                    'healthy_count': healthy_count,
                    'warning_count': warning_count,
                    'error_count': error_count,
                    'timestamp': raw_data.get('timestamp', datetime.now().isoformat()),
                    'stages': list(stages_data.values()),
                    'logs': logs,
                    'trend_data': trend_data,
                    'distribution_trend': distribution_trend,
                    'success': error_count == 0
                }
                return data
            except json.JSONDecodeError as e:
                logger.error(f"监控脚本输出非JSON: {result.stdout}")
                return {
                    "status": "error",
                    "timestamp": datetime.now().isoformat(),
                    "details": {},
                    "logs": [
                        {
                            "timestamp": datetime.now().isoformat(),
                            "level": "ERROR",
                            "message": f"监控脚本输出非JSON: {str(e)}",
                            "stage": "系统检查"
                        }
                    ],
                    "error": str(e)
                }
        else:
            # 没有找到JSON输出，检查是否有错误信息
            if result.returncode != 0 and result.stderr:
                logger.error(f"监控脚本执行失败: {result.stderr}")
                return {
                    "status": "error",
                    "timestamp": datetime.now().isoformat(),
                    "details": {},
                    "logs": [
                        {
                            "timestamp": datetime.now().isoformat(),
                            "level": "ERROR",
                            "message": f"监控脚本执行失败: {result.stderr}",
                            "stage": "系统检查"
                        }
                    ],
                    "error": result.stderr
                }
            else:
                # 新增：返回原始stdout和详细错误，便于定位
                return {
                    "status": "error",
                    "timestamp": datetime.now().isoformat(),
                    "details": {},
                    "logs": [
                        {
                            "timestamp": datetime.now().isoformat(),
                            "level": "ERROR",
                            "message": "未找到JSON内容，原始输出：" + result.stdout,
                            "stage": "系统检查"
                        }
                    ],
                    "error": "未找到JSON内容，原始输出已返回，请检查监控脚本输出格式"
                }
    except Exception as e:
        logger.error(f"获取量表问卷监控数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取量表问卷监控数据失败: {str(e)}"
        )

@router.get("/status", response_model=Dict[str, Any])
async def get_monitoring_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取监控状态"""
    try:
        # 运行监控脚本
        result = subprocess.run(
            ["python", MONITOR_SCRIPT],
            capture_output=True,
            text=True,
            cwd=SCRIPTS_DIR,
            encoding='utf-8',
            errors='replace'  # 处理编码错误
        )
        
        if result.returncode == 0:
            # 解析输出获取监控数据
            output_lines = result.stdout.strip().split('\n')
            status_data = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "details": {
                    "database_save": "healthy",
                    "frontend_query": "healthy",
                    "missing_template_id": 0,
                    "duplicate_identifiers": 0,
                    "missing_identifiers": 0,
                    "orphan_records": 0
                },
                "logs": output_lines
            }
            
            # 解析输出中的具体数据
            for line in output_lines:
                if "missing template_id" in line.lower():
                    try:
                        count = int(line.split()[-1])
                        status_data["details"]["missing_template_id"] = count
                        if count > 0:
                            status_data["status"] = "warning"
                    except:
                        pass
                elif "duplicate unique_identifier" in line.lower():
                    try:
                        count = int(line.split()[-1])
                        status_data["details"]["duplicate_identifiers"] = count
                        if count > 0:
                            status_data["status"] = "warning"
                    except:
                        pass
                elif "missing unique_identifier" in line.lower():
                    try:
                        count = int(line.split()[-1])
                        status_data["details"]["missing_identifiers"] = count
                        if count > 0:
                            status_data["status"] = "warning"
                    except:
                        pass
                elif "orphan" in line.lower():
                    try:
                        count = int(line.split()[-1])
                        status_data["details"]["orphan_records"] = count
                        if count > 0:
                            status_data["status"] = "warning"
                    except:
                        pass
            
            return status_data
        else:
            logger.error(f"监控脚本执行失败: {result.stderr}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": result.stderr,
                "details": {}
            }
            
    except Exception as e:
        logger.error(f"获取监控状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取监控状态失败: {str(e)}"
        )

@router.post("/fix-issues", response_model=Dict[str, Any])
async def fix_monitoring_issues(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """修复监控发现的问题"""
    try:
        # 运行修复脚本
        result = subprocess.run(
            ["python", FIX_SCRIPT],
            capture_output=True,
            text=True,
            cwd=SCRIPTS_DIR,
            encoding='utf-8',
            errors='replace'  # 处理编码错误
        )
        
        if result.returncode == 0:
            return {
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "message": "问题修复完成",
                "details": result.stdout
            }
        else:
            logger.error(f"修复脚本执行失败: {result.stderr}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": result.stderr,
                "message": "修复失败"
            }
            
    except Exception as e:
        logger.error(f"修复监控问题失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修复监控问题失败: {str(e)}"
        )

@router.post("/fix", response_model=Dict[str, Any])
async def fix_monitoring_issues(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """修复监控发现的问题"""
    try:
        # 运行修复脚本
        result = subprocess.run(
            ["python", FIX_SCRIPT],
            capture_output=True,
            text=True,
            cwd=SCRIPTS_DIR,
            encoding='utf-8',
            errors='replace'  # 处理编码错误
        )
        
        if result.returncode == 0:
            output_lines = result.stdout.strip().split('\n')
            
            # 解析修复结果
            fixed_count = 0
            for line in output_lines:
                if "records were fixed" in line.lower():
                    try:
                        fixed_count = int(line.split()[0])
                    except:
                        pass
            
            return {
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "fixed_count": fixed_count,
                "logs": output_lines,
                "message": f"成功修复 {fixed_count} 条记录"
            }
        else:
            logger.error(f"修复脚本执行失败: {result.stderr}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": result.stderr,
                "message": "修复失败"
            }
            
    except Exception as e:
        logger.error(f"修复监控问题失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修复监控问题失败: {str(e)}"
        )

@router.get("/export-report", response_model=Dict[str, Any])
async def export_monitoring_report(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """导出监控报告"""
    try:
        # 获取当前监控状态
        status_data = await get_assessment_questionnaire_monitoring(db, current_user)
        
        # 生成报告
        report = {
            "report_type": "assessment_questionnaire_monitoring_report",
            "generated_at": datetime.now().isoformat(),
            "generated_by": current_user.username,
            "summary": {
                "overall_status": status_data.get("status", "unknown"),
                "total_checks": len(status_data.get("logs", [])),
                "issues_count": len([log for log in status_data.get("logs", []) if log.get("level") in ["ERROR", "WARNING"]])
            },
            "details": status_data.get("details", {}),
            "logs": status_data.get("logs", [])
        }
        
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "report": report,
            "download_url": f"/api/monitoring/download-report?timestamp={datetime.now().timestamp()}"
        }
        
    except Exception as e:
        logger.error(f"导出监控报告失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出监控报告失败: {str(e)}"
        )

@router.get("/export", response_model=Dict[str, Any])
async def export_monitoring_report(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """导出监控报告"""
    try:
        # 获取当前监控状态
        status_data = await get_monitoring_status(db, current_user)
        
        # 生成报告
        report = {
            "report_type": "monitoring_report",
            "generated_at": datetime.now().isoformat(),
            "generated_by": current_user.username,
            "summary": {
                "overall_status": status_data.get("status", "unknown"),
                "total_issues": (
                    status_data.get("details", {}).get("missing_template_id", 0) +
                    status_data.get("details", {}).get("duplicate_identifiers", 0) +
                    status_data.get("details", {}).get("missing_identifiers", 0) +
                    status_data.get("details", {}).get("orphan_records", 0)
                )
            },
            "details": status_data.get("details", {}),
            "logs": status_data.get("logs", [])
        }
        
        return {
            "status": "success",
            "report": report,
            "download_url": f"/api/monitoring/download-report?timestamp={datetime.now().timestamp()}"
        }
        
    except Exception as e:
        logger.error(f"导出监控报告失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出监控报告失败: {str(e)}"
        )

@router.get("/user/{user_id}", response_model=Dict[str, Any])
async def get_user_monitoring_status(
    user_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取特定用户的监控状态"""
    try:
        from app.models.user import User
        from app.models.assessment import Assessment
        from app.models.questionnaire import Questionnaire
        from sqlalchemy import func, or_
        
        # 查找用户（支持通过ID、用户名或custom_id查找）
        user = db.query(User).filter(
            or_(
                User.id == user_id,
                User.username == user_id,
                User.custom_id == user_id
            )
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户 {user_id} 不存在"
            )
        
        # 查询用户的评估统计
        assessment_stats = db.query(
            func.count(Assessment.id).label('total'),
            func.sum(case((Assessment.status == 'completed', 1), else_=0)).label('completed'),
            func.sum(case((Assessment.status == 'pending', 1), else_=0)).label('pending'),
            func.sum(case((Assessment.status == 'error', 1), else_=0)).label('error')
        ).filter(Assessment.custom_id == user.custom_id).first()
        
        # 查询用户的问卷统计
        questionnaire_stats = db.query(
            func.count(Questionnaire.id).label('total'),
            func.sum(case((Questionnaire.status == 'completed', 1), else_=0)).label('completed'),
            func.sum(case((Questionnaire.status == 'pending', 1), else_=0)).label('pending'),
            func.sum(case((Questionnaire.status == 'error', 1), else_=0)).label('error')
        ).filter(Questionnaire.custom_id == user.custom_id).first()
        
        # 获取最近活动时间
        last_assessment = db.query(Assessment.updated_at).filter(
            Assessment.custom_id == user.custom_id
        ).order_by(Assessment.updated_at.desc()).first()
        
        last_questionnaire = db.query(Questionnaire.updated_at).filter(
            Questionnaire.custom_id == user.custom_id
        ).order_by(Questionnaire.updated_at.desc()).first()
        
        last_activity = None
        if last_assessment and last_questionnaire:
            last_activity = max(last_assessment.updated_at, last_questionnaire.updated_at).isoformat()
        elif last_assessment:
            last_activity = last_assessment.updated_at.isoformat()
        elif last_questionnaire:
            last_activity = last_questionnaire.updated_at.isoformat()
        
        # 判断用户状态
        total_errors = (assessment_stats.error or 0) + (questionnaire_stats.error or 0)
        total_pending = (assessment_stats.pending or 0) + (questionnaire_stats.pending or 0)
        
        if total_errors > 0:
            status = "error"
        elif total_pending > 5:
            status = "warning"
        else:
            status = "healthy"
        
        return {
            "id": user.id,
            "username": user.username,
            "custom_id": getattr(user, 'custom_id', None),
            "name": getattr(user, 'name', user.username),
            "email": user.email,
            "is_active": user.is_active,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "assessment_count": assessment_stats.total or 0,
            "assessment_completed": assessment_stats.completed or 0,
            "assessment_pending": assessment_stats.pending or 0,
            "assessment_error": assessment_stats.error or 0,
            "questionnaire_count": questionnaire_stats.total or 0,
            "questionnaire_completed": questionnaire_stats.completed or 0,
            "questionnaire_pending": questionnaire_stats.pending or 0,
            "questionnaire_error": questionnaire_stats.error or 0,
            "last_activity": last_activity
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户监控状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户监控状态失败: {str(e)}"
        )

@router.get("/users", response_model=Dict[str, Any])
async def get_users_monitoring(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有用户的监控数据"""
    try:
        from app.models.user import User
        from app.models.assessment import Assessment
        from app.models.questionnaire import Questionnaire
        from sqlalchemy import func
        
        # 获取所有用户
        users = db.query(User).all()
        
        users_monitoring = []
        for user in users:
            # 查询用户的评估统计
            assessment_stats = db.query(
                func.count(Assessment.id).label('total'),
                func.sum(case((Assessment.status == 'completed', 1), else_=0)).label('completed'),
                func.sum(case((Assessment.status == 'pending', 1), else_=0)).label('pending'),
                func.sum(case((Assessment.status == 'error', 1), else_=0)).label('error')
            ).filter(Assessment.custom_id == user.custom_id).first()
            
            # 查询用户的问卷统计
            questionnaire_stats = db.query(
                func.count(Questionnaire.id).label('total'),
                func.sum(case((Questionnaire.status == 'completed', 1), else_=0)).label('completed'),
                func.sum(case((Questionnaire.status == 'pending', 1), else_=0)).label('pending'),
                func.sum(case((Questionnaire.status == 'error', 1), else_=0)).label('error')
            ).filter(Questionnaire.custom_id == user.custom_id).first()
            
            users_monitoring.append({
                "id": user.id,
                "username": user.username,
                "custom_id": getattr(user, 'custom_id', None),
                "name": getattr(user, 'name', user.username),
                "email": user.email,
                "is_active": user.is_active,
                "assessment_count": assessment_stats.total or 0,
                "assessment_completed": assessment_stats.completed or 0,
                "assessment_pending": assessment_stats.pending or 0,
                "assessment_error": assessment_stats.error or 0,
                "questionnaire_count": questionnaire_stats.total or 0,
                "questionnaire_completed": questionnaire_stats.completed or 0,
                "questionnaire_pending": questionnaire_stats.pending or 0,
                "questionnaire_error": questionnaire_stats.error or 0
            })
        # 修正：无用户时返回结构化空数据
        return {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "users": users_monitoring,
            "total_users": len(users_monitoring)
        }
        
    except Exception as e:
        logger.error(f"获取用户监控数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户监控数据失败: {str(e)}"
        )

@router.get("/realtime", response_model=Dict[str, Any])
async def get_realtime_monitoring(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取实时监控数据"""
    try:
        # 获取实时监控数据
        status_data = await get_monitoring_status(db, current_user)
        
        # 添加实时统计信息
        realtime_data = {
            "timestamp": datetime.now().isoformat(),
            "status": status_data.get("status", "unknown"),
            "metrics": {
                "response_time": 0.1,  # 可以添加实际的响应时间监控
                "active_users": 0,     # 可以从数据库查询活跃用户数
                "system_load": 0.5     # 可以添加系统负载监控
            },
            "issues": status_data.get("details", {}),
            "trends": {
                "hourly_assessments": [],  # 可以添加趋势数据
                "hourly_questionnaires": []
            }
        }
        
        return realtime_data
        
    except Exception as e:
        logger.error(f"获取实时监控数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取实时监控数据失败: {str(e)}"
        )