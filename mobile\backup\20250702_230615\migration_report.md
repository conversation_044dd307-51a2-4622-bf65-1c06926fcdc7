# 迁移报告

**迁移时间**: 2025-07-02 23:06:20
**备份目录**: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230615

## 迁移摘要

- **总操作数**: 24
- **成功操作**: 20
- **警告操作**: 3
- **失败操作**: 1

## 详细日志

**✓ 备份文件**
- 时间: 2025-07-02T23:06:15.655400
- 状态: SUCCESS
- 详情: main.py

**✓ 备份文件**
- 时间: 2025-07-02T23:06:15.658733
- 状态: SUCCESS
- 详情: theme.py

**⚠ 备份文件**
- 时间: 2025-07-02T23:06:15.659408
- 状态: WARNING
- 详情: api/api_config.py 不存在

**✓ 备份文件**
- 时间: 2025-07-02T23:06:15.663770
- 状态: SUCCESS
- 详情: api/api_client.py

**✓ 备份文件**
- 时间: 2025-07-02T23:06:15.669858
- 状态: SUCCESS
- 详情: screens/homepage_screen.py

**⚠ 备份文件**
- 时间: 2025-07-02T23:06:15.671593
- 状态: WARNING
- 详情: config.json 不存在

**✓ 备份目录**
- 时间: 2025-07-02T23:06:15.938343
- 状态: SUCCESS
- 详情: data/

**✓ 备份目录**
- 时间: 2025-07-02T23:06:16.019754
- 状态: SUCCESS
- 详情: logs/

**✓ 备份完成**
- 时间: 2025-07-02T23:06:16.020645
- 状态: SUCCESS
- 详情: 共备份 6 个文件/目录

**✓ 配置迁移**
- 时间: 2025-07-02T23:06:16.024753
- 状态: SUCCESS
- 详情: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json

**⚠ 数据库迁移**
- 时间: 2025-07-02T23:06:16.027384
- 状态: WARNING
- 详情: 未找到可迁移的数据库文件

**✓ 用户数据迁移**
- 时间: 2025-07-02T23:06:16.041793
- 状态: SUCCESS
- 详情: data/users/

**✓ 用户数据迁移**
- 时间: 2025-07-02T23:06:16.044884
- 状态: SUCCESS
- 详情: cache/

**✓ 用户数据迁移完成**
- 时间: 2025-07-02T23:06:16.045854
- 状态: SUCCESS
- 详情: 共迁移 0 个文件

**✓ 更新导入**
- 时间: 2025-07-02T23:06:17.420655
- 状态: SUCCESS
- 详情: migration_tool.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:17.789058
- 状态: SUCCESS
- 详情: run_tests.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:18.063675
- 状态: SUCCESS
- 详情: test_mobile_fixes.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:18.225559
- 状态: SUCCESS
- 详情: logo.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:18.296975
- 状态: SUCCESS
- 详情: font_definitions.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:18.326017
- 状态: SUCCESS
- 详情: theming.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:19.654027
- 状态: SUCCESS
- 详情: label.py

**✓ 更新导入**
- 时间: 2025-07-02T23:06:19.672099
- 状态: SUCCESS
- 详情: textfield.py

**✓ 导入更新完成**
- 时间: 2025-07-02T23:06:20.515592
- 状态: SUCCESS
- 详情: 共更新 8 个文件

**✗ 创建迁移脚本**
- 时间: 2025-07-02T23:06:20.579545
- 状态: ERROR
- 详情: name 'missing_files' is not defined

## 错误详情

**创建迁移脚本**: name 'missing_files' is not defined

## 后续步骤

1. 检查上述错误信息
2. 手动修复相关问题
3. 重新运行迁移工具
4. 如需要，可从备份目录恢复文件

⚠️ 迁移过程中遇到问题，请根据错误信息进行修复。
