# -*- coding: utf-8 -*-
"""
管理日志屏幕模块

提供用户查看健康管理记录的功能，包括：
- 健康指标变化记录
- 医疗干预记录
- 健康建议记录
- 风险评估记录
"""

from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, NumericProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.widget import Widget
import os
import json
import sys
from datetime import datetime, date, timedelta
import threading
import tempfile
import traceback
from kivy.logger import Logger
from kivy.uix.progressbar import ProgressBar

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton, MDFabButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.list import MDList, MDListItem, MDListItemLeadingIcon, MDListItemHeadlineText, MDListItemSupportingText, MDListItemTrailingIcon
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldHelperText
from kivymd.uix.chip import MDChip, MDChipText
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.slider import MDSlider
from kivymd.uix.divider import MDDivider
from kivymd.uix.segmentedbutton import MDSegmentedButton, MDSegmentedButtonItem
# 使用Kivy的原生ProgressBar
from kivy.uix.progressbar import ProgressBar

# 自定义MDProgressBar类
class MDProgressBar(ProgressBar):
    """自定义MDProgressBar类，继承自Kivy的ProgressBar"""
    def __init__(self, **kwargs):
        self.color = kwargs.pop('color', [0, 0, 1, 1])
        super().__init__(**kwargs)

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入Logo组件
from widgets.logo import HealthLogo, add_logo_to_layout

# 定义KV语言字符串
KV = '''
<LogEntryCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: [1, 1, 1, 1]  # 白色背景
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(8)
        
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Primary"
            size_hint_x: 0.7
            
        MDLabel:
            text: root.date_text
            font_style: "Body"
            role: "small"
            theme_text_color: "Secondary"
            size_hint_x: 0.3
            halign: "right"
    
    MDDivider:
        height: dp(1)
    
    MDLabel:
        text: root.content_text
        font_style: "Body"
        role: "small"
        theme_text_color: "Primary"
        text_size: self.width, None
        size_hint_y: None
        height: self.texture_size[1]
        
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(4)
        
        # 使用标准标签替代MDChip
        MDBoxLayout:
            size_hint_x: None
            width: dp(100)
            padding: [dp(4), dp(2)]
            md_bg_color: root.category_color
            radius: [dp(16)]
            
            MDLabel:
                text: root.category
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]  # 白色文字
                halign: "center"
                padding: [dp(8), dp(4)]
            
        Widget:
            size_hint_x: 1
            
        MDIconButton:
            icon: "information"
            icon_size: dp(20)
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR
            on_release: root.on_detail()

<HealthIndicatorCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(8)
        
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Primary"
            
        Widget:
            size_hint_x: 1
            
        MDLabel:
            text: root.value_text
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Custom"
            text_color: root.value_color
    
    MDBoxLayout:
        orientation: 'vertical'
        size_hint_y: None
        height: dp(40)
        spacing: dp(4)
        
        MDLabel:
            text: root.description
            font_style: "Body"
            role: "small"
            theme_text_color: "Secondary"
            size_hint_y: None
            height: self.texture_size[1]
            
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(20)
            spacing: dp(8)
            
            MDProgressBar:
                id: progress
                value: root.progress_value
                color: root.progress_color
                size_hint_y: None
                height: dp(8)
                
            MDLabel:
                text: root.trend_text
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: root.trend_color
                size_hint_x: None
                width: dp(40)
                halign: "right"

<ManagementLogScreen>:
    canvas.before:
        Color:
            rgba: [0.95, 0.95, 0.95, 1]  # 浅灰色背景
        Rectangle:
            pos: self.pos
            size: self.size
            
    MDBoxLayout:
        orientation: 'vertical'
        
        # 顶部栏
        MDBoxLayout:
            id: top_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: [0.2, 0.6, 0.9, 1]  # 蓝色
            padding: [dp(8), dp(0), dp(8), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: [1, 1, 1, 1]  # 白色
                pos_hint: {"center_y": 0.5}
                on_release: root.go_back()
                
            MDLabel:
                text: "健康管理日志"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]  # 白色
                size_hint_x: 0.7
                pos_hint: {"center_y": 0.5}
                halign: "center"
                
            MDIconButton:
                icon: "filter-variant"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: [1, 1, 1, 1]  # 白色
                pos_hint: {"center_y": 0.5}
                on_release: root.show_filter_dialog()
        
        # 分段按钮
        MDSegmentedButton:
            id: segment_button
            size_hint_x: 0.9
            pos_hint: {"center_x": 0.5}
            size_hint_y: None
            height: dp(48)
            padding: [dp(16), dp(8), dp(16), dp(0)]
            
            MDSegmentedButtonItem:
                id: overview_tab
                text: "概览"
                selected: True
                on_release: root.switch_tab("overview")
                
            MDSegmentedButtonItem:
                id: indicators_tab
                text: "健康指标"
                on_release: root.switch_tab("indicators")
                
            MDSegmentedButtonItem:
                id: logs_tab
                text: "管理记录"
                on_release: root.switch_tab("logs")
        
        # 主内容区
        MDScrollView:
            id: scroll_view
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(12)
                
                # 概览面板
                MDBoxLayout:
                    id: overview_panel
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    opacity: 1
                    disabled: False
                    
                    # 健康评分卡片
                    MDCard:
                        id: health_score_card
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(120)
                        md_bg_color: [0.8, 0.9, 1, 1]  # 浅蓝色
                        radius: [dp(12)]
                        elevation: 2
                        padding: [dp(16), dp(12), dp(16), dp(12)]
                        
                        MDLabel:
                            text: "健康评分"
                            font_style: "Body"
                            role: "medium"
                            bold: True
                            theme_text_color: "Primary"
                            size_hint_y: None
                            height: dp(24)
                            
                        MDBoxLayout:
                            orientation: 'horizontal'
                            size_hint_y: None
                            height: dp(60)
                            
                            MDLabel:
                                id: health_score
                                text: "85"
                                font_style: "Headline"
                                role: "large"
                                bold: True
                                theme_text_color: "Custom"
                                text_color: [0.2, 0.6, 0.9, 1]  # 蓝色
                                halign: "center"
                                size_hint_x: 0.3
                                
                            MDBoxLayout:
                                orientation: 'vertical'
                                size_hint_x: 0.7
                                spacing: dp(4)
                                
                                MDLabel:
                                    text: "良好"
                                    font_style: "Body"
                                    role: "medium"
                                    bold: True
                                    theme_text_color: "Primary"
                                    
                                MDLabel:
                                    text: "您的健康状况良好，请继续保持健康的生活方式。"
                                    font_style: "Body"
                                    role: "small"
                                    theme_text_color: "Secondary"
                    
                    # 最近活动
                    MDLabel:
                        text: "最近活动"
                        font_style: "Body"
                        role: "medium"
                        bold: True
                        theme_text_color: "Primary"
                        size_hint_y: None
                        height: dp(24)
                    
                    # 活动列表容器
                    MDBoxLayout:
                        id: recent_activities
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)
                
                # 健康指标面板
                MDBoxLayout:
                    id: indicators_panel
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    opacity: 0
                    disabled: True
                    
                    # 指标列表容器
                    MDBoxLayout:
                        id: indicators_list
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(12)
                
                # 管理记录面板
                MDBoxLayout:
                    id: logs_panel
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    opacity: 0
                    disabled: True
                    
                    # 记录列表容器
                    MDBoxLayout:
                        id: logs_list
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class LogEntryCard(MDCard):
    """日志条目卡片组件"""
    title = StringProperty("")
    date_text = StringProperty("")
    content_text = StringProperty("")
    category = StringProperty("")
    category_color = ListProperty([0, 0, 0, 1])
    entry_data = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
    def on_detail(self):
        """查看详情"""
        screen = self.get_root_window().children[0].current_screen
        if hasattr(screen, 'show_log_detail'):
            screen.show_log_detail(self.entry_data)

class HealthIndicatorCard(MDCard):
    """健康指标卡片组件"""
    title = StringProperty("")
    value_text = StringProperty("")
    value_color = ListProperty([0, 0, 0, 1])
    description = StringProperty("")
    progress_value = NumericProperty(0)
    progress_color = ListProperty([0, 0, 0, 1])
    trend_text = StringProperty("")
    trend_color = ListProperty([0, 0, 0, 1])
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class ManagementLogScreen(BaseScreen):
    """健康管理日志屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        Builder.load_string(KV)
        self.log_entries = []
        self.health_indicators = []
        self.current_tab = "overview"
        self.filter_options = {
            "category": "all",
            "time_range": "all"
        }
        Clock.schedule_once(self.init_ui, 0.2)
    
    def init_ui(self, dt=0):
        self.load_data()
        self.refresh_ui()
    
    def on_enter(self):
        super().on_enter()
        self.init_ui()
    
    def go_back(self):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'homepage_screen'
    
    def switch_tab(self, tab_name):
        """切换标签页"""
        self.current_tab = tab_name
        
        # 更新UI可见性
        self.ids.overview_panel.opacity = 1 if tab_name == "overview" else 0
        self.ids.overview_panel.disabled = tab_name != "overview"
        
        self.ids.indicators_panel.opacity = 1 if tab_name == "indicators" else 0
        self.ids.indicators_panel.disabled = tab_name != "indicators"
        
        self.ids.logs_panel.opacity = 1 if tab_name == "logs" else 0
        self.ids.logs_panel.disabled = tab_name != "logs"
    
    def show_filter_dialog(self):
        """显示筛选对话框"""
        # 创建对话框内容
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(200),
            padding=[dp(0), dp(8), dp(0), dp(8)]
        )
        
        # 分类筛选
        category_label = MDLabel(
            text="分类",
            font_style="Body",
            role="medium",
            bold=True,
            size_hint_y=None,
            height=dp(24)
        )
        content.add_widget(category_label)
        
        category_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            spacing=dp(8)
        )
        
        categories = [
            {"name": "all", "text": "全部"},
            {"name": "health_indicator", "text": "健康指标"},
            {"name": "medical_intervention", "text": "医疗干预"},
            {"name": "health_advice", "text": "健康建议"},
            {"name": "risk_assessment", "text": "风险评估"}
        ]
        
        category_buttons = []
        selected_category = [self.filter_options["category"]]
        
        def on_category_select(category_name):
            selected_category[0] = category_name
            for btn, cat in zip(category_buttons, categories):
                if cat["name"] == category_name:
                    btn.style = "filled"
                else:
                    btn.style = "outlined"
        
        for category in categories:
            btn = MDButton(
                MDButtonText(text=category["text"]),
                style="filled" if category["name"] == selected_category[0] else "outlined",
                size_hint_x=None,
                width=dp(80)
            )
            btn.bind(on_release=lambda x, name=category["name"]: on_category_select(name))
            category_buttons.append(btn)
            category_layout.add_widget(btn)
        
        content.add_widget(category_layout)
        
        # 时间范围筛选
        time_label = MDLabel(
            text="时间范围",
            font_style="Body",
            role="medium",
            bold=True,
            size_hint_y=None,
            height=dp(24)
        )
        content.add_widget(time_label)
        
        time_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48),
            spacing=dp(8)
        )
        
        time_ranges = [
            {"name": "all", "text": "全部"},
            {"name": "week", "text": "一周内"},
            {"name": "month", "text": "一月内"},
            {"name": "quarter", "text": "三月内"},
            {"name": "year", "text": "一年内"}
        ]
        
        time_buttons = []
        selected_time = [self.filter_options["time_range"]]
        
        def on_time_select(time_name):
            selected_time[0] = time_name
            for btn, time_range in zip(time_buttons, time_ranges):
                if time_range["name"] == time_name:
                    btn.style = "filled"
                else:
                    btn.style = "outlined"
        
        for time_range in time_ranges:
            btn = MDButton(
                MDButtonText(text=time_range["text"]),
                style="filled" if time_range["name"] == selected_time[0] else "outlined",
                size_hint_x=None,
                width=dp(80)
            )
            btn.bind(on_release=lambda x, name=time_range["name"]: on_time_select(name))
            time_buttons.append(btn)
            time_layout.add_widget(btn)
        
        content.add_widget(time_layout)
        
        # 创建对话框
        dialog = MDDialog(
            title="筛选选项",
            content=content,
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="应用"),
                    style="filled",
                    on_release=lambda x: self.apply_filter(dialog, selected_category[0], selected_time[0])
                ),
            ],
        )
        dialog.open()
    
    def apply_filter(self, dialog, category, time_range):
        """应用筛选"""
        self.filter_options["category"] = category
        self.filter_options["time_range"] = time_range
        
        # 刷新UI
        self.refresh_ui()
        
        # 关闭对话框
        dialog.dismiss()
    
    def show_log_detail(self, entry_data):
        content = MDBoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(200))
        content.add_widget(MDLabel(text=entry_data["content"], font_style="Body", role="medium", theme_text_color="Primary"))
        dialog = MDDialog()
        dialog.add_widget(MDLabel(text="详细信息", font_style="Title", size_hint_y=None, height=dp(40)))
        dialog.add_widget(content)
        button_container = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48), padding=[dp(16), 0, dp(16), dp(8)])
        close_btn = MDButton(MDButtonText(text="关闭"), style="text", on_release=lambda x: dialog.dismiss())
        button_container.add_widget(close_btn)
        dialog.add_widget(button_container)
        dialog.open()
    
    def load_data(self):
        """加载数据"""
        self.load_log_entries()
        self.load_health_indicators()
    
    def load_log_entries(self):
        """加载日志条目"""
        try:
            # 在实际应用中，这里应该从API或本地存储加载数据
            # 这里使用模拟数据进行演示
            self.log_entries = [
                {
                    "id": "1",
                    "title": "血压检测异常",
                    "date": "2023-06-15",
                    "category": "health_indicator",
                    "category_text": "健康指标",
                    "content": "您的收缩压为145mmHg，舒张压为95mmHg，高于正常范围。建议减少盐分摄入，增加有氧运动，并定期监测血压变化。如持续异常，请咨询医生。"
                },
                {
                    "id": "2",
                    "title": "糖尿病风险评估",
                    "date": "2023-06-10",
                    "category": "risk_assessment",
                    "category_text": "风险评估",
                    "content": "根据您的家族史和当前健康指标，您的2型糖尿病风险为中等。建议每年进行一次空腹血糖检测，控制碳水化合物摄入，保持健康体重。"
                },
                {
                    "id": "3",
                    "title": "季节性过敏建议",
                    "date": "2023-05-20",
                    "category": "health_advice",
                    "category_text": "健康建议",
                    "content": "春季花粉过敏高发期已到，根据您的过敏史，建议：1. 外出戴口罩；2. 回家后更换衣物并洗手洗脸；3. 保持室内空气清新；4. 必要时使用抗过敏药物。"
                },
                {
                    "id": "4",
                    "title": "体检后用药调整",
                    "date": "2023-05-05",
                    "category": "medical_intervention",
                    "category_text": "医疗干预",
                    "content": "根据您最近的体检结果，医生调整了您的降压药物剂量。新的用药方案为：缬沙坦80mg，每日一次，早餐后服用。请严格按照医嘱服药，并在两周后复查血压。"
                },
                {
                    "id": "5",
                    "title": "血糖监测结果",
                    "date": "2023-04-25",
                    "category": "health_indicator",
                    "category_text": "健康指标",
                    "content": "您的空腹血糖为6.1mmol/L，处于正常高值范围。建议继续保持健康饮食习惯，控制碳水化合物摄入，增加运动量，定期监测血糖变化。"
                },
                {
                    "id": "6",
                    "title": "心脏健康评估",
                    "date": "2023-04-10",
                    "category": "risk_assessment",
                    "category_text": "风险评估",
                    "content": "根据您的年龄、血压、胆固醇水平和生活习惯，您的心血管疾病10年风险为8%，属于低风险水平。建议保持健康生活方式，定期监测血压和血脂。"
                },
                {
                    "id": "7",
                    "title": "运动处方更新",
                    "date": "2023-03-15",
                    "category": "health_advice",
                    "category_text": "健康建议",
                    "content": "根据您的健康状况和目标，更新了您的运动处方：1. 有氧运动：每周5次，每次30分钟，中等强度；2. 力量训练：每周2次，每次20分钟，中等强度；3. 柔韧性训练：每天10分钟。"
                },
                {
                    "id": "8",
                    "title": "疫苗接种记录",
                    "date": "2023-03-01",
                    "category": "medical_intervention",
                    "category_text": "医疗干预",
                    "content": "您已完成流感疫苗接种。接种日期：2023年3月1日，接种地点：市中心医院，疫苗批号：FL2023001。下次建议接种时间：2024年2月-3月。"
                }
            ]
        except Exception as e:
            Logger.error(f"加载日志条目失败: {str(e)}")
            self.log_entries = []
    
    def load_health_indicators(self):
        """加载健康指标"""
        try:
            # 在实际应用中，这里应该从API或本地存储加载数据
            # 这里使用模拟数据进行演示
            self.health_indicators = [
                {
                    "id": "1",
                    "title": "血压",
                    "value": "130/85",
                    "status": "normal",  # normal, warning, danger
                    "description": "收缩压/舒张压 (mmHg)",
                    "progress": 70,  # 0-100
                    "trend": "down",  # up, down, stable
                    "trend_text": "-5%"
                },
                {
                    "id": "2",
                    "title": "血糖",
                    "value": "5.8",
                    "status": "normal",
                    "description": "空腹血糖 (mmol/L)",
                    "progress": 60,
                    "trend": "stable",
                    "trend_text": "0%"
                },
                {
                    "id": "3",
                    "title": "体重",
                    "value": "68",
                    "status": "normal",
                    "description": "体重 (kg)",
                    "progress": 50,
                    "trend": "down",
                    "trend_text": "-2%"
                },
                {
                    "id": "4",
                    "title": "总胆固醇",
                    "value": "5.2",
                    "status": "warning",
                    "description": "总胆固醇 (mmol/L)",
                    "progress": 80,
                    "trend": "up",
                    "trend_text": "+3%"
                },
                {
                    "id": "5",
                    "title": "心率",
                    "value": "72",
                    "status": "normal",
                    "description": "静息心率 (次/分)",
                    "progress": 40,
                    "trend": "stable",
                    "trend_text": "0%"
                },
                {
                    "id": "6",
                    "title": "BMI",
                    "value": "23.5",
                    "status": "normal",
                    "description": "身体质量指数",
                    "progress": 55,
                    "trend": "down",
                    "trend_text": "-1%"
                }
            ]
        except Exception as e:
            Logger.error(f"加载健康指标失败: {str(e)}")
            self.health_indicators = []
    
    def refresh_ui(self):
        """刷新UI"""
        self.refresh_overview_panel()
        self.refresh_indicators_panel()
        self.refresh_logs_panel()
    
    def refresh_overview_panel(self):
        """刷新概览面板"""
        try:
            # 清空最近活动列表
            self.ids.recent_activities.clear_widgets()
            
            # 筛选最近活动
            filtered_entries = self.filter_entries(self.log_entries)
            recent_entries = filtered_entries[:3]  # 只显示最近3条
            
            if not recent_entries:
                # 显示空状态
                empty_card = MDCard(
                    MDBoxLayout(
                        MDLabel(
                            text="暂无最近活动记录",
                            halign="center",
                            theme_text_color="Secondary",
                            font_style="Body",
                            role="medium"
                        ),
                        orientation='vertical',
                        padding=dp(32)
                    ),
                    size_hint_y=None,
                    height=dp(80),
                    md_bg_color=self.app.theme.SURFACE_COLOR,
                    radius=[dp(12)],
                    elevation=1
                )
                self.ids.recent_activities.add_widget(empty_card)
                return
            
            # 添加最近活动条目
            for entry in recent_entries:
                entry_card = LogEntryCard(
                    title=entry["title"],
                    date_text=entry["date"],
                    content_text=entry["content"][:100] + ("..." if len(entry["content"]) > 100 else ""),
                    category=entry["category_text"],
                    category_color=self.get_category_color(entry["category"]),
                    entry_data=entry
                )
                self.ids.recent_activities.add_widget(entry_card)
                
        except Exception as e:
            Logger.error(f"刷新概览面板失败: {str(e)}")
    
    def refresh_indicators_panel(self):
        """刷新健康指标面板"""
        try:
            # 清空指标列表
            self.ids.indicators_list.clear_widgets()
            
            if not self.health_indicators:
                # 显示空状态
                # 创建布局
                box_layout = MDBoxLayout(
                    orientation='vertical',
                    padding=dp(32)
                )
                
                # 创建标签
                label = MDLabel(
                    text="暂无健康指标数据",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium"
                )
                
                # 添加标签到布局
                box_layout.add_widget(label)
                
                # 创建卡片并添加布局
                empty_card = MDCard()
                empty_card.size_hint_y = None
                empty_card.height = dp(80)
                empty_card.md_bg_color = self.app.theme.SURFACE_COLOR
                empty_card.radius = [dp(12)]
                empty_card.elevation = 1
                empty_card.add_widget(box_layout)
                return
            
            # 添加健康指标卡片
            for indicator in self.health_indicators:
                # 获取状态对应的颜色
                value_color = self.get_status_color(indicator["status"])
                
                # 获取趋势对应的颜色和文本
                trend_color = self.get_trend_color(indicator["trend"])
                
                indicator_card = HealthIndicatorCard(
                    title=indicator["title"],
                    value_text=indicator["value"],
                    value_color=value_color,
                    description=indicator["description"],
                    progress_value=indicator["progress"],
                    progress_color=value_color,
                    trend_text=indicator["trend_text"],
                    trend_color=trend_color
                )
                self.ids.indicators_list.add_widget(indicator_card)
                
        except Exception as e:
            Logger.error(f"刷新健康指标面板失败: {str(e)}")
    
    def refresh_logs_panel(self):
        """刷新管理记录面板"""
        try:
            # 清空记录列表
            self.ids.logs_list.clear_widgets()
            
            # 筛选记录
            filtered_entries = self.filter_entries(self.log_entries)
            
            if not filtered_entries:
                # 显示空状态
                # 创建布局
                box_layout = MDBoxLayout(
                    orientation='vertical',
                    padding=dp(32)
                )
                
                # 创建标签
                label = MDLabel(
                    text="还没有日志记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium"
                )
                
                # 添加标签到布局
                box_layout.add_widget(label)
                
                # 创建卡片并添加布局
                empty_card = MDCard()
                empty_card.size_hint_y = None
                empty_card.height = dp(120)
                empty_card.md_bg_color = self.app.theme.SURFACE_COLOR
                empty_card.radius = [dp(12)]
                empty_card.elevation = 1
                empty_card.add_widget(box_layout)
                return
            
            # 添加记录条目
            for entry in filtered_entries:
                entry_card = LogEntryCard(
                    title=entry["title"],
                    date_text=entry["date"],
                    content_text=entry["content"][:100] + ("..." if len(entry["content"]) > 100 else ""),
                    category=entry["category_text"],
                    category_color=self.get_category_color(entry["category"]),
                    entry_data=entry
                )
                self.ids.logs_list.add_widget(entry_card)
                
        except Exception as e:
            Logger.error(f"刷新管理记录面板失败: {str(e)}")
    
    def filter_entries(self, entries):
        """根据筛选条件过滤条目"""
        filtered = entries.copy()
        
        # 按分类筛选
        if self.filter_options["category"] != "all":
            filtered = [entry for entry in filtered if entry["category"] == self.filter_options["category"]]
        
        # 按时间范围筛选
        if self.filter_options["time_range"] != "all":
            today = datetime.now().date()
            
            if self.filter_options["time_range"] == "week":
                # 一周内
                start_date = today - timedelta(days=7)
            elif self.filter_options["time_range"] == "month":
                # 一月内
                start_date = today - timedelta(days=30)
            elif self.filter_options["time_range"] == "quarter":
                # 三月内
                start_date = today - timedelta(days=90)
            elif self.filter_options["time_range"] == "year":
                # 一年内
                start_date = today - timedelta(days=365)
            else:
                start_date = None
            
            if start_date:
                filtered = [entry for entry in filtered if datetime.strptime(entry["date"], "%Y-%m-%d").date() >= start_date]
        
        return filtered
    
    def get_category_color(self, category):
        """获取分类对应的颜色"""
        if category == "health_indicator":
            return self.app.theme.PRIMARY_COLOR
        elif category == "medical_intervention":
            return self.app.theme.HEALTH_RISK_COLOR
        elif category == "health_advice":
            return self.app.theme.HEALTH_DATA_COLOR
        elif category == "risk_assessment":
            return self.app.theme.WARNING_COLOR
        else:
            return self.app.theme.TEXT_SECONDARY
    
    def get_status_color(self, status):
        """获取状态对应的颜色"""
        if status == "normal":
            return self.app.theme.SUCCESS_COLOR
        elif status == "warning":
            return self.app.theme.WARNING_COLOR
        elif status == "danger":
            return self.app.theme.ERROR_COLOR
        else:
            return self.app.theme.TEXT_PRIMARY
    
    def get_trend_color(self, trend):
        """获取趋势对应的颜色"""
        if trend == "up":
            return self.app.theme.ERROR_COLOR
        elif trend == "down":
            return self.app.theme.SUCCESS_COLOR
        else:  # stable
            return self.app.theme.INFO_COLOR
    
    def show_info(self, message):
        """显示信息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()