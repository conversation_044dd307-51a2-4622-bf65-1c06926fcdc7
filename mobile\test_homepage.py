#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试homepage_screen的新布局
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from screens.homepage_screen import HomepageScreen
from theme import AppTheme

class TestHomepageScreen(HomepageScreen):
    """测试用的主页屏幕，重写on_enter方法"""
    def on_enter(self):
        """进入屏幕时调用 - 测试版本，跳过登录检查"""
        # 跳过登录检查，直接加载用户数据
        self.load_user_data()

class TestHomepageApp(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.theme = AppTheme()
        # 模拟用户数据
        self.user_data = {
            'username': '测试用户',
            'gender': 'male'
        }
        
    def build(self):
        # 设置主题
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # 创建屏幕管理器
        sm = MDScreenManager()
        
        # 添加主页屏幕
        homepage = TestHomepageScreen(name='homepage')
        sm.add_widget(homepage)
        
        # 设置当前屏幕
        sm.current = 'homepage'
        
        return sm

if __name__ == '__main__':
    TestHomepageApp().run()