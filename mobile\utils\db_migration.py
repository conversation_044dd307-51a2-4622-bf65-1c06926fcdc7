import logging
import sqlite3
from datetime import datetime
from .database import get_db_manager
from .db_models import Schema

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('db_migration')

class DatabaseMigration:
    """数据库迁移工具"""
    
    def __init__(self):
        self.db_manager = get_db_manager()
    
    def migrate_user_database(self, user_id):
        """迁移用户数据库到最新版本"""
        logger.info(f"开始迁移用户 {user_id} 的数据库")
        
        # 连接到用户数据库
        if not self.db_manager.connect(user_id):
            logger.error(f"无法连接到用户 {user_id} 的数据库")
            return False
        
        try:
            # 检查并迁移各个表
            self._migrate_medication_table(user_id)
            self._migrate_examination_report_table(user_id)
            self._migrate_other_tables(user_id)
            
            logger.info(f"用户 {user_id} 的数据库迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"迁移用户 {user_id} 的数据库时出错: {str(e)}")
            return False
        finally:
            self.db_manager.disconnect()
    
    def _migrate_medication_table(self, user_id):
        """迁移medication表"""
        logger.info("检查medication表结构")
        
        # 检查表是否存在
        if not self.db_manager.table_exists('medication', user_id):
            logger.info("medication表不存在，创建新表")
            self.db_manager.execute_script(Schema.MEDICATION, user_id)
            return
        
        # 检查custom_id字段是否存在
        table_info = self.db_manager.get_table_info('medication', user_id)
        columns = [col['name'] for col in table_info]
        
        if 'custom_id' not in columns:
            logger.info("medication表缺少custom_id字段，开始迁移")
            self._add_custom_id_to_medication(user_id)
        else:
            logger.info("medication表已包含custom_id字段")
    
    def _add_custom_id_to_medication(self, user_id):
        """为medication表添加custom_id字段"""
        try:
            with self.db_manager.get_connection(user_id) as conn:
                # 备份原表数据
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM medication")
                old_data = cursor.fetchall()
                
                # 重命名原表
                conn.execute("ALTER TABLE medication RENAME TO medication_old")
                
                # 创建新表
                conn.execute(Schema.MEDICATION)
                
                # 迁移数据
                if old_data:
                    # 获取当前用户的custom_id
                    # 这里需要从用户数据中获取，暂时使用user_id作为custom_id
                    for row in old_data:
                        # 将Row对象转换为字典
                        row_dict = dict(row) if hasattr(row, 'keys') else row
                        
                        # 构建插入语句
                        insert_sql = """
                        INSERT INTO medication (
                            custom_id, name, dosage, frequency, start_date, end_date,
                            instructions, prescription_required, notes, medication_type,
                            is_current, stop_reason, prescriber, hospital, purpose,
                            side_effects, specification, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        # 准备数据，添加custom_id字段
                        values = (
                            user_id,  # 使用user_id作为custom_id
                            row_dict.get('name', ''),
                            row_dict.get('dosage', ''),
                            row_dict.get('frequency', ''),
                            row_dict.get('start_date', ''),
                            row_dict.get('end_date', ''),
                            row_dict.get('instructions', ''),
                            row_dict.get('prescription_required', 0),
                            row_dict.get('notes', ''),
                            row_dict.get('medication_type', ''),
                            row_dict.get('is_current', 1),
                            row_dict.get('stop_reason', ''),
                            row_dict.get('prescriber', ''),
                            row_dict.get('hospital', ''),
                            row_dict.get('purpose', ''),
                            row_dict.get('side_effects', ''),
                            row_dict.get('specification', ''),
                            row_dict.get('created_at', datetime.now().isoformat()),
                            row_dict.get('updated_at', datetime.now().isoformat())
                        )
                        
                        conn.execute(insert_sql, values)
                
                # 删除旧表
                conn.execute("DROP TABLE medication_old")
                
                # 提交事务
                conn.commit()
                
                logger.info(f"medication表迁移完成，迁移了 {len(old_data)} 条记录")
                
        except Exception as e:
            logger.error(f"迁移medication表时出错: {str(e)}")
            raise
    
    def _migrate_examination_report_table(self, user_id):
        """迁移examination_report表"""
        logger.info("检查examination_report表结构")
        
        # 检查表是否存在
        if not self.db_manager.table_exists('examination_report', user_id):
            logger.info("examination_report表不存在，创建新表")
            self.db_manager.execute_script(Schema.EXAMINATION_REPORT, user_id)
            return
        
        # 检查custom_id字段是否存在
        table_info = self.db_manager.get_table_info('examination_report', user_id)
        columns = [col['name'] for col in table_info]
        
        if 'custom_id' not in columns:
            logger.info("examination_report表缺少custom_id字段，开始迁移")
            self._add_custom_id_to_examination_report(user_id)
        else:
            logger.info("examination_report表已包含custom_id字段")
    
    def _add_custom_id_to_examination_report(self, user_id):
        """为examination_report表添加custom_id字段"""
        try:
            with self.db_manager.get_connection(user_id) as conn:
                # 备份原表数据
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM examination_report")
                old_data = cursor.fetchall()
                
                # 重命名原表
                conn.execute("ALTER TABLE examination_report RENAME TO examination_report_old")
                
                # 创建新表
                conn.execute(Schema.EXAMINATION_REPORT)
                
                # 迁移数据
                if old_data:
                    for row in old_data:
                        # 将Row对象转换为字典
                        row_dict = dict(row) if hasattr(row, 'keys') else row
                        
                        # 构建插入语句
                        insert_sql = """
                        INSERT INTO examination_report (
                            custom_id, hospital_name, exam_type, exam_part, exam_time,
                            report_time, device, doctor, description, conclusion,
                            notes, local_file_path, cloud_file_id, record_time, last_update_time
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        # 准备数据，添加custom_id字段
                        values = (
                            user_id,  # 使用user_id作为custom_id
                            row_dict.get('hospital_name', ''),
                            row_dict.get('exam_type', ''),
                            row_dict.get('exam_part', ''),
                            row_dict.get('exam_time', ''),
                            row_dict.get('report_time', ''),
                            row_dict.get('device', ''),
                            row_dict.get('doctor', ''),
                            row_dict.get('description', ''),
                            row_dict.get('conclusion', ''),
                            row_dict.get('notes', ''),
                            row_dict.get('local_file_path', ''),
                            row_dict.get('cloud_file_id', ''),
                            row_dict.get('record_time', datetime.now().isoformat()),
                            row_dict.get('last_update_time', datetime.now().isoformat())
                        )
                        
                        conn.execute(insert_sql, values)
                
                # 删除旧表
                conn.execute("DROP TABLE examination_report_old")
                
                # 提交事务
                conn.commit()
                
                logger.info(f"examination_report表迁移完成，迁移了 {len(old_data)} 条记录")
                
        except Exception as e:
            logger.error(f"迁移examination_report表时出错: {str(e)}")
            raise
    
    def _migrate_other_tables(self, user_id):
        """迁移其他可能需要custom_id字段的表"""
        tables_to_check = [
            'health_info', 'disease_history', 'family_disease', 'drug_allergy',
            'gene_info', 'hospital_record', 'surgery_record', 'outpatient_record',
            'lab_report', 'questionnaire', 'assessment_scale', 'health_log',
            'doctor_log', 'wearable_device', 'file_upload'
        ]
        
        for table_name in tables_to_check:
            if self.db_manager.table_exists(table_name, user_id):
                table_info = self.db_manager.get_table_info(table_name, user_id)
                columns = [col['name'] for col in table_info]
                
                if 'custom_id' not in columns:
                    logger.warning(f"表 {table_name} 缺少custom_id字段，需要手动迁移")
                    # 这里可以添加具体的迁移逻辑
    
    def migrate_all_users(self):
        """迁移所有用户的数据库"""
        logger.info("开始迁移所有用户的数据库")
        
        # 获取所有用户ID
        user_db_map = self.db_manager._load_user_db_map()
        
        success_count = 0
        total_count = len(user_db_map)
        
        for user_id in user_db_map.keys():
            try:
                if self.migrate_user_database(user_id):
                    success_count += 1
                    logger.info(f"用户 {user_id} 迁移成功")
                else:
                    logger.error(f"用户 {user_id} 迁移失败")
            except Exception as e:
                logger.error(f"迁移用户 {user_id} 时出错: {str(e)}")
        
        logger.info(f"数据库迁移完成: {success_count}/{total_count} 个用户迁移成功")
        return success_count == total_count


def migrate_current_user_database(user_id):
    """迁移当前用户的数据库"""
    migration = DatabaseMigration()
    return migration.migrate_user_database(user_id)


def migrate_all_databases():
    """迁移所有用户的数据库"""
    migration = DatabaseMigration()
    return migration.migrate_all_users()