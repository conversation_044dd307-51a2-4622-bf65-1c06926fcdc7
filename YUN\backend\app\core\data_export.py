#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据导入导出工具模块
提供Excel、CSV、JSON等格式的数据处理功能
支持健康管理系统中各种数据表的导入导出操作

版本: 2.0
作者: Health Management System
创建时间: 2024
更新时间: 2024-12-30
"""

import os
import io
import json
import csv
import asyncio
import pandas as pd
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union, Type, BinaryIO, Tuple
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import zipfile
import tempfile
from contextlib import asynccontextmanager
import uuid
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy import inspect, text, MetaData, Table
from sqlalchemy.ext.declarative import DeclarativeMeta
from fastapi import HTTPException, UploadFile
from fastapi.responses import StreamingResponse

from .env_config import env_config
from .database_utils import DatabaseManager
from .error_handler import ErrorHandler, BusinessException
from .logging_utils import get_logger
from .file_utils import FileManager, FileType
from .security_utils import SecurityManager
from .validators import DataValidator
from .cache_utils import CacheManager
from ..models import *

logger = get_logger(__name__)

# 配置信息
config = env_config

class ExportFormat(str, Enum):
    """导出格式枚举"""
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"
    XML = "xml"
    PARQUET = "parquet"
    TSV = "tsv"
    YAML = "yaml"

class ImportFormat(str, Enum):
    """导入格式枚举"""
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"
    XML = "xml"
    TSV = "tsv"
    YAML = "yaml"

class DataOperation(str, Enum):
    """数据操作类型"""
    INSERT = "insert"
    UPDATE = "update"
    UPSERT = "upsert"
    DELETE = "delete"
    REPLACE = "replace"

class CompressionType(str, Enum):
    """压缩类型"""
    NONE = "none"
    ZIP = "zip"
    GZIP = "gzip"
    BZIP2 = "bzip2"

@dataclass
class ExportConfig:
    """导出配置"""
    format: ExportFormat
    include_headers: bool = True
    include_metadata: bool = True
    compression: CompressionType = CompressionType.NONE
    encoding: str = "utf-8"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    chunk_size: int = 10000
    max_rows: Optional[int] = None
    columns: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None
    custom_filename: Optional[str] = None
    include_relations: bool = False
    flatten_json: bool = True
    decimal_places: int = 2
    null_value: str = ""
    
    def __post_init__(self):
        """配置验证"""
        if self.chunk_size <= 0:
            raise ValueError("chunk_size must be positive")
        if self.max_rows is not None and self.max_rows <= 0:
            raise ValueError("max_rows must be positive")

@dataclass
class ImportConfig:
    """导入配置"""
    format: ImportFormat
    operation: DataOperation = DataOperation.INSERT
    validate_data: bool = True
    skip_errors: bool = False
    batch_size: int = 1000
    encoding: str = "utf-8"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    column_mapping: Optional[Dict[str, str]] = None
    default_values: Optional[Dict[str, Any]] = None
    unique_fields: Optional[List[str]] = None
    ignore_duplicates: bool = False
    update_existing: bool = False
    create_backup: bool = True
    
    def __post_init__(self):
        """配置验证"""
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")

@dataclass
class DataValidationResult:
    """数据验证结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    total_rows: int = 0
    valid_rows: int = 0
    invalid_rows: int = 0
    validation_time: float = 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_rows == 0:
            return 0.0
        return (self.valid_rows / self.total_rows) * 100

@dataclass
class ExportResult:
    """导出结果"""
    success: bool
    file_path: Optional[str] = None
    file_size: int = 0
    rows_exported: int = 0
    export_time: float = 0.0
    format: Optional[ExportFormat] = None
    compression: Optional[CompressionType] = None
    errors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ImportResult:
    """导入结果"""
    success: bool
    rows_processed: int = 0
    rows_inserted: int = 0
    rows_updated: int = 0
    rows_skipped: int = 0
    rows_failed: int = 0
    import_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    backup_file: Optional[str] = None

class DataExportService:
    """数据导出服务"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.db_manager = DatabaseManager(config)
        self.file_manager = FileManager()
        self.security_manager = SecurityManager()
        self.cache_manager = CacheManager()
        self.error_handler = ErrorHandler()
        self.validator = DataValidator()
        
        # 支持的表映射
        self._table_models = self._initialize_table_models()
    
    def _initialize_table_models(self) -> Dict[str, Type]:
        """初始化表模型映射"""
        models = {
            'users': User,
            'health_records': HealthRecord,
            'questionnaire_templates': QuestionnaireTemplate,
            'questionnaire_results': QuestionnaireResult,
            'assessment_results': AssessmentResult,
            'assessment_templates': AssessmentTemplate,
            # 'clinical_scales': ClinicalScale,  # 模型不存在，暂时注释
            'health_diaries': HealthDiary,
            'medical_records': MedicalRecord,
            'lab_reports': LabReport,
            'examination_reports': ExaminationReport,
            'follow_up_records': FollowUpRecord,
            'medications': Medication,
            'imaging_reports': ImagingReport,
            'other_records': OtherRecord,
            'user_health_records': HealthRecord,  # 使用HealthRecord替代不存在的UserHealthRecord
            'questionnaire_responses': QuestionnaireResponse,
            'assessment_responses': AssessmentResponse,
            'questionnaire_distributions': QuestionnaireDistribution,
            'assessment_distributions': AssessmentDistribution
        }
        
        # 验证模型是否存在
        validated_models = {}
        for table_name, model_class in models.items():
            try:
                if hasattr(model_class, '__tablename__'):
                    validated_models[table_name] = model_class
                    self.logger.debug(f"已注册表模型: {table_name} -> {model_class.__name__}")
            except Exception as e:
                self.logger.warning(f"跳过无效模型 {table_name}: {str(e)}")
        
        self.logger.info(f"已初始化 {len(validated_models)} 个表模型")
        return validated_models
    
    def get_supported_tables(self) -> List[str]:
        """获取支持的表列表"""
        return list(self._table_models.keys())
    
    def get_table_model(self, table_name: str) -> Type:
        """获取表模型"""
        if table_name not in self._table_models:
            raise BusinessException(f"不支持的表: {table_name}")
        return self._table_models[table_name]
    
    async def export_data(
        self, 
        table_name: str, 
        config: ExportConfig, 
        db: Session
    ) -> ExportResult:
        """导出数据"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始导出数据: {table_name}, 格式: {config.format.value}")
            
            # 验证表名
            model_class = self.get_table_model(table_name)
            
            # 构建查询
            query = self._build_export_query(db, model_class, config)
            
            # 获取数据
            data = await self._fetch_data(query, config)
            
            # 生成文件
            file_path = await self._generate_export_file(data, config, table_name)
            
            # 计算文件大小
            file_size = os.path.getsize(file_path) if file_path and os.path.exists(file_path) else 0
            
            export_time = (datetime.now() - start_time).total_seconds()
            
            result = ExportResult(
                success=True,
                file_path=file_path,
                file_size=file_size,
                rows_exported=len(data),
                export_time=export_time,
                format=config.format,
                compression=config.compression,
                metadata={
                    'table_name': table_name,
                    'export_timestamp': start_time.isoformat(),
                    'config': config.__dict__
                }
            )
            
            self.logger.info(f"导出完成: {len(data)} 行, 耗时: {export_time:.2f}秒")
            return result
            
        except Exception as e:
            export_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"导出失败: {str(e)}"
            self.logger.error(error_msg)
            
            return ExportResult(
                success=False,
                export_time=export_time,
                errors=[error_msg]
            )
    
    def _build_export_query(self, db: Session, model_class: Type, config: ExportConfig):
        """构建导出查询"""
        query = db.query(model_class)
        
        # 应用过滤器
        if config.filters:
            for field, value in config.filters.items():
                if hasattr(model_class, field):
                    query = query.filter(getattr(model_class, field) == value)
        
        # 应用列选择
        if config.columns:
            selected_columns = []
            for col in config.columns:
                if hasattr(model_class, col):
                    selected_columns.append(getattr(model_class, col))
            if selected_columns:
                query = query.with_entities(*selected_columns)
        
        # 应用行数限制
        if config.max_rows:
            query = query.limit(config.max_rows)
        
        return query
    
    async def _fetch_data(self, query, config: ExportConfig) -> List[Dict[str, Any]]:
        """获取数据"""
        data = []
        
        try:
            # 分块获取数据
            offset = 0
            while True:
                chunk = query.offset(offset).limit(config.chunk_size).all()
                if not chunk:
                    break
                
                # 转换为字典
                for row in chunk:
                    row_dict = self._row_to_dict(row)
                    data.append(row_dict)
                
                offset += config.chunk_size
                
                # 检查最大行数限制
                if config.max_rows and len(data) >= config.max_rows:
                    data = data[:config.max_rows]
                    break
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")
            raise BusinessException(f"数据获取失败: {str(e)}")
    
    def _row_to_dict(self, row) -> Dict[str, Any]:
        """将数据行转换为字典"""
        if hasattr(row, '__dict__'):
            # SQLAlchemy 模型对象
            result = {}
            for key, value in row.__dict__.items():
                if not key.startswith('_'):
                    result[key] = self._serialize_value(value)
            return result
        elif hasattr(row, '_asdict'):
            # 命名元组
            return {k: self._serialize_value(v) for k, v in row._asdict().items()}
        else:
            # 其他类型
            return {'value': self._serialize_value(row)}
    
    def _serialize_value(self, value: Any) -> Any:
        """序列化值"""
        if isinstance(value, (datetime, date)):
            return value.isoformat()
        elif isinstance(value, Decimal):
            return float(value)
        elif isinstance(value, uuid.UUID):
            return str(value)
        elif value is None:
            return None
        else:
            return value
    
    async def _generate_export_file(
        self, 
        data: List[Dict[str, Any]], 
        config: ExportConfig, 
        table_name: str
    ) -> str:
        """生成导出文件"""
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = config.custom_filename or f"{table_name}_export_{timestamp}.{config.format.value}"
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="data_export_")
        file_path = os.path.join(temp_dir, filename)
        
        try:
            if config.format == ExportFormat.CSV:
                await self._export_to_csv(data, file_path, config)
            elif config.format == ExportFormat.JSON:
                await self._export_to_json(data, file_path, config)
            elif config.format == ExportFormat.EXCEL:
                await self._export_to_excel(data, file_path, config)
            elif config.format == ExportFormat.XML:
                await self._export_to_xml(data, file_path, config)
            elif config.format == ExportFormat.PARQUET:
                await self._export_to_parquet(data, file_path, config)
            else:
                raise BusinessException(f"不支持的导出格式: {config.format}")
            
            # 应用压缩
            if config.compression != CompressionType.NONE:
                file_path = await self._compress_file(file_path, config.compression)
            
            return file_path
            
        except Exception as e:
            self.logger.error(f"生成导出文件失败: {str(e)}")
            raise BusinessException(f"文件生成失败: {str(e)}")
    
    async def _export_to_csv(self, data: List[Dict[str, Any]], file_path: str, config: ExportConfig):
        """导出为CSV格式"""
        if not data:
            # 创建空文件
            with open(file_path, 'w', encoding=config.encoding, newline='') as f:
                pass
            return
        
        fieldnames = list(data[0].keys())
        
        with open(file_path, 'w', encoding=config.encoding, newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            if config.include_headers:
                writer.writeheader()
            
            for row in data:
                # 处理空值
                processed_row = {k: (v if v is not None else config.null_value) for k, v in row.items()}
                writer.writerow(processed_row)
    
    async def _export_to_json(self, data: List[Dict[str, Any]], file_path: str, config: ExportConfig):
        """导出为JSON格式"""
        export_data = {
            'data': data,
            'metadata': {
                'export_time': datetime.now().isoformat(),
                'total_rows': len(data),
                'format': config.format.value
            } if config.include_metadata else None
        }
        
        if not config.include_metadata:
            export_data = data
        
        with open(file_path, 'w', encoding=config.encoding) as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
    
    async def _export_to_excel(self, data: List[Dict[str, Any]], file_path: str, config: ExportConfig):
        """导出为Excel格式"""
        df = pd.DataFrame(data)
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Data', index=False)
            
            if config.include_metadata:
                metadata_df = pd.DataFrame([
                    {'Key': 'Export Time', 'Value': datetime.now().isoformat()},
                    {'Key': 'Total Rows', 'Value': len(data)},
                    {'Key': 'Format', 'Value': config.format.value}
                ])
                metadata_df.to_excel(writer, sheet_name='Metadata', index=False)
    
    async def _export_to_xml(self, data: List[Dict[str, Any]], file_path: str, config: ExportConfig):
        """导出为XML格式"""
        try:
            import xml.etree.ElementTree as ET
            
            root = ET.Element('data')
            
            if config.include_metadata:
                metadata = ET.SubElement(root, 'metadata')
                ET.SubElement(metadata, 'export_time').text = datetime.now().isoformat()
                ET.SubElement(metadata, 'total_rows').text = str(len(data))
                ET.SubElement(metadata, 'format').text = config.format.value
            
            records = ET.SubElement(root, 'records')
            
            for row in data:
                record = ET.SubElement(records, 'record')
                for key, value in row.items():
                    elem = ET.SubElement(record, key)
                    elem.text = str(value) if value is not None else config.null_value
            
            tree = ET.ElementTree(root)
            tree.write(file_path, encoding=config.encoding, xml_declaration=True)
            
        except ImportError:
            raise BusinessException("XML导出需要xml模块支持")
    
    async def _export_to_parquet(self, data: List[Dict[str, Any]], file_path: str, config: ExportConfig):
        """导出为Parquet格式"""
        try:
            df = pd.DataFrame(data)
            df.to_parquet(file_path, index=False)
        except ImportError:
            raise BusinessException("Parquet导出需要pyarrow模块支持")
    
    async def _compress_file(self, file_path: str, compression: CompressionType) -> str:
        """压缩文件"""
        if compression == CompressionType.ZIP:
            zip_path = file_path + '.zip'
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(file_path, os.path.basename(file_path))
            os.remove(file_path)
            return zip_path
        elif compression == CompressionType.GZIP:
            import gzip
            gz_path = file_path + '.gz'
            with open(file_path, 'rb') as f_in:
                with gzip.open(gz_path, 'wb') as f_out:
                    f_out.writelines(f_in)
            os.remove(file_path)
            return gz_path
        elif compression == CompressionType.BZIP2:
            import bz2
            bz2_path = file_path + '.bz2'
            with open(file_path, 'rb') as f_in:
                with bz2.open(bz2_path, 'wb') as f_out:
                    f_out.writelines(f_in)
            os.remove(file_path)
            return bz2_path
        else:
            return file_path

class DataImportService:
    """数据导入服务"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.db_manager = DatabaseManager(config)
        self.file_manager = FileManager()
        self.security_manager = SecurityManager()
        self.validator = DataValidator()
        self.error_handler = ErrorHandler()
        
        # 获取导出服务实例以复用表模型
        self.export_service = DataExportService()
    
    async def import_data(
        self, 
        file: UploadFile, 
        table_name: str, 
        config: ImportConfig, 
        db: Session
    ) -> ImportResult:
        """导入数据"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始导入数据: {table_name}, 格式: {config.format.value}")
            
            # 验证表名
            model_class = self.export_service.get_table_model(table_name)
            
            # 读取文件数据
            data = await self._read_import_file(file, config)
            
            # 验证数据
            validation_result = await self._validate_import_data(data, model_class, config)
            
            if not validation_result.is_valid and not config.skip_errors:
                return ImportResult(
                    success=False,
                    errors=validation_result.errors,
                    warnings=validation_result.warnings
                )
            
            # 创建备份
            backup_file = None
            if config.create_backup:
                backup_file = await self._create_backup(table_name, db)
            
            # 执行导入
            import_result = await self._execute_import(data, model_class, config, db)
            import_result.backup_file = backup_file
            
            import_time = (datetime.now() - start_time).total_seconds()
            import_result.import_time = import_time
            
            self.logger.info(f"导入完成: 处理 {import_result.rows_processed} 行, 耗时: {import_time:.2f}秒")
            return import_result
            
        except Exception as e:
            import_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"导入失败: {str(e)}"
            self.logger.error(error_msg)
            
            return ImportResult(
                success=False,
                import_time=import_time,
                errors=[error_msg]
            )
    
    async def _read_import_file(self, file: UploadFile, config: ImportConfig) -> List[Dict[str, Any]]:
        """读取导入文件"""
        content = await file.read()
        
        if config.format == ImportFormat.CSV:
            return await self._read_csv(content, config)
        elif config.format == ImportFormat.JSON:
            return await self._read_json(content, config)
        elif config.format == ImportFormat.EXCEL:
            return await self._read_excel(content, config)
        else:
            raise BusinessException(f"不支持的导入格式: {config.format}")
    
    async def _read_csv(self, content: bytes, config: ImportConfig) -> List[Dict[str, Any]]:
        """读取CSV文件"""
        text_content = content.decode(config.encoding)
        reader = csv.DictReader(io.StringIO(text_content))
        return [row for row in reader]
    
    async def _read_json(self, content: bytes, config: ImportConfig) -> List[Dict[str, Any]]:
        """读取JSON文件"""
        text_content = content.decode(config.encoding)
        data = json.loads(text_content)
        
        if isinstance(data, list):
            return data
        elif isinstance(data, dict) and 'data' in data:
            return data['data']
        else:
            return [data]
    
    async def _read_excel(self, content: bytes, config: ImportConfig) -> List[Dict[str, Any]]:
        """读取Excel文件"""
        df = pd.read_excel(io.BytesIO(content))
        return df.to_dict('records')
    
    async def _validate_import_data(
        self, 
        data: List[Dict[str, Any]], 
        model_class: Type, 
        config: ImportConfig
    ) -> DataValidationResult:
        """验证导入数据"""
        if not config.validate_data:
            return DataValidationResult(
                is_valid=True,
                total_rows=len(data),
                valid_rows=len(data)
            )
        
        # 使用数据验证器进行验证
        return await self.validator.validate_bulk_data(data, model_class)
    
    async def _create_backup(self, table_name: str, db: Session) -> str:
        """创建数据备份"""
        try:
            export_config = ExportConfig(
                format=ExportFormat.JSON,
                include_metadata=True,
                compression=CompressionType.ZIP
            )
            
            result = await self.export_service.export_data(table_name, export_config, db)
            
            if result.success:
                return result.file_path
            else:
                self.logger.warning(f"备份创建失败: {result.errors}")
                return None
                
        except Exception as e:
            self.logger.warning(f"创建备份时出错: {str(e)}")
            return None
    
    async def _execute_import(
        self, 
        data: List[Dict[str, Any]], 
        model_class: Type, 
        config: ImportConfig, 
        db: Session
    ) -> ImportResult:
        """执行数据导入"""
        result = ImportResult(success=True)
        
        try:
            # 分批处理数据
            for i in range(0, len(data), config.batch_size):
                batch = data[i:i + config.batch_size]
                batch_result = await self._process_batch(batch, model_class, config, db)
                
                # 累计结果
                result.rows_processed += batch_result.rows_processed
                result.rows_inserted += batch_result.rows_inserted
                result.rows_updated += batch_result.rows_updated
                result.rows_skipped += batch_result.rows_skipped
                result.rows_failed += batch_result.rows_failed
                result.errors.extend(batch_result.errors)
                result.warnings.extend(batch_result.warnings)
            
            # 提交事务
            db.commit()
            
            return result
            
        except Exception as e:
            db.rollback()
            result.success = False
            result.errors.append(f"导入执行失败: {str(e)}")
            return result
    
    async def _process_batch(
        self, 
        batch: List[Dict[str, Any]], 
        model_class: Type, 
        config: ImportConfig, 
        db: Session
    ) -> ImportResult:
        """处理数据批次"""
        result = ImportResult(success=True)
        
        for row_data in batch:
            try:
                # 应用列映射
                if config.column_mapping:
                    mapped_data = {}
                    for source_col, target_col in config.column_mapping.items():
                        if source_col in row_data:
                            mapped_data[target_col] = row_data[source_col]
                    row_data = mapped_data
                
                # 应用默认值
                if config.default_values:
                    for key, value in config.default_values.items():
                        if key not in row_data or row_data[key] is None:
                            row_data[key] = value
                
                # 执行操作
                if config.operation == DataOperation.INSERT:
                    await self._insert_row(row_data, model_class, db, result)
                elif config.operation == DataOperation.UPDATE:
                    await self._update_row(row_data, model_class, config, db, result)
                elif config.operation == DataOperation.UPSERT:
                    await self._upsert_row(row_data, model_class, config, db, result)
                elif config.operation == DataOperation.DELETE:
                    await self._delete_row(row_data, model_class, config, db, result)
                
                result.rows_processed += 1
                
            except Exception as e:
                result.rows_failed += 1
                error_msg = f"处理行数据失败: {str(e)}"
                result.errors.append(error_msg)
                
                if not config.skip_errors:
                    raise
        
        return result
    
    async def _insert_row(
        self, 
        row_data: Dict[str, Any], 
        model_class: Type, 
        db: Session, 
        result: ImportResult
    ):
        """插入行数据"""
        try:
            instance = model_class(**row_data)
            db.add(instance)
            result.rows_inserted += 1
        except Exception as e:
            raise BusinessException(f"插入数据失败: {str(e)}")
    
    async def _update_row(
        self, 
        row_data: Dict[str, Any], 
        model_class: Type, 
        config: ImportConfig, 
        db: Session, 
        result: ImportResult
    ):
        """更新行数据"""
        if not config.unique_fields:
            raise BusinessException("更新操作需要指定unique_fields")
        
        # 构建查询条件
        query = db.query(model_class)
        for field in config.unique_fields:
            if field in row_data:
                query = query.filter(getattr(model_class, field) == row_data[field])
        
        instance = query.first()
        if instance:
            for key, value in row_data.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            result.rows_updated += 1
        else:
            result.rows_skipped += 1
            result.warnings.append(f"未找到要更新的记录: {row_data}")
    
    async def _upsert_row(
        self, 
        row_data: Dict[str, Any], 
        model_class: Type, 
        config: ImportConfig, 
        db: Session, 
        result: ImportResult
    ):
        """插入或更新行数据"""
        if not config.unique_fields:
            # 没有唯一字段，直接插入
            await self._insert_row(row_data, model_class, db, result)
            return
        
        # 检查记录是否存在
        query = db.query(model_class)
        for field in config.unique_fields:
            if field in row_data:
                query = query.filter(getattr(model_class, field) == row_data[field])
        
        instance = query.first()
        if instance:
            # 更新现有记录
            for key, value in row_data.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            result.rows_updated += 1
        else:
            # 插入新记录
            instance = model_class(**row_data)
            db.add(instance)
            result.rows_inserted += 1
    
    async def _delete_row(
        self, 
        row_data: Dict[str, Any], 
        model_class: Type, 
        config: ImportConfig, 
        db: Session, 
        result: ImportResult
    ):
        """删除行数据"""
        if not config.unique_fields:
            raise BusinessException("删除操作需要指定unique_fields")
        
        # 构建查询条件
        query = db.query(model_class)
        for field in config.unique_fields:
            if field in row_data:
                query = query.filter(getattr(model_class, field) == row_data[field])
        
        deleted_count = query.delete()
        if deleted_count > 0:
            result.rows_updated += deleted_count  # 使用updated计数器记录删除的行数
        else:
            result.rows_skipped += 1
            result.warnings.append(f"未找到要删除的记录: {row_data}")

class DataTransformService:
    """数据转换服务"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.export_service = DataExportService()
        self.import_service = DataImportService()
    
    async def transform_format(
        self, 
        file: UploadFile, 
        source_format: ImportFormat, 
        target_format: ExportFormat
    ) -> StreamingResponse:
        """格式转换"""
        try:
            self.logger.info(f"开始格式转换: {source_format.value} -> {target_format.value}")
            
            # 读取源文件
            import_config = ImportConfig(format=source_format, validate_data=False)
            data = await self.import_service._read_import_file(file, import_config)
            
            # 生成目标文件
            export_config = ExportConfig(format=target_format)
            file_path = await self.export_service._generate_export_file(
                data, export_config, "transformed_data"
            )
            
            # 返回文件流
            def file_generator():
                with open(file_path, 'rb') as f:
                    while True:
                        chunk = f.read(8192)
                        if not chunk:
                            break
                        yield chunk
                # 清理临时文件
                os.remove(file_path)
            
            filename = f"transformed_data.{target_format.value}"
            media_type = self._get_media_type(target_format)
            
            return StreamingResponse(
                file_generator(),
                media_type=media_type,
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
            
        except Exception as e:
            self.logger.error(f"格式转换失败: {str(e)}")
            raise BusinessException(f"格式转换失败: {str(e)}")
    
    def _get_media_type(self, format: ExportFormat) -> str:
        """获取媒体类型"""
        media_types = {
            ExportFormat.CSV: "text/csv",
            ExportFormat.JSON: "application/json",
            ExportFormat.EXCEL: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ExportFormat.XML: "application/xml",
            ExportFormat.PARQUET: "application/octet-stream"
        }
        return media_types.get(format, "application/octet-stream")

# 服务实例
export_service = DataExportService()
import_service = DataImportService()
transform_service = DataTransformService()

# 支持的表列表（向后兼容）
SUPPORTED_TABLES = export_service.get_supported_tables()

# 导出的主要接口
__all__ = [
    'ExportFormat',
    'ImportFormat', 
    'DataOperation',
    'CompressionType',
    'ExportConfig',
    'ImportConfig',
    'DataValidationResult',
    'ExportResult',
    'ImportResult',
    'DataExportService',
    'DataImportService', 
    'DataTransformService',
    'export_service',
    'import_service',
    'transform_service',
    'SUPPORTED_TABLES'
]