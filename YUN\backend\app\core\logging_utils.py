#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
提供统一的日志记录、格式化和管理功能
"""

import os
import sys
import json
import logging
import logging.handlers
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import traceback
import asyncio
from contextlib import contextmanager
import threading

from ..config.environment import get_settings

class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogFormat(str, Enum):
    """日志格式枚举"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    JSON = "json"
    STRUCTURED = "structured"

class LogHandler(str, Enum):
    """日志处理器类型枚举"""
    CONSOLE = "console"
    FILE = "file"
    ROTATING_FILE = "rotating_file"
    TIMED_ROTATING_FILE = "timed_rotating_file"
    SYSLOG = "syslog"
    HTTP = "http"
    EMAIL = "email"

@dataclass
class LogConfig:
    """日志配置"""
    level: LogLevel = LogLevel.INFO
    format_type: LogFormat = LogFormat.DETAILED
    handlers: List[LogHandler] = None
    log_dir: str = "logs"
    log_file: str = "app.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    rotation_interval: str = "midnight"
    encoding: str = "utf-8"
    enable_console: bool = True
    enable_file: bool = True
    enable_json_logs: bool = False
    include_caller_info: bool = True
    include_thread_info: bool = False
    include_process_info: bool = False
    custom_fields: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.handlers is None:
            self.handlers = [LogHandler.CONSOLE, LogHandler.ROTATING_FILE]
        if self.custom_fields is None:
            self.custom_fields = {}

@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: str
    logger_name: str
    message: str
    module: Optional[str] = None
    function: Optional[str] = None
    line_number: Optional[int] = None
    thread_id: Optional[int] = None
    process_id: Optional[int] = None
    exception_info: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = self.to_dict()
        # 处理datetime序列化
        data['timestamp'] = self.timestamp.isoformat()
        return json.dumps(data, ensure_ascii=False)

class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def __init__(self, config: LogConfig):
        self.config = config
        super().__init__()
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 创建日志条目
        log_entry = self._create_log_entry(record)
        
        if self.config.format_type == LogFormat.JSON:
            return log_entry.to_json()
        elif self.config.format_type == LogFormat.STRUCTURED:
            return self._format_structured(log_entry)
        elif self.config.format_type == LogFormat.DETAILED:
            return self._format_detailed(log_entry)
        else:  # SIMPLE
            return self._format_simple(log_entry)
    
    def _create_log_entry(self, record: logging.LogRecord) -> LogEntry:
        """创建日志条目"""
        # 获取调用者信息
        module = getattr(record, 'module', record.module) if hasattr(record, 'module') else None
        function = getattr(record, 'funcName', None)
        line_number = getattr(record, 'lineno', None)
        
        # 获取异常信息
        exception_info = None
        if record.exc_info:
            exception_info = self.formatException(record.exc_info)
        
        # 获取额外数据
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                extra_data[key] = value
        
        return LogEntry(
            timestamp=datetime.fromtimestamp(record.created),
            level=record.levelname,
            logger_name=record.name,
            message=record.getMessage(),
            module=module,
            function=function,
            line_number=line_number,
            thread_id=record.thread if self.config.include_thread_info else None,
            process_id=record.process if self.config.include_process_info else None,
            exception_info=exception_info,
            extra_data=extra_data if extra_data else None,
            request_id=getattr(record, 'request_id', None),
            user_id=getattr(record, 'user_id', None),
            session_id=getattr(record, 'session_id', None)
        )
    
    def _format_simple(self, entry: LogEntry) -> str:
        """简单格式"""
        return f"{entry.timestamp.strftime('%Y-%m-%d %H:%M:%S')} [{entry.level}] {entry.message}"
    
    def _format_detailed(self, entry: LogEntry) -> str:
        """详细格式"""
        parts = [
            entry.timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
            f"[{entry.level}]",
            f"[{entry.logger_name}]"
        ]
        
        if self.config.include_caller_info and entry.module:
            caller_info = f"{entry.module}"
            if entry.function:
                caller_info += f".{entry.function}"
            if entry.line_number:
                caller_info += f":{entry.line_number}"
            parts.append(f"[{caller_info}]")
        
        if entry.request_id:
            parts.append(f"[req:{entry.request_id}]")
        
        if entry.user_id:
            parts.append(f"[user:{entry.user_id}]")
        
        parts.append(f"- {entry.message}")
        
        result = " ".join(parts)
        
        if entry.exception_info:
            result += f"\n{entry.exception_info}"
        
        return result
    
    def _format_structured(self, entry: LogEntry) -> str:
        """结构化格式"""
        data = {
            "timestamp": entry.timestamp.isoformat(),
            "level": entry.level,
            "logger": entry.logger_name,
            "message": entry.message
        }
        
        if entry.module:
            data["module"] = entry.module
        if entry.function:
            data["function"] = entry.function
        if entry.line_number:
            data["line"] = entry.line_number
        if entry.request_id:
            data["request_id"] = entry.request_id
        if entry.user_id:
            data["user_id"] = entry.user_id
        if entry.session_id:
            data["session_id"] = entry.session_id
        if entry.exception_info:
            data["exception"] = entry.exception_info
        if entry.extra_data:
            data["extra"] = entry.extra_data
        
        return json.dumps(data, ensure_ascii=False)

class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config: Optional[LogConfig] = None):
        self.config = config or self._get_default_config()
        self._loggers: Dict[str, logging.Logger] = {}
        self._handlers: List[logging.Handler] = []
        self._initialized = False
        self._lock = threading.Lock()
    
    def _get_default_config(self) -> LogConfig:
        """获取默认配置"""
        env_config = get_settings()
        
        return LogConfig(
            level=LogLevel(env_config.get_log_level().upper()),
            log_dir=env_config.logging.file_path,
            log_file="app.log",
            enable_console=env_config.logging.console_enabled,
            enable_file=env_config.logging.file_enabled,
            enable_json_logs=False
        )
    
    def initialize(self):
        """初始化日志系统"""
        with self._lock:
            if self._initialized:
                return
            
            # 创建日志目录
            log_dir = Path(self.config.log_dir)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 设置根日志级别
            logging.getLogger().setLevel(getattr(logging, self.config.level.value))
            
            # 创建处理器
            self._create_handlers()
            
            # 配置根日志器
            root_logger = logging.getLogger()
            for handler in self._handlers:
                root_logger.addHandler(handler)
            
            self._initialized = True
    
    def _create_handlers(self):
        """创建日志处理器"""
        formatter = CustomFormatter(self.config)
        
        for handler_type in self.config.handlers:
            handler = self._create_handler(handler_type)
            if handler:
                handler.setFormatter(formatter)
                handler.setLevel(getattr(logging, self.config.level.value))
                self._handlers.append(handler)
    
    def _create_handler(self, handler_type: LogHandler) -> Optional[logging.Handler]:
        """创建特定类型的处理器"""
        try:
            if handler_type == LogHandler.CONSOLE and self.config.enable_console:
                return logging.StreamHandler(sys.stdout)
            
            elif handler_type == LogHandler.FILE and self.config.enable_file:
                log_file_path = Path(self.config.log_dir) / self.config.log_file
                return logging.FileHandler(
                    log_file_path,
                    encoding=self.config.encoding
                )
            
            elif handler_type == LogHandler.ROTATING_FILE and self.config.enable_file:
                log_file_path = Path(self.config.log_dir) / self.config.log_file
                return logging.handlers.RotatingFileHandler(
                    log_file_path,
                    maxBytes=self.config.max_file_size,
                    backupCount=self.config.backup_count,
                    encoding=self.config.encoding
                )
            
            elif handler_type == LogHandler.TIMED_ROTATING_FILE and self.config.enable_file:
                log_file_path = Path(self.config.log_dir) / self.config.log_file
                return logging.handlers.TimedRotatingFileHandler(
                    log_file_path,
                    when=self.config.rotation_interval,
                    backupCount=self.config.backup_count,
                    encoding=self.config.encoding
                )
            
            elif handler_type == LogHandler.SYSLOG:
                return logging.handlers.SysLogHandler()
            
            # 其他处理器类型可以在这里添加
            
        except Exception as e:
            print(f"创建日志处理器失败 {handler_type}: {e}")
        
        return None
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志器"""
        if not self._initialized:
            self.initialize()
        
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def shutdown(self):
        """关闭日志系统"""
        with self._lock:
            for handler in self._handlers:
                handler.close()
            self._handlers.clear()
            self._loggers.clear()
            self._initialized = False

class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name: str, manager: Optional[LoggerManager] = None):
        self.manager = manager or get_default_logger_manager()
        self.logger = self.manager.get_logger(name)
        self._context: Dict[str, Any] = {}
    
    def set_context(self, **kwargs):
        """设置日志上下文"""
        self._context.update(kwargs)
    
    def clear_context(self):
        """清除日志上下文"""
        self._context.clear()
    
    def _log(self, level: str, message: str, **kwargs):
        """记录日志"""
        # 合并上下文和额外参数
        extra = {**self._context, **kwargs}

        # 获取调用者信息
        frame = sys._getframe(2)
        # logging 保留字段自动过滤，便于未来扩展
        reserved = set(logging.LogRecord.__init__.__code__.co_varnames)
        reserved.update({'module', 'funcName', 'lineno', 'exc_info', 'stack_info', 'args', 'msg', 'levelname', 'levelno', 'pathname', 'filename', 'created', 'msecs', 'relativeCreated', 'thread', 'threadName', 'processName', 'process', 'exc_text'})
        for key in list(extra.keys()):
            if key in reserved:
                extra.pop(key)
        module = frame.f_globals.get('__name__', '')
        funcName = frame.f_code.co_name
        lineno = frame.f_lineno
        exc_info = kwargs.get('exc_info', None)
        getattr(self.logger, level.lower())(
            message,
            extra={**extra, 'caller_module': module, 'caller_funcName': funcName, 'caller_lineno': lineno},
            exc_info=exc_info
        )
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self._log('DEBUG', message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self._log('INFO', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self._log('WARNING', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self._log('ERROR', message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        self._log('CRITICAL', message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """记录异常日志"""
        kwargs['exc_info'] = True
        self._log('ERROR', message, **kwargs)

class RequestLogger:
    """请求日志器"""
    
    def __init__(self, logger: StructuredLogger):
        self.logger = logger
    
    def log_request_start(
        self,
        request_id: str,
        method: str,
        url: str,
        user_id: Optional[str] = None,
        **kwargs
    ):
        """记录请求开始"""
        self.logger.info(
            f"Request started: {method} {url}",
            request_id=request_id,
            user_id=user_id,
            method=method,
            url=url,
            **kwargs
        )
    
    def log_request_end(
        self,
        request_id: str,
        status_code: int,
        duration_ms: float,
        **kwargs
    ):
        """记录请求结束"""
        self.logger.info(
            f"Request completed: {status_code} ({duration_ms:.2f}ms)",
            request_id=request_id,
            status_code=status_code,
            duration_ms=duration_ms,
            **kwargs
        )
    
    def log_request_error(
        self,
        request_id: str,
        error: Exception,
        **kwargs
    ):
        """记录请求错误"""
        self.logger.exception(
            f"Request error: {str(error)}",
            request_id=request_id,
            error_type=type(error).__name__,
            **kwargs
        )

class DatabaseLogger:
    """数据库日志器"""
    
    def __init__(self, logger: StructuredLogger):
        self.logger = logger
    
    def log_query(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None,
        **kwargs
    ):
        """记录数据库查询"""
        message = f"Database query executed"
        if duration_ms is not None:
            message += f" ({duration_ms:.2f}ms)"
        
        self.logger.debug(
            message,
            query=query,
            params=params,
            duration_ms=duration_ms,
            **kwargs
        )
    
    def log_transaction_start(self, transaction_id: str, **kwargs):
        """记录事务开始"""
        self.logger.debug(
            "Database transaction started",
            transaction_id=transaction_id,
            **kwargs
        )
    
    def log_transaction_commit(self, transaction_id: str, **kwargs):
        """记录事务提交"""
        self.logger.debug(
            "Database transaction committed",
            transaction_id=transaction_id,
            **kwargs
        )
    
    def log_transaction_rollback(
        self,
        transaction_id: str,
        reason: Optional[str] = None,
        **kwargs
    ):
        """记录事务回滚"""
        self.logger.warning(
            f"Database transaction rolled back: {reason or 'Unknown reason'}",
            transaction_id=transaction_id,
            reason=reason,
            **kwargs
        )

class SecurityLogger:
    """安全日志器"""
    
    def __init__(self, logger: StructuredLogger):
        self.logger = logger
    
    def log_login_attempt(
        self,
        username: str,
        success: bool,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        **kwargs
    ):
        """记录登录尝试"""
        status = "successful" if success else "failed"
        self.logger.info(
            f"Login attempt {status} for user: {username}",
            username=username,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            **kwargs
        )
    
    def log_permission_denied(
        self,
        user_id: str,
        resource: str,
        action: str,
        **kwargs
    ):
        """记录权限拒绝"""
        self.logger.warning(
            f"Permission denied: user {user_id} attempted {action} on {resource}",
            user_id=user_id,
            resource=resource,
            action=action,
            **kwargs
        )
    
    def log_suspicious_activity(
        self,
        description: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        **kwargs
    ):
        """记录可疑活动"""
        self.logger.warning(
            f"Suspicious activity detected: {description}",
            description=description,
            user_id=user_id,
            ip_address=ip_address,
            **kwargs
        )

# 日志装饰器
def log_function_call(
    logger: Optional[StructuredLogger] = None,
    level: str = "DEBUG",
    include_args: bool = True,
    include_result: bool = True
):
    """函数调用日志装饰器"""
    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = get_logger(func.__module__)
        
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                start_time = datetime.now()
                
                # 记录函数调用开始
                log_data = {"function": func.__name__}
                if include_args:
                    log_data.update({"args": str(args), "kwargs": str(kwargs)})
                
                getattr(logger, level.lower())(
                    f"Function {func.__name__} called",
                    **log_data
                )
                
                try:
                    result = await func(*args, **kwargs)
                    
                    # 记录函数调用成功
                    duration = (datetime.now() - start_time).total_seconds() * 1000
                    log_data = {
                        "function": func.__name__,
                        "duration_ms": duration,
                        "success": True
                    }
                    if include_result:
                        log_data["result"] = str(result)
                    
                    getattr(logger, level.lower())(
                        f"Function {func.__name__} completed successfully",
                        **log_data
                    )
                    
                    return result
                
                except Exception as e:
                    # 记录函数调用失败
                    duration = (datetime.now() - start_time).total_seconds() * 1000
                    logger.exception(
                        f"Function {func.__name__} failed",
                        function=func.__name__,
                        duration_ms=duration,
                        success=False,
                        error=str(e)
                    )
                    raise
            
            return async_wrapper
        
        else:
            def sync_wrapper(*args, **kwargs):
                start_time = datetime.now()
                
                # 记录函数调用开始
                log_data = {"function": func.__name__}
                if include_args:
                    log_data.update({"args": str(args), "kwargs": str(kwargs)})
                
                getattr(logger, level.lower())(
                    f"Function {func.__name__} called",
                    **log_data
                )
                
                try:
                    result = func(*args, **kwargs)
                    
                    # 记录函数调用成功
                    duration = (datetime.now() - start_time).total_seconds() * 1000
                    log_data = {
                        "function": func.__name__,
                        "duration_ms": duration,
                        "success": True
                    }
                    if include_result:
                        log_data["result"] = str(result)
                    
                    getattr(logger, level.lower())(
                        f"Function {func.__name__} completed successfully",
                        **log_data
                    )
                    
                    return result
                
                except Exception as e:
                    # 记录函数调用失败
                    duration = (datetime.now() - start_time).total_seconds() * 1000
                    logger.exception(
                        f"Function {func.__name__} failed",
                        function=func.__name__,
                        duration_ms=duration,
                        success=False,
                        error=str(e)
                    )
                    raise
            
            return sync_wrapper
    
    return decorator

@contextmanager
def log_context(**kwargs):
    """日志上下文管理器"""
    logger = get_logger()
    old_context = logger._context.copy()
    
    try:
        logger.set_context(**kwargs)
        yield logger
    finally:
        logger._context = old_context

# 全局日志管理器
_default_logger_manager: Optional[LoggerManager] = None

def get_default_logger_manager() -> LoggerManager:
    """获取默认日志管理器"""
    global _default_logger_manager
    
    if _default_logger_manager is None:
        _default_logger_manager = LoggerManager()
    
    return _default_logger_manager

def set_default_logger_manager(manager: LoggerManager):
    """设置默认日志管理器"""
    global _default_logger_manager
    _default_logger_manager = manager

# 便捷函数
def get_logger(name: str = None) -> StructuredLogger:
    """获取结构化日志器"""
    if name is None:
        # 获取调用者模块名
        frame = sys._getframe(1)
        name = frame.f_globals.get('__name__', 'unknown')
    
    manager = get_default_logger_manager()
    return StructuredLogger(name, manager)

def get_request_logger(name: str = None) -> RequestLogger:
    """获取请求日志器"""
    logger = get_logger(name)
    return RequestLogger(logger)

def get_database_logger(name: str = None) -> DatabaseLogger:
    """获取数据库日志器"""
    logger = get_logger(name)
    return DatabaseLogger(logger)

def get_security_logger(name: str = None) -> SecurityLogger:
    """获取安全日志器"""
    logger = get_logger(name)
    return SecurityLogger(logger)

def configure_logging(config: Optional[LogConfig] = None):
    """配置日志系统"""
    manager = LoggerManager(config)
    manager.initialize()
    set_default_logger_manager(manager)

def shutdown_logging():
    """关闭日志系统"""
    manager = get_default_logger_manager()
    manager.shutdown()

# 日志分析工具
class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self, log_file_path: str):
        self.log_file_path = Path(log_file_path)
    
    def analyze_log_file(self) -> Dict[str, Any]:
        """分析日志文件"""
        if not self.log_file_path.exists():
            return {"error": "日志文件不存在"}
        
        stats = {
            "total_lines": 0,
            "levels": {"DEBUG": 0, "INFO": 0, "WARNING": 0, "ERROR": 0, "CRITICAL": 0},
            "errors": [],
            "warnings": [],
            "start_time": None,
            "end_time": None,
            "file_size": self.log_file_path.stat().st_size
        }
        
        try:
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    stats["total_lines"] += 1
                    
                    # 简单的日志级别检测
                    for level in stats["levels"]:
                        if f"[{level}]" in line:
                            stats["levels"][level] += 1
                            
                            if level == "ERROR" and len(stats["errors"]) < 10:
                                stats["errors"].append(line.strip())
                            elif level == "WARNING" and len(stats["warnings"]) < 10:
                                stats["warnings"].append(line.strip())
                            break
                    
                    # 提取时间戳（简单实现）
                    if line and stats["start_time"] is None:
                        stats["start_time"] = line[:19]  # 假设时间戳在开头
                    
                    if line:
                        stats["end_time"] = line[:19]
        
        except Exception as e:
            stats["error"] = f"分析日志文件失败: {e}"
        
        return stats
    
    def get_recent_errors(self, hours: int = 24) -> List[str]:
        """获取最近的错误日志"""
        errors = []
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        try:
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if "[ERROR]" in line or "[CRITICAL]" in line:
                        # 简单的时间检查（需要根据实际日志格式调整）
                        try:
                            timestamp_str = line[:19]
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            if timestamp >= cutoff_time:
                                errors.append(line.strip())
                        except ValueError:
                            # 如果时间戳解析失败，仍然包含这行
                            errors.append(line.strip())
        
        except Exception as e:
            return [f"读取日志文件失败: {e}"]
        
        return errors

# 日志轮转工具
class LogRotator:
    """日志轮转工具"""
    
    def __init__(self, log_dir: str):
        self.log_dir = Path(log_dir)
    
    def rotate_logs(self, max_age_days: int = 30, max_size_mb: int = 100):
        """轮转日志文件"""
        if not self.log_dir.exists():
            return
        
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        max_size_bytes = max_size_mb * 1024 * 1024
        
        for log_file in self.log_dir.glob("*.log*"):
            try:
                # 检查文件年龄
                file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_time:
                    log_file.unlink()
                    print(f"删除过期日志文件: {log_file}")
                    continue
                
                # 检查文件大小
                if log_file.stat().st_size > max_size_bytes:
                    # 压缩大文件
                    self._compress_log_file(log_file)
            
            except Exception as e:
                print(f"处理日志文件 {log_file} 失败: {e}")
    
    def _compress_log_file(self, log_file: Path):
        """压缩日志文件"""
        try:
            import gzip
            
            compressed_file = log_file.with_suffix(log_file.suffix + '.gz')
            
            with open(log_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            log_file.unlink()
            print(f"压缩日志文件: {log_file} -> {compressed_file}")
        
        except Exception as e:
            print(f"压缩日志文件失败: {e}")

# 初始化函数
def init_logging(config: Optional[LogConfig] = None):
    """初始化日志系统"""
    configure_logging(config)
    
    # 设置未捕获异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger = get_logger("uncaught")
        logger.critical(
            "Uncaught exception",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    sys.excepthook = handle_exception