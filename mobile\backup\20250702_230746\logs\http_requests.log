2025-04-15 14:29:10,342 - INFO - HTTP请求日志初始化完成，日志文件: C:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 14:30:32,580 - INFO - HTTP请求日志初始化完成，日志文件: C:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 14:30:32,590 - DEBUG - [23267e5c] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:32,591 - DEBUG - [99864d10] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:32,650 - DEBUG - [99864d10] 响应状态码: 204 (耗时: 0.059秒)
2025-04-15 14:30:32,651 - DEBUG - [99864d10] 响应头: {'Connection': 'close', 'Access-Control-Allow-Headers': 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization', 'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH', 'Access-Control-Allow-Origin': '*', 'Access-Control-Max-Age': '1728000', 'Content-Type': 'text/plain; charset=utf-8', 'Date': 'Tue, 15 Apr 2025 06:30:36 GMT', 'Server': 'nginx'}
2025-04-15 14:30:32,653 - DEBUG - [99864d10] 响应内容: 
2025-04-15 14:30:32,654 - DEBUG - [23267e5c] 响应状态码: 204 (耗时: 0.064秒)
2025-04-15 14:30:32,655 - DEBUG - [23267e5c] 响应内容: 
2025-04-15 14:30:32,655 - INFO - API测试 [失败] GET http://8.138.188.26/api/health - 状态码: 204 (预期: 200), 耗时: 0.065秒
2025-04-15 14:30:32,663 - DEBUG - [2f21cf0f] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:32,664 - DEBUG - [2f21cf0f] 请求数据: {'username': 'test_user_1744698632', 'name': '测试用户', 'gender': '男', 'birth_date': '1990-01-01', 'id_number': '1101046986321234', 'phone': '13900000000', 'email': '<EMAIL>', 'password_hash': '969fdf03e82fe9c964ed1f9177f5914246b0f30620a7c84180bbb42a65953224'}
2025-04-15 14:30:32,665 - DEBUG - [e254f0d8] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:32,665 - DEBUG - [e254f0d8] 请求数据: {'username': 'test_user_1744698632', 'name': '测试用户', 'gender': '男', 'birth_date': '1990-01-01', 'id_number': '******', 'phone': '******', 'email': '******', 'password_hash': '******'}
2025-04-15 14:30:32,722 - DEBUG - [e254f0d8] 响应状态码: 204 (耗时: 0.057秒)
2025-04-15 14:30:32,723 - DEBUG - [e254f0d8] 响应头: {'Connection': 'close', 'Access-Control-Allow-Headers': 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization', 'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH', 'Access-Control-Allow-Origin': '*', 'Access-Control-Max-Age': '1728000', 'Content-Type': 'text/plain; charset=utf-8', 'Date': 'Tue, 15 Apr 2025 06:30:36 GMT', 'Server': 'nginx'}
2025-04-15 14:30:32,724 - DEBUG - [e254f0d8] 响应内容: 
2025-04-15 14:30:32,724 - DEBUG - [2f21cf0f] 响应状态码: 204 (耗时: 0.061秒)
2025-04-15 14:30:32,725 - DEBUG - [2f21cf0f] 响应内容: 
2025-04-15 14:30:32,725 - INFO - API测试 [失败] POST http://8.138.188.26/api/auth/register - 状态码: 204 (预期: 200), 耗时: 0.062秒
2025-04-15 14:30:32,745 - DEBUG - [759d95ee] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:32,746 - DEBUG - [6d2a22df] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:33,590 - DEBUG - [705099b8] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:33,592 - DEBUG - [3f9224e8] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:30:33,608 - DEBUG - [6d2a22df] 响应状态码: 502 (耗时: 0.862秒)
2025-04-15 14:30:33,609 - DEBUG - [6d2a22df] 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-04-15 14:30:33,610 - DEBUG - [6d2a22df] 响应内容: 
2025-04-15 14:30:33,613 - DEBUG - [759d95ee] 响应状态码: 502 (耗时: 0.868秒)
2025-04-15 14:30:33,615 - DEBUG - [759d95ee] 响应内容: 
2025-04-15 14:30:33,741 - DEBUG - [3f9224e8] 响应状态码: 502 (耗时: 0.148秒)
2025-04-15 14:30:33,742 - DEBUG - [3f9224e8] 响应头: {'Connection': 'close', 'Content-Length': '0'}
2025-04-15 14:30:33,743 - DEBUG - [3f9224e8] 响应内容: 
2025-04-15 14:30:33,744 - DEBUG - [705099b8] 响应状态码: 502 (耗时: 0.155秒)
2025-04-15 14:30:33,746 - DEBUG - [705099b8] 响应内容: 
2025-04-15 14:47:55,981 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 14:47:55,989 - DEBUG - [23655dc2] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:47:55,990 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 14:47:56,043 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 14:47:56,044 - DEBUG - [23655dc2] 响应状态码: 204 (耗时: 0.055秒)
2025-04-15 14:47:56,044 - DEBUG - [23655dc2] 响应内容: 
2025-04-15 14:47:56,045 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 204]), 耗时: 0.056秒
2025-04-15 14:47:56,047 - DEBUG - [522b1e62] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:47:56,048 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 14:47:56,989 - DEBUG - [3092e07f] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:47:56,989 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 14:47:57,089 - INFO - RESPONSE: Status=204 Time=0.10s
2025-04-15 14:47:57,090 - DEBUG - [3092e07f] 响应状态码: 204 (耗时: 0.102秒)
2025-04-15 14:47:57,091 - DEBUG - [3092e07f] 响应内容: 
2025-04-15 14:48:01,063 - INFO - RESPONSE: Status=502 Time=5.01s
2025-04-15 14:48:01,065 - DEBUG - [522b1e62] 响应状态码: 502 (耗时: 5.018秒)
2025-04-15 14:48:01,066 - DEBUG - [522b1e62] 响应内容: 
2025-04-15 14:48:01,067 - WARNING - 收到502网关错误，将在2秒后重试 (1/2)
2025-04-15 14:48:03,069 - DEBUG - [7f3667c5] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:03,070 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 14:48:08,092 - INFO - RESPONSE: Status=502 Time=5.02s
2025-04-15 14:48:08,094 - DEBUG - [7f3667c5] 响应状态码: 502 (耗时: 5.025秒)
2025-04-15 14:48:08,096 - DEBUG - [7f3667c5] 响应内容: 
2025-04-15 14:48:08,098 - WARNING - 收到502网关错误，将在4秒后重试 (2/2)
2025-04-15 14:48:12,101 - DEBUG - [923f2c48] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:12,103 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 14:48:17,122 - INFO - RESPONSE: Status=502 Time=5.02s
2025-04-15 14:48:17,125 - DEBUG - [923f2c48] 响应状态码: 502 (耗时: 5.023秒)
2025-04-15 14:48:17,126 - DEBUG - [923f2c48] 响应内容: 
2025-04-15 14:48:17,128 - INFO - API测试 [失败] GET http://8.138.188.26:8088/api/health - 状态码: 502 (预期: [200, 204]), 耗时: 5.027秒
2025-04-15 14:48:17,139 - DEBUG - [c8fc20ed] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:17,140 - DEBUG - [c8fc20ed] 请求数据: {'username': 'test_user_1744699675', 'name': '测试用户', 'gender': '男', 'birth_date': '1990-01-01', 'id_number': '1101046996751234', 'phone': '13900000000', 'email': '<EMAIL>', 'password_hash': '969fdf03e82fe9c964ed1f9177f5914246b0f30620a7c84180bbb42a65953224'}
2025-04-15 14:48:17,144 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/auth/register
2025-04-15 14:48:17,183 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 14:48:17,186 - DEBUG - [c8fc20ed] 响应状态码: 204 (耗时: 0.047秒)
2025-04-15 14:48:17,187 - DEBUG - [c8fc20ed] 响应内容: 
2025-04-15 14:48:17,188 - INFO - API测试 [成功] POST http://8.138.188.26/api/auth/register - 状态码: 204 (预期: [200, 204, 400]), 耗时: 0.049秒
2025-04-15 14:48:17,222 - DEBUG - [33475bb2] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:17,223 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 14:48:17,276 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 14:48:17,279 - DEBUG - [33475bb2] 响应状态码: 204 (耗时: 0.057秒)
2025-04-15 14:48:17,283 - DEBUG - [33475bb2] 响应内容: 
2025-04-15 14:48:17,300 - DEBUG - [291a8a8e] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:17,302 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 14:48:17,376 - INFO - RESPONSE: Status=204 Time=0.07s
2025-04-15 14:48:17,378 - DEBUG - [291a8a8e] 响应状态码: 204 (耗时: 0.078秒)
2025-04-15 14:48:17,382 - DEBUG - [291a8a8e] 响应内容: 
2025-04-15 14:48:17,384 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 204]), 耗时: 0.084秒
2025-04-15 14:48:17,388 - DEBUG - [8ebb0936] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:17,390 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 14:48:17,434 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 14:48:17,437 - DEBUG - [8ebb0936] 响应状态码: 204 (耗时: 0.049秒)
2025-04-15 14:48:17,439 - DEBUG - [8ebb0936] 响应内容: 
2025-04-15 14:48:17,441 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 201, 204]), 耗时: 0.052秒
2025-04-15 14:48:17,444 - DEBUG - [91014429] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 14:48:17,447 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/non-existent-endpoint-502
2025-04-15 14:48:17,534 - INFO - RESPONSE: Status=204 Time=0.09s
2025-04-15 14:48:17,537 - DEBUG - [91014429] 响应状态码: 204 (耗时: 0.093秒)
2025-04-15 14:48:17,539 - DEBUG - [91014429] 响应内容: 
2025-04-15 14:48:17,541 - INFO - API测试 [成功] GET http://8.138.188.26/api/non-existent-endpoint-502 - 状态码: 204 (预期: [200, 204, 502]), 耗时: 0.096秒
2025-04-15 16:35:53,091 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 16:35:53,107 - DEBUG - [a67465d3] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:35:53,108 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:35:53,217 - INFO - RESPONSE: Status=204 Time=0.11s
2025-04-15 16:35:53,219 - DEBUG - [a67465d3] 响应状态码: 204 (耗时: 0.113秒)
2025-04-15 16:35:53,221 - DEBUG - [a67465d3] 响应内容: 
2025-04-15 16:35:53,222 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 204]), 耗时: 0.115秒
2025-04-15 16:35:53,225 - DEBUG - [7faef0ee] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:35:53,225 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 16:35:54,103 - DEBUG - [f1eb5fd0] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:35:54,106 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:35:54,146 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 16:35:54,148 - DEBUG - [f1eb5fd0] 响应状态码: 204 (耗时: 0.045秒)
2025-04-15 16:35:54,149 - DEBUG - [f1eb5fd0] 响应内容: 
2025-04-15 16:35:58,250 - INFO - RESPONSE: Status=502 Time=5.02s
2025-04-15 16:35:58,252 - DEBUG - [7faef0ee] 响应状态码: 502 (耗时: 5.027秒)
2025-04-15 16:35:58,253 - DEBUG - [7faef0ee] 响应内容: 
2025-04-15 16:35:58,258 - WARNING - 收到502网关错误，将在2秒后重试 (1/2)
2025-04-15 16:36:00,260 - DEBUG - [05fb6542] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:00,262 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 16:36:05,274 - INFO - RESPONSE: Status=502 Time=5.01s
2025-04-15 16:36:05,277 - DEBUG - [05fb6542] 响应状态码: 502 (耗时: 5.016秒)
2025-04-15 16:36:05,278 - DEBUG - [05fb6542] 响应内容: 
2025-04-15 16:36:05,280 - WARNING - 收到502网关错误，将在4秒后重试 (2/2)
2025-04-15 16:36:09,283 - DEBUG - [caba69fc] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:09,285 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 16:36:14,311 - INFO - RESPONSE: Status=502 Time=5.03s
2025-04-15 16:36:14,313 - DEBUG - [caba69fc] 响应状态码: 502 (耗时: 5.030秒)
2025-04-15 16:36:14,315 - DEBUG - [caba69fc] 响应内容: 
2025-04-15 16:36:14,318 - INFO - API测试 [失败] GET http://8.138.188.26:8088/api/health - 状态码: 502 (预期: [200, 204]), 耗时: 5.035秒
2025-04-15 16:36:14,341 - DEBUG - [1a54c36c] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:14,343 - DEBUG - [1a54c36c] 请求数据: {'username': 'test_user_1744706153', 'name': '测试用户', 'gender': '男', 'birth_date': '1990-01-01', 'id_number': '1101047061531234', 'phone': '13900000000', 'email': '<EMAIL>', 'password_hash': '969fdf03e82fe9c964ed1f9177f5914246b0f30620a7c84180bbb42a65953224'}
2025-04-15 16:36:14,347 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/auth/register
2025-04-15 16:36:14,429 - INFO - RESPONSE: Status=204 Time=0.08s
2025-04-15 16:36:14,431 - DEBUG - [1a54c36c] 响应状态码: 204 (耗时: 0.091秒)
2025-04-15 16:36:14,433 - DEBUG - [1a54c36c] 响应内容: 
2025-04-15 16:36:14,435 - INFO - API测试 [成功] POST http://8.138.188.26/api/auth/register - 状态码: 204 (预期: [200, 204, 400]), 耗时: 0.094秒
2025-04-15 16:36:14,490 - DEBUG - [8f3a492c] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:14,492 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:36:14,534 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 16:36:14,538 - DEBUG - [8f3a492c] 响应状态码: 204 (耗时: 0.048秒)
2025-04-15 16:36:14,541 - DEBUG - [8f3a492c] 响应内容: 
2025-04-15 16:36:14,556 - DEBUG - [9838651b] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:14,559 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:36:14,618 - INFO - RESPONSE: Status=204 Time=0.06s
2025-04-15 16:36:14,622 - DEBUG - [9838651b] 响应状态码: 204 (耗时: 0.065秒)
2025-04-15 16:36:14,624 - DEBUG - [9838651b] 响应内容: 
2025-04-15 16:36:14,627 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 204]), 耗时: 0.070秒
2025-04-15 16:36:14,631 - DEBUG - [c6ad602a] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:14,633 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:36:14,681 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 16:36:14,684 - DEBUG - [c6ad602a] 响应状态码: 204 (耗时: 0.053秒)
2025-04-15 16:36:14,687 - DEBUG - [c6ad602a] 响应内容: 
2025-04-15 16:36:14,689 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 201, 204]), 耗时: 0.058秒
2025-04-15 16:36:14,694 - DEBUG - [ebadd8f0] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:36:14,697 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/non-existent-endpoint-502
2025-04-15 16:36:14,784 - INFO - RESPONSE: Status=204 Time=0.09s
2025-04-15 16:36:14,787 - DEBUG - [ebadd8f0] 响应状态码: 204 (耗时: 0.093秒)
2025-04-15 16:36:14,789 - DEBUG - [ebadd8f0] 响应内容: 
2025-04-15 16:36:14,791 - INFO - API测试 [成功] GET http://8.138.188.26/api/non-existent-endpoint-502 - 状态码: 204 (预期: [200, 204, 502]), 耗时: 0.097秒
2025-04-15 16:39:55,729 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 16:39:55,736 - DEBUG - [414fae20] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:39:55,736 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:39:55,770 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 16:39:55,771 - DEBUG - [414fae20] 响应状态码: 204 (耗时: 0.036秒)
2025-04-15 16:39:55,771 - DEBUG - [414fae20] 响应内容: 
2025-04-15 16:39:55,772 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 204]), 耗时: 0.036秒
2025-04-15 16:39:55,773 - DEBUG - [849db3be] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:39:55,773 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 16:39:56,735 - DEBUG - [cca7d59f] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:39:56,735 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:39:56,842 - INFO - RESPONSE: Status=204 Time=0.11s
2025-04-15 16:39:56,843 - DEBUG - [cca7d59f] 响应状态码: 204 (耗时: 0.108秒)
2025-04-15 16:39:56,844 - DEBUG - [cca7d59f] 响应内容: 
2025-04-15 16:40:00,782 - INFO - RESPONSE: Status=502 Time=5.01s
2025-04-15 16:40:00,783 - DEBUG - [849db3be] 响应状态码: 502 (耗时: 5.010秒)
2025-04-15 16:40:00,785 - DEBUG - [849db3be] 响应内容: 
2025-04-15 16:40:00,786 - WARNING - 收到502网关错误，将在2秒后重试 (1/2)
2025-04-15 16:40:02,788 - DEBUG - [1d4f3ef1] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:02,789 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 16:40:07,813 - INFO - RESPONSE: Status=502 Time=5.02s
2025-04-15 16:40:07,816 - DEBUG - [1d4f3ef1] 响应状态码: 502 (耗时: 5.028秒)
2025-04-15 16:40:07,817 - DEBUG - [1d4f3ef1] 响应内容: 
2025-04-15 16:40:07,819 - WARNING - 收到502网关错误，将在4秒后重试 (2/2)
2025-04-15 16:40:11,822 - DEBUG - [11290545] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:11,823 - INFO - REQUEST: OPTIONS http://8.138.188.26:8088/api/health
2025-04-15 16:40:16,840 - INFO - RESPONSE: Status=502 Time=5.02s
2025-04-15 16:40:16,841 - DEBUG - [11290545] 响应状态码: 502 (耗时: 5.019秒)
2025-04-15 16:40:16,842 - DEBUG - [11290545] 响应内容: 
2025-04-15 16:40:16,843 - INFO - API测试 [失败] GET http://8.138.188.26:8088/api/health - 状态码: 502 (预期: [200, 204]), 耗时: 5.022秒
2025-04-15 16:40:16,850 - DEBUG - [915a90c3] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:16,851 - DEBUG - [915a90c3] 请求数据: {'username': 'test_user_1744706395', 'name': '测试用户', 'gender': '男', 'birth_date': '1990-01-01', 'id_number': '1101047063951234', 'phone': '13900000000', 'email': '<EMAIL>', 'password_hash': '969fdf03e82fe9c964ed1f9177f5914246b0f30620a7c84180bbb42a65953224'}
2025-04-15 16:40:16,853 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/auth/register
2025-04-15 16:40:16,891 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 16:40:16,893 - DEBUG - [915a90c3] 响应状态码: 204 (耗时: 0.043秒)
2025-04-15 16:40:16,894 - DEBUG - [915a90c3] 响应内容: 
2025-04-15 16:40:16,895 - INFO - API测试 [成功] POST http://8.138.188.26/api/auth/register - 状态码: 204 (预期: [200, 204, 400]), 耗时: 0.045秒
2025-04-15 16:40:16,927 - DEBUG - [01d31bf8] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:16,928 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:40:17,009 - INFO - RESPONSE: Status=204 Time=0.08s
2025-04-15 16:40:17,012 - DEBUG - [01d31bf8] 响应状态码: 204 (耗时: 0.085秒)
2025-04-15 16:40:17,014 - DEBUG - [01d31bf8] 响应内容: 
2025-04-15 16:40:17,029 - DEBUG - [bc9b448d] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:17,030 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:40:17,132 - INFO - RESPONSE: Status=204 Time=0.10s
2025-04-15 16:40:17,135 - DEBUG - [bc9b448d] 响应状态码: 204 (耗时: 0.106秒)
2025-04-15 16:40:17,136 - DEBUG - [bc9b448d] 响应内容: 
2025-04-15 16:40:17,138 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 204]), 耗时: 0.109秒
2025-04-15 16:40:17,141 - DEBUG - [d5791207] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:17,143 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/health
2025-04-15 16:40:17,181 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 16:40:17,184 - DEBUG - [d5791207] 响应状态码: 204 (耗时: 0.042秒)
2025-04-15 16:40:17,186 - DEBUG - [d5791207] 响应内容: 
2025-04-15 16:40:17,187 - INFO - API测试 [成功] GET http://8.138.188.26/api/health - 状态码: 204 (预期: [200, 201, 204]), 耗时: 0.046秒
2025-04-15 16:40:17,193 - DEBUG - [97375d98] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:40:17,195 - INFO - REQUEST: OPTIONS http://8.138.188.26/api/non-existent-endpoint-502
2025-04-15 16:40:17,245 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 16:40:17,247 - DEBUG - [97375d98] 响应状态码: 204 (耗时: 0.055秒)
2025-04-15 16:40:17,249 - DEBUG - [97375d98] 响应内容: 
2025-04-15 16:40:17,250 - INFO - API测试 [成功] GET http://8.138.188.26/api/non-existent-endpoint-502 - 状态码: 204 (预期: [200, 204, 502]), 耗时: 0.057秒
2025-04-15 16:47:35,662 - INFO - HTTP请求日志初始化完成，日志文件: C:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 16:47:35,666 - INFO - 已启用HTTP请求日志记录
2025-04-15 16:51:14,606 - INFO - HTTP请求日志初始化完成，日志文件: C:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 16:51:14,609 - INFO - 已启用HTTP请求日志记录
2025-04-15 16:51:14,616 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 16:51:14,617 - DEBUG - [a2dac564] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:51:14,651 - DEBUG - [a2dac564] 响应状态码: 204 (耗时: 0.034秒)
2025-04-15 16:51:14,652 - DEBUG - [a2dac564] 响应内容: 
2025-04-15 16:51:14,653 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 16:54:06,436 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 16:54:06,440 - INFO - 已启用HTTP请求日志记录
2025-04-15 16:54:06,494 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 16:54:06,497 - DEBUG - [742e2b6b] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 16:54:06,548 - DEBUG - [742e2b6b] 响应状态码: 204 (耗时: 0.051秒)
2025-04-15 16:54:06,549 - DEBUG - [742e2b6b] 响应内容: 
2025-04-15 16:54:06,550 - INFO - RESPONSE: Status=204 Time=0.06s
2025-04-15 17:01:07,189 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:01:07,191 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:01:07,200 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:01:07,200 - DEBUG - [ff0c06f5] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:01:07,247 - DEBUG - [ff0c06f5] 响应状态码: 204 (耗时: 0.047秒)
2025-04-15 17:01:07,247 - DEBUG - [ff0c06f5] 响应内容: 
2025-04-15 17:01:07,248 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:02:56,575 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:02:56,578 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:02:56,590 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:02:56,591 - DEBUG - [a12b405f] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:02:56,638 - DEBUG - [a12b405f] 响应状态码: 204 (耗时: 0.047秒)
2025-04-15 17:02:56,638 - DEBUG - [a12b405f] 响应内容: 
2025-04-15 17:02:56,639 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:03:09,314 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:03:09,334 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:03:09,347 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:03:09,347 - DEBUG - [62436435] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:03:09,412 - DEBUG - [62436435] 响应状态码: 204 (耗时: 0.065秒)
2025-04-15 17:03:09,413 - DEBUG - [62436435] 响应内容: 
2025-04-15 17:03:09,413 - INFO - RESPONSE: Status=204 Time=0.07s
2025-04-15 17:05:24,284 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:05:24,285 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:05:24,296 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:05:24,297 - DEBUG - [81ed7899] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:05:24,338 - DEBUG - [81ed7899] 响应状态码: 204 (耗时: 0.041秒)
2025-04-15 17:05:24,338 - DEBUG - [81ed7899] 响应内容: 
2025-04-15 17:05:24,339 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:05:58,730 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:06:00,099 - DEBUG - [aa1ae907] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:01,575 - DEBUG - [7b7e478e] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:02,305 - DEBUG - [056d361b] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:02,879 - DEBUG - [316b631f] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:05,121 - ERROR - [aa1ae907] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:05,146 - DEBUG - [a04da6c6] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:06,627 - ERROR - [7b7e478e] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:06,644 - DEBUG - [08635c85] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:07,309 - ERROR - [056d361b] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:07,320 - DEBUG - [6f69d935] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:07,970 - ERROR - [316b631f] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:07,980 - DEBUG - [1861242e] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:08,315 - DEBUG - [9cb6e448] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:08,411 - DEBUG - [9cb6e448] 响应状态码: 204 (耗时: 0.096秒)
2025-04-15 17:06:08,412 - DEBUG - [9cb6e448] 响应内容: 
2025-04-15 17:06:10,153 - ERROR - [a04da6c6] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:11,650 - ERROR - [08635c85] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:12,325 - ERROR - [6f69d935] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:12,983 - ERROR - [1861242e] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:06:55,845 - DEBUG - [e4b027b8] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:06:55,846 - DEBUG - [e4b027b8] 请求数据: {'username': 'mjj', 'password_hash': '3cf64228962e24507b0025edf5305d775180f0e557857a768922977ac947a1af', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '24c56d6633526ed266dfadb33e66727726e69b5d5c0ca2e990d94458f6487358'}
2025-04-15 17:06:56,839 - DEBUG - [0bacb708] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:07:00,852 - DEBUG - [e4b027b8] 响应状态码: 502 (耗时: 5.008秒)
2025-04-15 17:07:00,853 - DEBUG - [e4b027b8] 响应内容: 
2025-04-15 17:07:00,860 - DEBUG - [3e159f85] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:07:00,860 - DEBUG - [3e159f85] 请求数据: {'username': 'mjj', 'password_hash': '3cf64228962e24507b0025edf5305d775180f0e557857a768922977ac947a1af', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '24c56d6633526ed266dfadb33e66727726e69b5d5c0ca2e990d94458f6487358'}
2025-04-15 17:07:01,842 - ERROR - [0bacb708] 请求异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)
2025-04-15 17:07:05,868 - DEBUG - [3e159f85] 响应状态码: 502 (耗时: 5.009秒)
2025-04-15 17:07:05,870 - DEBUG - [3e159f85] 响应内容: 
2025-04-15 17:07:07,877 - DEBUG - [b72c6adb] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:07:07,878 - DEBUG - [b72c6adb] 请求数据: {'username': 'mjj', 'password_hash': '3cf64228962e24507b0025edf5305d775180f0e557857a768922977ac947a1af', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '24c56d6633526ed266dfadb33e66727726e69b5d5c0ca2e990d94458f6487358'}
2025-04-15 17:07:12,894 - DEBUG - [b72c6adb] 响应状态码: 502 (耗时: 5.017秒)
2025-04-15 17:07:12,896 - DEBUG - [b72c6adb] 响应内容: 
2025-04-15 17:07:16,902 - DEBUG - [cca26023] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:07:16,903 - DEBUG - [cca26023] 请求数据: {'username': 'mjj', 'password_hash': '3cf64228962e24507b0025edf5305d775180f0e557857a768922977ac947a1af', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '24c56d6633526ed266dfadb33e66727726e69b5d5c0ca2e990d94458f6487358'}
2025-04-15 17:07:21,930 - DEBUG - [cca26023] 响应状态码: 502 (耗时: 5.028秒)
2025-04-15 17:07:21,931 - DEBUG - [cca26023] 响应内容: 
2025-04-15 17:07:29,936 - DEBUG - [8641c7a9] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:07:29,937 - DEBUG - [8641c7a9] 请求数据: {'username': 'mjj', 'password_hash': '3cf64228962e24507b0025edf5305d775180f0e557857a768922977ac947a1af', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '24c56d6633526ed266dfadb33e66727726e69b5d5c0ca2e990d94458f6487358'}
2025-04-15 17:07:34,966 - DEBUG - [8641c7a9] 响应状态码: 502 (耗时: 5.031秒)
2025-04-15 17:07:34,968 - DEBUG - [8641c7a9] 响应内容: 
2025-04-15 17:08:09,671 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:08:09,674 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:08:09,688 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:08:09,689 - DEBUG - [e6465754] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:08:09,729 - DEBUG - [e6465754] 响应状态码: 204 (耗时: 0.040秒)
2025-04-15 17:08:09,729 - DEBUG - [e6465754] 响应内容: 
2025-04-15 17:08:09,730 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:08:50,428 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:08:50,429 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:08:50,443 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:08:50,444 - DEBUG - [13d25c5b] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:08:50,482 - DEBUG - [13d25c5b] 响应状态码: 204 (耗时: 0.038秒)
2025-04-15 17:08:50,483 - DEBUG - [13d25c5b] 响应内容: 
2025-04-15 17:08:50,484 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:12:57,710 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:12:57,712 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:12:57,728 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:12:57,728 - DEBUG - [34226e10] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:12:57,772 - DEBUG - [34226e10] 响应状态码: 204 (耗时: 0.044秒)
2025-04-15 17:12:57,773 - DEBUG - [34226e10] 响应内容: 
2025-04-15 17:12:57,773 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:12:57,777 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:12:57,778 - DEBUG - [73add229] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:12:57,855 - DEBUG - [73add229] 响应状态码: 204 (耗时: 0.077秒)
2025-04-15 17:12:57,855 - DEBUG - [73add229] 响应内容: 
2025-04-15 17:12:57,856 - INFO - RESPONSE: Status=204 Time=0.08s
2025-04-15 17:12:57,870 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:12:57,871 - DEBUG - [0d25cfcb] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:12:57,871 - DEBUG - [0d25cfcb] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': 'f56890265207d13dfee0265a714a6ebe0b48daafecd64a9590d703285c641851'}
2025-04-15 17:12:57,904 - DEBUG - [0d25cfcb] 响应状态码: 204 (耗时: 0.033秒)
2025-04-15 17:12:57,904 - DEBUG - [0d25cfcb] 响应内容: 
2025-04-15 17:12:57,905 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:12:57,910 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:12:57,911 - DEBUG - [cbbb8519] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:12:57,911 - DEBUG - [cbbb8519] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '970d2f4540f62f3a2faa7293bd73982b5353ec026eb44227732a72b860a7dd6d'}
2025-04-15 17:12:57,948 - DEBUG - [cbbb8519] 响应状态码: 204 (耗时: 0.037秒)
2025-04-15 17:12:57,949 - DEBUG - [cbbb8519] 响应内容: 
2025-04-15 17:12:57,950 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:12:57,955 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:12:57,958 - DEBUG - [50bc25bd] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:12:57,959 - DEBUG - [50bc25bd] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '970d2f4540f62f3a2faa7293bd73982b5353ec026eb44227732a72b860a7dd6d'}
2025-04-15 17:12:57,993 - DEBUG - [50bc25bd] 响应状态码: 204 (耗时: 0.035秒)
2025-04-15 17:12:57,993 - DEBUG - [50bc25bd] 响应内容: 
2025-04-15 17:12:57,994 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:14:47,168 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:14:47,170 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:14:47,180 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:14:47,180 - DEBUG - [716aceea] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:14:47,223 - DEBUG - [716aceea] 响应状态码: 204 (耗时: 0.042秒)
2025-04-15 17:14:47,223 - DEBUG - [716aceea] 响应内容: 
2025-04-15 17:14:47,224 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:14:47,227 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:14:47,228 - DEBUG - [bd60a146] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:14:47,266 - DEBUG - [bd60a146] 响应状态码: 204 (耗时: 0.038秒)
2025-04-15 17:14:47,267 - DEBUG - [bd60a146] 响应内容: 
2025-04-15 17:14:47,268 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:14:47,276 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:14:47,276 - DEBUG - [9685f71b] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:14:47,277 - DEBUG - [9685f71b] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '80795277ea3297fcf01f2a4ec72d5b6d1de85e5c21fdf8c1523dcd55b144c59c'}
2025-04-15 17:14:47,306 - DEBUG - [9685f71b] 响应状态码: 204 (耗时: 0.029秒)
2025-04-15 17:14:47,306 - DEBUG - [9685f71b] 响应内容: 
2025-04-15 17:14:47,307 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:14:47,310 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:14:47,313 - DEBUG - [b5a2c601] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:14:47,314 - DEBUG - [b5a2c601] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '80795277ea3297fcf01f2a4ec72d5b6d1de85e5c21fdf8c1523dcd55b144c59c'}
2025-04-15 17:14:47,344 - DEBUG - [b5a2c601] 响应状态码: 204 (耗时: 0.031秒)
2025-04-15 17:14:47,344 - DEBUG - [b5a2c601] 响应内容: 
2025-04-15 17:14:47,345 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:14:47,356 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:14:47,357 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:14:47,357 - DEBUG - [6725e0ac] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:14:47,387 - DEBUG - [6725e0ac] 响应状态码: 204 (耗时: 0.029秒)
2025-04-15 17:14:47,387 - DEBUG - [6725e0ac] 响应内容: 
2025-04-15 17:14:47,388 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:15:01,605 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:15:01,607 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:15:01,621 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:15:01,622 - DEBUG - [17be44bc] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:01,655 - DEBUG - [17be44bc] 响应状态码: 204 (耗时: 0.033秒)
2025-04-15 17:15:01,656 - DEBUG - [17be44bc] 响应内容: 
2025-04-15 17:15:01,656 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:15:01,660 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:15:01,661 - DEBUG - [d5156c5c] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:01,702 - DEBUG - [d5156c5c] 响应状态码: 204 (耗时: 0.041秒)
2025-04-15 17:15:01,702 - DEBUG - [d5156c5c] 响应内容: 
2025-04-15 17:15:01,703 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:15:01,713 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:15:01,714 - DEBUG - [b3d78afe] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:01,714 - DEBUG - [b3d78afe] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': 'dc4ea10890c52abb14d3f562ec02e5f0b12ac5aa7dcf3a6afdaee85fb3798bc9'}
2025-04-15 17:15:01,775 - DEBUG - [b3d78afe] 响应状态码: 204 (耗时: 0.061秒)
2025-04-15 17:15:01,776 - DEBUG - [b3d78afe] 响应内容: 
2025-04-15 17:15:01,776 - INFO - RESPONSE: Status=204 Time=0.06s
2025-04-15 17:15:01,780 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:15:01,781 - DEBUG - [4d87e7ad] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:01,784 - DEBUG - [4d87e7ad] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': 'dc4ea10890c52abb14d3f562ec02e5f0b12ac5aa7dcf3a6afdaee85fb3798bc9'}
2025-04-15 17:15:01,822 - DEBUG - [4d87e7ad] 响应状态码: 204 (耗时: 0.041秒)
2025-04-15 17:15:01,823 - DEBUG - [4d87e7ad] 响应内容: 
2025-04-15 17:15:01,823 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:15:01,837 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:15:01,837 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:15:01,838 - DEBUG - [27a330bc] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:01,864 - DEBUG - [27a330bc] 响应状态码: 204 (耗时: 0.026秒)
2025-04-15 17:15:01,866 - DEBUG - [27a330bc] 响应内容: 
2025-04-15 17:15:01,868 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:15:46,014 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:15:46,016 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:15:46,030 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:15:46,030 - DEBUG - [23753fe1] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:46,060 - DEBUG - [23753fe1] 响应状态码: 204 (耗时: 0.029秒)
2025-04-15 17:15:46,061 - DEBUG - [23753fe1] 响应内容: 
2025-04-15 17:15:46,062 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:15:46,066 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:15:46,067 - DEBUG - [8121fcff] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:46,110 - DEBUG - [8121fcff] 响应状态码: 204 (耗时: 0.044秒)
2025-04-15 17:15:46,112 - DEBUG - [8121fcff] 响应内容: 
2025-04-15 17:15:46,113 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:15:46,121 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:15:46,122 - DEBUG - [0cf8c7da] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:46,122 - DEBUG - [0cf8c7da] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '3b853e0cf815e661ea773b943b6fdbad3c053f4be60f98cc623330a9221c163c'}
2025-04-15 17:15:46,167 - DEBUG - [0cf8c7da] 响应状态码: 204 (耗时: 0.045秒)
2025-04-15 17:15:46,167 - DEBUG - [0cf8c7da] 响应内容: 
2025-04-15 17:15:46,168 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:15:46,171 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:15:46,172 - DEBUG - [455ef2ad] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:46,173 - DEBUG - [455ef2ad] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '3b853e0cf815e661ea773b943b6fdbad3c053f4be60f98cc623330a9221c163c'}
2025-04-15 17:15:46,205 - DEBUG - [455ef2ad] 响应状态码: 204 (耗时: 0.033秒)
2025-04-15 17:15:46,205 - DEBUG - [455ef2ad] 响应内容: 
2025-04-15 17:15:46,206 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:15:46,218 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:15:46,219 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:15:46,219 - DEBUG - [35269926] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:15:46,251 - DEBUG - [35269926] 响应状态码: 204 (耗时: 0.032秒)
2025-04-15 17:15:46,252 - DEBUG - [35269926] 响应内容: 
2025-04-15 17:15:46,252 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:18:26,388 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:18:26,390 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:18:26,402 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:18:26,403 - DEBUG - [431ed8ac] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:18:26,450 - DEBUG - [431ed8ac] 响应状态码: 204 (耗时: 0.048秒)
2025-04-15 17:18:26,451 - DEBUG - [431ed8ac] 响应内容: 
2025-04-15 17:18:26,451 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:18:26,456 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:18:26,457 - DEBUG - [83024c81] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:18:26,504 - DEBUG - [83024c81] 响应状态码: 204 (耗时: 0.047秒)
2025-04-15 17:18:26,504 - DEBUG - [83024c81] 响应内容: 
2025-04-15 17:18:26,505 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:18:26,514 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:18:26,515 - DEBUG - [4b83d8bb] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:18:26,515 - DEBUG - [4b83d8bb] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '03902c4056ec606231bf0c4d2e186c04436297d7f31f6087a9f0f356f0b82cde'}
2025-04-15 17:18:26,546 - DEBUG - [4b83d8bb] 响应状态码: 204 (耗时: 0.032秒)
2025-04-15 17:18:26,547 - DEBUG - [4b83d8bb] 响应内容: 
2025-04-15 17:18:26,547 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:18:26,551 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:18:26,552 - DEBUG - [0acf0446] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:18:26,552 - DEBUG - [0acf0446] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '03902c4056ec606231bf0c4d2e186c04436297d7f31f6087a9f0f356f0b82cde'}
2025-04-15 17:18:26,578 - DEBUG - [0acf0446] 响应状态码: 204 (耗时: 0.026秒)
2025-04-15 17:18:26,578 - DEBUG - [0acf0446] 响应内容: 
2025-04-15 17:18:26,579 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:18:26,590 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:18:26,591 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:18:26,591 - DEBUG - [3829ed06] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:18:26,620 - DEBUG - [3829ed06] 响应状态码: 204 (耗时: 0.028秒)
2025-04-15 17:18:26,620 - DEBUG - [3829ed06] 响应内容: 
2025-04-15 17:18:26,621 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:19:34,092 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:19:34,094 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:19:34,118 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:19:34,119 - DEBUG - [da5a9266] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:19:34,369 - DEBUG - [da5a9266] 响应状态码: 204 (耗时: 0.251秒)
2025-04-15 17:19:34,370 - DEBUG - [da5a9266] 响应内容: 
2025-04-15 17:19:34,371 - INFO - RESPONSE: Status=204 Time=0.25s
2025-04-15 17:19:34,375 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:19:34,375 - DEBUG - [c9a21d2e] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:19:34,492 - DEBUG - [c9a21d2e] 响应状态码: 204 (耗时: 0.117秒)
2025-04-15 17:19:34,493 - DEBUG - [c9a21d2e] 响应内容: 
2025-04-15 17:19:34,493 - INFO - RESPONSE: Status=204 Time=0.12s
2025-04-15 17:19:34,503 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:19:34,504 - DEBUG - [127c53d7] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:19:34,504 - DEBUG - [127c53d7] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': 'c24677ec838b284ba9625ef481a1b5a092a49dc69840c3d44de62605e9755bb8'}
2025-04-15 17:19:34,552 - DEBUG - [127c53d7] 响应状态码: 204 (耗时: 0.048秒)
2025-04-15 17:19:34,552 - DEBUG - [127c53d7] 响应内容: 
2025-04-15 17:19:34,553 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:19:34,557 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:19:34,558 - DEBUG - [daee54d7] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:19:34,558 - DEBUG - [daee54d7] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': 'c24677ec838b284ba9625ef481a1b5a092a49dc69840c3d44de62605e9755bb8'}
2025-04-15 17:19:34,603 - DEBUG - [daee54d7] 响应状态码: 204 (耗时: 0.046秒)
2025-04-15 17:19:34,604 - DEBUG - [daee54d7] 响应内容: 
2025-04-15 17:19:34,604 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:19:34,617 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:19:34,618 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:19:34,619 - DEBUG - [5eca8cc6] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:19:34,655 - DEBUG - [5eca8cc6] 响应状态码: 204 (耗时: 0.036秒)
2025-04-15 17:19:34,656 - DEBUG - [5eca8cc6] 响应内容: 
2025-04-15 17:19:34,656 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:20:47,385 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:20:47,389 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:20:47,419 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:20:47,423 - DEBUG - [1b76e696] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:20:47,509 - DEBUG - [1b76e696] 响应状态码: 204 (耗时: 0.086秒)
2025-04-15 17:20:47,510 - DEBUG - [1b76e696] 响应内容: 
2025-04-15 17:20:47,510 - INFO - RESPONSE: Status=204 Time=0.09s
2025-04-15 17:20:47,514 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:20:47,515 - DEBUG - [711ffd6c] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:20:47,558 - DEBUG - [711ffd6c] 响应状态码: 204 (耗时: 0.044秒)
2025-04-15 17:20:47,559 - DEBUG - [711ffd6c] 响应内容: 
2025-04-15 17:20:47,559 - INFO - RESPONSE: Status=204 Time=0.05s
2025-04-15 17:20:47,567 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:20:47,568 - DEBUG - [3c62b809] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:20:47,568 - DEBUG - [3c62b809] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '32e5dab13af00122b1b3beb9d66693136edcc4c8ef9e9ffc8a17dba9709fed5c'}
2025-04-15 17:20:47,646 - DEBUG - [3c62b809] 响应状态码: 204 (耗时: 0.078秒)
2025-04-15 17:20:47,647 - DEBUG - [3c62b809] 响应内容: 
2025-04-15 17:20:47,647 - INFO - RESPONSE: Status=204 Time=0.08s
2025-04-15 17:20:47,651 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:20:47,651 - DEBUG - [31b02f22] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:20:47,652 - DEBUG - [31b02f22] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '32e5dab13af00122b1b3beb9d66693136edcc4c8ef9e9ffc8a17dba9709fed5c'}
2025-04-15 17:20:47,691 - DEBUG - [31b02f22] 响应状态码: 204 (耗时: 0.040秒)
2025-04-15 17:20:47,692 - DEBUG - [31b02f22] 响应内容: 
2025-04-15 17:20:47,692 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:20:47,706 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:20:47,707 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:20:47,708 - DEBUG - [05458187] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:20:47,740 - DEBUG - [05458187] 响应状态码: 204 (耗时: 0.032秒)
2025-04-15 17:20:47,741 - DEBUG - [05458187] 响应内容: 
2025-04-15 17:20:47,741 - INFO - RESPONSE: Status=204 Time=0.03s
2025-04-15 17:21:00,857 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests.log
2025-04-15 17:21:00,859 - INFO - 已启用HTTP请求日志记录
2025-04-15 17:21:00,871 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:21:00,871 - DEBUG - [a08d8e12] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:21:00,939 - DEBUG - [a08d8e12] 响应状态码: 204 (耗时: 0.068秒)
2025-04-15 17:21:00,940 - DEBUG - [a08d8e12] 响应内容: 
2025-04-15 17:21:00,940 - INFO - RESPONSE: Status=204 Time=0.07s
2025-04-15 17:21:00,944 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/health
2025-04-15 17:21:00,944 - DEBUG - [1eb03133] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:21:00,984 - DEBUG - [1eb03133] 响应状态码: 204 (耗时: 0.040秒)
2025-04-15 17:21:00,985 - DEBUG - [1eb03133] 响应内容: 
2025-04-15 17:21:00,986 - INFO - RESPONSE: Status=204 Time=0.04s
2025-04-15 17:21:00,995 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/register
2025-04-15 17:21:00,996 - DEBUG - [632ba730] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:21:00,996 - DEBUG - [632ba730] 请求数据: {'username': 'testuser_**********', 'fullName': '测试用户', 'email': '<EMAIL>', 'role': 'personal_user', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': '97a9a78c7a5a17a3b91b004e90ace897bf4c8a2c6f460ee70036aec5b8dc7d73'}
2025-04-15 17:21:01,081 - DEBUG - [632ba730] 响应状态码: 204 (耗时: 0.085秒)
2025-04-15 17:21:01,081 - DEBUG - [632ba730] 响应内容: 
2025-04-15 17:21:01,082 - INFO - RESPONSE: Status=204 Time=0.09s
2025-04-15 17:21:01,089 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/login
2025-04-15 17:21:01,091 - DEBUG - [de796998] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:21:01,092 - DEBUG - [de796998] 请求数据: {'username': 'testuser_**********', 'password_hash': 'b55c8792d1ce458e279308835f8a97b580263503e76e1998e279703e35ad0c2e', 'timestamp': **********, 'app_id': 'health_trea_app', 'signature': 'f7cd046fe1db73adb487a881dacd0768d7e26d98d36d5c5c5a8eaa87b9886e51'}
2025-04-15 17:21:01,163 - DEBUG - [de796998] 响应状态码: 204 (耗时: 0.072秒)
2025-04-15 17:21:01,164 - DEBUG - [de796998] 响应内容: 
2025-04-15 17:21:01,164 - INFO - RESPONSE: Status=204 Time=0.07s
2025-04-15 17:21:01,189 - INFO - REQUEST: UNKNOWN http://8.138.188.26/api/auth/logout
2025-04-15 17:21:01,191 - INFO - HEADERS: {'Authorization': '******'}
2025-04-15 17:21:01,193 - DEBUG - [2e7506b2] 发送 UNKNOWN 请求: UNKNOWN
2025-04-15 17:21:01,241 - DEBUG - [2e7506b2] 响应状态码: 204 (耗时: 0.049秒)
2025-04-15 17:21:01,242 - DEBUG - [2e7506b2] 响应内容: 
2025-04-15 17:21:01,243 - INFO - RESPONSE: Status=204 Time=0.05s
