#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存工具模块
提供Redis缓存操作的便捷方法和工具
"""

import json
import pickle
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable, Type
from dataclasses import dataclass
from enum import Enum
from functools import wraps
import asyncio

try:
    import redis
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from .error_handler import CacheException, BusinessException
from .env_config import env_config

class CacheType(str, Enum):
    """缓存类型枚举"""
    MEMORY = "memory"
    REDIS = "redis"
    HYBRID = "hybrid"

class SerializationType(str, Enum):
    """序列化类型枚举"""
    JSON = "json"
    PICKLE = "pickle"
    STRING = "string"

class CacheStrategy(str, Enum):
    """缓存策略枚举"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于过期时间

@dataclass
class CacheConfig:
    """缓存配置"""
    cache_type: CacheType = CacheType.MEMORY
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    redis_ssl: bool = False
    max_connections: int = 10
    default_ttl: int = 3600  # 默认过期时间（秒）
    max_memory_size: int = 100 * 1024 * 1024  # 内存缓存最大大小（字节）
    serialization: SerializationType = SerializationType.JSON
    key_prefix: str = "health_app"
    enable_compression: bool = False
    strategy: CacheStrategy = CacheStrategy.LRU

@dataclass
class CacheItem:
    """缓存项"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime] = None
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size: int = 0

@dataclass
class CacheStats:
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    memory_usage: int = 0
    item_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0

class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, config: CacheConfig):
        self.config = config
        self._cache: Dict[str, CacheItem] = {}
        self._stats = CacheStats()
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        async with self._lock:
            full_key = self._build_key(key)
            
            if full_key not in self._cache:
                self._stats.misses += 1
                return None
            
            item = self._cache[full_key]
            
            # 检查是否过期
            if item.expires_at and datetime.now() > item.expires_at:
                del self._cache[full_key]
                self._stats.misses += 1
                self._stats.evictions += 1
                return None
            
            # 更新访问信息
            item.access_count += 1
            item.last_accessed = datetime.now()
            
            self._stats.hits += 1
            return item.value
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        async with self._lock:
            full_key = self._build_key(key)
            
            # 计算过期时间
            expires_at = None
            if ttl or self.config.default_ttl:
                expires_at = datetime.now() + timedelta(seconds=ttl or self.config.default_ttl)
            
            # 序列化值并计算大小
            serialized_value = self._serialize(value)
            size = len(str(serialized_value))
            
            # 检查内存限制
            await self._ensure_memory_limit(size)
            
            # 创建缓存项
            item = CacheItem(
                key=full_key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at,
                size=size
            )
            
            self._cache[full_key] = item
            self._stats.sets += 1
            self._stats.memory_usage += size
            self._stats.item_count = len(self._cache)
            
            return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        async with self._lock:
            full_key = self._build_key(key)
            
            if full_key in self._cache:
                item = self._cache[full_key]
                del self._cache[full_key]
                
                self._stats.deletes += 1
                self._stats.memory_usage -= item.size
                self._stats.item_count = len(self._cache)
                
                return True
            
            return False
    
    async def clear(self) -> bool:
        """清空缓存"""
        async with self._lock:
            self._cache.clear()
            self._stats = CacheStats()
            return True
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        full_key = self._build_key(key)
        return full_key in self._cache
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        import fnmatch
        
        async with self._lock:
            if pattern == "*":
                return list(self._cache.keys())
            
            return [
                key for key in self._cache.keys()
                if fnmatch.fnmatch(key, pattern)
            ]
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        return self._stats
    
    def _build_key(self, key: str) -> str:
        """构建完整键名"""
        return f"{self.config.key_prefix}:{key}"
    
    def _serialize(self, value: Any) -> Any:
        """序列化值"""
        if self.config.serialization == SerializationType.JSON:
            return json.dumps(value, default=str)
        elif self.config.serialization == SerializationType.PICKLE:
            return pickle.dumps(value)
        else:
            return str(value)
    
    def _deserialize(self, value: Any) -> Any:
        """反序列化值"""
        if self.config.serialization == SerializationType.JSON:
            return json.loads(value)
        elif self.config.serialization == SerializationType.PICKLE:
            return pickle.loads(value)
        else:
            return value
    
    async def _ensure_memory_limit(self, new_size: int):
        """确保内存限制"""
        while (self._stats.memory_usage + new_size) > self.config.max_memory_size:
            await self._evict_item()
    
    async def _evict_item(self):
        """驱逐缓存项"""
        if not self._cache:
            return
        
        if self.config.strategy == CacheStrategy.LRU:
            # 驱逐最近最少使用的项
            oldest_key = min(
                self._cache.keys(),
                key=lambda k: self._cache[k].last_accessed or self._cache[k].created_at
            )
        elif self.config.strategy == CacheStrategy.LFU:
            # 驱逐使用频率最少的项
            oldest_key = min(
                self._cache.keys(),
                key=lambda k: self._cache[k].access_count
            )
        elif self.config.strategy == CacheStrategy.FIFO:
            # 驱逐最早创建的项
            oldest_key = min(
                self._cache.keys(),
                key=lambda k: self._cache[k].created_at
            )
        else:  # TTL
            # 驱逐最早过期的项
            oldest_key = min(
                self._cache.keys(),
                key=lambda k: self._cache[k].expires_at or datetime.max
            )
        
        item = self._cache[oldest_key]
        del self._cache[oldest_key]
        
        self._stats.evictions += 1
        self._stats.memory_usage -= item.size
        self._stats.item_count = len(self._cache)

class RedisCache:
    """Redis缓存实现"""
    
    def __init__(self, config: CacheConfig):
        if not REDIS_AVAILABLE:
            raise CacheException("Redis库未安装，无法使用Redis缓存")
        
        self.config = config
        self._redis: Optional[aioredis.Redis] = None
        self._stats = CacheStats()
    
    async def connect(self):
        """连接Redis"""
        if self._redis:
            return
        
        try:
            self._redis = aioredis.from_url(
                f"redis://{self.config.redis_host}:{self.config.redis_port}/{self.config.redis_db}",
                password=self.config.redis_password,
                ssl=self.config.redis_ssl,
                max_connections=self.config.max_connections,
                decode_responses=True
            )
            
            # 测试连接
            await self._redis.ping()
        
        except Exception as e:
            raise CacheException(f"Redis连接失败: {e}")
    
    async def disconnect(self):
        """断开Redis连接"""
        if self._redis:
            await self._redis.close()
            self._redis = None
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        await self.connect()
        
        try:
            full_key = self._build_key(key)
            value = await self._redis.get(full_key)
            
            if value is None:
                self._stats.misses += 1
                return None
            
            self._stats.hits += 1
            return self._deserialize(value)
        
        except Exception as e:
            self._stats.misses += 1
            raise CacheException(f"Redis获取失败: {e}")
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        await self.connect()
        
        try:
            full_key = self._build_key(key)
            serialized_value = self._serialize(value)
            
            if ttl or self.config.default_ttl:
                await self._redis.setex(
                    full_key,
                    ttl or self.config.default_ttl,
                    serialized_value
                )
            else:
                await self._redis.set(full_key, serialized_value)
            
            self._stats.sets += 1
            return True
        
        except Exception as e:
            raise CacheException(f"Redis设置失败: {e}")
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        await self.connect()
        
        try:
            full_key = self._build_key(key)
            result = await self._redis.delete(full_key)
            
            if result > 0:
                self._stats.deletes += 1
                return True
            
            return False
        
        except Exception as e:
            raise CacheException(f"Redis删除失败: {e}")
    
    async def clear(self) -> bool:
        """清空缓存"""
        await self.connect()
        
        try:
            pattern = f"{self.config.key_prefix}:*"
            keys = await self._redis.keys(pattern)
            
            if keys:
                await self._redis.delete(*keys)
            
            self._stats = CacheStats()
            return True
        
        except Exception as e:
            raise CacheException(f"Redis清空失败: {e}")
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        await self.connect()
        
        try:
            full_key = self._build_key(key)
            return await self._redis.exists(full_key) > 0
        
        except Exception as e:
            raise CacheException(f"Redis检查存在失败: {e}")
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        await self.connect()
        
        try:
            full_pattern = f"{self.config.key_prefix}:{pattern}"
            keys = await self._redis.keys(full_pattern)
            
            # 移除前缀
            prefix_len = len(self.config.key_prefix) + 1
            return [key[prefix_len:] for key in keys]
        
        except Exception as e:
            raise CacheException(f"Redis获取键列表失败: {e}")
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        await self.connect()
        
        try:
            info = await self._redis.info("memory")
            self._stats.memory_usage = info.get("used_memory", 0)
            
            # 获取键数量
            pattern = f"{self.config.key_prefix}:*"
            keys = await self._redis.keys(pattern)
            self._stats.item_count = len(keys)
            
            return self._stats
        
        except Exception as e:
            raise CacheException(f"Redis获取统计失败: {e}")
    
    def _build_key(self, key: str) -> str:
        """构建完整键名"""
        return f"{self.config.key_prefix}:{key}"
    
    def _serialize(self, value: Any) -> str:
        """序列化值"""
        if self.config.serialization == SerializationType.JSON:
            return json.dumps(value, default=str)
        elif self.config.serialization == SerializationType.PICKLE:
            return pickle.dumps(value).decode('latin1')
        else:
            return str(value)
    
    def _deserialize(self, value: str) -> Any:
        """反序列化值"""
        if self.config.serialization == SerializationType.JSON:
            return json.loads(value)
        elif self.config.serialization == SerializationType.PICKLE:
            return pickle.loads(value.encode('latin1'))
        else:
            return value

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, config: Optional[CacheConfig] = None):
        self.config = config or self._get_default_config()
        self._cache = self._create_cache()
    
    def _get_default_config(self) -> CacheConfig:
        """获取默认配置"""
        # env_config已经在文件顶部导入
        
        return CacheConfig(
            cache_type=CacheType.MEMORY,
            redis_host=env_config.REDIS_HOST,
            redis_port=env_config.REDIS_PORT,
            redis_db=env_config.REDIS_DB,
            redis_password=env_config.REDIS_PASSWORD,
            default_ttl=getattr(env_config, 'CACHE_TTL', 3600),
            key_prefix="health_app"
        )
    
    def _create_cache(self) -> Union[MemoryCache, RedisCache]:
        """创建缓存实例"""
        if self.config.cache_type == CacheType.REDIS:
            return RedisCache(self.config)
        else:
            return MemoryCache(self.config)
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        return await self._cache.get(key)
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """设置缓存值"""
        return await self._cache.set(key, value, ttl)
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        return await self._cache.delete(key)
    
    async def clear(self) -> bool:
        """清空缓存"""
        return await self._cache.clear()
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        return await self._cache.exists(key)
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        return await self._cache.keys(pattern)
    
    async def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        return await self._cache.get_stats()
    
    async def close(self):
        """关闭缓存连接"""
        if isinstance(self._cache, RedisCache):
            await self._cache.disconnect()

# 缓存装饰器
def cache(
    key_template: str = "{func_name}:{args_hash}",
    ttl: Optional[int] = None,
    cache_manager: Optional[CacheManager] = None
):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 获取缓存管理器
            cm = cache_manager or get_default_cache_manager()
            
            # 生成缓存键
            cache_key = _generate_cache_key(key_template, func, args, kwargs)
            
            # 尝试从缓存获取
            cached_result = await cm.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 存储到缓存
            await cm.set(cache_key, result, ttl)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 对于同步函数，需要在事件循环中运行
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(async_wrapper(*args, **kwargs))
        
        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def cache_invalidate(
    key_pattern: str,
    cache_manager: Optional[CacheManager] = None
):
    """缓存失效装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 获取缓存管理器
            cm = cache_manager or get_default_cache_manager()
            
            # 删除匹配的缓存键
            keys = await cm.keys(key_pattern)
            for key in keys:
                await cm.delete(key)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(async_wrapper(*args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 全局缓存管理器
_default_cache_manager: Optional[CacheManager] = None

def get_default_cache_manager() -> CacheManager:
    """获取默认缓存管理器"""
    global _default_cache_manager
    
    if _default_cache_manager is None:
        _default_cache_manager = CacheManager()
    
    return _default_cache_manager

def set_default_cache_manager(cache_manager: CacheManager):
    """设置默认缓存管理器"""
    global _default_cache_manager
    _default_cache_manager = cache_manager

# 便捷函数
async def get_cache(key: str) -> Optional[Any]:
    """获取缓存值"""
    cm = get_default_cache_manager()
    return await cm.get(key)

async def set_cache(
    key: str,
    value: Any,
    ttl: Optional[int] = None
) -> bool:
    """设置缓存值"""
    cm = get_default_cache_manager()
    return await cm.set(key, value, ttl)

async def delete_cache(key: str) -> bool:
    """删除缓存值"""
    cm = get_default_cache_manager()
    return await cm.delete(key)

async def clear_cache() -> bool:
    """清空缓存"""
    cm = get_default_cache_manager()
    return await cm.clear()

async def cache_exists(key: str) -> bool:
    """检查缓存是否存在"""
    cm = get_default_cache_manager()
    return await cm.exists(key)

def _generate_cache_key(
    template: str,
    func: Callable,
    args: tuple,
    kwargs: dict
) -> str:
    """生成缓存键"""
    # 创建参数哈希
    args_str = str(args) + str(sorted(kwargs.items()))
    args_hash = hashlib.md5(args_str.encode()).hexdigest()[:8]
    
    # 替换模板变量
    return template.format(
        func_name=func.__name__,
        args_hash=args_hash,
        module=func.__module__
    )

# 缓存预热
class CacheWarmer:
    """缓存预热器"""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager or get_default_cache_manager()
        self._warmup_tasks: List[Callable] = []
    
    def register_warmup_task(self, func: Callable, *args, **kwargs):
        """注册预热任务"""
        self._warmup_tasks.append(lambda: func(*args, **kwargs))
    
    async def warmup(self):
        """执行缓存预热"""
        for task in self._warmup_tasks:
            try:
                await task()
            except Exception as e:
                print(f"缓存预热任务失败: {e}")
    
    def clear_tasks(self):
        """清空预热任务"""
        self._warmup_tasks.clear()

# 缓存监控
class CacheMonitor:
    """缓存监控器"""
    
    def __init__(self, cache_manager: Optional[CacheManager] = None):
        self.cache_manager = cache_manager or get_default_cache_manager()
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self, interval: int = 60):
        """开始监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(
            self._monitor_loop(interval)
        )
    
    async def stop_monitoring(self):
        """停止监控"""
        self._monitoring = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_loop(self, interval: int):
        """监控循环"""
        while self._monitoring:
            try:
                stats = await self.cache_manager.get_stats()
                
                # 记录统计信息
                print(f"缓存统计 - 命中率: {stats.hit_rate:.2%}, "
                      f"内存使用: {stats.memory_usage / 1024 / 1024:.2f}MB, "
                      f"项目数: {stats.item_count}")
                
                # 检查告警条件
                if stats.hit_rate < 0.5:  # 命中率低于50%
                    print("警告: 缓存命中率过低")
                
                if stats.memory_usage > 500 * 1024 * 1024:  # 内存使用超过500MB
                    print("警告: 缓存内存使用过高")
                
                await asyncio.sleep(interval)
            
            except Exception as e:
                print(f"缓存监控错误: {e}")
                await asyncio.sleep(interval)

# 分布式锁
class DistributedLock:
    """分布式锁"""
    
    def __init__(
        self,
        key: str,
        timeout: int = 30,
        cache_manager: Optional[CacheManager] = None
    ):
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.cache_manager = cache_manager or get_default_cache_manager()
        self._acquired = False
    
    async def acquire(self) -> bool:
        """获取锁"""
        if self._acquired:
            return True
        
        # 尝试设置锁
        success = await self.cache_manager.set(
            self.key,
            datetime.now().isoformat(),
            ttl=self.timeout
        )
        
        if success:
            self._acquired = True
        
        return success
    
    async def release(self) -> bool:
        """释放锁"""
        if not self._acquired:
            return True
        
        success = await self.cache_manager.delete(self.key)
        
        if success:
            self._acquired = False
        
        return success
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not await self.acquire():
            raise CacheException(f"无法获取锁: {self.key}")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.release()

# 缓存工具函数
def create_cache_config(**kwargs) -> CacheConfig:
    """创建缓存配置"""
    return CacheConfig(**kwargs)

def create_cache_manager(config: Optional[CacheConfig] = None) -> CacheManager:
    """创建缓存管理器"""
    return CacheManager(config)

async def test_cache_connection(cache_manager: Optional[CacheManager] = None) -> bool:
    """测试缓存连接"""
    cm = cache_manager or get_default_cache_manager()
    
    try:
        test_key = "test_connection"
        test_value = "test_value"
        
        # 测试设置和获取
        await cm.set(test_key, test_value, ttl=10)
        result = await cm.get(test_key)
        
        # 清理测试数据
        await cm.delete(test_key)
        
        return result == test_value
    
    except Exception:
        return False

def get_cache_key_for_user(user_id: str, key: str) -> str:
    """为用户生成缓存键"""
    return f"user:{user_id}:{key}"

def get_cache_key_for_session(session_id: str, key: str) -> str:
    """为会话生成缓存键"""
    return f"session:{session_id}:{key}"

def get_cache_key_for_api(endpoint: str, params_hash: str) -> str:
    """为API生成缓存键"""
    return f"api:{endpoint}:{params_hash}"