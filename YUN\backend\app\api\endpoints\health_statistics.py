from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

from app.api.deps import get_db, get_current_user
from app.core.security import verify_user_access
from app.models.user import User
from app.models.questionnaire import QuestionnaireResponse
from app.models.assessment import AssessmentResponse
from app.models.result import QuestionnaireResult, AssessmentResult
from app.models.distribution import QuestionnaireDistribution, AssessmentDistribution
from app.models.health_record import HealthRecord
from app.utils.health_records_cache import cache_health_records
from app.utils.performance_monitor import monitor_performance, log_api_access
from app.middleware.error_handler import (
    HealthRecordsErrorHandler,
    user_not_found_error,
    permission_denied_error,
    validation_error,
    database_error
)

router = APIRouter()

@router.get("/user-health-statistics/{custom_id}", response_model=dict)
@cache_health_records(ttl=600)  # 缓存10分钟
@monitor_performance("get_user_health_statistics")
def get_user_health_statistics(
    custom_id: str,
    period: Optional[str] = Query("30d", description="统计周期: 7d, 30d, 90d, 1y"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户健康记录统计信息
    """
    try:
        # 权限验证
        if not verify_user_access(current_user, custom_id):
            return permission_denied_error()
        
        # 获取用户
        user = db.query(User).filter(User.custom_id == custom_id).first()
        if not user:
            return user_not_found_error()
        
        # 计算时间范围
        end_date = datetime.now()
        if period == "7d":
            start_date = end_date - timedelta(days=7)
        elif period == "30d":
            start_date = end_date - timedelta(days=30)
        elif period == "90d":
            start_date = end_date - timedelta(days=90)
        elif period == "1y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)
        
        # 获取问卷统计
        questionnaire_stats = get_questionnaire_statistics(db, user.id, start_date, end_date)
        
        # 获取评估统计
        assessment_stats = get_assessment_statistics(db, user.id, start_date, end_date)
        
        # 获取健康记录统计
        health_record_stats = get_health_record_statistics(db, user.id, start_date, end_date)
        
        # 获取完成趋势
        completion_trend = get_completion_trend(db, user.id, start_date, end_date)
        
        return HealthRecordsErrorHandler.create_success_response(
            data={
                "period": period,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "questionnaire_stats": questionnaire_stats,
                "assessment_stats": assessment_stats,
                "health_record_stats": health_record_stats,
                "completion_trend": completion_trend,
                "summary": {
                    "total_completed": questionnaire_stats["completed"] + assessment_stats["completed"],
                    "total_pending": questionnaire_stats["pending"] + assessment_stats["pending"],
                    "completion_rate": calculate_completion_rate(
                        questionnaire_stats["completed"] + assessment_stats["completed"],
                        questionnaire_stats["total"] + assessment_stats["total"]
                    )
                }
            },
            message="获取用户健康统计成功"
        )
    
    except Exception as e:
        print(f"获取用户健康统计时出错: {e}")
        return database_error()

def get_questionnaire_statistics(db: Session, user_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """
    获取问卷统计信息
    """
    # 总分发数
    total_distributed = db.query(func.count(QuestionnaireDistribution.id)).filter(
        and_(
            QuestionnaireDistribution.user_id == user_id,
            QuestionnaireDistribution.created_at >= start_date,
            QuestionnaireDistribution.created_at <= end_date
        )
    ).scalar() or 0
    
    # 已完成数
    completed = db.query(func.count(QuestionnaireResponse.id)).filter(
        and_(
            QuestionnaireResponse.user_id == user_id,
            QuestionnaireResponse.created_at >= start_date,
            QuestionnaireResponse.created_at <= end_date
        )
    ).scalar() or 0
    
    # 待完成数
    pending = total_distributed - completed
    
    # 按类型统计
    type_stats = db.query(
        QuestionnaireDistribution.questionnaire_type,
        func.count(QuestionnaireDistribution.id).label('count')
    ).filter(
        and_(
            QuestionnaireDistribution.user_id == user_id,
            QuestionnaireDistribution.created_at >= start_date,
            QuestionnaireDistribution.created_at <= end_date
        )
    ).group_by(QuestionnaireDistribution.questionnaire_type).all()
    
    return {
        "total": total_distributed,
        "completed": completed,
        "pending": pending,
        "completion_rate": calculate_completion_rate(completed, total_distributed),
        "by_type": {stat.questionnaire_type: stat.count for stat in type_stats}
    }

def get_assessment_statistics(db: Session, user_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """
    获取评估统计信息
    """
    # 总分发数
    total_distributed = db.query(func.count(AssessmentDistribution.id)).filter(
        and_(
            AssessmentDistribution.user_id == user_id,
            AssessmentDistribution.created_at >= start_date,
            AssessmentDistribution.created_at <= end_date
        )
    ).scalar() or 0
    
    # 已完成数
    completed = db.query(func.count(AssessmentResponse.id)).filter(
        and_(
            AssessmentResponse.user_id == user_id,
            AssessmentResponse.created_at >= start_date,
            AssessmentResponse.created_at <= end_date
        )
    ).scalar() or 0
    
    # 待完成数
    pending = total_distributed - completed
    
    # 按类型统计
    type_stats = db.query(
        AssessmentDistribution.assessment_type,
        func.count(AssessmentDistribution.id).label('count')
    ).filter(
        and_(
            AssessmentDistribution.user_id == user_id,
            AssessmentDistribution.created_at >= start_date,
            AssessmentDistribution.created_at <= end_date
        )
    ).group_by(AssessmentDistribution.assessment_type).all()
    
    return {
        "total": total_distributed,
        "completed": completed,
        "pending": pending,
        "completion_rate": calculate_completion_rate(completed, total_distributed),
        "by_type": {stat.assessment_type: stat.count for stat in type_stats}
    }

def get_health_record_statistics(db: Session, user_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
    """
    获取健康记录统计信息
    """
    # 按类型统计健康记录
    type_stats = db.query(
        HealthRecord.record_type,
        func.count(HealthRecord.id).label('count')
    ).filter(
        and_(
            HealthRecord.user_id == user_id,
            HealthRecord.created_at >= start_date,
            HealthRecord.created_at <= end_date
        )
    ).group_by(HealthRecord.record_type).all()
    
    total_records = sum(stat.count for stat in type_stats)
    
    return {
        "total": total_records,
        "by_type": {stat.record_type: stat.count for stat in type_stats}
    }

def get_completion_trend(db: Session, user_id: int, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
    """
    获取完成趋势数据（按天统计）
    """
    # 问卷完成趋势
    questionnaire_trend = db.query(
        func.date(QuestionnaireResponse.created_at).label('date'),
        func.count(QuestionnaireResponse.id).label('count')
    ).filter(
        and_(
            QuestionnaireResponse.user_id == user_id,
            QuestionnaireResponse.created_at >= start_date,
            QuestionnaireResponse.created_at <= end_date
        )
    ).group_by(func.date(QuestionnaireResponse.created_at)).all()
    
    # 评估完成趋势
    assessment_trend = db.query(
        func.date(AssessmentResponse.created_at).label('date'),
        func.count(AssessmentResponse.id).label('count')
    ).filter(
        and_(
            AssessmentResponse.user_id == user_id,
            AssessmentResponse.created_at >= start_date,
            AssessmentResponse.created_at <= end_date
        )
    ).group_by(func.date(AssessmentResponse.created_at)).all()
    
    # 合并趋势数据
    trend_data = {}
    
    for item in questionnaire_trend:
        date_str = item.date.isoformat()
        if date_str not in trend_data:
            trend_data[date_str] = {'questionnaires': 0, 'assessments': 0}
        trend_data[date_str]['questionnaires'] = item.count
    
    for item in assessment_trend:
        date_str = item.date.isoformat()
        if date_str not in trend_data:
            trend_data[date_str] = {'questionnaires': 0, 'assessments': 0}
        trend_data[date_str]['assessments'] = item.count
    
    # 转换为列表格式
    result = []
    for date_str, counts in sorted(trend_data.items()):
        result.append({
            'date': date_str,
            'questionnaires': counts['questionnaires'],
            'assessments': counts['assessments'],
            'total': counts['questionnaires'] + counts['assessments']
        })
    
    return result

def calculate_completion_rate(completed: int, total: int) -> float:
    """
    计算完成率
    """
    if total == 0:
        return 0.0
    return round((completed / total) * 100, 2)