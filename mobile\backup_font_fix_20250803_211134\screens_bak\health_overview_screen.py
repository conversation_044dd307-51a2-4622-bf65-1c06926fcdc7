from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
import io
from datetime import datetime, timedelta
import random

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from kivy.uix.image import Image
from kivy.core.image import Image as CoreImage

# 导入matplotlib用于绘制图表
import matplotlib.pyplot as plt

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles

# 导入工具类
from utils.health_data_manager import get_health_data_manager

# 设置日志
logger = logging.getLogger(__name__)

# 定义KV语言字符串
KV = '''
<HealthTrendChart>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(250)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(16), dp(16), dp(16)]
    
    MDLabel:
        text: root.title
        font_style: "Body"
        role: "large"
        bold: True
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]

<HealthMetricCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: app.metrics.HEALTH_CARD_HEIGHT
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    
    MDBoxLayout:
        orientation: 'horizontal'
        
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_x: 0.7
            spacing: dp(4)
            
            MDLabel:
                text: root.title
                font_style: "Body"
                role: "medium"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_PRIMARY
                size_hint_y: None
                height: self.texture_size[1]
            
            MDLabel:
                text: root.description
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_SECONDARY
                size_hint_y: None
                height: self.texture_size[1]
        
        MDBoxLayout:
            orientation: 'vertical'
            size_hint_x: 0.3
            
            MDLabel:
                text: root.value
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: root.value_color if root.value_color else app.theme.PRIMARY_COLOR
                halign: "right"
                size_hint_y: None
                height: self.texture_size[1]
            
            MDLabel:
                text: root.unit
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_SECONDARY
                halign: "right"
                size_hint_y: None
                height: self.texture_size[1]

<HealthSummaryCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(16), dp(16), dp(16)]
    
    MDLabel:
        text: root.title
        font_style: "Body"
        role: "large"
        bold: True
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]
    
    MDDivider:
        height: dp(1)
        size_hint_y: None
        md_bg_color: app.theme.DIVIDER_COLOR
        padding: [0, dp(8), 0, dp(8)]
    
    MDLabel:
        text: root.content
        font_style: "Body"
        role: "medium"
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
        padding: [0, dp(8), 0, 0]

<HealthOverviewScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    MDBoxLayout:
        orientation: 'vertical'
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "健康状态总览"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.refresh_data()
        
        # 顶部logo
        HealthLogo:
            id: top_logo
            size_hint_y: None
            height: dp(60)
            pos_hint: {"center_x": 0.5}
        
        # 滚动内容区域
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            size_hint_y: 1
            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(16)
                
                # 健康状态总结卡片
                HealthSummaryCard:
                    id: health_summary
                    title: "健康状态总结"
                    content: "根据您的健康数据，您的整体健康状况良好。建议保持规律作息，均衡饮食，适量运动。"
                
                # 重要指标标题
                MDLabel:
                    text: "重要健康指标"
                    font_style: "Body"
                    role: "large"
                    bold: True
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK
                    size_hint_y: None
                    height: self.texture_size[1]
                
                # 健康指标卡片容器
                MDBoxLayout:
                    id: metrics_container
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(12)
                
                # 健康趋势标题
                MDLabel:
                    text: "健康趋势"
                    font_style: "Body"
                    role: "large"
                    bold: True
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK
                    size_hint_y: None
                    height: self.texture_size[1]
                    padding: [0, dp(16), 0, 0]
                
                # 健康趋势图表容器
                MDBoxLayout:
                    id: trends_container
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                
                # 健康建议标题
                MDLabel:
                    text: "健康建议"
                    font_style: "Body"
                    role: "large"
                    bold: True
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK
                    size_hint_y: None
                    height: self.texture_size[1]
                    padding: [0, dp(16), 0, 0]
                
                # 健康建议卡片
                HealthSummaryCard:
                    id: health_advice
                    title: "个性化健康建议"
                    content: "1. 保持规律作息，每天保证7-8小时睡眠\\n2. 均衡饮食，增加蔬果摄入\\n3. 每周进行至少150分钟中等强度有氧运动\\n4. 定期体检，关注血压、血糖变化"
'''

# 只加载一次KV，确保ids绑定唯一
from kivy.lang import Builder
Builder.load_string(KV)

class HealthTrendChart(MDCard):
    """健康趋势图表卡片"""
    title = StringProperty("健康趋势")
    chart_type = StringProperty("blood_pressure")  # 图表类型：blood_pressure, blood_sugar, weight, heart_rate, blood_oxygen
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.chart_image = Image(size_hint_y=None, height=dp(180))
        self.add_widget(self.chart_image)
    
    def update_chart(self, data):
        """更新图表
        
        Args:
            data (dict): 图表数据，格式为 {"dates": [...], "values": [...], "title": "...", "unit": "..."}
        """
        # 生成图表
        buf = self._generate_chart(data)
        # 更新图像
        self.chart_image.texture = CoreImage(buf, ext='png').texture
    
    def _generate_chart(self, data):
        """生成图表
        
        Args:
            data (dict): 图表数据
            
        Returns:
            BytesIO: 图表图像数据
        """
        plt.figure(figsize=(5, 3))
        plt.clf()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        
        # 绘制图表
        if self.chart_type == "blood_pressure":
            # 血压有两条线：收缩压和舒张压
            plt.plot(data["dates"], data["systolic"], 'r-', label="收缩压")
            plt.plot(data["dates"], data["diastolic"], 'b-', label="舒张压")
            plt.legend()
        else:
            # 其他指标只有一条线
            plt.plot(data["dates"], data["values"], 'g-')
        
        # 设置标题和标签
        plt.title(data["title"])
        plt.ylabel(data["unit"])
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存图表到内存
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=100)
        buf.seek(0)
        return buf


class HealthMetricCard(MDCard):
    """健康指标卡片"""
    title = StringProperty("指标名称")
    description = StringProperty("指标描述")
    value = StringProperty("0")
    unit = StringProperty("")
    value_color = ListProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class HealthSummaryCard(MDCard):
    """健康摘要卡片"""
    title = StringProperty("摘要标题")
    content = StringProperty("摘要内容")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class HealthOverviewScreen(BaseScreen):
    """健康状态总览屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        
        # 初始化健康数据管理器
        self.health_data_manager = get_health_data_manager()
        
        # 初始化图表
        self.trend_charts = {}
        
        # 加载健康数据
        Clock.schedule_once(self.load_health_data, 0.5)
    
    def load_health_data(self, *args):
        """加载健康数据"""
        try:
            # 获取用户信息
            user_data = getattr(self.app, 'user_data', {})
            user_id = user_data.get('user_id', None)
            gender = user_data.get('gender', '男')
            age = user_data.get('age', 30)
            
            # 清空指标网格
            if hasattr(self, 'ids') and 'metrics_container' in self.ids:
                self.ids.metrics_container.clear_widgets()
            
            # 添加示例健康指标
            # 血压
            blood_pressure_card = HealthMetricCard(
                title="血压",
                value="120/80",
                unit="mmHg",
                description="最近测量: 昨天",
                value_color=self.app.theme.SUCCESS_COLOR
            )
            self.ids.metrics_container.add_widget(blood_pressure_card)
            
            # 血糖
            blood_sugar_card = HealthMetricCard(
                title="血糖",
                value="5.4",
                unit="mmol/L",
                description="最近测量: 今天",
                value_color=self.app.theme.SUCCESS_COLOR
            )
            self.ids.metrics_container.add_widget(blood_sugar_card)
            
            # 体重
            weight_card = HealthMetricCard(
                title="体重",
                value="68",
                unit="kg",
                description="最近测量: 3天前",
                value_color=self.app.theme.INFO_COLOR
            )
            self.ids.metrics_container.add_widget(weight_card)
            
            # 心率
            heart_rate_card = HealthMetricCard(
                title="心率",
                value="72",
                unit="bpm",
                description="最近测量: 今天",
                value_color=self.app.theme.SUCCESS_COLOR
            )
            self.ids.metrics_container.add_widget(heart_rate_card)
            
            # 血氧
            blood_oxygen_card = HealthMetricCard(
                title="血氧",
                value="98",
                unit="%",
                description="最近测量: 今天",
                value_color=self.app.theme.SUCCESS_COLOR
            )
            self.ids.metrics_container.add_widget(blood_oxygen_card)
            
            # 加载健康趋势图表
            self.load_health_trends()
            
            # 更新健康状态总结
            self.update_health_summary()
            
        except Exception as e:
            logger.error(f"加载健康数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    def load_health_trends(self):
        """加载健康趋势图表"""
        try:
            # 清空趋势容器
            if hasattr(self, 'ids') and 'trends_container' in self.ids:
                self.ids.trends_container.clear_widgets()
            
            # 获取用户信息
            user_data = getattr(self.app, 'user_data', {})
            user_id = user_data.get('user_id', None)
            
            # 生成血压趋势图表
            blood_pressure_chart = HealthTrendChart(
                title="血压趋势 (mmHg)",
                chart_type="blood_pressure"
            )
            self.ids.trends_container.add_widget(blood_pressure_chart)
            
            # 生成血压趋势数据
            bp_data = self._generate_blood_pressure_trend_data()
            blood_pressure_chart.update_chart(bp_data)
            
            # 生成血糖趋势图表
            blood_sugar_chart = HealthTrendChart(
                title="血糖趋势 (mmol/L)",
                chart_type="blood_sugar"
            )
            self.ids.trends_container.add_widget(blood_sugar_chart)
            
            # 生成血糖趋势数据
            bs_data = self._generate_trend_data("血糖", "mmol/L", 5.0, 6.0)
            blood_sugar_chart.update_chart(bs_data)
            
            # 生成体重趋势图表
            weight_chart = HealthTrendChart(
                title="体重趋势 (kg)",
                chart_type="weight"
            )
            self.ids.trends_container.add_widget(weight_chart)
            
            # 生成体重趋势数据
            weight_data = self._generate_trend_data("体重", "kg", 65, 70)
            weight_chart.update_chart(weight_data)
            
        except Exception as e:
            logger.error(f"加载健康趋势图表失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _generate_blood_pressure_trend_data(self):
        """生成血压趋势数据
        
        Returns:
            dict: 血压趋势数据
        """
        # 生成日期列表（最近7天）
        dates = [(datetime.now() - timedelta(days=i)).strftime("%m-%d") for i in range(6, -1, -1)]
        
        # 生成收缩压数据（模拟）
        systolic = [random.randint(115, 125) for _ in range(7)]
        
        # 生成舒张压数据（模拟）
        diastolic = [random.randint(75, 85) for _ in range(7)]
        
        return {
            "dates": dates,
            "systolic": systolic,
            "diastolic": diastolic,
            "title": "血压趋势",
            "unit": "mmHg"
        }
        
    def _generate_trend_data(self, title, unit, min_value, max_value):
        """生成趋势数据
        
        Args:
            title (str): 图表标题
            unit (str): 单位
            min_value (float): 最小值
            max_value (float): 最大值
            
        Returns:
            dict: 趋势数据
        """
        # 生成日期列表（最近7天）
        dates = [(datetime.now() - timedelta(days=i)).strftime("%m-%d") for i in range(6, -1, -1)]
        
        # 生成数据（模拟）
        values = [round(random.uniform(min_value, max_value), 1) for _ in range(7)]
        
        return {
            "dates": dates,
            "values": values,
            "title": title,
            "unit": unit
        }
    
    def update_health_summary(self):
        """更新健康状态总结"""
        try:
            # 获取用户信息
            app = MDApp.get_running_app()
            user_data = app.user_data if hasattr(app, 'user_data') else {}
            
            # 生成个性化健康总结
            # 实际应用中，这应该基于用户的健康数据和AI分析生成
            summary = "根据您的健康数据，您的整体健康状况良好。血压、血糖指标在正常范围内，心率和血氧饱和度也保持稳定。"
            
            # 根据性别添加不同的建议
            gender = user_data.get('gender', '').lower()
            if gender in ['female', 'f', '女', '女性']:
                summary += " 作为女性，建议您关注钙质摄入和骨密度变化，定期进行乳腺检查。"
            else:
                summary += " 作为男性，建议您关注前列腺健康，控制血压和血脂。"
            
            # 更新健康总结卡片
            self.ids.health_summary.content = summary
            
            # 更新健康建议
            advice = "1. 保持规律作息，每天保证7-8小时睡眠\n"
            advice += "2. 均衡饮食，增加蔬果摄入\n"
            advice += "3. 每周进行至少150分钟中等强度有氧运动\n"
            advice += "4. 定期体检，关注血压、血糖变化"
            
            # 根据年龄添加不同的建议
            age = user_data.get('age', 0)
            if age > 40:
                advice += "\n5. 40岁以上人群应每年进行一次全面体检"
            
            # 更新健康建议卡片
            self.ids.health_advice.content = advice
            
        except Exception as e:
            logger.error(f"更新健康状态总结失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    def refresh_data(self):
        """刷新健康数据"""
        # 显示加载提示
        self.show_loading_dialog("正在刷新健康数据...")
        
        # 模拟数据加载延迟
        Clock.schedule_once(self.finish_refresh, 1.5)
    
    def finish_refresh(self, dt):
        """完成刷新"""
        # 关闭加载对话框
        self.dismiss_loading_dialog()
        
        # 重新加载健康数据
        self.load_health_data()
        
        # 显示刷新完成提示
        self.show_info("健康数据已更新")
    
    def go_back(self):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'homepage_screen'
    
    def show_loading_dialog(self, text="加载中..."):
        """显示加载对话框"""
        from kivymd.uix.dialog import MDDialog, MDDialogSupportingText
        
        # 创建加载对话框
        self.loading_dialog = MDDialog(
            MDDialogSupportingText(
                text=text,
                halign="center",
            ),
            radius=[20, 7, 20, 7]
        )
        self.loading_dialog.open()
    
    def dismiss_loading_dialog(self):
        """关闭加载对话框"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.dismiss()
    
    def show_info(self, message):
        """显示信息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()

# 用于测试的应用程序
if __name__ == '__main__':
    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles
        
        def build(self):
            self.user_data = {"username": "测试用户", "gender": "男", "age": 45}
            return HealthOverviewScreen()