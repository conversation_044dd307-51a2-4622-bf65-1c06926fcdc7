"""CloudAPI辅助函数模块

包含用于降低主类认知复杂度的辅助函数
"""

import logging
import requests
from .cloud_api_constants import *

logger = logging.getLogger(__name__)


class RequestHelper:
    """请求处理辅助类"""
    
    @staticmethod
    def prepare_headers(token=None, custom_id=None, bypass_auth=False, additional_headers=None):
        """准备请求头
        
        Args:
            token: 认证令牌
            custom_id: 用户自定义ID
            bypass_auth: 是否绕过认证
            additional_headers: 额外的请求头
            
        Returns:
            dict: 准备好的请求头
        """
        headers = additional_headers.copy() if additional_headers else {}
        
        if not bypass_auth and token:
            headers[HEADER_AUTHORIZATION] = f"{AUTHORIZATION_BEARER} {token}"
            logger.debug(f"添加Bearer认证头: {token[:10]}...")
        
        if not bypass_auth and custom_id:
            headers[HEADER_X_USER_ID] = custom_id
            logger.info(f"添加X-User-ID头: {custom_id}")
            
        return headers
    
    @staticmethod
    def build_url(base_url, endpoint):
        """构建完整的请求URL
        
        Args:
            base_url: 基础URL
            endpoint: API端点
            
        Returns:
            str: 完整的URL
        """
        current_url = base_url
        if not current_url.endswith('/api'):
            current_url = current_url + '/api' if not current_url.endswith('/') else current_url + 'api'
        
        endpoint = endpoint.lstrip('/')
        return f"{current_url}/{endpoint}"
    
    @staticmethod
    def prepare_request_kwargs(method, url, params=None, data=None, json_data=None, 
                             headers=None, files=None, timeout=None):
        """准备请求参数
        
        Args:
            method: HTTP方法
            url: 请求URL
            params: URL参数
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            files: 文件数据
            timeout: 超时时间
            
        Returns:
            dict: 请求参数字典
        """
        request_kwargs = {
            'method': method,
            'url': url,
            'params': params,
            'data': data,
            'json': json_data,
            'headers': headers,
            'files': files,
            'proxies': {
                'http': None,
                'https': None,
                'ftp': None,
                'socks4': None,
                'socks5': None
            }
        }
        
        # 只有在非文件上传请求时才设置timeout
        if files is None and timeout is not None:
            request_kwargs['timeout'] = timeout
        elif files is not None:
            logger.info("文件上传请求，不设置超时")
            
        return request_kwargs


class ResponseHandler:
    """响应处理辅助类"""
    
    @staticmethod
    def handle_success_response(response):
        """处理成功响应
        
        Args:
            response: requests响应对象
            
        Returns:
            dict: 解析后的响应数据
        """
        try:
            return response.json()
        except ValueError:
            return {STATUS_SUCCESS: True, "message": response.text}
    
    @staticmethod
    def handle_server_error(response):
        """处理服务器错误
        
        Args:
            response: requests响应对象
            
        Returns:
            dict: 错误响应数据
        """
        error_msg = f"服务器返回错误状态码: {response.status_code}"
        logger.error(error_msg)
        
        try:
            return response.json()
        except ValueError:
            return {STATUS_ERROR: True, "message": error_msg}
    
    @staticmethod
    def handle_client_error(response):
        """处理客户端错误
        
        Args:
            response: requests响应对象
            
        Returns:
            dict: 错误响应数据
        """
        error_msg = f"客户端错误，状态码: {response.status_code}"
        logger.error(error_msg)
        
        # 特殊处理422验证错误
        if response.status_code == 422:
            return ResponseHandler._handle_validation_error(response)
        
        try:
            return response.json()
        except ValueError:
            return {STATUS_ERROR: True, "message": error_msg, "detail": response.text}
    
    @staticmethod
    def _handle_validation_error(response):
        """处理422验证错误
        
        Args:
            response: requests响应对象
            
        Returns:
            dict: 验证错误响应数据
        """
        try:
            error_response = response.json()
            validation_errors = error_response.get('detail', [])
            if validation_errors:
                logger.error(f"验证失败(422): {validation_errors}")
            else:
                logger.error(f"验证失败(422): {error_response}")
            return error_response
        except ValueError:
            logger.error("验证失败(422): 无法解析错误响应")
            return {
                STATUS_ERROR: True, 
                "message": MSG_VALIDATION_FAILED, 
                "detail": response.text
            }
    
    @staticmethod
    def handle_request_exception(exception):
        """处理请求异常
        
        Args:
            exception: 请求异常对象
            
        Returns:
            dict: 异常响应数据
        """
        error_msg = f"请求异常: {str(exception)}"
        logger.error(error_msg)
        
        # 检查是否是代理相关错误
        if any(keyword in str(exception).lower() for keyword in PROXY_KEYWORDS):
            logger.warning("检测到代理相关错误，尝试修复代理设置")
            try:
                from .proxy_config import fix_proxy_issues
                if fix_proxy_issues():
                    logger.info("代理问题已修复，将在下次重试时生效")
            except Exception as proxy_fix_error:
                logger.error(f"修复代理设置失败: {proxy_fix_error}")
        
        return {STATUS_ERROR: True, "message": error_msg}


class ServerManager:
    """服务器管理辅助类"""
    
    @staticmethod
    def should_switch_server(server_configs, current_index):
        """判断是否应该切换服务器
        
        Args:
            server_configs: 服务器配置列表
            current_index: 当前服务器索引
            
        Returns:
            bool: 是否应该切换服务器
        """
        current_server = server_configs[current_index]
        return current_server["failure_count"] >= MAX_FAILURE_COUNT
    
    @staticmethod
    def find_next_available_server(server_configs, current_index):
        """查找下一个可用的服务器
        
        Args:
            server_configs: 服务器配置列表
            current_index: 当前服务器索引
            
        Returns:
            int: 下一个可用服务器的索引，如果没有则返回-1
        """
        for i in range(len(server_configs)):
            if i != current_index and server_configs[i]["available"]:
                return i
        return -1
    
    @staticmethod
    def reset_server_failure_count(server_configs, index):
        """重置服务器失败计数
        
        Args:
            server_configs: 服务器配置列表
            index: 服务器索引
        """
        if 0 <= index < len(server_configs):
            server_configs[index]["failure_count"] = 0
    
    @staticmethod
    def increment_server_failure_count(server_configs, index):
        """增加服务器失败计数
        
        Args:
            server_configs: 服务器配置列表
            index: 服务器索引
        """
        if 0 <= index < len(server_configs):
            server_configs[index]["failure_count"] += 1


class DataProcessor:
    """数据处理辅助类"""
    
    @staticmethod
    def extract_documents_from_response(result, document_type=None):
        """从响应中提取文档数据
        
        Args:
            result: API响应数据
            document_type: 文档类型过滤
            
        Returns:
            dict: 包含文档列表的标准格式数据
        """
        if not result or not (result.get("success") or result.get("status") == STATUS_SUCCESS):
            return None
        
        data = result.get("data", {})
        if not data and isinstance(result, dict):
            data = result
        
        # 处理不同的响应格式
        documents = []
        if "records" in data:
            documents = data["records"]
        elif "documents" in data:
            documents = data["documents"]
        elif "items" in data:
            documents = data["items"]
        elif isinstance(data, list):
            documents = data
        
        # 过滤指定类型的文档
        if document_type:
            documents = [
                doc for doc in documents 
                if (doc.get("type") or doc.get("document_type")) == document_type
            ]
        
        return {
            "documents": documents,
            "total": data.get("total", len(documents)),
            "page_total": data.get("page_total", len(documents))
        }
    
    @staticmethod
    def standardize_response_format(result):
        """标准化响应格式
        
        Args:
            result: 原始响应数据
            
        Returns:
            dict: 标准化后的响应数据
        """
        if isinstance(result, dict):
            if result.get('status') == STATUS_SUCCESS or result.get('success'):
                return result
            elif result.get('status') == STATUS_ERROR:
                return result
            else:
                # 没有明确状态的字典响应，假设为成功
                logger.info(MSG_DICT_RESPONSE_SUCCESS)
                return {STATUS_SUCCESS: True, "data": result}
        elif isinstance(result, list):
            # 直接返回列表，包装为标准格式
            return {STATUS_SUCCESS: True, "data": result}
        else:
            error_msg = f"未知响应格式: {type(result)}"
            logger.error(error_msg)
            return {STATUS_ERROR: True, "message": error_msg}