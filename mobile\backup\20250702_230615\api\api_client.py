# mobile/api/api_client.py
import os
import logging
import sys
import time
import requests
from utils.cloud_api import CloudAPI, get_cloud_api
from datetime import datetime
from utils.user_manager import get_user_manager

# 配置日志 - 使用简单的日志配置
logger = logging.getLogger(__name__)
# 避免在模块级别调用任何可能导致递归的日志方法

class APIClient:
    """API客户端类，用于处理与服务器的通信"""

    def __init__(self):
        """初始化API客户端"""
        # 使用统一的API配置
        from utils.app_config import API_CONFIG
        self.base_url = API_CONFIG['BASE_URL']  # 使用配置文件中的主地址
        self.backup_url = "http://localhost:8006/api"     # 本地服务器备用地址
        self.token = None
        # 初始化requests.Session，兼容所有API调用
        self.session = requests.Session()
        # 使用单例模式获取云端API客户端，确保全局只有一个实例
        from utils.cloud_api import get_cloud_api
        # 传递主地址和备用地址给CloudAPI，并设置自动切换
        self.cloud_api = get_cloud_api(base_url=self.base_url, backup_url=self.backup_url, auto_switch=True)

        # 检查上传队列大小，但不立即处理
        try:
            # 检查队列大小
            queue_size = self.cloud_api.get_upload_queue_size()
            if queue_size > 0:
                # 首先检查用户是否已登录
                from utils.user_manager import get_user_manager
                user_manager = get_user_manager()
                current_user = user_manager.get_current_user()

                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    # 用户已登录，可以处理上传队列
                    logger.info(f"发现 {queue_size} 个文件在上传队列中，用户已登录，尝试后台处理")
                    # 使用单独线程处理队列，避免阻塞UI
                    import threading
                    threading.Thread(
                        target=self._process_upload_queue_background,
                        args=(3,),  # 最多处理3个文件
                        daemon=True
                    ).start()
                else:
                    # 用户未登录，记录队列大小但不处理
                    logger.info(f"发现 {queue_size} 个文件在上传队列中，但用户未登录，等待用户登录后再处理")
            else:
                logger.info("上传队列为空，无需处理")
        except Exception as e:
            logger.error(f"初始化时检查上传队列时出错: {str(e)}")

        # 启动服务器健康检查
        self._start_health_check()

    def _inject_user_identity(self, params=None):
        """自动注入当前登录用户custom_id和token"""
        user = get_user_manager().get_current_user()
        if not params:
            params = {}
        if user:
            params['custom_id'] = getattr(user, 'custom_id', None)
            params['token'] = getattr(user, 'token', None)
        return params

    def post(self, url, data=None, **kwargs):
        """
        封装POST请求，禁止直接传入以/开头的url，必须传完整URL或仅用于特殊用途。
        推荐统一通过cloud_api的高层API方法获取量表/问卷等数据。
        """
        if url.startswith('/') and not url.startswith('http'):
            raise ValueError('禁止直接传入以/开头的url，请使用cloud_api的高层API方法')
        data = self._inject_user_identity(data)
        # 兼容headers参数
        headers = kwargs.pop('headers', None)
        if headers:
            return self.session.post(url, json=data, headers=headers, **kwargs)
        return self.session.post(url, json=data, **kwargs)

    def get(self, url, params=None, **kwargs):
        """
        封装GET请求，禁止直接传入以/开头的url，必须传完整URL或仅用于特殊用途。
        推荐统一通过cloud_api的高层API方法获取量表/问卷等数据。
        """
        if url.startswith('/') and not url.startswith('http'):
            raise ValueError('禁止直接传入以/开头的url，请使用cloud_api的高层API方法')
        params = self._inject_user_identity(params)
        # 兼容headers参数
        headers = kwargs.pop('headers', None)
        if headers:
            return self.session.get(url, params=params, headers=headers, **kwargs)
        return self.session.get(url, params=params, **kwargs)

    def _start_health_check(self):
        """启动服务器健康检查"""
        def check_server():
            while True:
                try:
                    # 每15分钟检查一次服务器状态
                    import time
                    time.sleep(900)

                    # 检查服务器状态
                    is_healthy = self.check_server_health()
                    if not is_healthy:
                        logger.warning("服务器健康检查失败，可能出现连接问题")
                except Exception as e:
                    logger.error(f"健康检查过程中出错: {str(e)}")

        # 在后台线程中运行
        import threading
        threading.Thread(target=check_server, daemon=True).start()
        logger.debug("已启动服务器健康检查")

        # 启动OCR队列处理
        self._start_ocr_queue_processor()

    def _start_ocr_queue_processor(self):
        """启动OCR队列处理器"""
        def process_ocr_queue():
            while True:
                try:
                    # 每5分钟处理一次OCR队列
                    import time
                    time.sleep(300)

                    # 检查认证状态
                    if not self.cloud_api.is_authenticated():
                        logger.debug("未认证，暂不处理OCR队列")
                        continue

                    # 处理OCR队列
                    self._process_ocr_queue()

                except Exception as e:
                    logger.error(f"处理OCR队列时出错: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # 出错后等待较长时间再重试
                    time.sleep(600)

        # 在后台线程中运行
        import threading
        threading.Thread(target=process_ocr_queue, daemon=True).start()
        logger.debug("已启动OCR队列处理器")

    def _process_ocr_queue(self, max_items=3):
        """处理OCR队列

        Args:
            max_items (int): 最大处理数量

        Returns:
            tuple: (成功数, 失败数)
        """
        # 获取OCR队列目录
        ocr_queue_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'ocr_queue')

        # 确保目录存在
        if not os.path.exists(ocr_queue_dir):
            os.makedirs(ocr_queue_dir, exist_ok=True)
            return (0, 0)

        # 获取队列文件
        import glob
        queue_files = glob.glob(os.path.join(ocr_queue_dir, "*.json"))

        if not queue_files:
            return (0, 0)

        # 按创建时间排序
        queue_files.sort(key=lambda x: os.path.getmtime(x))

        # 限制处理数量
        queue_files = queue_files[:max_items]

        success_count = 0
        fail_count = 0

        import json
        current_time = int(time.time())

        for file_path in queue_files:
            try:
                # 读取队列项
                with open(file_path, 'r', encoding='utf-8') as f:
                    queue_item = json.load(f)

                file_id = queue_item.get('file_id')
                options = queue_item.get('options', {})
                retries = queue_item.get('retries', 0)
                next_retry = queue_item.get('next_retry', 0)

                # 检查是否到达重试时间
                if next_retry > current_time:
                    logger.debug(f"OCR队列项 {file_id} 还未到达重试时间，跳过")
                    continue

                # 检查重试次数
                if retries >= 5:
                    logger.warning(f"OCR队列项 {file_id} 已达到最大重试次数，放弃处理")
                    os.remove(file_path)
                    fail_count += 1
                    continue

                # 请求OCR处理
                logger.info(f"从队列中处理OCR请求: {file_id}")
                ocr_result = self.cloud_api.request_ocr(file_id, options=options)

                if ocr_result and (ocr_result.get('success') or ocr_result.get('task_id')):
                    # 请求成功
                    task_id = ocr_result.get('task_id', '')
                    logger.info(f"OCR请求成功，任务ID: {task_id}")

                    # 更新本地数据库中的OCR状态
                    from utils.health_data_manager import get_health_data_manager
                    health_data_manager = get_health_data_manager()
                    health_data_manager.update_document_ocr_status(
                        file_id,
                        'processing',
                        task_id
                    )

                    # 启动异步OCR结果检查
                    import threading
                    threading.Thread(
                        target=self._check_and_process_ocr_result,
                        args=(file_id, task_id),
                        daemon=True
                    ).start()

                    # 删除队列项
                    os.remove(file_path)
                    success_count += 1
                else:
                    # 请求失败，更新重试信息
                    queue_item['retries'] = retries + 1

                    # 使用指数退避策略
                    backoff_time = min(2 ** (retries + 1), 60) * 60  # 最大60分钟
                    queue_item['next_retry'] = current_time + backoff_time

                    # 保存回队列
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(queue_item, f, ensure_ascii=False)

                    logger.warning(f"OCR请求失败，将在 {backoff_time/60:.1f} 分钟后重试: {file_id}")
                    fail_count += 1

            except Exception as e:
                logger.error(f"处理OCR队列项时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                fail_count += 1

        if success_count > 0 or fail_count > 0:
            logger.info(f"OCR队列处理完成: {success_count} 个成功, {fail_count} 个失败")

        return (success_count, fail_count)

    def check_server_health(self):
        """检查服务器健康状态"""
        if self.cloud_api:
            return self.cloud_api.check_server_health()
        return False

    def upload_document(self, file_path):
        """上传文档到服务器

        Args:
            file_path: 要上传的文件路径

        Returns:
            dict: 上传结果
        """
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": "文件不存在"}

            # 使用cloud_api上传文件
            return self.cloud_api.upload_file(file_path)
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_documents(self, custom_id=None, page=1, page_size=20):
        """
        获取用户文档列表

        Args:
            custom_id: 用户自定义ID (可选)
            page: 页码
            page_size: 每页数量
        Returns:
            dict: 文档列表
        """
        try:
            params = {
                'page': page,
                'page_size': page_size
            }
            if custom_id:
                params['custom_id'] = custom_id
            # 使用cloud_api获取文档列表
            result = self.cloud_api.get_documents(**params)
            return result
        except Exception as e:
            print(f"获取文档列表失败: {str(e)}")
            return None

    def _process_upload_queue_background(self, max_items=5):
        """在后台线程中处理上传队列

        Args:
            max_items: 最大处理数量
        """
        try:
            # 等待3秒，确保应用已完全初始化
            import time
            time.sleep(3)

            # 首先检查用户是否已登录
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            # 如果用户未登录，不处理上传队列
            if not current_user:
                logger.info("用户未登录，暂不处理上传队列")
                return

            # 尝试加载认证信息
            self.cloud_api.load_auth_info()

            # 检查是否是本地服务器模式
            is_local_server = "localhost" in self.cloud_api.base_url or "127.0.0.1" in self.cloud_api.base_url

            # 如果已认证，立即处理队列
            if self.cloud_api.is_authenticated():
                logger.info("开始处理上传队列...")
                success, fail = self.cloud_api.process_upload_queue(max_items)
                logger.info(f"上传队列处理完成: {success} 个成功, {fail} 个失败")
            else:
                # 检查是否有custom_id
                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    # 设置custom_id到cloud_api
                    self.cloud_api.custom_id = current_user.custom_id
                    logger.info(f"使用用户custom_id: {self.cloud_api.custom_id}处理上传队列")
                    success, fail = self.cloud_api.process_upload_queue(max_items)
                    logger.info(f"使用custom_id上传队列处理完成: {success} 个成功, {fail} 个失败")
                else:
                    if is_local_server:
                        logger.info("本地服务器模式下未认证，暂不处理上传队列，请先登录")
                    else:
                        logger.info("未认证，暂时不处理上传队列")

                    # 启动定时任务，每隔一段时间检查认证状态并尝试处理队列
                    import threading
                    def delayed_queue_processor():
                        # 等待30秒后再次尝试
                        time.sleep(30)
                        try:
                            # 再次检查用户是否已登录
                            current_user = user_manager.get_current_user()
                            if not current_user:
                                logger.info("用户仍未登录，暂不处理上传队列")
                                return

                            # 再次尝试加载认证信息
                            self.cloud_api.load_auth_info()

                            # 检查是否是本地服务器模式
                            is_local_server = "localhost" in self.cloud_api.base_url or "127.0.0.1" in self.cloud_api.base_url

                            if self.cloud_api.is_authenticated():
                                logger.info("延迟处理上传队列...")
                                success, fail = self.cloud_api.process_upload_queue(max_items)
                                logger.info(f"延迟上传队列处理完成: {success} 个成功, {fail} 个失败")
                            else:
                                # 检查是否有custom_id
                                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                                    # 设置custom_id到cloud_api
                                    self.cloud_api.custom_id = current_user.custom_id
                                    logger.info(f"使用用户custom_id: {self.cloud_api.custom_id}延迟处理上传队列")
                                    success, fail = self.cloud_api.process_upload_queue(max_items)
                                    logger.info(f"使用custom_id延迟上传队列处理完成: {success} 个成功, {fail} 个失败")
                                else:
                                    if is_local_server:
                                        logger.info("本地服务器模式下仍未认证，暂不处理上传队列，请先登录")
                                    else:
                                        logger.info("仍未认证，无法处理上传队列")
                        except Exception as e:
                            logger.error(f"延迟处理上传队列时出错: {str(e)}")

                    # 启动延迟处理线程
                    threading.Thread(target=delayed_queue_processor, daemon=True).start()
        except Exception as e:
            logger.error(f"处理上传队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    # 推荐：统一通过cloud_api获取量表/问卷数据
    def get_assessments(self, custom_id=None, status=None):
        """
        获取分发的评估量表，推荐所有页面统一调用此方法
        
        Args:
            custom_id: 用户自定义ID
            status: 状态过滤，可选值：pending/completed
        """
        return self.cloud_api.get_mobile_assessments(custom_id=custom_id, status=status)

    def get_questionnaires(self, custom_id=None):
        """获取问卷列表"""
        return self.cloud_api.get_questionnaires(custom_id)

    def start_voice_triage(self):
        """启动语音分诊会话
        
        Returns:
            dict: 包含session_id和success字段的字典
        """
        try:
            # 生成唯一会话ID
            import uuid
            session_id = str(uuid.uuid4())
            
            # 初始化会话
            from utils.triage_manager import get_triage_manager
            triage_manager = get_triage_manager()
            triage_manager.create_session(session_id)
            
            # 返回成功结果
            return {
                'success': True,
                'session_id': session_id
            }
        except Exception as e:
            logger.error(f"启动语音分诊会话失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': f"启动语音分诊会话失败: {str(e)}"
            }
    
    def send_voice_message(self, session_id, audio_file_path):
        """发送语音消息
        
        Args:
            session_id (str): 会话ID
            audio_file_path (str): 音频文件路径
            
        Returns:
            dict: 包含response和success字段的字典
        """
        try:
            # 保存音频消息到本地
            from utils.triage_manager import get_triage_manager
            triage_manager = get_triage_manager()
            
            # 读取音频文件
            with open(audio_file_path, 'rb') as f:
                audio_data = f.read()
            
            # 保存音频消息
            message_id = triage_manager.save_audio_message(session_id, audio_data)
            
            # 模拟AI回复
            import time
            time.sleep(1)  # 模拟处理时间
            
            # 保存文本回复
            response_text = "我已收到您的语音消息。请继续描述您的症状，或者您可以询问我具体的健康问题。"
            triage_manager.save_text_message(session_id, response_text, direction="incoming")
            
            # 返回成功结果
            return {
                'success': True,
                'response': {
                    'type': 'text',
                    'content': {
                        'text': response_text
                    }
                }
            }
        except Exception as e:
            logger.error(f"发送语音消息失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': f"发送语音消息失败: {str(e)}"
            }
    
    def send_text_message(self, session_id, text):
        """发送文本消息
        
        Args:
            session_id (str): 会话ID
            text (str): 文本内容
            
        Returns:
            dict: 包含response和success字段的字典
        """
        try:
            # 保存文本消息到本地
            from utils.triage_manager import get_triage_manager
            triage_manager = get_triage_manager()
            triage_manager.save_text_message(session_id, text)
            
            # 模拟AI回复
            import time
            time.sleep(0.5)  # 模拟处理时间
            
            # 根据输入内容生成简单回复
            response_text = self._generate_triage_response(text)
            
            # 保存回复
            triage_manager.save_text_message(session_id, response_text, direction="incoming")
            
            # 返回成功结果
            return {
                'success': True,
                'response': {
                    'type': 'text',
                    'content': {
                        'text': response_text
                    }
                }
            }
        except Exception as e:
            logger.error(f"发送文本消息失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': f"发送文本消息失败: {str(e)}"
            }
    
    def _generate_triage_response(self, text):
        """根据用户输入生成简单的分诊回复
        
        Args:
            text (str): 用户输入文本
            
        Returns:
            str: 回复文本
        """
        text = text.lower()
        
        if '头痛' in text:
            return "您提到了头痛症状。请问是持续性头痛还是间歇性头痛？疼痛部位在哪里？是否伴有其他症状如恶心、呕吐或视力模糊？"
        elif '发热' in text or '发烧' in text:
            return "您提到了发热症状。请问体温大约是多少？是否伴有其他症状如咳嗽、喉咙痛或全身酸痛？"
        elif '咳嗽' in text:
            return "您提到了咳嗽症状。请问是干咳还是有痰？持续多久了？是否伴有发热、胸痛或呼吸困难？"
        elif '腹痛' in text or '肚子痛' in text:
            return "您提到了腹痛症状。请问疼痛位置在哪里？是持续性还是间歇性疼痛？是否伴有恶心、呕吐、腹泻或便秘？"
        elif '胸痛' in text:
            return "您提到了胸痛症状，这可能需要及时医疗关注。请问疼痛是否向左臂、下巴或背部放射？是否伴有呼吸困难、出汗或恶心？如果症状严重，请立即就医。"
        elif '呼吸困难' in text or '喘不上气' in text:
            return "您提到了呼吸困难，这可能是严重问题的征兆。请问是突然发生的还是逐渐加重？是否伴有胸痛、咳嗽或发热？如果症状严重，请立即就医。"
        elif '过敏' in text:
            return "您提到了过敏症状。请问具体表现是什么？皮疹、瘙痒、呼吸困难还是其他症状？是否知道过敏原？"
        elif '疲劳' in text or '乏力' in text or '没精神' in text:
            return "您提到了疲劳或乏力症状。请问这种情况持续多久了？是否伴有其他症状如发热、体重减轻或睡眠问题？"
        elif '失眠' in text or '睡不着' in text:
            return "您提到了睡眠问题。请问是难以入睡、易醒还是早醒？持续多久了？是否有压力、焦虑或其他可能影响睡眠的因素？"
        else:
            return "感谢您的描述。为了更好地了解您的情况，请详细描述您的症状，包括症状开始的时间、位置、严重程度以及是否有任何缓解或加重因素。"
    
    def get_triage_summary(self, session_id):
        """获取分诊会话总结
        
        Args:
            session_id (str): 会话ID
            
        Returns:
            dict: 包含summary和success字段的字典
        """
        try:
            # 获取会话消息
            from utils.triage_manager import get_triage_manager
            triage_manager = get_triage_manager()
            messages = triage_manager.get_session_messages(session_id)
            
            # 生成简单总结
            summary = {
                'main_complaint': '根据您的描述，主要症状包括不适感和身体不适',
                'possible_conditions': [
                    {'name': '普通感冒', 'probability': '中等'},
                    {'name': '季节性过敏', 'probability': '低'},
                    {'name': '轻度焦虑', 'probability': '低'}
                ],
                'recommendations': [
                    '建议多休息，保持充分水分摄入',
                    '如症状持续或加重，建议前往医院就诊',
                    '可以考虑进行更详细的健康检查'
                ],
                'urgency_level': '一般',
                'department_recommendation': '全科'
            }
            
            # 根据消息内容调整总结
            for message in messages:
                if message.get('direction') == 'outgoing' and message.get('type') == 'text':
                    text = message.get('content', {}).get('text', '').lower()
                    
                    if '头痛' in text:
                        summary['main_complaint'] = '头痛'
                        summary['possible_conditions'] = [
                            {'name': '紧张性头痛', 'probability': '高'},
                            {'name': '偏头痛', 'probability': '中等'},
                            {'name': '鼻窦炎', 'probability': '低'}
                        ]
                        summary['department_recommendation'] = '神经内科'
                    elif '发热' in text or '发烧' in text:
                        summary['main_complaint'] = '发热'
                        summary['possible_conditions'] = [
                            {'name': '上呼吸道感染', 'probability': '高'},
                            {'name': '流感', 'probability': '中等'},
                            {'name': '新冠病毒感染', 'probability': '需排除'}
                        ]
                        summary['department_recommendation'] = '呼吸内科或感染科'
                    elif '咳嗽' in text:
                        summary['main_complaint'] = '咳嗽'
                        summary['possible_conditions'] = [
                            {'name': '上呼吸道感染', 'probability': '高'},
                            {'name': '支气管炎', 'probability': '中等'},
                            {'name': '过敏性咳嗽', 'probability': '低'}
                        ]
                        summary['department_recommendation'] = '呼吸内科'
                    elif '腹痛' in text or '肚子痛' in text:
                        summary['main_complaint'] = '腹痛'
                        summary['possible_conditions'] = [
                            {'name': '胃肠炎', 'probability': '高'},
                            {'name': '肠易激综合征', 'probability': '中等'},
                            {'name': '胆囊炎', 'probability': '低'}
                        ]
                        summary['department_recommendation'] = '消化内科'
                    elif '胸痛' in text:
                        summary['main_complaint'] = '胸痛'
                        summary['urgency_level'] = '紧急'
                        summary['possible_conditions'] = [
                            {'name': '心绞痛', 'probability': '需排除'},
                            {'name': '胃食管反流', 'probability': '中等'},
                            {'name': '肋间神经痛', 'probability': '中等'}
                        ]
                        summary['department_recommendation'] = '心内科或急诊科'
            
            # 返回成功结果
            return {
                'success': True,
                'summary': summary
            }
        except Exception as e:
            logger.error(f"获取分诊会话总结失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': f"获取分诊会话总结失败: {str(e)}"
            }