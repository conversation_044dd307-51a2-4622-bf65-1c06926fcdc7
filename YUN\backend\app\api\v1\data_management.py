#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据管理API路由
提供数据导入导出、格式转换等功能的HTTP接口
支持健康管理系统中各种数据表的管理操作

版本: 2.0
作者: Health Management System
创建时间: 2024
更新时间: 2024-12-30
"""

import os
import tempfile
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, BackgroundTasks
from fastapi.responses import StreamingResponse, FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, validator

from ...core.db_connection import get_db
from ...core.data_export import (
    export_service, import_service, transform_service,
    ExportFormat, ImportFormat, DataOperation, CompressionType,
    ExportConfig, ImportConfig, ExportResult, ImportResult,
    SUPPORTED_TABLES
)
from ...core.error_handler import ErrorHandler, BusinessException
from ...core.logging_utils import get_logger
from ...core.security_utils import SecurityManager
from ...core.response_handler import ResponseHandler
from ...core.validators import DataValidator as CoreDataValidator
from ...core.cache_utils import CacheManager
from ...core.file_utils import FileManager
from ...core.auth import get_current_user
from ...models import User

logger = get_logger(__name__)
router = APIRouter(prefix="/data-management", tags=["数据管理"])

# 健康检查端点
@router.get(
    "/health",
    summary="数据管理API健康检查",
    description="检查数据管理API服务的健康状态"
)
async def data_management_health_check():
    """数据管理API健康检查"""
    try:
        return {
            "status": "ok",
            "service": "数据管理API服务",
            "timestamp": datetime.now().isoformat(),
            "version": "2.0",
            "components": {
                "database": "ok",
                "file_system": "ok",
                "export_service": "ok",
                "import_service": "ok"
            },
            "supported_tables": SUPPORTED_TABLES
        }
    except Exception as e:
        logger.error(f"数据管理API健康检查失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "service": "数据管理API服务",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

# 初始化服务
error_handler = ErrorHandler()
security_manager = SecurityManager()
response_handler = ResponseHandler()
data_validator = CoreDataValidator()
cache_manager = CacheManager()
file_manager = FileManager()

# ==================== 请求/响应模型 ====================

class ExportConfigRequest(BaseModel):
    """导出配置请求模型"""
    format: ExportFormat = Field(default=ExportFormat.CSV, description="导出格式")
    include_headers: bool = Field(default=True, description="是否包含表头")
    include_metadata: bool = Field(default=True, description="是否包含元数据")
    compression: CompressionType = Field(default=CompressionType.NONE, description="压缩类型")
    encoding: str = Field(default="utf-8", description="文件编码")
    date_format: str = Field(default="%Y-%m-%d %H:%M:%S", description="日期格式")
    chunk_size: int = Field(default=10000, ge=1, le=100000, description="分块大小")
    max_rows: Optional[int] = Field(default=None, ge=1, description="最大行数")
    columns: Optional[List[str]] = Field(default=None, description="指定列")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="过滤条件")
    custom_filename: Optional[str] = Field(default=None, description="自定义文件名")
    include_relations: bool = Field(default=False, description="是否包含关联数据")
    flatten_json: bool = Field(default=True, description="是否扁平化JSON")
    decimal_places: int = Field(default=2, ge=0, le=10, description="小数位数")
    null_value: str = Field(default="", description="空值表示")
    
    @validator('custom_filename')
    def validate_filename(cls, v):
        if v and ('/' in v or '\\' in v or '..' in v):
            raise ValueError("文件名不能包含路径分隔符")
        return v

class ImportConfigRequest(BaseModel):
    """导入配置请求模型"""
    format: ImportFormat = Field(description="导入格式")
    operation: DataOperation = Field(default=DataOperation.INSERT, description="操作类型")
    validate_data: bool = Field(default=True, description="是否验证数据")
    skip_errors: bool = Field(default=False, description="是否跳过错误")
    batch_size: int = Field(default=1000, ge=1, le=10000, description="批处理大小")
    encoding: str = Field(default="utf-8", description="文件编码")
    date_format: str = Field(default="%Y-%m-%d %H:%M:%S", description="日期格式")
    column_mapping: Optional[Dict[str, str]] = Field(default=None, description="列映射")
    default_values: Optional[Dict[str, Any]] = Field(default=None, description="默认值")
    unique_fields: Optional[List[str]] = Field(default=None, description="唯一字段")
    ignore_duplicates: bool = Field(default=False, description="是否忽略重复")
    update_existing: bool = Field(default=False, description="是否更新现有记录")
    create_backup: bool = Field(default=True, description="是否创建备份")

class ExportResponse(BaseModel):
    """导出响应模型"""
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    file_path: Optional[str] = Field(default=None, description="文件路径")
    file_size: int = Field(default=0, description="文件大小")
    rows_exported: int = Field(default=0, description="导出行数")
    export_time: float = Field(default=0.0, description="导出耗时")
    format: Optional[str] = Field(default=None, description="导出格式")
    compression: Optional[str] = Field(default=None, description="压缩类型")
    download_url: Optional[str] = Field(default=None, description="下载链接")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class ImportResponse(BaseModel):
    """导入响应模型"""
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    rows_processed: int = Field(default=0, description="处理行数")
    rows_inserted: int = Field(default=0, description="插入行数")
    rows_updated: int = Field(default=0, description="更新行数")
    rows_skipped: int = Field(default=0, description="跳过行数")
    rows_failed: int = Field(default=0, description="失败行数")
    import_time: float = Field(default=0.0, description="导入耗时")
    backup_file: Optional[str] = Field(default=None, description="备份文件")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    warnings: List[str] = Field(default_factory=list, description="警告信息")

class TableInfoResponse(BaseModel):
    """表信息响应模型"""
    table_name: str = Field(description="表名")
    display_name: str = Field(description="显示名称")
    description: str = Field(description="表描述")
    total_rows: int = Field(description="总行数")
    columns: List[Dict[str, Any]] = Field(description="列信息")
    supported_operations: List[str] = Field(description="支持的操作")

class BatchExportRequest(BaseModel):
    """批量导出请求模型"""
    tables: List[str] = Field(description="表名列表")
    config: ExportConfigRequest = Field(description="导出配置")
    archive_name: Optional[str] = Field(default=None, description="压缩包名称")

# ==================== 工具函数 ====================

def validate_table_name(table_name: str) -> str:
    """验证表名"""
    if table_name not in SUPPORTED_TABLES:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的表名: {table_name}。支持的表: {', '.join(SUPPORTED_TABLES)}"
        )
    return table_name

def get_table_display_name(table_name: str) -> str:
    """获取表的显示名称"""
    display_names = {
        'users': '用户信息',
        'health_records': '健康档案',
        'questionnaire_templates': '问卷模板',
        'questionnaire_results': '问卷结果',
        'assessment_results': '评估结果',
        'assessment_templates': '评估模板',
        'clinical_scales': '临床量表',
        'health_diaries': '健康日记',
        'medical_records': '医疗记录',
        'lab_reports': '检验报告',
        'examination_reports': '检查报告',
        'follow_up_records': '随访记录',
        'medications': '用药记录',
        'imaging_reports': '影像报告',
        'other_records': '其他记录',
        'user_health_records': '用户健康记录',
        'questionnaire_responses': '问卷回答',
        'assessment_responses': '评估回答',
        'questionnaire_distributions': '问卷分发',
        'assessment_distributions': '评估分发'
    }
    return display_names.get(table_name, table_name)

def get_table_description(table_name: str) -> str:
    """获取表的描述"""
    descriptions = {
        'users': '系统用户基本信息表',
        'health_records': '用户健康档案主表',
        'questionnaire_templates': '问卷调查模板定义',
        'questionnaire_results': '问卷调查结果数据',
        'assessment_results': '健康评估结果数据',
        'assessment_templates': '健康评估模板定义',
        'clinical_scales': '临床评估量表',
        'health_diaries': '用户健康日记记录',
        'medical_records': '医疗诊断记录',
        'lab_reports': '实验室检验报告',
        'examination_reports': '体检报告数据',
        'follow_up_records': '患者随访记录',
        'medications': '用药信息记录',
        'imaging_reports': '医学影像报告',
        'other_records': '其他类型记录',
        'user_health_records': '用户健康记录关联表',
        'questionnaire_responses': '问卷回答详细数据',
        'assessment_responses': '评估回答详细数据',
        'questionnaire_distributions': '问卷分发管理',
        'assessment_distributions': '评估分发管理'
    }
    return descriptions.get(table_name, f'{table_name}数据表')

async def cleanup_temp_file(file_path: str, delay: int = 3600):
    """清理临时文件"""
    import asyncio
    await asyncio.sleep(delay)
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"已清理临时文件: {file_path}")
    except Exception as e:
        logger.warning(f"清理临时文件失败: {file_path}, 错误: {str(e)}")

# ==================== API 端点 ====================

@router.get("/tables", response_model=List[TableInfoResponse], summary="获取支持的表列表")
async def get_supported_tables(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取系统支持的所有数据表信息"""
    try:
        tables_info = []
        
        for table_name in SUPPORTED_TABLES:
            try:
                # 获取表的行数
                model_class = export_service.get_table_model(table_name)
                total_rows = db.query(model_class).count()
                
                # 获取列信息
                columns = []
                for column in model_class.__table__.columns:
                    columns.append({
                        'name': column.name,
                        'type': str(column.type),
                        'nullable': column.nullable,
                        'primary_key': column.primary_key,
                        'default': str(column.default) if column.default else None
                    })
                
                tables_info.append(TableInfoResponse(
                    table_name=table_name,
                    display_name=get_table_display_name(table_name),
                    description=get_table_description(table_name),
                    total_rows=total_rows,
                    columns=columns,
                    supported_operations=["export", "import", "transform"]
                ))
                
            except Exception as e:
                logger.warning(f"获取表 {table_name} 信息失败: {str(e)}")
                continue
        
        return response_handler.success(
            data=tables_info,
            message=f"成功获取 {len(tables_info)} 个表的信息"
        )
        
    except Exception as e:
        logger.error(f"获取表列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取表列表失败: {str(e)}")

@router.post("/export/{table_name}", response_model=ExportResponse, summary="导出表数据")
async def export_table_data(
    table_name: str,
    config: ExportConfigRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """导出指定表的数据"""
    try:
        # 验证表名
        validate_table_name(table_name)
        
        # 转换配置
        export_config = ExportConfig(**config.dict())
        
        # 执行导出
        result = await export_service.export_data(table_name, export_config, db)
        
        if result.success:
            # 添加后台任务清理临时文件
            if result.file_path:
                background_tasks.add_task(cleanup_temp_file, result.file_path)
            
            return response_handler.success(
                data=ExportResponse(
                    success=True,
                    message=f"成功导出 {table_name} 数据",
                    file_path=result.file_path,
                    file_size=result.file_size,
                    rows_exported=result.rows_exported,
                    export_time=result.export_time,
                    format=result.format.value if result.format else None,
                    compression=result.compression.value if result.compression else None,
                    download_url=f"/api/v1/data-management/download/{os.path.basename(result.file_path)}" if result.file_path else None,
                    metadata=result.metadata
                ),
                message="导出成功"
            )
        else:
            return response_handler.error(
                message="导出失败",
                errors=result.errors
            )
            
    except Exception as e:
        logger.error(f"导出数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出数据失败: {str(e)}")

@router.get("/export/{table_name}/download", summary="下载导出文件")
async def download_export_file(
    table_name: str,
    config: ExportConfigRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """直接下载导出的文件"""
    try:
        # 验证表名
        validate_table_name(table_name)
        
        # 转换配置
        export_config = ExportConfig(**config.dict())
        
        # 执行导出
        result = await export_service.export_data(table_name, export_config, db)
        
        if result.success and result.file_path and os.path.exists(result.file_path):
            # 生成文件名
            filename = os.path.basename(result.file_path)
            
            # 确定媒体类型
            media_types = {
                ExportFormat.CSV: "text/csv",
                ExportFormat.JSON: "application/json",
                ExportFormat.EXCEL: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ExportFormat.XML: "application/xml",
                ExportFormat.PARQUET: "application/octet-stream"
            }
            media_type = media_types.get(result.format, "application/octet-stream")
            
            # 如果有压缩，调整媒体类型
            if result.compression and result.compression != CompressionType.NONE:
                media_type = "application/octet-stream"
            
            # 返回文件流
            def file_generator():
                with open(result.file_path, 'rb') as f:
                    while True:
                        chunk = f.read(8192)
                        if not chunk:
                            break
                        yield chunk
                # 清理临时文件
                try:
                    os.remove(result.file_path)
                except:
                    pass
            
            return StreamingResponse(
                file_generator(),
                media_type=media_type,
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
        else:
            raise HTTPException(status_code=404, detail="导出文件不存在")
            
    except Exception as e:
        logger.error(f"下载导出文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载导出文件失败: {str(e)}")

@router.post("/import/{table_name}", response_model=ImportResponse, summary="导入表数据")
async def import_table_data(
    table_name: str,
    file: UploadFile = File(..., description="要导入的文件"),
    config: str = Form(..., description="导入配置JSON字符串"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """导入数据到指定表"""
    try:
        # 验证表名
        validate_table_name(table_name)
        
        # 解析配置
        import json
        config_dict = json.loads(config)
        import_config = ImportConfig(**config_dict)
        
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 执行导入
        result = await import_service.import_data(file, table_name, import_config, db)
        
        return response_handler.success(
            data=ImportResponse(
                success=result.success,
                message="导入成功" if result.success else "导入失败",
                rows_processed=result.rows_processed,
                rows_inserted=result.rows_inserted,
                rows_updated=result.rows_updated,
                rows_skipped=result.rows_skipped,
                rows_failed=result.rows_failed,
                import_time=result.import_time,
                backup_file=result.backup_file,
                errors=result.errors,
                warnings=result.warnings
            ),
            message="导入完成"
        )
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="配置格式错误")
    except Exception as e:
        logger.error(f"导入数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入数据失败: {str(e)}")

@router.post("/transform", summary="数据格式转换")
async def transform_data_format(
    file: UploadFile = File(..., description="要转换的文件"),
    source_format: ImportFormat = Form(..., description="源格式"),
    target_format: ExportFormat = Form(..., description="目标格式"),
    current_user: User = Depends(get_current_user)
):
    """转换数据文件格式"""
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 执行格式转换
        return await transform_service.transform_format(file, source_format, target_format)
        
    except Exception as e:
        logger.error(f"格式转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"格式转换失败: {str(e)}")

@router.post("/batch-export", summary="批量导出多个表")
async def batch_export_tables(
    request: BatchExportRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量导出多个表的数据为压缩包"""
    try:
        # 验证表名
        for table_name in request.tables:
            validate_table_name(table_name)
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="batch_export_")
        files_to_zip = []
        
        # 转换配置
        export_config = ExportConfig(**request.config.dict())
        
        # 导出每个表
        for table_name in request.tables:
            try:
                result = await export_service.export_data(table_name, export_config, db)
                
                if result.success and result.file_path:
                    # 移动文件到临时目录
                    new_filename = f"{table_name}_{os.path.basename(result.file_path)}"
                    new_path = os.path.join(temp_dir, new_filename)
                    os.rename(result.file_path, new_path)
                    files_to_zip.append(new_path)
                    
            except Exception as e:
                logger.warning(f"导出表 {table_name} 失败: {str(e)}")
                continue
        
        if not files_to_zip:
            raise HTTPException(status_code=400, detail="没有成功导出的文件")
        
        # 创建压缩包
        import zipfile
        archive_name = request.archive_name or f"batch_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        zip_path = os.path.join(temp_dir, archive_name)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in files_to_zip:
                zipf.write(file_path, os.path.basename(file_path))
        
        # 添加后台任务清理临时目录
        background_tasks.add_task(cleanup_temp_file, temp_dir)
        
        # 返回压缩包
        def file_generator():
            with open(zip_path, 'rb') as f:
                while True:
                    chunk = f.read(8192)
                    if not chunk:
                        break
                    yield chunk
        
        return StreamingResponse(
            file_generator(),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={archive_name}"}
        )
        
    except Exception as e:
        logger.error(f"批量导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量导出失败: {str(e)}")

@router.get("/download/{filename}", summary="下载文件")
async def download_file(
    filename: str,
    current_user: User = Depends(get_current_user)
):
    """下载指定的文件"""
    try:
        # 安全检查文件名
        if '..' in filename or '/' in filename or '\\' in filename:
            raise HTTPException(status_code=400, detail="无效的文件名")
        
        # 查找文件（这里需要实现文件存储逻辑）
        # 暂时返回404，实际应用中需要实现文件存储和检索
        raise HTTPException(status_code=404, detail="文件不存在")
        
    except Exception as e:
        logger.error(f"下载文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

@router.get("/formats", summary="获取支持的格式")
async def get_supported_formats():
    """获取系统支持的导入导出格式"""
    return response_handler.success(
        data={
            "export_formats": [format.value for format in ExportFormat],
            "import_formats": [format.value for format in ImportFormat],
            "compression_types": [comp.value for comp in CompressionType],
            "data_operations": [op.value for op in DataOperation]
        },
        message="获取支持格式成功"
    )

@router.get("/status/{table_name}", summary="获取表状态")
async def get_table_status(
    table_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定表的状态信息"""
    try:
        # 验证表名
        validate_table_name(table_name)
        
        # 获取表模型
        model_class = export_service.get_table_model(table_name)
        
        # 获取统计信息
        total_rows = db.query(model_class).count()
        
        # 获取最近更新时间（如果表有更新时间字段）
        last_updated = None
        if hasattr(model_class, 'updated_at'):
            latest_record = db.query(model_class).order_by(model_class.updated_at.desc()).first()
            if latest_record:
                last_updated = latest_record.updated_at.isoformat()
        elif hasattr(model_class, 'created_at'):
            latest_record = db.query(model_class).order_by(model_class.created_at.desc()).first()
            if latest_record:
                last_updated = latest_record.created_at.isoformat()
        
        return response_handler.success(
            data={
                "table_name": table_name,
                "display_name": get_table_display_name(table_name),
                "total_rows": total_rows,
                "last_updated": last_updated,
                "status": "active" if total_rows > 0 else "empty"
            },
            message="获取表状态成功"
        )
        
    except Exception as e:
        logger.error(f"获取表状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取表状态失败: {str(e)}")

@router.delete("/clear/{table_name}", summary="清空表数据")
async def clear_table_data(
    table_name: str,
    confirm: bool = Query(False, description="确认清空"),
    create_backup: bool = Query(True, description="是否创建备份"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """清空指定表的所有数据（危险操作）"""
    try:
        # 验证表名
        validate_table_name(table_name)
        
        if not confirm:
            raise HTTPException(status_code=400, detail="请确认清空操作")
        
        # 获取表模型
        model_class = export_service.get_table_model(table_name)
        
        # 创建备份
        backup_file = None
        if create_backup:
            export_config = ExportConfig(
                format=ExportFormat.JSON,
                include_metadata=True,
                compression=CompressionType.ZIP
            )
            backup_result = await export_service.export_data(table_name, export_config, db)
            if backup_result.success:
                backup_file = backup_result.file_path
        
        # 获取删除前的行数
        rows_before = db.query(model_class).count()
        
        # 执行清空操作
        db.query(model_class).delete()
        db.commit()
        
        return response_handler.success(
            data={
                "table_name": table_name,
                "rows_deleted": rows_before,
                "backup_file": backup_file
            },
            message=f"成功清空表 {table_name}，删除了 {rows_before} 行数据"
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"清空表数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空表数据失败: {str(e)}")

# ==================== 错误处理 ====================
# 注意：异常处理应该在应用级别配置，而不是路由级别

# ==================== 导出的接口 ====================

__all__ = [
    'router',
    'ExportConfigRequest',
    'ImportConfigRequest',
    'ExportResponse',
    'ImportResponse',
    'TableInfoResponse',
    'BatchExportRequest'
]