2025-04-16 10:53:17,514 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 10:53:17,515 - INFO - 已启用HTTP请求日志记录
2025-04-16 10:53:17,530 - DEBUG - [9004762a] HTTP请求开始 - GET http://8.138.188.26/api/health
2025-04-16 10:53:17,568 - WARNING - [9004762a] HTTP响应 - GET http://8.138.188.26/api/health - 状态码: 405 - 耗时: 0.039s
2025-04-16 10:53:17,579 - DEBUG - HTTP请求详情: {"request_id": "9004762a", "timestamp": "2025-04-16T10:53:17.530067", "method": "GET", "url": "http://8.138.188.26/api/health", "status_code": 405, "elapsed_time": "0.040s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}", "exception": "name 'error_logger' is not defined"}
2025-04-16 10:53:17,607 - DEBUG - [585f8eed] HTTP请求开始 - POST http://8.138.188.26/api/auth/register
2025-04-16 10:53:17,637 - WARNING - [585f8eed] HTTP响应 - POST http://8.138.188.26/api/auth/register - 状态码: 422 - 耗时: 0.030s
2025-04-16 10:53:17,646 - DEBUG - HTTP请求详情: {"request_id": "585f8eed", "timestamp": "2025-04-16T10:53:17.607066", "method": "POST", "url": "http://8.138.188.26/api/auth/register", "json": {"username": "testuser_**********", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "f11cfd138e23f3554f9a44f2201e1e6f1561997744a46c306331b01844cb3a3f"}, "status_code": 422, "elapsed_time": "0.032s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}", "exception": "name 'error_logger' is not defined"}
2025-04-16 10:53:17,660 - DEBUG - [1f25a019] HTTP请求开始 - POST http://8.138.188.26/api/auth/login
2025-04-16 10:53:17,705 - WARNING - [1f25a019] HTTP响应 - POST http://8.138.188.26/api/auth/login - 状态码: 422 - 耗时: 0.045s
2025-04-16 10:53:17,716 - DEBUG - HTTP请求详情: {"request_id": "1f25a019", "timestamp": "2025-04-16T10:53:17.660221", "method": "POST", "url": "http://8.138.188.26/api/auth/login", "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "f11cfd138e23f3554f9a44f2201e1e6f1561997744a46c306331b01844cb3a3f"}, "status_code": 422, "elapsed_time": "0.047s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}", "exception": "name 'error_logger' is not defined"}
2025-04-16 10:53:17,743 - DEBUG - [00d0d0f5] HTTP请求开始 - GET http://8.138.188.26/api/health
2025-04-16 10:53:17,785 - WARNING - [00d0d0f5] HTTP响应 - GET http://8.138.188.26/api/health - 状态码: 405 - 耗时: 0.042s
2025-04-16 10:53:17,792 - DEBUG - HTTP请求详情: {"request_id": "00d0d0f5", "timestamp": "2025-04-16T10:53:17.743404", "method": "GET", "url": "http://8.138.188.26/api/health", "status_code": 405, "elapsed_time": "0.044s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}", "exception": "name 'error_logger' is not defined"}
2025-04-16 10:53:17,798 - DEBUG - [50a9be7c] HTTP请求开始 - POST http://8.138.188.26/api/auth/logout
2025-04-16 10:53:17,839 - WARNING - [50a9be7c] HTTP响应 - POST http://8.138.188.26/api/auth/logout - 状态码: 405 - 耗时: 0.041s
2025-04-16 10:53:17,847 - DEBUG - HTTP请求详情: {"request_id": "50a9be7c", "timestamp": "2025-04-16T10:53:17.798883", "method": "POST", "url": "http://8.138.188.26/api/auth/logout", "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.042s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}", "exception": "name 'error_logger' is not defined"}
2025-04-16 10:53:17,850 - DEBUG - [ff949be5] HTTP请求开始 - POST http://8.138.188.26/api/auth/logout
2025-04-16 10:53:17,897 - WARNING - [ff949be5] HTTP响应 - POST http://8.138.188.26/api/auth/logout - 状态码: 405 - 耗时: 0.047s
2025-04-16 10:53:17,910 - DEBUG - HTTP请求详情: {"request_id": "ff949be5", "timestamp": "2025-04-16T10:53:17.850187", "method": "POST", "url": "http://8.138.188.26/api/auth/logout", "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.050s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}", "exception": "name 'error_logger' is not defined"}
2025-04-16 10:58:23,522 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 10:58:23,523 - INFO - 已启用HTTP请求日志记录
2025-04-16 10:58:33,208 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 10:58:33,210 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:01:48,072 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:01:48,075 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:01:48,096 - DEBUG - [f6df034c] HTTP请求开始 - GET None
2025-04-16 11:01:49,090 - DEBUG - [d93731da] HTTP请求开始 - GET None
2025-04-16 11:01:53,124 - WARNING - [f6df034c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.028s
2025-04-16 11:01:53,125 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:01:53,126 - DEBUG - HTTP请求详情: {"request_id": "f6df034c", "timestamp": "2025-04-16T11:01:48.096544", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.028s"}
2025-04-16 11:01:53,131 - DEBUG - [52f86d85] HTTP请求开始 - GET None
2025-04-16 11:01:54,099 - WARNING - [d93731da] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:01:54,100 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:01:54,101 - DEBUG - HTTP请求详情: {"request_id": "d93731da", "timestamp": "2025-04-16T11:01:49.090390", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:01:58,457 - ERROR - [52f86d85] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.011s
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 11:01:58,496 - DEBUG - HTTP请求详情: {"request_id": "52f86d85", "timestamp": "2025-04-16T11:01:53.131362", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.011s"}
2025-04-16 11:01:58,584 - DEBUG - [fd64c910] HTTP请求开始 - POST None
2025-04-16 11:02:03,638 - WARNING - [fd64c910] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.054s
2025-04-16 11:02:03,640 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 11:02:03,642 - DEBUG - HTTP请求详情: {"request_id": "fd64c910", "timestamp": "2025-04-16T11:01:58.584393", "method": "POST", "url": null, "json": {"username": "testuser_1744772508", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "67809917bed406762baff885a7de8158ecb0515eeba9d52a783700fedfb4d39a"}, "status_code": 502, "elapsed_time": "5.054s"}
2025-04-16 11:02:03,665 - DEBUG - [e745c936] HTTP请求开始 - POST None
2025-04-16 11:02:08,682 - WARNING - [e745c936] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.018s
2025-04-16 11:02:08,684 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 11:02:08,686 - DEBUG - HTTP请求详情: {"request_id": "e745c936", "timestamp": "2025-04-16T11:02:03.664987", "method": "POST", "url": null, "json": {"username": "testuser_1744772508", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "d6af1a2e655bc131dc0039401648045f95514e4b864b31ba3e7492b380a47f73"}, "status_code": 502, "elapsed_time": "5.018s"}
2025-04-16 11:02:08,719 - DEBUG - [4f187011] HTTP请求开始 - GET None
2025-04-16 11:02:13,738 - WARNING - [4f187011] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.019s
2025-04-16 11:02:13,740 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:02:13,742 - DEBUG - HTTP请求详情: {"request_id": "4f187011", "timestamp": "2025-04-16T11:02:08.719441", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.019s"}
2025-04-16 11:02:13,756 - DEBUG - [c4db2224] HTTP请求开始 - POST None
2025-04-16 11:02:18,774 - WARNING - [c4db2224] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.019s
2025-04-16 11:02:18,777 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 11:02:18,779 - DEBUG - HTTP请求详情: {"request_id": "c4db2224", "timestamp": "2025-04-16T11:02:13.756126", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 502, "elapsed_time": "5.019s"}
2025-04-16 11:05:13,445 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:05:13,446 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:05:13,470 - DEBUG - [11d86fa0] HTTP请求开始 - GET None
2025-04-16 11:05:14,465 - DEBUG - [be2377bb] HTTP请求开始 - GET None
2025-04-16 11:05:18,499 - WARNING - [11d86fa0] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.029s
2025-04-16 11:05:18,501 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:05:18,502 - DEBUG - HTTP请求详情: {"request_id": "11d86fa0", "timestamp": "2025-04-16T11:05:13.470341", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.029s"}
2025-04-16 11:05:18,511 - DEBUG - [b9f66321] HTTP请求开始 - GET None
2025-04-16 11:05:19,503 - WARNING - [be2377bb] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.038s
2025-04-16 11:05:19,504 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:05:19,506 - DEBUG - HTTP请求详情: {"request_id": "be2377bb", "timestamp": "2025-04-16T11:05:14.465494", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.038s"}
2025-04-16 11:05:23,548 - ERROR - [b9f66321] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.013s
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 11:05:23,622 - DEBUG - HTTP请求详情: {"request_id": "b9f66321", "timestamp": "2025-04-16T11:05:18.511409", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.013s"}
2025-04-16 11:05:23,665 - DEBUG - [86ab5e16] HTTP请求开始 - POST None
2025-04-16 11:05:28,690 - WARNING - [86ab5e16] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.026s
2025-04-16 11:05:28,692 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 11:05:28,694 - DEBUG - HTTP请求详情: {"request_id": "86ab5e16", "timestamp": "2025-04-16T11:05:23.664238", "method": "POST", "url": null, "json": {"username": "testuser_1744772713", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "b07cff8e1ab53952776f558ea2c68eb7f328572985b2dbda1dd0b0d67129e429"}, "status_code": 502, "elapsed_time": "5.026s"}
2025-04-16 11:05:28,721 - DEBUG - [25abd05c] HTTP请求开始 - POST None
2025-04-16 11:05:33,732 - WARNING - [25abd05c] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.011s
2025-04-16 11:05:33,733 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 11:05:33,736 - DEBUG - HTTP请求详情: {"request_id": "25abd05c", "timestamp": "2025-04-16T11:05:28.721309", "method": "POST", "url": null, "json": {"username": "testuser_1744772713", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "f5c3859f934c5450681f08bd76d2ffc356f36ca5198763f24df658cd4f84d48b"}, "status_code": 502, "elapsed_time": "5.011s"}
2025-04-16 11:05:33,765 - DEBUG - [c12119fa] HTTP请求开始 - GET None
2025-04-16 11:05:38,781 - WARNING - [c12119fa] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:05:38,783 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:05:38,785 - DEBUG - HTTP请求详情: {"request_id": "c12119fa", "timestamp": "2025-04-16T11:05:33.765223", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:05:38,803 - DEBUG - [a617e579] HTTP请求开始 - POST None
2025-04-16 11:05:43,830 - WARNING - [a617e579] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.027s
2025-04-16 11:05:43,832 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 11:05:43,834 - DEBUG - HTTP请求详情: {"request_id": "a617e579", "timestamp": "2025-04-16T11:05:38.803072", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 502, "elapsed_time": "5.027s"}
2025-04-16 11:32:57,153 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:32:57,154 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:32:57,273 - DEBUG - [edef0d0f] HTTP请求开始 - GET None
2025-04-16 11:32:57,310 - DEBUG - [b0a4fcb0] HTTP请求开始 - GET None
2025-04-16 11:33:02,317 - WARNING - [edef0d0f] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.043s
2025-04-16 11:33:02,317 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:02,318 - DEBUG - HTTP请求详情: {"request_id": "edef0d0f", "timestamp": "2025-04-16T11:32:57.273321", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.043s"}
2025-04-16 11:33:02,319 - DEBUG - [78d53da9] HTTP请求开始 - GET None
2025-04-16 11:33:02,323 - WARNING - [b0a4fcb0] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:33:02,323 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:02,324 - DEBUG - HTTP请求详情: {"request_id": "b0a4fcb0", "timestamp": "2025-04-16T11:32:57.310875", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:33:02,326 - DEBUG - [0fc23483] HTTP请求开始 - GET None
2025-04-16 11:33:02,362 - INFO - [78d53da9] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.042s
2025-04-16 11:33:02,363 - DEBUG - HTTP请求详情: {"request_id": "78d53da9", "timestamp": "2025-04-16T11:33:02.319778", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.042s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:02,366 - DEBUG - [20c650e5] HTTP请求开始 - GET None
2025-04-16 11:33:07,333 - WARNING - [0fc23483] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.007s
2025-04-16 11:33:07,333 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:07,334 - DEBUG - HTTP请求详情: {"request_id": "0fc23483", "timestamp": "2025-04-16T11:33:02.326213", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.007s"}
2025-04-16 11:33:07,336 - DEBUG - [447fa7ff] HTTP请求开始 - GET None
2025-04-16 11:33:07,377 - INFO - [447fa7ff] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 11:33:07,377 - WARNING - [20c650e5] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.011s
2025-04-16 11:33:07,378 - DEBUG - HTTP请求详情: {"request_id": "447fa7ff", "timestamp": "2025-04-16T11:33:07.336148", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:07,379 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:07,381 - DEBUG - [ad2033b7] HTTP请求开始 - GET None
2025-04-16 11:33:07,382 - DEBUG - HTTP请求详情: {"request_id": "20c650e5", "timestamp": "2025-04-16T11:33:02.366283", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.011s"}
2025-04-16 11:33:07,385 - DEBUG - [f05183d0] HTTP请求开始 - GET None
2025-04-16 11:33:07,436 - INFO - [ad2033b7] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.054s
2025-04-16 11:33:07,436 - DEBUG - HTTP请求详情: {"request_id": "ad2033b7", "timestamp": "2025-04-16T11:33:07.381678", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.054s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:07,438 - DEBUG - [c40672f5] HTTP请求开始 - GET None
2025-04-16 11:33:12,414 - WARNING - [f05183d0] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.030s
2025-04-16 11:33:12,416 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:12,417 - DEBUG - HTTP请求详情: {"request_id": "f05183d0", "timestamp": "2025-04-16T11:33:07.385204", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.030s"}
2025-04-16 11:33:12,420 - DEBUG - [e69a029b] HTTP请求开始 - GET None
2025-04-16 11:33:12,450 - WARNING - [c40672f5] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:33:12,452 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:12,454 - DEBUG - HTTP请求详情: {"request_id": "c40672f5", "timestamp": "2025-04-16T11:33:07.438833", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:33:12,457 - DEBUG - [f29b2234] HTTP请求开始 - GET None
2025-04-16 11:33:12,516 - INFO - [e69a029b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.095s
2025-04-16 11:33:12,517 - DEBUG - HTTP请求详情: {"request_id": "e69a029b", "timestamp": "2025-04-16T11:33:12.420581", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.095s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:12,520 - DEBUG - [71e5a9bb] HTTP请求开始 - GET None
2025-04-16 11:33:17,467 - WARNING - [f29b2234] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:33:17,468 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:17,470 - DEBUG - HTTP请求详情: {"request_id": "f29b2234", "timestamp": "2025-04-16T11:33:12.457349", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:33:17,473 - DEBUG - [86ed0fb8] HTTP请求开始 - GET None
2025-04-16 11:33:17,525 - INFO - [86ed0fb8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.051s
2025-04-16 11:33:17,526 - DEBUG - HTTP请求详情: {"request_id": "86ed0fb8", "timestamp": "2025-04-16T11:33:17.473450", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.051s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:17,532 - DEBUG - [0bbea827] HTTP请求开始 - GET None
2025-04-16 11:33:17,558 - WARNING - [71e5a9bb] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.037s
2025-04-16 11:33:17,559 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:17,561 - DEBUG - HTTP请求详情: {"request_id": "71e5a9bb", "timestamp": "2025-04-16T11:33:12.520712", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.037s"}
2025-04-16 11:33:17,566 - DEBUG - [cbc79b74] HTTP请求开始 - GET None
2025-04-16 11:33:17,573 - INFO - [0bbea827] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.042s
2025-04-16 11:33:17,576 - DEBUG - HTTP请求详情: {"request_id": "0bbea827", "timestamp": "2025-04-16T11:33:17.531925", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.042s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:17,582 - DEBUG - [0a941945] HTTP请求开始 - GET None
2025-04-16 11:33:22,595 - WARNING - [0a941945] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.013s
2025-04-16 11:33:22,595 - WARNING - [cbc79b74] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.029s
2025-04-16 11:33:22,596 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:22,597 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:22,598 - DEBUG - HTTP请求详情: {"request_id": "0a941945", "timestamp": "2025-04-16T11:33:17.582270", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.013s"}
2025-04-16 11:33:22,599 - DEBUG - HTTP请求详情: {"request_id": "cbc79b74", "timestamp": "2025-04-16T11:33:17.566593", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.029s"}
2025-04-16 11:33:22,601 - DEBUG - [c3f4fcc6] HTTP请求开始 - GET None
2025-04-16 11:33:22,603 - DEBUG - [09f5b3ea] HTTP请求开始 - GET None
2025-04-16 11:33:22,638 - INFO - [c3f4fcc6] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.037s
2025-04-16 11:33:22,639 - DEBUG - HTTP请求详情: {"request_id": "c3f4fcc6", "timestamp": "2025-04-16T11:33:22.601494", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.037s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:22,641 - DEBUG - [00b79237] HTTP请求开始 - GET None
2025-04-16 11:33:27,622 - WARNING - [09f5b3ea] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.019s
2025-04-16 11:33:27,624 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:27,625 - DEBUG - HTTP请求详情: {"request_id": "09f5b3ea", "timestamp": "2025-04-16T11:33:22.603180", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.019s"}
2025-04-16 11:33:27,628 - DEBUG - [7a85b294] HTTP请求开始 - GET None
2025-04-16 11:33:27,668 - WARNING - [00b79237] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.027s
2025-04-16 11:33:27,672 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:27,674 - DEBUG - HTTP请求详情: {"request_id": "00b79237", "timestamp": "2025-04-16T11:33:22.640993", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.027s"}
2025-04-16 11:33:27,678 - DEBUG - [e5370d4a] HTTP请求开始 - GET None
2025-04-16 11:33:27,709 - INFO - [7a85b294] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.081s
2025-04-16 11:33:27,711 - DEBUG - HTTP请求详情: {"request_id": "7a85b294", "timestamp": "2025-04-16T11:33:27.628590", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.081s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:27,717 - DEBUG - [185cc83c] HTTP请求开始 - GET None
2025-04-16 11:33:32,697 - WARNING - [e5370d4a] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.019s
2025-04-16 11:33:32,697 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:32,698 - DEBUG - HTTP请求详情: {"request_id": "e5370d4a", "timestamp": "2025-04-16T11:33:27.678329", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.019s"}
2025-04-16 11:33:32,700 - DEBUG - [25d26fa5] HTTP请求开始 - GET None
2025-04-16 11:33:32,731 - WARNING - [185cc83c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:33:32,732 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:32,733 - DEBUG - HTTP请求详情: {"request_id": "185cc83c", "timestamp": "2025-04-16T11:33:27.717007", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:33:32,735 - DEBUG - [c3901f65] HTTP请求开始 - GET None
2025-04-16 11:33:32,789 - INFO - [25d26fa5] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.089s
2025-04-16 11:33:32,790 - DEBUG - HTTP请求详情: {"request_id": "25d26fa5", "timestamp": "2025-04-16T11:33:32.700132", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.089s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:32,792 - DEBUG - [e9165b85] HTTP请求开始 - GET None
2025-04-16 11:33:37,776 - WARNING - [c3901f65] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.041s
2025-04-16 11:33:37,777 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:37,777 - DEBUG - HTTP请求详情: {"request_id": "c3901f65", "timestamp": "2025-04-16T11:33:32.735218", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.041s"}
2025-04-16 11:33:37,779 - DEBUG - [14f139ae] HTTP请求开始 - GET None
2025-04-16 11:33:37,810 - WARNING - [e9165b85] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.018s
2025-04-16 11:33:37,810 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:37,811 - DEBUG - HTTP请求详情: {"request_id": "e9165b85", "timestamp": "2025-04-16T11:33:32.792239", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.018s"}
2025-04-16 11:33:37,812 - DEBUG - [2b5e9632] HTTP请求开始 - GET None
2025-04-16 11:33:37,848 - INFO - [14f139ae] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.069s
2025-04-16 11:33:37,849 - DEBUG - HTTP请求详情: {"request_id": "14f139ae", "timestamp": "2025-04-16T11:33:37.779155", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.069s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:37,850 - DEBUG - [3e32182a] HTTP请求开始 - GET None
2025-04-16 11:33:42,827 - WARNING - [2b5e9632] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:33:42,828 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:42,830 - DEBUG - HTTP请求详情: {"request_id": "2b5e9632", "timestamp": "2025-04-16T11:33:37.812539", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:33:42,834 - DEBUG - [0412382e] HTTP请求开始 - GET None
2025-04-16 11:33:42,859 - WARNING - [3e32182a] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.009s
2025-04-16 11:33:42,862 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:42,863 - DEBUG - HTTP请求详情: {"request_id": "3e32182a", "timestamp": "2025-04-16T11:33:37.850616", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.009s"}
2025-04-16 11:33:42,867 - DEBUG - [ecd89495] HTTP请求开始 - GET None
2025-04-16 11:33:42,882 - INFO - [0412382e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.048s
2025-04-16 11:33:42,883 - DEBUG - HTTP请求详情: {"request_id": "0412382e", "timestamp": "2025-04-16T11:33:42.834556", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.048s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:42,885 - DEBUG - [6f85d08c] HTTP请求开始 - GET None
2025-04-16 11:33:47,896 - WARNING - [ecd89495] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.029s
2025-04-16 11:33:47,897 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:47,897 - DEBUG - HTTP请求详情: {"request_id": "ecd89495", "timestamp": "2025-04-16T11:33:42.867269", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.029s"}
2025-04-16 11:33:47,899 - DEBUG - [d8142413] HTTP请求开始 - GET None
2025-04-16 11:33:47,915 - WARNING - [6f85d08c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.030s
2025-04-16 11:33:47,916 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:47,919 - DEBUG - HTTP请求详情: {"request_id": "6f85d08c", "timestamp": "2025-04-16T11:33:42.885849", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.030s"}
2025-04-16 11:33:47,921 - DEBUG - [3bcd3b97] HTTP请求开始 - GET None
2025-04-16 11:33:48,035 - INFO - [d8142413] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.136s
2025-04-16 11:33:48,036 - DEBUG - HTTP请求详情: {"request_id": "d8142413", "timestamp": "2025-04-16T11:33:47.899186", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.136s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:48,037 - DEBUG - [7f0db843] HTTP请求开始 - GET None
2025-04-16 11:33:52,946 - WARNING - [3bcd3b97] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.025s
2025-04-16 11:33:52,947 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:52,948 - DEBUG - HTTP请求详情: {"request_id": "3bcd3b97", "timestamp": "2025-04-16T11:33:47.921175", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.025s"}
2025-04-16 11:33:52,951 - DEBUG - [9d8a69f4] HTTP请求开始 - GET None
2025-04-16 11:33:52,996 - INFO - [9d8a69f4] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 11:33:52,998 - DEBUG - HTTP请求详情: {"request_id": "9d8a69f4", "timestamp": "2025-04-16T11:33:52.951804", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:53,000 - DEBUG - [e7310476] HTTP请求开始 - GET None
2025-04-16 11:33:53,063 - WARNING - [7f0db843] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.025s
2025-04-16 11:33:53,065 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:53,066 - DEBUG - HTTP请求详情: {"request_id": "7f0db843", "timestamp": "2025-04-16T11:33:48.037778", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.025s"}
2025-04-16 11:33:53,069 - DEBUG - [90074f04] HTTP请求开始 - GET None
2025-04-16 11:33:53,229 - INFO - [e7310476] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.229s
2025-04-16 11:33:53,230 - DEBUG - HTTP请求详情: {"request_id": "e7310476", "timestamp": "2025-04-16T11:33:53.000941", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.229s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:53,232 - DEBUG - [f0ba75a3] HTTP请求开始 - GET None
2025-04-16 11:33:58,084 - WARNING - [90074f04] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:33:58,085 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:58,087 - DEBUG - HTTP请求详情: {"request_id": "90074f04", "timestamp": "2025-04-16T11:33:53.069738", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:33:58,090 - DEBUG - [e7630a7e] HTTP请求开始 - GET None
2025-04-16 11:33:58,260 - WARNING - [f0ba75a3] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.028s
2025-04-16 11:33:58,262 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:33:58,263 - DEBUG - HTTP请求详情: {"request_id": "f0ba75a3", "timestamp": "2025-04-16T11:33:53.232060", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.028s"}
2025-04-16 11:33:58,267 - DEBUG - [dedb6dd8] HTTP请求开始 - GET None
2025-04-16 11:33:58,430 - INFO - [e7630a7e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.340s
2025-04-16 11:33:58,431 - DEBUG - HTTP请求详情: {"request_id": "e7630a7e", "timestamp": "2025-04-16T11:33:58.090081", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.340s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:33:58,434 - DEBUG - [9bf1b7d1] HTTP请求开始 - GET None
2025-04-16 11:34:03,274 - WARNING - [dedb6dd8] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.008s
2025-04-16 11:34:03,276 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:03,277 - DEBUG - HTTP请求详情: {"request_id": "dedb6dd8", "timestamp": "2025-04-16T11:33:58.267244", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.008s"}
2025-04-16 11:34:03,281 - DEBUG - [01e93a6b] HTTP请求开始 - GET None
2025-04-16 11:34:03,323 - INFO - [01e93a6b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 11:34:03,325 - DEBUG - HTTP请求详情: {"request_id": "01e93a6b", "timestamp": "2025-04-16T11:34:03.281340", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:03,333 - DEBUG - [4e92b773] HTTP请求开始 - GET None
2025-04-16 11:34:03,416 - INFO - [4e92b773] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.083s
2025-04-16 11:34:03,418 - DEBUG - HTTP请求详情: {"request_id": "4e92b773", "timestamp": "2025-04-16T11:34:03.333350", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.083s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:03,422 - DEBUG - [33204a0e] HTTP请求开始 - GET None
2025-04-16 11:34:03,450 - WARNING - [9bf1b7d1] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:34:03,452 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:03,453 - DEBUG - HTTP请求详情: {"request_id": "9bf1b7d1", "timestamp": "2025-04-16T11:33:58.434878", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:34:03,456 - DEBUG - [52fffd76] HTTP请求开始 - GET None
2025-04-16 11:34:03,519 - INFO - [33204a0e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.097s
2025-04-16 11:34:03,521 - DEBUG - HTTP请求详情: {"request_id": "33204a0e", "timestamp": "2025-04-16T11:34:03.422200", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.097s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:03,529 - DEBUG - [53ecef37] HTTP请求开始 - GET None
2025-04-16 11:34:08,475 - WARNING - [52fffd76] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.018s
2025-04-16 11:34:08,476 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:08,478 - DEBUG - HTTP请求详情: {"request_id": "52fffd76", "timestamp": "2025-04-16T11:34:03.456842", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.018s"}
2025-04-16 11:34:08,480 - DEBUG - [a13a4acb] HTTP请求开始 - GET None
2025-04-16 11:34:08,548 - INFO - [a13a4acb] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.067s
2025-04-16 11:34:08,549 - DEBUG - HTTP请求详情: {"request_id": "a13a4acb", "timestamp": "2025-04-16T11:34:08.480798", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.067s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:08,550 - WARNING - [53ecef37] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.022s
2025-04-16 11:34:08,551 - DEBUG - [04f36074] HTTP请求开始 - GET None
2025-04-16 11:34:08,551 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:08,553 - DEBUG - HTTP请求详情: {"request_id": "53ecef37", "timestamp": "2025-04-16T11:34:03.528700", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.022s"}
2025-04-16 11:34:08,555 - DEBUG - [cc956b2d] HTTP请求开始 - GET None
2025-04-16 11:34:08,622 - INFO - [04f36074] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.071s
2025-04-16 11:34:08,623 - DEBUG - HTTP请求详情: {"request_id": "04f36074", "timestamp": "2025-04-16T11:34:08.551503", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.071s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:08,625 - DEBUG - [8541ef71] HTTP请求开始 - GET None
2025-04-16 11:34:13,572 - WARNING - [cc956b2d] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.017s
2025-04-16 11:34:13,573 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:13,573 - DEBUG - HTTP请求详情: {"request_id": "cc956b2d", "timestamp": "2025-04-16T11:34:08.555672", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.017s"}
2025-04-16 11:34:13,575 - DEBUG - [97d00d4b] HTTP请求开始 - GET None
2025-04-16 11:34:13,618 - INFO - [97d00d4b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.044s
2025-04-16 11:34:13,620 - DEBUG - HTTP请求详情: {"request_id": "97d00d4b", "timestamp": "2025-04-16T11:34:13.575072", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.044s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:13,624 - DEBUG - [27a4e996] HTTP请求开始 - GET None
2025-04-16 11:34:13,632 - WARNING - [8541ef71] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.008s
2025-04-16 11:34:13,633 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:13,635 - DEBUG - HTTP请求详情: {"request_id": "8541ef71", "timestamp": "2025-04-16T11:34:08.625083", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.008s"}
2025-04-16 11:34:13,637 - DEBUG - [ece0b5c3] HTTP请求开始 - GET None
2025-04-16 11:34:13,716 - INFO - [27a4e996] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.093s
2025-04-16 11:34:13,718 - DEBUG - HTTP请求详情: {"request_id": "27a4e996", "timestamp": "2025-04-16T11:34:13.624101", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.093s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:13,727 - DEBUG - [8661c758] HTTP请求开始 - GET None
2025-04-16 11:34:18,657 - WARNING - [ece0b5c3] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.020s
2025-04-16 11:34:18,658 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:18,658 - DEBUG - HTTP请求详情: {"request_id": "ece0b5c3", "timestamp": "2025-04-16T11:34:13.637425", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.020s"}
2025-04-16 11:34:18,660 - DEBUG - [7a2af3a6] HTTP请求开始 - GET None
2025-04-16 11:34:18,731 - INFO - [7a2af3a6] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.072s
2025-04-16 11:34:18,733 - DEBUG - HTTP请求详情: {"request_id": "7a2af3a6", "timestamp": "2025-04-16T11:34:18.660252", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.072s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:18,735 - DEBUG - [036e9fcf] HTTP请求开始 - GET None
2025-04-16 11:34:18,800 - INFO - [036e9fcf] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.065s
2025-04-16 11:34:18,802 - DEBUG - HTTP请求详情: {"request_id": "036e9fcf", "timestamp": "2025-04-16T11:34:18.735556", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.065s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:18,806 - DEBUG - [5592d7e9] HTTP请求开始 - GET None
2025-04-16 11:34:18,860 - INFO - [5592d7e9] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.055s
2025-04-16 11:34:18,862 - DEBUG - HTTP请求详情: {"request_id": "5592d7e9", "timestamp": "2025-04-16T11:34:18.806200", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.055s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:18,866 - DEBUG - [fa00cba8] HTTP请求开始 - GET None
2025-04-16 11:34:18,912 - INFO - [fa00cba8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.046s
2025-04-16 11:34:18,917 - DEBUG - HTTP请求详情: {"request_id": "fa00cba8", "timestamp": "2025-04-16T11:34:18.866237", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.046s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:18,920 - DEBUG - [fe77d72d] HTTP请求开始 - GET None
2025-04-16 11:34:18,928 - WARNING - [8661c758] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.202s
2025-04-16 11:34:18,930 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:18,932 - DEBUG - HTTP请求详情: {"request_id": "8661c758", "timestamp": "2025-04-16T11:34:13.726919", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.202s"}
2025-04-16 11:34:18,937 - DEBUG - [f7652691] HTTP请求开始 - GET None
2025-04-16 11:34:18,965 - INFO - [fe77d72d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.044s
2025-04-16 11:34:18,967 - DEBUG - HTTP请求详情: {"request_id": "fe77d72d", "timestamp": "2025-04-16T11:34:18.920542", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.044s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:18,974 - DEBUG - [255db563] HTTP请求开始 - GET None
2025-04-16 11:34:23,953 - DEBUG - HTTP请求详情: {"request_id": "f7652691", "timestamp": "2025-04-16T11:34:18.936837", "method": "GET", "url": null}
2025-04-16 11:34:42,686 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:34:42,688 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:34:42,758 - DEBUG - [1f321164] HTTP请求开始 - GET None
2025-04-16 11:34:42,772 - DEBUG - [c09834d2] HTTP请求开始 - GET None
2025-04-16 11:34:47,798 - WARNING - [c09834d2] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.026s
2025-04-16 11:34:47,798 - WARNING - [1f321164] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.040s
2025-04-16 11:34:47,799 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:47,801 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:47,802 - DEBUG - HTTP请求详情: {"request_id": "c09834d2", "timestamp": "2025-04-16T11:34:42.772158", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.026s"}
2025-04-16 11:34:47,804 - DEBUG - HTTP请求详情: {"request_id": "1f321164", "timestamp": "2025-04-16T11:34:42.758234", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.040s"}
2025-04-16 11:34:47,807 - DEBUG - [6f73d907] HTTP请求开始 - GET None
2025-04-16 11:34:47,810 - DEBUG - [af1a4b01] HTTP请求开始 - GET None
2025-04-16 11:34:47,888 - INFO - [6f73d907] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.081s
2025-04-16 11:34:47,889 - DEBUG - HTTP请求详情: {"request_id": "6f73d907", "timestamp": "2025-04-16T11:34:47.807707", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.081s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:47,891 - DEBUG - [3808b9b2] HTTP请求开始 - GET None
2025-04-16 11:34:52,839 - WARNING - [af1a4b01] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.030s
2025-04-16 11:34:52,841 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:52,843 - DEBUG - HTTP请求详情: {"request_id": "af1a4b01", "timestamp": "2025-04-16T11:34:47.810397", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.030s"}
2025-04-16 11:34:52,846 - DEBUG - [eb19d734] HTTP请求开始 - GET None
2025-04-16 11:34:52,903 - WARNING - [3808b9b2] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.011s
2025-04-16 11:34:52,904 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:52,906 - DEBUG - HTTP请求详情: {"request_id": "3808b9b2", "timestamp": "2025-04-16T11:34:47.891650", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.011s"}
2025-04-16 11:34:52,909 - DEBUG - [02271349] HTTP请求开始 - GET None
2025-04-16 11:34:52,921 - INFO - [eb19d734] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.075s
2025-04-16 11:34:52,923 - DEBUG - HTTP请求详情: {"request_id": "eb19d734", "timestamp": "2025-04-16T11:34:52.846423", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.075s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:52,926 - DEBUG - [9f016e28] HTTP请求开始 - GET None
2025-04-16 11:34:57,923 - WARNING - [02271349] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:34:57,925 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:57,927 - DEBUG - HTTP请求详情: {"request_id": "02271349", "timestamp": "2025-04-16T11:34:52.909533", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:34:57,931 - DEBUG - [bb7d6ace] HTTP请求开始 - GET None
2025-04-16 11:34:57,970 - WARNING - [9f016e28] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.044s
2025-04-16 11:34:57,972 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:34:57,976 - DEBUG - HTTP请求详情: {"request_id": "9f016e28", "timestamp": "2025-04-16T11:34:52.926740", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.044s"}
2025-04-16 11:34:57,979 - DEBUG - [0d0e5d7e] HTTP请求开始 - GET None
2025-04-16 11:34:58,013 - INFO - [bb7d6ace] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.082s
2025-04-16 11:34:58,014 - DEBUG - HTTP请求详情: {"request_id": "bb7d6ace", "timestamp": "2025-04-16T11:34:57.930989", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.082s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:34:58,022 - DEBUG - [646c5b85] HTTP请求开始 - GET None
2025-04-16 11:35:03,005 - WARNING - [0d0e5d7e] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.026s
2025-04-16 11:35:03,006 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:03,008 - DEBUG - HTTP请求详情: {"request_id": "0d0e5d7e", "timestamp": "2025-04-16T11:34:57.979452", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.026s"}
2025-04-16 11:35:03,011 - DEBUG - [451694e4] HTTP请求开始 - GET None
2025-04-16 11:35:03,053 - WARNING - [646c5b85] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.031s
2025-04-16 11:35:03,055 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:03,057 - DEBUG - HTTP请求详情: {"request_id": "646c5b85", "timestamp": "2025-04-16T11:34:58.022511", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.031s"}
2025-04-16 11:35:03,061 - DEBUG - [522e8014] HTTP请求开始 - GET None
2025-04-16 11:35:03,082 - INFO - [451694e4] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.070s
2025-04-16 11:35:03,084 - DEBUG - HTTP请求详情: {"request_id": "451694e4", "timestamp": "2025-04-16T11:35:03.011715", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.070s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:03,088 - DEBUG - [334a7d79] HTTP请求开始 - GET None
2025-04-16 11:35:08,074 - WARNING - [522e8014] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.013s
2025-04-16 11:35:08,075 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:08,077 - DEBUG - HTTP请求详情: {"request_id": "522e8014", "timestamp": "2025-04-16T11:35:03.061485", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.013s"}
2025-04-16 11:35:08,079 - DEBUG - [b9636a9b] HTTP请求开始 - GET None
2025-04-16 11:35:08,097 - WARNING - [334a7d79] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:35:08,101 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:08,102 - DEBUG - HTTP请求详情: {"request_id": "334a7d79", "timestamp": "2025-04-16T11:35:03.088122", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:35:08,105 - DEBUG - [41a3c621] HTTP请求开始 - GET None
2025-04-16 11:35:08,128 - INFO - [b9636a9b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.049s
2025-04-16 11:35:08,129 - DEBUG - HTTP请求详情: {"request_id": "b9636a9b", "timestamp": "2025-04-16T11:35:08.079315", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.049s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:08,134 - DEBUG - [8df4dc95] HTTP请求开始 - GET None
2025-04-16 11:35:13,117 - WARNING - [41a3c621] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.011s
2025-04-16 11:35:13,117 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:13,118 - DEBUG - HTTP请求详情: {"request_id": "41a3c621", "timestamp": "2025-04-16T11:35:08.105555", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.011s"}
2025-04-16 11:35:13,120 - DEBUG - [84fc2801] HTTP请求开始 - GET None
2025-04-16 11:35:13,144 - WARNING - [8df4dc95] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:35:13,145 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:13,147 - DEBUG - HTTP请求详情: {"request_id": "8df4dc95", "timestamp": "2025-04-16T11:35:08.134471", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:35:13,150 - DEBUG - [4fd1e185] HTTP请求开始 - GET None
2025-04-16 11:35:13,162 - INFO - [84fc2801] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.042s
2025-04-16 11:35:13,163 - DEBUG - HTTP请求详情: {"request_id": "84fc2801", "timestamp": "2025-04-16T11:35:13.120278", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.042s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:13,166 - DEBUG - [897a2c30] HTTP请求开始 - GET None
2025-04-16 11:35:18,164 - WARNING - [4fd1e185] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:35:18,165 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:18,166 - DEBUG - HTTP请求详情: {"request_id": "4fd1e185", "timestamp": "2025-04-16T11:35:13.150112", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:35:18,169 - DEBUG - [7a02b897] HTTP请求开始 - GET None
2025-04-16 11:35:18,174 - WARNING - [897a2c30] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.008s
2025-04-16 11:35:18,175 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:18,175 - DEBUG - HTTP请求详情: {"request_id": "897a2c30", "timestamp": "2025-04-16T11:35:13.166438", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.008s"}
2025-04-16 11:35:18,177 - DEBUG - [1b0c291c] HTTP请求开始 - GET None
2025-04-16 11:35:18,249 - INFO - [7a02b897] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.080s
2025-04-16 11:35:18,250 - DEBUG - HTTP请求详情: {"request_id": "7a02b897", "timestamp": "2025-04-16T11:35:18.169498", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.080s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:18,253 - DEBUG - [0676b962] HTTP请求开始 - GET None
2025-04-16 11:35:23,182 - WARNING - [1b0c291c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.005s
2025-04-16 11:35:23,183 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:23,185 - DEBUG - HTTP请求详情: {"request_id": "1b0c291c", "timestamp": "2025-04-16T11:35:18.177619", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.005s"}
2025-04-16 11:35:23,187 - DEBUG - [38a96a65] HTTP请求开始 - GET None
2025-04-16 11:35:23,246 - INFO - [38a96a65] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.058s
2025-04-16 11:35:23,248 - DEBUG - HTTP请求详情: {"request_id": "38a96a65", "timestamp": "2025-04-16T11:35:23.187843", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.058s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:23,251 - DEBUG - [cd75cfa9] HTTP请求开始 - GET None
2025-04-16 11:35:23,267 - WARNING - [0676b962] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:35:23,268 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:23,270 - DEBUG - HTTP请求详情: {"request_id": "0676b962", "timestamp": "2025-04-16T11:35:18.253283", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:35:23,273 - DEBUG - [a3dd47ba] HTTP请求开始 - GET None
2025-04-16 11:35:23,335 - INFO - [cd75cfa9] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.084s
2025-04-16 11:35:23,337 - DEBUG - HTTP请求详情: {"request_id": "cd75cfa9", "timestamp": "2025-04-16T11:35:23.251555", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.084s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:23,340 - DEBUG - [718365c2] HTTP请求开始 - GET None
2025-04-16 11:35:28,309 - WARNING - [a3dd47ba] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.036s
2025-04-16 11:35:28,310 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:28,312 - DEBUG - HTTP请求详情: {"request_id": "a3dd47ba", "timestamp": "2025-04-16T11:35:23.273435", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.036s"}
2025-04-16 11:35:28,315 - DEBUG - [ad8388a0] HTTP请求开始 - GET None
2025-04-16 11:35:28,358 - INFO - [ad8388a0] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.043s
2025-04-16 11:35:28,359 - DEBUG - HTTP请求详情: {"request_id": "ad8388a0", "timestamp": "2025-04-16T11:35:28.315093", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.043s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:28,362 - DEBUG - [6406c4b9] HTTP请求开始 - GET None
2025-04-16 11:35:28,368 - WARNING - [718365c2] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.028s
2025-04-16 11:35:28,369 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:28,370 - DEBUG - HTTP请求详情: {"request_id": "718365c2", "timestamp": "2025-04-16T11:35:23.340079", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.028s"}
2025-04-16 11:35:28,373 - DEBUG - [50c86a7a] HTTP请求开始 - GET None
2025-04-16 11:35:28,405 - INFO - [6406c4b9] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.043s
2025-04-16 11:35:28,407 - DEBUG - HTTP请求详情: {"request_id": "6406c4b9", "timestamp": "2025-04-16T11:35:28.362332", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.043s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:28,409 - DEBUG - [b7dfdb00] HTTP请求开始 - GET None
2025-04-16 11:35:33,382 - WARNING - [50c86a7a] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.009s
2025-04-16 11:35:33,383 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:33,385 - DEBUG - HTTP请求详情: {"request_id": "50c86a7a", "timestamp": "2025-04-16T11:35:28.373280", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.009s"}
2025-04-16 11:35:33,388 - DEBUG - [194b81b6] HTTP请求开始 - GET None
2025-04-16 11:35:33,417 - WARNING - [b7dfdb00] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.008s
2025-04-16 11:35:33,418 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:33,419 - DEBUG - HTTP请求详情: {"request_id": "b7dfdb00", "timestamp": "2025-04-16T11:35:28.409604", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.008s"}
2025-04-16 11:35:33,422 - DEBUG - [ce10e503] HTTP请求开始 - GET None
2025-04-16 11:35:33,426 - INFO - [194b81b6] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.038s
2025-04-16 11:35:33,430 - DEBUG - HTTP请求详情: {"request_id": "194b81b6", "timestamp": "2025-04-16T11:35:33.388002", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.038s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:33,433 - DEBUG - [96929157] HTTP请求开始 - GET None
2025-04-16 11:35:38,453 - WARNING - [ce10e503] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.031s
2025-04-16 11:35:38,453 - WARNING - [96929157] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.021s
2025-04-16 11:35:38,454 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:38,455 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:38,456 - DEBUG - HTTP请求详情: {"request_id": "ce10e503", "timestamp": "2025-04-16T11:35:33.422702", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.031s"}
2025-04-16 11:35:38,458 - DEBUG - HTTP请求详情: {"request_id": "96929157", "timestamp": "2025-04-16T11:35:33.433226", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.021s"}
2025-04-16 11:35:38,462 - DEBUG - [b0660a37] HTTP请求开始 - GET None
2025-04-16 11:35:38,464 - DEBUG - [674ea3c1] HTTP请求开始 - GET None
2025-04-16 11:35:38,505 - INFO - [b0660a37] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.043s
2025-04-16 11:35:38,506 - DEBUG - HTTP请求详情: {"request_id": "b0660a37", "timestamp": "2025-04-16T11:35:38.462119", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.043s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:38,509 - DEBUG - [822b733a] HTTP请求开始 - GET None
2025-04-16 11:35:43,474 - WARNING - [674ea3c1] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:35:43,475 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:43,475 - DEBUG - HTTP请求详情: {"request_id": "674ea3c1", "timestamp": "2025-04-16T11:35:38.464585", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:35:43,476 - DEBUG - [10a2c75f] HTTP请求开始 - GET None
2025-04-16 11:35:43,521 - WARNING - [822b733a] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.013s
2025-04-16 11:35:43,522 - INFO - [10a2c75f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 11:35:43,522 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:43,523 - DEBUG - HTTP请求详情: {"request_id": "10a2c75f", "timestamp": "2025-04-16T11:35:43.476762", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:43,524 - DEBUG - HTTP请求详情: {"request_id": "822b733a", "timestamp": "2025-04-16T11:35:38.508961", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.013s"}
2025-04-16 11:35:43,525 - DEBUG - [bafb6b1b] HTTP请求开始 - GET None
2025-04-16 11:35:43,527 - DEBUG - [7ffd91a4] HTTP请求开始 - GET None
2025-04-16 11:35:43,580 - INFO - [bafb6b1b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.055s
2025-04-16 11:35:43,581 - DEBUG - HTTP请求详情: {"request_id": "bafb6b1b", "timestamp": "2025-04-16T11:35:43.525109", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.055s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:43,582 - DEBUG - [420350dd] HTTP请求开始 - GET None
2025-04-16 11:35:48,549 - WARNING - [7ffd91a4] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.022s
2025-04-16 11:35:48,550 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:48,552 - DEBUG - HTTP请求详情: {"request_id": "7ffd91a4", "timestamp": "2025-04-16T11:35:43.527858", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.022s"}
2025-04-16 11:35:48,554 - DEBUG - [ce158c0c] HTTP请求开始 - GET None
2025-04-16 11:35:48,594 - WARNING - [420350dd] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:35:48,596 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:48,597 - DEBUG - HTTP请求详情: {"request_id": "420350dd", "timestamp": "2025-04-16T11:35:43.582682", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:35:48,601 - DEBUG - [f7ce99ff] HTTP请求开始 - GET None
2025-04-16 11:35:48,646 - INFO - [ce158c0c] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.092s
2025-04-16 11:35:48,648 - DEBUG - HTTP请求详情: {"request_id": "ce158c0c", "timestamp": "2025-04-16T11:35:48.554889", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.092s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:48,650 - DEBUG - [585a2503] HTTP请求开始 - GET None
2025-04-16 11:35:53,645 - WARNING - [f7ce99ff] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.045s
2025-04-16 11:35:53,646 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:53,647 - DEBUG - HTTP请求详情: {"request_id": "f7ce99ff", "timestamp": "2025-04-16T11:35:48.601159", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.045s"}
2025-04-16 11:35:53,649 - DEBUG - [933f96a0] HTTP请求开始 - GET None
2025-04-16 11:35:53,665 - WARNING - [585a2503] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:35:53,666 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:53,667 - DEBUG - HTTP请求详情: {"request_id": "585a2503", "timestamp": "2025-04-16T11:35:48.650856", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:35:53,671 - DEBUG - [0311040d] HTTP请求开始 - GET None
2025-04-16 11:35:53,761 - INFO - [933f96a0] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.111s
2025-04-16 11:35:53,762 - DEBUG - HTTP请求详情: {"request_id": "933f96a0", "timestamp": "2025-04-16T11:35:53.649858", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.111s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:53,765 - DEBUG - [ea4b1777] HTTP请求开始 - GET None
2025-04-16 11:35:58,686 - WARNING - [0311040d] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:35:58,688 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:58,689 - DEBUG - HTTP请求详情: {"request_id": "0311040d", "timestamp": "2025-04-16T11:35:53.671222", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:35:58,695 - DEBUG - [8a5f6fe0] HTTP请求开始 - GET None
2025-04-16 11:35:58,755 - INFO - [8a5f6fe0] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.061s
2025-04-16 11:35:58,757 - DEBUG - HTTP请求详情: {"request_id": "8a5f6fe0", "timestamp": "2025-04-16T11:35:58.695259", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.061s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:58,762 - DEBUG - [2edb0e5f] HTTP请求开始 - GET None
2025-04-16 11:35:58,773 - WARNING - [ea4b1777] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.008s
2025-04-16 11:35:58,775 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:35:58,777 - DEBUG - HTTP请求详情: {"request_id": "ea4b1777", "timestamp": "2025-04-16T11:35:53.765591", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.008s"}
2025-04-16 11:35:58,781 - DEBUG - [c94ee0e9] HTTP请求开始 - GET None
2025-04-16 11:35:58,839 - INFO - [2edb0e5f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.077s
2025-04-16 11:35:58,842 - DEBUG - HTTP请求详情: {"request_id": "2edb0e5f", "timestamp": "2025-04-16T11:35:58.762724", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.077s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:35:58,845 - DEBUG - [e65db886] HTTP请求开始 - GET None
2025-04-16 11:36:03,797 - WARNING - [c94ee0e9] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:36:03,799 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:03,800 - DEBUG - HTTP请求详情: {"request_id": "c94ee0e9", "timestamp": "2025-04-16T11:35:58.781498", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:36:03,803 - DEBUG - [03d82db4] HTTP请求开始 - GET None
2025-04-16 11:36:03,844 - INFO - [03d82db4] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 11:36:03,845 - DEBUG - HTTP请求详情: {"request_id": "03d82db4", "timestamp": "2025-04-16T11:36:03.803102", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:03,849 - DEBUG - [75478297] HTTP请求开始 - GET None
2025-04-16 11:36:03,855 - WARNING - [e65db886] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:36:03,856 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:03,859 - DEBUG - HTTP请求详情: {"request_id": "e65db886", "timestamp": "2025-04-16T11:35:58.845506", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:36:03,862 - DEBUG - [c962934a] HTTP请求开始 - GET None
2025-04-16 11:36:03,901 - INFO - [75478297] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.052s
2025-04-16 11:36:03,903 - DEBUG - HTTP请求详情: {"request_id": "75478297", "timestamp": "2025-04-16T11:36:03.849305", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.052s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:03,905 - DEBUG - [cb040b5b] HTTP请求开始 - GET None
2025-04-16 11:36:08,885 - WARNING - [c962934a] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.023s
2025-04-16 11:36:08,886 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:08,888 - DEBUG - HTTP请求详情: {"request_id": "c962934a", "timestamp": "2025-04-16T11:36:03.862574", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.023s"}
2025-04-16 11:36:08,891 - DEBUG - [adb76d27] HTTP请求开始 - GET None
2025-04-16 11:36:08,913 - WARNING - [cb040b5b] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.008s
2025-04-16 11:36:08,914 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:08,915 - DEBUG - HTTP请求详情: {"request_id": "cb040b5b", "timestamp": "2025-04-16T11:36:03.905269", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.008s"}
2025-04-16 11:36:08,917 - DEBUG - [63cab611] HTTP请求开始 - GET None
2025-04-16 11:36:08,929 - INFO - [adb76d27] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.038s
2025-04-16 11:36:08,930 - DEBUG - HTTP请求详情: {"request_id": "adb76d27", "timestamp": "2025-04-16T11:36:08.891033", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.038s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:08,932 - DEBUG - [5c808a73] HTTP请求开始 - GET None
2025-04-16 11:36:13,934 - WARNING - [63cab611] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.017s
2025-04-16 11:36:13,935 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:13,936 - DEBUG - HTTP请求详情: {"request_id": "63cab611", "timestamp": "2025-04-16T11:36:08.917400", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.017s"}
2025-04-16 11:36:13,939 - DEBUG - [9ff40e19] HTTP请求开始 - GET None
2025-04-16 11:36:13,944 - WARNING - [5c808a73] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:36:13,945 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:13,947 - DEBUG - HTTP请求详情: {"request_id": "5c808a73", "timestamp": "2025-04-16T11:36:08.932678", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:36:13,949 - DEBUG - [43ed9370] HTTP请求开始 - GET None
2025-04-16 11:36:13,989 - INFO - [9ff40e19] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.050s
2025-04-16 11:36:13,991 - DEBUG - HTTP请求详情: {"request_id": "9ff40e19", "timestamp": "2025-04-16T11:36:13.939555", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.050s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:13,993 - DEBUG - [b0896c4a] HTTP请求开始 - GET None
2025-04-16 11:36:18,954 - WARNING - [43ed9370] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.005s
2025-04-16 11:36:18,956 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:18,957 - DEBUG - HTTP请求详情: {"request_id": "43ed9370", "timestamp": "2025-04-16T11:36:13.949049", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.005s"}
2025-04-16 11:36:18,959 - DEBUG - [b9ed9bef] HTTP请求开始 - GET None
2025-04-16 11:36:18,998 - INFO - [b9ed9bef] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.038s
2025-04-16 11:36:18,998 - WARNING - [b0896c4a] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.005s
2025-04-16 11:36:18,999 - DEBUG - HTTP请求详情: {"request_id": "b9ed9bef", "timestamp": "2025-04-16T11:36:18.959836", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.038s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:19,000 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:19,003 - DEBUG - HTTP请求详情: {"request_id": "b0896c4a", "timestamp": "2025-04-16T11:36:13.993171", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.005s"}
2025-04-16 11:36:19,004 - DEBUG - [635e5ce6] HTTP请求开始 - GET None
2025-04-16 11:36:19,010 - DEBUG - [25aa2fe6] HTTP请求开始 - GET None
2025-04-16 11:36:19,157 - INFO - [635e5ce6] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.153s
2025-04-16 11:36:19,159 - DEBUG - HTTP请求详情: {"request_id": "635e5ce6", "timestamp": "2025-04-16T11:36:19.004213", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.153s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:19,162 - DEBUG - [e74012eb] HTTP请求开始 - GET None
2025-04-16 11:36:24,030 - WARNING - [25aa2fe6] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.020s
2025-04-16 11:36:24,032 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:24,033 - DEBUG - HTTP请求详情: {"request_id": "25aa2fe6", "timestamp": "2025-04-16T11:36:19.010567", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.020s"}
2025-04-16 11:36:24,036 - DEBUG - [4726c7dd] HTTP请求开始 - GET None
2025-04-16 11:36:24,076 - INFO - [4726c7dd] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.040s
2025-04-16 11:36:24,077 - DEBUG - HTTP请求详情: {"request_id": "4726c7dd", "timestamp": "2025-04-16T11:36:24.036230", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.040s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:24,080 - DEBUG - [3de3da53] HTTP请求开始 - GET None
2025-04-16 11:36:24,125 - INFO - [3de3da53] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 11:36:24,127 - DEBUG - HTTP请求详情: {"request_id": "3de3da53", "timestamp": "2025-04-16T11:36:24.080541", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:24,130 - DEBUG - [feefc120] HTTP请求开始 - GET None
2025-04-16 11:36:24,175 - INFO - [feefc120] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.046s
2025-04-16 11:36:24,177 - DEBUG - HTTP请求详情: {"request_id": "feefc120", "timestamp": "2025-04-16T11:36:24.130152", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.046s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:24,179 - DEBUG - [c0b90e6d] HTTP请求开始 - GET None
2025-04-16 11:36:24,192 - WARNING - [e74012eb] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.030s
2025-04-16 11:36:24,193 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:24,194 - DEBUG - HTTP请求详情: {"request_id": "e74012eb", "timestamp": "2025-04-16T11:36:19.162258", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.030s"}
2025-04-16 11:36:24,197 - DEBUG - [57a64911] HTTP请求开始 - GET None
2025-04-16 11:36:24,211 - INFO - [c0b90e6d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.032s
2025-04-16 11:36:24,213 - DEBUG - HTTP请求详情: {"request_id": "c0b90e6d", "timestamp": "2025-04-16T11:36:24.179894", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.032s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:24,216 - DEBUG - [b30f6d75] HTTP请求开始 - GET None
2025-04-16 11:36:29,220 - WARNING - [57a64911] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.023s
2025-04-16 11:36:29,221 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:29,222 - DEBUG - HTTP请求详情: {"request_id": "57a64911", "timestamp": "2025-04-16T11:36:24.197564", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.023s"}
2025-04-16 11:36:29,225 - DEBUG - [a950215e] HTTP请求开始 - GET None
2025-04-16 11:36:29,247 - WARNING - [b30f6d75] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.031s
2025-04-16 11:36:29,248 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:29,249 - DEBUG - HTTP请求详情: {"request_id": "b30f6d75", "timestamp": "2025-04-16T11:36:24.216219", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.031s"}
2025-04-16 11:36:29,252 - DEBUG - [9a650755] HTTP请求开始 - GET None
2025-04-16 11:36:29,272 - INFO - [a950215e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.047s
2025-04-16 11:36:29,274 - DEBUG - HTTP请求详情: {"request_id": "a950215e", "timestamp": "2025-04-16T11:36:29.225786", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.047s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:29,277 - DEBUG - [be91b96c] HTTP请求开始 - GET None
2025-04-16 11:36:34,267 - WARNING - [9a650755] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:36:34,269 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:34,270 - DEBUG - HTTP请求详情: {"request_id": "9a650755", "timestamp": "2025-04-16T11:36:29.252558", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:36:34,273 - DEBUG - [702c280e] HTTP请求开始 - GET None
2025-04-16 11:36:34,292 - WARNING - [be91b96c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.014s
2025-04-16 11:36:34,293 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:34,294 - DEBUG - HTTP请求详情: {"request_id": "be91b96c", "timestamp": "2025-04-16T11:36:29.277674", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.014s"}
2025-04-16 11:36:34,297 - DEBUG - [86cc87b0] HTTP请求开始 - GET None
2025-04-16 11:36:34,327 - INFO - [702c280e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.054s
2025-04-16 11:36:34,328 - DEBUG - HTTP请求详情: {"request_id": "702c280e", "timestamp": "2025-04-16T11:36:34.273392", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.054s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:34,331 - DEBUG - [6a64eade] HTTP请求开始 - GET None
2025-04-16 11:36:39,317 - WARNING - [86cc87b0] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.020s
2025-04-16 11:36:39,318 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:39,319 - DEBUG - HTTP请求详情: {"request_id": "86cc87b0", "timestamp": "2025-04-16T11:36:34.297344", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.020s"}
2025-04-16 11:36:39,322 - DEBUG - [24f8f344] HTTP请求开始 - GET None
2025-04-16 11:36:39,350 - WARNING - [6a64eade] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.019s
2025-04-16 11:36:39,351 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:39,353 - DEBUG - HTTP请求详情: {"request_id": "6a64eade", "timestamp": "2025-04-16T11:36:34.331359", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.019s"}
2025-04-16 11:36:39,356 - DEBUG - [6da39cba] HTTP请求开始 - GET None
2025-04-16 11:36:39,368 - INFO - [24f8f344] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.046s
2025-04-16 11:36:39,370 - DEBUG - HTTP请求详情: {"request_id": "24f8f344", "timestamp": "2025-04-16T11:36:39.322704", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.046s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:39,374 - DEBUG - [7ef74a17] HTTP请求开始 - GET None
2025-04-16 11:36:44,372 - WARNING - [6da39cba] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:36:44,373 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:44,373 - DEBUG - HTTP请求详情: {"request_id": "6da39cba", "timestamp": "2025-04-16T11:36:39.356335", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:36:44,374 - DEBUG - [02eab7f0] HTTP请求开始 - GET None
2025-04-16 11:36:44,395 - WARNING - [7ef74a17] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.022s
2025-04-16 11:36:44,396 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:44,397 - DEBUG - HTTP请求详情: {"request_id": "7ef74a17", "timestamp": "2025-04-16T11:36:39.374393", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.022s"}
2025-04-16 11:36:44,399 - DEBUG - [54006952] HTTP请求开始 - GET None
2025-04-16 11:36:44,416 - INFO - [02eab7f0] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 11:36:44,417 - DEBUG - HTTP请求详情: {"request_id": "02eab7f0", "timestamp": "2025-04-16T11:36:44.374930", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:44,418 - DEBUG - [22475496] HTTP请求开始 - GET None
2025-04-16 11:36:49,414 - WARNING - [54006952] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:36:49,416 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:49,417 - DEBUG - HTTP请求详情: {"request_id": "54006952", "timestamp": "2025-04-16T11:36:44.399210", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:36:49,420 - DEBUG - [967ed282] HTTP请求开始 - GET None
2025-04-16 11:36:49,439 - WARNING - [22475496] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.020s
2025-04-16 11:36:49,440 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:49,441 - DEBUG - HTTP请求详情: {"request_id": "22475496", "timestamp": "2025-04-16T11:36:44.418758", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.020s"}
2025-04-16 11:36:49,444 - DEBUG - [c5d28371] HTTP请求开始 - GET None
2025-04-16 11:36:49,470 - INFO - [967ed282] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.050s
2025-04-16 11:36:49,471 - DEBUG - HTTP请求详情: {"request_id": "967ed282", "timestamp": "2025-04-16T11:36:49.419962", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.050s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:49,473 - DEBUG - [2d3c6c1d] HTTP请求开始 - GET None
2025-04-16 11:36:54,460 - WARNING - [c5d28371] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:36:54,461 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:54,462 - DEBUG - HTTP请求详情: {"request_id": "c5d28371", "timestamp": "2025-04-16T11:36:49.444363", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:36:54,463 - DEBUG - [80eaa6b8] HTTP请求开始 - GET None
2025-04-16 11:36:54,509 - INFO - [80eaa6b8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 11:36:54,509 - WARNING - [2d3c6c1d] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.036s
2025-04-16 11:36:54,510 - DEBUG - HTTP请求详情: {"request_id": "80eaa6b8", "timestamp": "2025-04-16T11:36:54.463742", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:54,511 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:54,512 - DEBUG - [d2b47f51] HTTP请求开始 - GET None
2025-04-16 11:36:54,513 - DEBUG - HTTP请求详情: {"request_id": "2d3c6c1d", "timestamp": "2025-04-16T11:36:49.473163", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.036s"}
2025-04-16 11:36:54,516 - DEBUG - [3972638b] HTTP请求开始 - GET None
2025-04-16 11:36:54,580 - INFO - [d2b47f51] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.068s
2025-04-16 11:36:54,581 - DEBUG - HTTP请求详情: {"request_id": "d2b47f51", "timestamp": "2025-04-16T11:36:54.512854", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.068s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:54,584 - DEBUG - [5c9399f8] HTTP请求开始 - GET None
2025-04-16 11:36:59,528 - WARNING - [3972638b] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:36:59,529 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:59,530 - DEBUG - HTTP请求详情: {"request_id": "3972638b", "timestamp": "2025-04-16T11:36:54.515964", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:36:59,533 - DEBUG - [6f4fedb3] HTTP请求开始 - GET None
2025-04-16 11:36:59,581 - INFO - [6f4fedb3] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.048s
2025-04-16 11:36:59,582 - DEBUG - HTTP请求详情: {"request_id": "6f4fedb3", "timestamp": "2025-04-16T11:36:59.533771", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.048s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:59,586 - DEBUG - [6f8d62e7] HTTP请求开始 - GET None
2025-04-16 11:36:59,605 - WARNING - [5c9399f8] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.021s
2025-04-16 11:36:59,606 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:36:59,608 - DEBUG - HTTP请求详情: {"request_id": "5c9399f8", "timestamp": "2025-04-16T11:36:54.584407", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.021s"}
2025-04-16 11:36:59,610 - DEBUG - [bf2fcc4c] HTTP请求开始 - GET None
2025-04-16 11:36:59,623 - INFO - [6f8d62e7] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.037s
2025-04-16 11:36:59,624 - DEBUG - HTTP请求详情: {"request_id": "6f8d62e7", "timestamp": "2025-04-16T11:36:59.586050", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.037s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:36:59,626 - DEBUG - [db76d06d] HTTP请求开始 - GET None
2025-04-16 11:37:04,648 - WARNING - [bf2fcc4c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.037s
2025-04-16 11:37:04,649 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:37:04,651 - DEBUG - HTTP请求详情: {"request_id": "bf2fcc4c", "timestamp": "2025-04-16T11:36:59.610820", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.037s"}
2025-04-16 11:37:04,654 - DEBUG - [92d5930f] HTTP请求开始 - GET None
2025-04-16 11:37:04,658 - DEBUG - HTTP请求详情: {"request_id": "db76d06d", "timestamp": "2025-04-16T11:36:59.626804", "method": "GET", "url": null}
2025-04-16 11:41:25,775 - INFO - HTTP请求日志初始化完成，日志文件: C:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:41:25,776 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:41:25,787 - DEBUG - [1a299708] HTTP请求开始 - GET None
2025-04-16 11:41:25,796 - DEBUG - [68af5afd] HTTP请求开始 - GET None
2025-04-16 11:41:30,802 - WARNING - [1a299708] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:41:30,803 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:30,804 - DEBUG - HTTP请求详情: {"request_id": "1a299708", "timestamp": "2025-04-16T11:41:25.787660", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:41:30,806 - DEBUG - [8495cb52] HTTP请求开始 - GET None
2025-04-16 11:41:30,812 - WARNING - [68af5afd] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:41:30,814 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:30,818 - DEBUG - HTTP请求详情: {"request_id": "68af5afd", "timestamp": "2025-04-16T11:41:25.796760", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:41:30,822 - DEBUG - [f46025be] HTTP请求开始 - GET None
2025-04-16 11:41:30,859 - INFO - [8495cb52] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.053s
2025-04-16 11:41:30,860 - DEBUG - HTTP请求详情: {"request_id": "8495cb52", "timestamp": "2025-04-16T11:41:30.806104", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.053s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:30,863 - DEBUG - [c3233b8b] HTTP请求开始 - GET None
2025-04-16 11:41:35,851 - WARNING - [f46025be] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.029s
2025-04-16 11:41:35,853 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:35,854 - DEBUG - HTTP请求详情: {"request_id": "f46025be", "timestamp": "2025-04-16T11:41:30.822335", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.029s"}
2025-04-16 11:41:35,857 - DEBUG - [272360f8] HTTP请求开始 - GET None
2025-04-16 11:41:35,880 - WARNING - [c3233b8b] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.018s
2025-04-16 11:41:35,883 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:35,885 - DEBUG - HTTP请求详情: {"request_id": "c3233b8b", "timestamp": "2025-04-16T11:41:30.863034", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.018s"}
2025-04-16 11:41:35,890 - DEBUG - [6097d5dc] HTTP请求开始 - GET None
2025-04-16 11:41:35,896 - INFO - [272360f8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.039s
2025-04-16 11:41:35,900 - DEBUG - HTTP请求详情: {"request_id": "272360f8", "timestamp": "2025-04-16T11:41:35.857640", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.039s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:35,906 - DEBUG - [1f1bae56] HTTP请求开始 - GET None
2025-04-16 11:41:40,917 - WARNING - [6097d5dc] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.027s
2025-04-16 11:41:40,918 - WARNING - [1f1bae56] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:41:40,918 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:40,920 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:40,920 - DEBUG - HTTP请求详情: {"request_id": "6097d5dc", "timestamp": "2025-04-16T11:41:35.890573", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.027s"}
2025-04-16 11:41:40,921 - DEBUG - HTTP请求详情: {"request_id": "1f1bae56", "timestamp": "2025-04-16T11:41:35.906054", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:41:40,924 - DEBUG - [a3bc9c48] HTTP请求开始 - GET None
2025-04-16 11:41:40,924 - DEBUG - [7c260304] HTTP请求开始 - GET None
2025-04-16 11:41:40,958 - INFO - [a3bc9c48] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.034s
2025-04-16 11:41:40,959 - DEBUG - HTTP请求详情: {"request_id": "a3bc9c48", "timestamp": "2025-04-16T11:41:40.924227", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.034s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:40,960 - DEBUG - [71740989] HTTP请求开始 - GET None
2025-04-16 11:41:45,931 - WARNING - [7c260304] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.007s
2025-04-16 11:41:45,932 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:45,933 - DEBUG - HTTP请求详情: {"request_id": "7c260304", "timestamp": "2025-04-16T11:41:40.924664", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.007s"}
2025-04-16 11:41:45,935 - DEBUG - [e2efbc96] HTTP请求开始 - GET None
2025-04-16 11:41:45,978 - WARNING - [71740989] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.018s
2025-04-16 11:41:45,979 - INFO - [e2efbc96] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.044s
2025-04-16 11:41:45,981 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:45,982 - DEBUG - HTTP请求详情: {"request_id": "e2efbc96", "timestamp": "2025-04-16T11:41:45.935640", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.044s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:45,983 - DEBUG - HTTP请求详情: {"request_id": "71740989", "timestamp": "2025-04-16T11:41:40.960644", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.018s"}
2025-04-16 11:41:45,985 - DEBUG - [0f071a86] HTTP请求开始 - GET None
2025-04-16 11:41:45,987 - DEBUG - [f6e33642] HTTP请求开始 - GET None
2025-04-16 11:41:46,028 - INFO - [0f071a86] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.043s
2025-04-16 11:41:46,031 - DEBUG - HTTP请求详情: {"request_id": "0f071a86", "timestamp": "2025-04-16T11:41:45.985497", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.043s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:46,033 - DEBUG - [6b04e887] HTTP请求开始 - GET None
2025-04-16 11:41:50,993 - WARNING - [f6e33642] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.006s
2025-04-16 11:41:50,994 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:50,996 - DEBUG - HTTP请求详情: {"request_id": "f6e33642", "timestamp": "2025-04-16T11:41:45.986983", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.006s"}
2025-04-16 11:41:50,998 - DEBUG - [7435597d] HTTP请求开始 - GET None
2025-04-16 11:41:51,049 - WARNING - [6b04e887] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:41:51,050 - INFO - [7435597d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.051s
2025-04-16 11:41:51,051 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:51,051 - DEBUG - HTTP请求详情: {"request_id": "7435597d", "timestamp": "2025-04-16T11:41:50.998602", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.051s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:51,052 - DEBUG - HTTP请求详情: {"request_id": "6b04e887", "timestamp": "2025-04-16T11:41:46.033471", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:41:51,054 - DEBUG - [6e61cef1] HTTP请求开始 - GET None
2025-04-16 11:41:51,056 - DEBUG - [a0bd3b8e] HTTP请求开始 - GET None
2025-04-16 11:41:51,091 - INFO - [6e61cef1] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.037s
2025-04-16 11:41:51,093 - DEBUG - HTTP请求详情: {"request_id": "6e61cef1", "timestamp": "2025-04-16T11:41:51.054713", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.037s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:51,097 - DEBUG - [43731e08] HTTP请求开始 - GET None
2025-04-16 11:41:56,081 - WARNING - [a0bd3b8e] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.025s
2025-04-16 11:41:56,082 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:56,083 - DEBUG - HTTP请求详情: {"request_id": "a0bd3b8e", "timestamp": "2025-04-16T11:41:51.056309", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.025s"}
2025-04-16 11:41:56,085 - DEBUG - [65a43c5a] HTTP请求开始 - GET None
2025-04-16 11:41:56,119 - WARNING - [43731e08] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.022s
2025-04-16 11:41:56,120 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:41:56,121 - DEBUG - HTTP请求详情: {"request_id": "43731e08", "timestamp": "2025-04-16T11:41:51.097213", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.022s"}
2025-04-16 11:41:56,122 - DEBUG - [e14251e0] HTTP请求开始 - GET None
2025-04-16 11:41:56,135 - INFO - [65a43c5a] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.050s
2025-04-16 11:41:56,136 - DEBUG - HTTP请求详情: {"request_id": "65a43c5a", "timestamp": "2025-04-16T11:41:56.085058", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.050s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:41:56,137 - DEBUG - [be9b5844] HTTP请求开始 - GET None
2025-04-16 11:42:01,135 - WARNING - [e14251e0] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.013s
2025-04-16 11:42:01,136 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:01,137 - DEBUG - HTTP请求详情: {"request_id": "e14251e0", "timestamp": "2025-04-16T11:41:56.122514", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.013s"}
2025-04-16 11:42:01,138 - DEBUG - [0a5fd23b] HTTP请求开始 - GET None
2025-04-16 11:42:01,142 - WARNING - [be9b5844] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.004s
2025-04-16 11:42:01,142 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:01,143 - DEBUG - HTTP请求详情: {"request_id": "be9b5844", "timestamp": "2025-04-16T11:41:56.137782", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.004s"}
2025-04-16 11:42:01,145 - DEBUG - [8af7bc57] HTTP请求开始 - GET None
2025-04-16 11:42:01,182 - INFO - [0a5fd23b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.044s
2025-04-16 11:42:01,184 - DEBUG - HTTP请求详情: {"request_id": "0a5fd23b", "timestamp": "2025-04-16T11:42:01.138495", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.044s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:01,186 - DEBUG - [76a5302c] HTTP请求开始 - GET None
2025-04-16 11:42:06,150 - WARNING - [8af7bc57] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.005s
2025-04-16 11:42:06,151 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:06,152 - DEBUG - HTTP请求详情: {"request_id": "8af7bc57", "timestamp": "2025-04-16T11:42:01.145083", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.005s"}
2025-04-16 11:42:06,154 - DEBUG - [0fd8121f] HTTP请求开始 - GET None
2025-04-16 11:42:06,182 - INFO - [0fd8121f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.028s
2025-04-16 11:42:06,183 - DEBUG - HTTP请求详情: {"request_id": "0fd8121f", "timestamp": "2025-04-16T11:42:06.154702", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.028s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:06,185 - DEBUG - [5d1542b5] HTTP请求开始 - GET None
2025-04-16 11:42:06,196 - WARNING - [76a5302c] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:42:06,197 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:06,198 - DEBUG - HTTP请求详情: {"request_id": "76a5302c", "timestamp": "2025-04-16T11:42:01.186328", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:42:06,199 - DEBUG - [5897dc51] HTTP请求开始 - GET None
2025-04-16 11:42:06,221 - INFO - [5d1542b5] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.036s
2025-04-16 11:42:06,222 - DEBUG - HTTP请求详情: {"request_id": "5d1542b5", "timestamp": "2025-04-16T11:42:06.185372", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.036s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:06,225 - DEBUG - [375545d6] HTTP请求开始 - GET None
2025-04-16 11:42:11,216 - WARNING - [5897dc51] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.016s
2025-04-16 11:42:11,217 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:11,218 - DEBUG - HTTP请求详情: {"request_id": "5897dc51", "timestamp": "2025-04-16T11:42:06.199909", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.016s"}
2025-04-16 11:42:11,219 - DEBUG - [cb3c13ab] HTTP请求开始 - GET None
2025-04-16 11:42:11,231 - WARNING - [375545d6] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.007s
2025-04-16 11:42:11,232 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:11,233 - DEBUG - HTTP请求详情: {"request_id": "375545d6", "timestamp": "2025-04-16T11:42:06.225027", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.007s"}
2025-04-16 11:42:11,235 - DEBUG - [6479d122] HTTP请求开始 - GET None
2025-04-16 11:42:11,259 - INFO - [cb3c13ab] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.040s
2025-04-16 11:42:11,261 - DEBUG - HTTP请求详情: {"request_id": "cb3c13ab", "timestamp": "2025-04-16T11:42:11.219403", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.040s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:11,265 - DEBUG - [8045adaf] HTTP请求开始 - GET None
2025-04-16 11:42:16,246 - WARNING - [6479d122] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:42:16,248 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:16,249 - DEBUG - HTTP请求详情: {"request_id": "6479d122", "timestamp": "2025-04-16T11:42:11.235053", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:42:16,251 - DEBUG - [97613fc4] HTTP请求开始 - GET None
2025-04-16 11:42:16,270 - WARNING - [8045adaf] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.005s
2025-04-16 11:42:16,271 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:16,272 - DEBUG - HTTP请求详情: {"request_id": "8045adaf", "timestamp": "2025-04-16T11:42:11.265653", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.005s"}
2025-04-16 11:42:16,275 - DEBUG - [fd85e53b] HTTP请求开始 - GET None
2025-04-16 11:42:16,285 - INFO - [97613fc4] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.035s
2025-04-16 11:42:16,286 - DEBUG - HTTP请求详情: {"request_id": "97613fc4", "timestamp": "2025-04-16T11:42:16.251185", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.035s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:16,288 - DEBUG - [1ae3399f] HTTP请求开始 - GET None
2025-04-16 11:42:21,290 - WARNING - [fd85e53b] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:42:21,292 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:21,293 - DEBUG - HTTP请求详情: {"request_id": "fd85e53b", "timestamp": "2025-04-16T11:42:16.274977", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:42:21,296 - DEBUG - [24092f00] HTTP请求开始 - GET None
2025-04-16 11:42:21,315 - WARNING - [1ae3399f] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.027s
2025-04-16 11:42:21,316 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:21,317 - DEBUG - HTTP请求详情: {"request_id": "1ae3399f", "timestamp": "2025-04-16T11:42:16.288424", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.027s"}
2025-04-16 11:42:21,319 - DEBUG - [c12fa06b] HTTP请求开始 - GET None
2025-04-16 11:42:21,340 - INFO - [24092f00] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 11:42:21,342 - DEBUG - HTTP请求详情: {"request_id": "24092f00", "timestamp": "2025-04-16T11:42:21.296257", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:21,347 - DEBUG - [273a9d10] HTTP请求开始 - GET None
2025-04-16 11:42:26,344 - WARNING - [c12fa06b] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.025s
2025-04-16 11:42:26,345 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:26,346 - DEBUG - HTTP请求详情: {"request_id": "c12fa06b", "timestamp": "2025-04-16T11:42:21.319018", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.025s"}
2025-04-16 11:42:26,347 - DEBUG - [9c2d300f] HTTP请求开始 - GET None
2025-04-16 11:42:26,351 - WARNING - [273a9d10] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.005s
2025-04-16 11:42:26,352 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:26,353 - DEBUG - HTTP请求详情: {"request_id": "273a9d10", "timestamp": "2025-04-16T11:42:21.347094", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.005s"}
2025-04-16 11:42:26,354 - DEBUG - [3ff313ef] HTTP请求开始 - GET None
2025-04-16 11:42:26,413 - INFO - [9c2d300f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.065s
2025-04-16 11:42:26,414 - DEBUG - HTTP请求详情: {"request_id": "9c2d300f", "timestamp": "2025-04-16T11:42:26.347763", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.065s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:26,417 - DEBUG - [8515ebc3] HTTP请求开始 - GET None
2025-04-16 11:42:31,367 - WARNING - [3ff313ef] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.012s
2025-04-16 11:42:31,369 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:42:31,370 - DEBUG - HTTP请求详情: {"request_id": "3ff313ef", "timestamp": "2025-04-16T11:42:26.354850", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.012s"}
2025-04-16 11:42:31,376 - DEBUG - [3f94bbbc] HTTP请求开始 - GET None
2025-04-16 11:42:31,410 - INFO - [3f94bbbc] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.034s
2025-04-16 11:42:31,411 - DEBUG - HTTP请求详情: {"request_id": "3f94bbbc", "timestamp": "2025-04-16T11:42:31.376100", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.034s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:42:31,412 - DEBUG - [43cabfa9] HTTP请求开始 - GET None
2025-04-16 11:42:31,435 - DEBUG - HTTP请求详情: {"request_id": "8515ebc3", "timestamp": "2025-04-16T11:42:26.417654", "method": "GET", "url": null}
2025-04-16 11:52:43,108 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:52:43,110 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:52:43,122 - DEBUG - [76a761e1] HTTP请求开始 - GET None
2025-04-16 11:52:43,132 - DEBUG - [86facfb8] HTTP请求开始 - GET None
2025-04-16 11:52:48,147 - WARNING - [76a761e1] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.025s
2025-04-16 11:52:48,148 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:52:48,150 - DEBUG - HTTP请求详情: {"request_id": "76a761e1", "timestamp": "2025-04-16T11:52:43.122479", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.025s"}
2025-04-16 11:52:48,151 - DEBUG - [e7db8bd1] HTTP请求开始 - GET None
2025-04-16 11:52:48,173 - WARNING - [86facfb8] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.041s
2025-04-16 11:52:48,175 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:52:48,176 - DEBUG - HTTP请求详情: {"request_id": "86facfb8", "timestamp": "2025-04-16T11:52:43.132108", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.041s"}
2025-04-16 11:52:48,180 - DEBUG - [beae29c9] HTTP请求开始 - GET None
2025-04-16 11:52:48,200 - INFO - [e7db8bd1] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.049s
2025-04-16 11:52:48,202 - DEBUG - HTTP请求详情: {"request_id": "e7db8bd1", "timestamp": "2025-04-16T11:52:48.151953", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.049s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:52:48,205 - DEBUG - [c18e8bda] HTTP请求开始 - GET None
2025-04-16 11:52:53,195 - WARNING - [beae29c9] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:52:53,197 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:52:53,199 - DEBUG - HTTP请求详情: {"request_id": "beae29c9", "timestamp": "2025-04-16T11:52:48.180500", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:52:53,216 - WARNING - [c18e8bda] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.010s
2025-04-16 11:52:53,218 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:52:53,220 - DEBUG - HTTP请求详情: {"request_id": "c18e8bda", "timestamp": "2025-04-16T11:52:48.205746", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.010s"}
2025-04-16 11:52:53,223 - DEBUG - [d9c494d8] HTTP请求开始 - GET None
2025-04-16 11:52:53,581 - DEBUG - [57a509cd] HTTP请求开始 - GET None
2025-04-16 11:52:58,245 - WARNING - [d9c494d8] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.022s
2025-04-16 11:52:58,248 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:52:58,250 - DEBUG - HTTP请求详情: {"request_id": "d9c494d8", "timestamp": "2025-04-16T11:52:53.223512", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.022s"}
2025-04-16 11:52:58,621 - ERROR - [57a509cd] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.022s
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 11:52:58,664 - DEBUG - HTTP请求详情: {"request_id": "57a509cd", "timestamp": "2025-04-16T11:52:53.581892", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.022s"}
2025-04-16 11:52:58,680 - DEBUG - [2d085bb8] HTTP请求开始 - POST None
2025-04-16 11:52:58,732 - WARNING - [2d085bb8] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.053s
2025-04-16 11:52:58,734 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 11:52:58,747 - DEBUG - HTTP请求详情: {"request_id": "2d085bb8", "timestamp": "2025-04-16T11:52:58.680252", "method": "POST", "url": null, "json": {"username": "testuser_1744775563", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "18f0cd96e1eb2abb0f2729b5749f70964413d56991d3f04bb87cd2e1908da021"}, "status_code": 422, "elapsed_time": "0.053s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 11:52:58,768 - DEBUG - [3b632031] HTTP请求开始 - POST None
2025-04-16 11:52:58,805 - WARNING - [3b632031] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.037s
2025-04-16 11:52:58,806 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 11:52:58,819 - DEBUG - HTTP请求详情: {"request_id": "3b632031", "timestamp": "2025-04-16T11:52:58.768437", "method": "POST", "url": null, "json": {"username": "testuser_1744775563", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "18f0cd96e1eb2abb0f2729b5749f70964413d56991d3f04bb87cd2e1908da021"}, "status_code": 422, "elapsed_time": "0.037s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 11:52:58,843 - DEBUG - [aae971a8] HTTP请求开始 - GET None
2025-04-16 11:52:58,874 - INFO - [aae971a8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.031s
2025-04-16 11:52:58,876 - DEBUG - HTTP请求详情: {"request_id": "aae971a8", "timestamp": "2025-04-16T11:52:58.843070", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.031s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:52:58,886 - DEBUG - [d4654328] HTTP请求开始 - POST None
2025-04-16 11:52:58,915 - WARNING - [d4654328] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.028s
2025-04-16 11:52:58,916 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 11:52:58,918 - DEBUG - HTTP请求详情: {"request_id": "d4654328", "timestamp": "2025-04-16T11:52:58.886503", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.028s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 11:55:18,824 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:55:18,824 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:55:18,899 - DEBUG - [26760f06] HTTP请求开始 - GET None
2025-04-16 11:55:18,904 - DEBUG - [5ac5b261] HTTP请求开始 - GET None
2025-04-16 11:55:23,921 - WARNING - [5ac5b261] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.017s
2025-04-16 11:55:23,922 - WARNING - [26760f06] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.023s
2025-04-16 11:55:23,922 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:55:23,924 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:55:23,925 - DEBUG - HTTP请求详情: {"request_id": "5ac5b261", "timestamp": "2025-04-16T11:55:18.904101", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.017s"}
2025-04-16 11:55:23,927 - DEBUG - HTTP请求详情: {"request_id": "26760f06", "timestamp": "2025-04-16T11:55:18.899588", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.023s"}
2025-04-16 11:55:23,930 - DEBUG - [96396d1b] HTTP请求开始 - GET None
2025-04-16 11:55:23,933 - DEBUG - [1e7d3a6d] HTTP请求开始 - GET None
2025-04-16 11:55:23,978 - INFO - [96396d1b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.047s
2025-04-16 11:55:23,981 - DEBUG - HTTP请求详情: {"request_id": "96396d1b", "timestamp": "2025-04-16T11:55:23.930720", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.047s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:55:23,984 - DEBUG - [564c92fd] HTTP请求开始 - GET None
2025-04-16 11:55:28,964 - WARNING - [1e7d3a6d] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.031s
2025-04-16 11:55:28,965 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:55:28,966 - DEBUG - HTTP请求详情: {"request_id": "1e7d3a6d", "timestamp": "2025-04-16T11:55:23.933322", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.031s"}
2025-04-16 11:55:29,024 - WARNING - [564c92fd] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.039s
2025-04-16 11:55:29,026 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:55:29,028 - DEBUG - HTTP请求详情: {"request_id": "564c92fd", "timestamp": "2025-04-16T11:55:23.984781", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.039s"}
2025-04-16 11:55:29,036 - DEBUG - [d37f4743] HTTP请求开始 - GET None
2025-04-16 11:55:34,053 - WARNING - [d37f4743] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.017s
2025-04-16 11:55:34,054 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:55:34,056 - DEBUG - HTTP请求详情: {"request_id": "d37f4743", "timestamp": "2025-04-16T11:55:29.036569", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.017s"}
2025-04-16 11:55:34,069 - DEBUG - [cf94b1f4] HTTP请求开始 - GET None
2025-04-16 11:55:34,118 - INFO - [cf94b1f4] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.049s
2025-04-16 11:55:34,119 - DEBUG - HTTP请求详情: {"request_id": "cf94b1f4", "timestamp": "2025-04-16T11:55:34.068990", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.049s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:55:34,136 - DEBUG - [19e59cb5] HTTP请求开始 - POST None
2025-04-16 11:55:34,192 - WARNING - [19e59cb5] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.056s
2025-04-16 11:55:34,193 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 11:55:34,205 - DEBUG - HTTP请求详情: {"request_id": "19e59cb5", "timestamp": "2025-04-16T11:55:34.135968", "method": "POST", "url": null, "json": {"username": "testuser_1744775718", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "d84e7a52c6535995d4f6f4c975ea29cb8c2f1b4a91321b1551570f3ec7a1e153"}, "status_code": 422, "elapsed_time": "0.056s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 11:55:34,227 - DEBUG - [c9be1d4e] HTTP请求开始 - POST None
2025-04-16 11:55:34,328 - WARNING - [c9be1d4e] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.101s
2025-04-16 11:55:34,329 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 11:55:34,350 - DEBUG - HTTP请求详情: {"request_id": "c9be1d4e", "timestamp": "2025-04-16T11:55:34.227208", "method": "POST", "url": null, "json": {"username": "testuser_1744775718", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "d84e7a52c6535995d4f6f4c975ea29cb8c2f1b4a91321b1551570f3ec7a1e153"}, "status_code": 422, "elapsed_time": "0.101s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 11:55:34,387 - DEBUG - [064973b8] HTTP请求开始 - GET None
2025-04-16 11:55:34,431 - INFO - [064973b8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 11:55:34,434 - DEBUG - HTTP请求详情: {"request_id": "064973b8", "timestamp": "2025-04-16T11:55:34.386955", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:55:34,452 - DEBUG - [e428e917] HTTP请求开始 - POST None
2025-04-16 11:55:34,493 - WARNING - [e428e917] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.042s
2025-04-16 11:55:34,495 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 11:55:34,496 - DEBUG - HTTP请求详情: {"request_id": "e428e917", "timestamp": "2025-04-16T11:55:34.452046", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.042s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 11:57:49,181 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 11:57:49,186 - INFO - 已启用HTTP请求日志记录
2025-04-16 11:57:49,254 - DEBUG - [850e82dc] HTTP请求开始 - GET None
2025-04-16 11:57:49,276 - DEBUG - [8c1ed8a0] HTTP请求开始 - GET None
2025-04-16 11:57:54,291 - WARNING - [850e82dc] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.038s
2025-04-16 11:57:54,291 - WARNING - [8c1ed8a0] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.015s
2025-04-16 11:57:54,293 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:57:54,293 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:57:54,294 - DEBUG - HTTP请求详情: {"request_id": "850e82dc", "timestamp": "2025-04-16T11:57:49.253992", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.038s"}
2025-04-16 11:57:54,295 - DEBUG - HTTP请求详情: {"request_id": "8c1ed8a0", "timestamp": "2025-04-16T11:57:49.276633", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.015s"}
2025-04-16 11:57:54,297 - DEBUG - [6169293c] HTTP请求开始 - GET None
2025-04-16 11:57:54,297 - DEBUG - [8eaa4972] HTTP请求开始 - GET None
2025-04-16 11:57:54,375 - INFO - [6169293c] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.078s
2025-04-16 11:57:54,376 - DEBUG - HTTP请求详情: {"request_id": "6169293c", "timestamp": "2025-04-16T11:57:54.297395", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.078s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:57:54,378 - DEBUG - [c681efcb] HTTP请求开始 - GET None
2025-04-16 11:57:59,347 - WARNING - [8eaa4972] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.050s
2025-04-16 11:57:59,376 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:57:59,377 - DEBUG - HTTP请求详情: {"request_id": "8eaa4972", "timestamp": "2025-04-16T11:57:54.297905", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.050s"}
2025-04-16 11:57:59,500 - WARNING - [c681efcb] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.122s
2025-04-16 11:57:59,509 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:57:59,519 - DEBUG - HTTP请求详情: {"request_id": "c681efcb", "timestamp": "2025-04-16T11:57:54.378272", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.122s"}
2025-04-16 11:57:59,582 - DEBUG - [8334adcc] HTTP请求开始 - GET None
2025-04-16 11:58:00,567 - DEBUG - [18f7650e] HTTP请求开始 - GET None
2025-04-16 11:58:04,656 - WARNING - [8334adcc] HTTP响应 - GET None - 状态码: 502 - 耗时: 5.074s
2025-04-16 11:58:04,658 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: None
2025-04-16 11:58:04,659 - DEBUG - HTTP请求详情: {"request_id": "8334adcc", "timestamp": "2025-04-16T11:57:59.582277", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "5.074s"}
2025-04-16 11:58:05,744 - ERROR - [18f7650e] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.035s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 11:58:05,795 - DEBUG - HTTP请求详情: {"request_id": "18f7650e", "timestamp": "2025-04-16T11:58:00.567357", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.035s"}
2025-04-16 11:58:05,843 - DEBUG - [b67e27dd] HTTP请求开始 - POST None
2025-04-16 11:58:05,892 - WARNING - [b67e27dd] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.048s
2025-04-16 11:58:05,894 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 11:58:05,907 - DEBUG - HTTP请求详情: {"request_id": "b67e27dd", "timestamp": "2025-04-16T11:58:05.843805", "method": "POST", "url": null, "json": {"username": "testuser_1744775869", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "f15c30a7b1f1c15c1feebb3aa8e1fc6f2b94633b6bac6dce3b78df20524f2cbc"}, "status_code": 422, "elapsed_time": "0.048s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 11:58:05,927 - DEBUG - [0f2e4826] HTTP请求开始 - POST None
2025-04-16 11:58:05,964 - WARNING - [0f2e4826] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.037s
2025-04-16 11:58:05,966 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 11:58:05,979 - DEBUG - HTTP请求详情: {"request_id": "0f2e4826", "timestamp": "2025-04-16T11:58:05.927365", "method": "POST", "url": null, "json": {"username": "testuser_1744775869", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "f15c30a7b1f1c15c1feebb3aa8e1fc6f2b94633b6bac6dce3b78df20524f2cbc"}, "status_code": 422, "elapsed_time": "0.037s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 11:58:06,004 - DEBUG - [bac27e80] HTTP请求开始 - GET None
2025-04-16 11:58:06,073 - INFO - [bac27e80] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.069s
2025-04-16 11:58:06,075 - DEBUG - HTTP请求详情: {"request_id": "bac27e80", "timestamp": "2025-04-16T11:58:06.004691", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.069s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 11:58:06,084 - DEBUG - [5126a26e] HTTP请求开始 - POST None
2025-04-16 11:58:06,178 - WARNING - [5126a26e] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.093s
2025-04-16 11:58:06,179 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 11:58:06,182 - DEBUG - HTTP请求详情: {"request_id": "5126a26e", "timestamp": "2025-04-16T11:58:06.084690", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.093s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 12:24:52,423 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 12:24:52,427 - INFO - 已启用HTTP请求日志记录
2025-04-16 12:24:52,482 - DEBUG - [945f0a17] HTTP请求开始 - GET None
2025-04-16 12:24:52,573 - INFO - [945f0a17] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.091s
2025-04-16 12:24:52,574 - DEBUG - HTTP请求详情: {"request_id": "945f0a17", "timestamp": "2025-04-16T12:24:52.482220", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.091s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 12:24:52,580 - DEBUG - [25538b44] HTTP请求开始 - GET None
2025-04-16 12:24:52,614 - INFO - [25538b44] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.034s
2025-04-16 12:24:52,615 - DEBUG - HTTP请求详情: {"request_id": "25538b44", "timestamp": "2025-04-16T12:24:52.580321", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.034s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 12:24:52,627 - DEBUG - [400226d7] HTTP请求开始 - POST None
2025-04-16 12:24:52,670 - WARNING - [400226d7] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.043s
2025-04-16 12:24:52,672 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 12:24:52,686 - DEBUG - HTTP请求详情: {"request_id": "400226d7", "timestamp": "2025-04-16T12:24:52.627154", "method": "POST", "url": null, "json": {"username": "testuser_**********", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "37f935fb810e3a8dd81cca3f9c6fe80cf45e1f276ab45eae8510a2d1bd9dc927"}, "status_code": 422, "elapsed_time": "0.043s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 12:24:52,703 - DEBUG - [18beb1f8] HTTP请求开始 - POST None
2025-04-16 12:24:52,747 - WARNING - [18beb1f8] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.043s
2025-04-16 12:24:52,748 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 12:24:52,756 - DEBUG - HTTP请求详情: {"request_id": "18beb1f8", "timestamp": "2025-04-16T12:24:52.703750", "method": "POST", "url": null, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "37f935fb810e3a8dd81cca3f9c6fe80cf45e1f276ab45eae8510a2d1bd9dc927"}, "status_code": 422, "elapsed_time": "0.043s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 12:24:52,772 - DEBUG - [9ac5b82c] HTTP请求开始 - GET None
2025-04-16 12:24:52,846 - INFO - [9ac5b82c] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.074s
2025-04-16 12:24:52,848 - DEBUG - HTTP请求详情: {"request_id": "9ac5b82c", "timestamp": "2025-04-16T12:24:52.772665", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.074s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 12:24:52,853 - DEBUG - [fa95b613] HTTP请求开始 - POST None
2025-04-16 12:24:52,885 - WARNING - [fa95b613] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.032s
2025-04-16 12:24:52,886 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 12:24:52,888 - DEBUG - HTTP请求详情: {"request_id": "fa95b613", "timestamp": "2025-04-16T12:24:52.853676", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.032s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 14:48:23,817 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 14:48:23,818 - INFO - 已启用HTTP请求日志记录
2025-04-16 14:48:23,834 - DEBUG - [ee967cb5] HTTP请求开始 - GET None
2025-04-16 14:48:23,876 - INFO - [ee967cb5] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 14:48:23,877 - DEBUG - HTTP请求详情: {"request_id": "ee967cb5", "timestamp": "2025-04-16T14:48:23.834778", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 14:48:23,881 - DEBUG - [4e04776e] HTTP请求开始 - GET None
2025-04-16 14:48:23,914 - INFO - [4e04776e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.033s
2025-04-16 14:48:23,915 - DEBUG - HTTP请求详情: {"request_id": "4e04776e", "timestamp": "2025-04-16T14:48:23.881284", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.033s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 14:48:23,929 - DEBUG - [d9782424] HTTP请求开始 - POST None
2025-04-16 14:48:23,977 - WARNING - [d9782424] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.048s
2025-04-16 14:48:23,979 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 14:48:23,989 - DEBUG - HTTP请求详情: {"request_id": "d9782424", "timestamp": "2025-04-16T14:48:23.929062", "method": "POST", "url": null, "json": {"username": "testuser_**********", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "4a2a9d0e45602c381d2db13f65d35ac6bec46b057f4c113bf302e3aca3d4b4bf"}, "status_code": 422, "elapsed_time": "0.048s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 14:48:24,014 - DEBUG - [924c2f87] HTTP请求开始 - POST None
2025-04-16 14:48:24,053 - WARNING - [924c2f87] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.039s
2025-04-16 14:48:24,054 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 14:48:24,063 - DEBUG - HTTP请求详情: {"request_id": "924c2f87", "timestamp": "2025-04-16T14:48:24.014276", "method": "POST", "url": null, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "16741ad6faefc97287248d21f789cad978b37ee8400f6f884a111fc26b2cc185"}, "status_code": 422, "elapsed_time": "0.039s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 14:48:24,080 - DEBUG - [b6153afe] HTTP请求开始 - GET None
2025-04-16 14:48:24,116 - INFO - [b6153afe] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.036s
2025-04-16 14:48:24,117 - DEBUG - HTTP请求详情: {"request_id": "b6153afe", "timestamp": "2025-04-16T14:48:24.080443", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.036s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 14:48:24,123 - DEBUG - [dbe25094] HTTP请求开始 - POST None
2025-04-16 14:48:24,221 - WARNING - [dbe25094] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.097s
2025-04-16 14:48:24,222 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 14:48:24,224 - DEBUG - HTTP请求详情: {"request_id": "dbe25094", "timestamp": "2025-04-16T14:48:24.123897", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.097s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 15:24:18,919 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 15:24:18,936 - INFO - 已启用HTTP请求日志记录
2025-04-16 15:24:19,188 - DEBUG - [f80a1196] HTTP请求开始 - GET None
2025-04-16 15:24:19,264 - INFO - [f80a1196] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.076s
2025-04-16 15:24:19,277 - DEBUG - HTTP请求详情: {"request_id": "f80a1196", "timestamp": "2025-04-16T15:24:19.188390", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.076s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 15:24:19,281 - DEBUG - [43657ed7] HTTP请求开始 - GET None
2025-04-16 15:24:19,347 - INFO - [43657ed7] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.066s
2025-04-16 15:24:19,348 - DEBUG - HTTP请求详情: {"request_id": "43657ed7", "timestamp": "2025-04-16T15:24:19.281054", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.066s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 15:24:19,361 - DEBUG - [86479390] HTTP请求开始 - POST None
2025-04-16 15:24:19,408 - WARNING - [86479390] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.046s
2025-04-16 15:24:19,410 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 15:24:19,420 - DEBUG - HTTP请求详情: {"request_id": "86479390", "timestamp": "2025-04-16T15:24:19.361522", "method": "POST", "url": null, "json": {"username": "testuser_**********", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "3443052bf9eeabdd6635b9227baa7d832ca8ebea9a3661bb70041828936d2bc1"}, "status_code": 422, "elapsed_time": "0.046s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 15:24:19,439 - DEBUG - [c3a20856] HTTP请求开始 - POST None
2025-04-16 15:24:19,512 - WARNING - [c3a20856] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.073s
2025-04-16 15:24:19,514 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 15:24:19,526 - DEBUG - HTTP请求详情: {"request_id": "c3a20856", "timestamp": "2025-04-16T15:24:19.439537", "method": "POST", "url": null, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "3443052bf9eeabdd6635b9227baa7d832ca8ebea9a3661bb70041828936d2bc1"}, "status_code": 422, "elapsed_time": "0.073s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 15:24:19,544 - DEBUG - [5451e02a] HTTP请求开始 - GET None
2025-04-16 15:24:19,599 - INFO - [5451e02a] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.055s
2025-04-16 15:24:19,600 - DEBUG - HTTP请求详情: {"request_id": "5451e02a", "timestamp": "2025-04-16T15:24:19.544217", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.055s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 15:24:19,605 - DEBUG - [c690a025] HTTP请求开始 - POST None
2025-04-16 15:24:19,686 - WARNING - [c690a025] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.080s
2025-04-16 15:24:19,688 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 15:24:19,689 - DEBUG - HTTP请求详情: {"request_id": "c690a025", "timestamp": "2025-04-16T15:24:19.605828", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.080s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:23:32,214 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:23:32,216 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:23:32,317 - DEBUG - [b991f56f] HTTP请求开始 - GET None
2025-04-16 16:23:32,449 - INFO - [b991f56f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.131s
2025-04-16 16:23:32,451 - DEBUG - HTTP请求详情: {"request_id": "b991f56f", "timestamp": "2025-04-16T16:23:32.317043", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.131s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:23:32,455 - DEBUG - [3e98fae2] HTTP请求开始 - GET None
2025-04-16 16:23:32,492 - INFO - [3e98fae2] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.037s
2025-04-16 16:23:32,494 - DEBUG - HTTP请求详情: {"request_id": "3e98fae2", "timestamp": "2025-04-16T16:23:32.455608", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.037s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:23:32,507 - DEBUG - [7ca8af66] HTTP请求开始 - POST None
2025-04-16 16:23:32,670 - WARNING - [7ca8af66] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.163s
2025-04-16 16:23:32,671 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:23:32,680 - DEBUG - HTTP请求详情: {"request_id": "7ca8af66", "timestamp": "2025-04-16T16:23:32.507431", "method": "POST", "url": null, "json": {"username": "testuser_**********", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "4f9f58146c9eab0ed85c0ea43a00ea3bacb8a57c67099f75e65179c23e474a76"}, "status_code": 422, "elapsed_time": "0.163s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:23:32,697 - DEBUG - [c09ea38b] HTTP请求开始 - POST None
2025-04-16 16:23:32,789 - WARNING - [c09ea38b] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.091s
2025-04-16 16:23:32,790 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:23:32,800 - DEBUG - HTTP请求详情: {"request_id": "c09ea38b", "timestamp": "2025-04-16T16:23:32.697642", "method": "POST", "url": null, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "4f9f58146c9eab0ed85c0ea43a00ea3bacb8a57c67099f75e65179c23e474a76"}, "status_code": 422, "elapsed_time": "0.091s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:23:32,819 - DEBUG - [095d3dc3] HTTP请求开始 - GET None
2025-04-16 16:23:32,890 - INFO - [095d3dc3] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.071s
2025-04-16 16:23:32,892 - DEBUG - HTTP请求详情: {"request_id": "095d3dc3", "timestamp": "2025-04-16T16:23:32.819527", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.071s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:23:32,898 - DEBUG - [e1fe8394] HTTP请求开始 - POST None
2025-04-16 16:23:32,952 - WARNING - [e1fe8394] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.054s
2025-04-16 16:23:32,958 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:23:32,960 - DEBUG - HTTP请求详情: {"request_id": "e1fe8394", "timestamp": "2025-04-16T16:23:32.898130", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.054s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:34:28,953 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:34:28,954 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:34:28,972 - DEBUG - [03633d82] HTTP请求开始 - GET None
2025-04-16 16:34:29,182 - INFO - [03633d82] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.210s
2025-04-16 16:34:29,184 - DEBUG - HTTP请求详情: {"request_id": "03633d82", "timestamp": "2025-04-16T16:34:28.972637", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.210s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:34:29,188 - DEBUG - [3159cf32] HTTP请求开始 - GET None
2025-04-16 16:34:29,260 - INFO - [3159cf32] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.072s
2025-04-16 16:34:29,262 - DEBUG - HTTP请求详情: {"request_id": "3159cf32", "timestamp": "2025-04-16T16:34:29.188271", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.072s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:34:29,273 - DEBUG - [6931f7eb] HTTP请求开始 - POST None
2025-04-16 16:34:29,366 - WARNING - [6931f7eb] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.093s
2025-04-16 16:34:29,367 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:34:29,378 - DEBUG - HTTP请求详情: {"request_id": "6931f7eb", "timestamp": "2025-04-16T16:34:29.273085", "method": "POST", "url": null, "json": {"username": "testuser_1744792468", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.093s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:34:29,391 - DEBUG - [7573a496] HTTP请求开始 - POST None
2025-04-16 16:34:29,483 - WARNING - [7573a496] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.092s
2025-04-16 16:34:29,484 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:34:29,497 - DEBUG - HTTP请求详情: {"request_id": "7573a496", "timestamp": "2025-04-16T16:34:29.391387", "method": "POST", "url": null, "json": {"username": "testuser_1744792468", "password_hash": "********", "timestamp": 1744792469}, "status_code": 422, "elapsed_time": "0.092s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:34:29,515 - DEBUG - [eb889f01] HTTP请求开始 - GET None
2025-04-16 16:34:29,601 - INFO - [eb889f01] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.086s
2025-04-16 16:34:29,603 - DEBUG - HTTP请求详情: {"request_id": "eb889f01", "timestamp": "2025-04-16T16:34:29.515732", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.086s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:34:29,610 - DEBUG - [8d2fd60e] HTTP请求开始 - POST None
2025-04-16 16:34:29,763 - WARNING - [8d2fd60e] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.153s
2025-04-16 16:34:29,765 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:34:29,767 - DEBUG - HTTP请求详情: {"request_id": "8d2fd60e", "timestamp": "2025-04-16T16:34:29.610647", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.153s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:35:31,047 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:35:31,048 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:35:31,071 - DEBUG - [238e6bfe] HTTP请求开始 - GET None
2025-04-16 16:35:31,152 - INFO - [238e6bfe] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.081s
2025-04-16 16:35:31,155 - DEBUG - HTTP请求详情: {"request_id": "238e6bfe", "timestamp": "2025-04-16T16:35:31.071237", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.081s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:35:31,161 - DEBUG - [e243ea65] HTTP请求开始 - GET None
2025-04-16 16:35:31,193 - INFO - [e243ea65] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.032s
2025-04-16 16:35:31,194 - DEBUG - HTTP请求详情: {"request_id": "e243ea65", "timestamp": "2025-04-16T16:35:31.161400", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.032s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:35:31,210 - DEBUG - [a319fe18] HTTP请求开始 - POST None
2025-04-16 16:35:31,263 - WARNING - [a319fe18] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.053s
2025-04-16 16:35:31,264 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_register() got an unexpected keyword argument 'args'"
}
2025-04-16 16:35:31,266 - DEBUG - HTTP请求详情: {"request_id": "a319fe18", "timestamp": "2025-04-16T16:35:31.209919", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744792531", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 500, "elapsed_time": "0.053s", "response": "{\n  \"detail\": \"Unexpected error: mobile_register() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:35:31,280 - DEBUG - [30d05a0c] HTTP请求开始 - POST None
2025-04-16 16:35:31,323 - WARNING - [30d05a0c] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.043s
2025-04-16 16:35:31,324 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_login() got an unexpected keyword argument 'args'"
}
2025-04-16 16:35:31,326 - DEBUG - HTTP请求详情: {"request_id": "30d05a0c", "timestamp": "2025-04-16T16:35:31.280007", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744792531", "password_hash": "********", "timestamp": 1744792531}, "status_code": 500, "elapsed_time": "0.043s", "response": "{\n  \"detail\": \"Unexpected error: mobile_login() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:35:31,342 - DEBUG - [242aece5] HTTP请求开始 - GET None
2025-04-16 16:35:31,380 - INFO - [242aece5] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.038s
2025-04-16 16:35:31,381 - DEBUG - HTTP请求详情: {"request_id": "242aece5", "timestamp": "2025-04-16T16:35:31.342247", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.038s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:35:31,389 - DEBUG - [665351fe] HTTP请求开始 - POST None
2025-04-16 16:35:31,422 - WARNING - [665351fe] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.034s
2025-04-16 16:35:31,423 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:35:31,425 - DEBUG - HTTP请求详情: {"request_id": "665351fe", "timestamp": "2025-04-16T16:35:31.389019", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.034s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:36:51,681 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:36:51,699 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:36:52,129 - DEBUG - [0ca456ef] HTTP请求开始 - GET None
2025-04-16 16:36:52,245 - INFO - [0ca456ef] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.116s
2025-04-16 16:36:52,246 - DEBUG - HTTP请求详情: {"request_id": "0ca456ef", "timestamp": "2025-04-16T16:36:52.129286", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.116s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:36:52,251 - DEBUG - [f7b1cac8] HTTP请求开始 - GET None
2025-04-16 16:36:52,353 - INFO - [f7b1cac8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.103s
2025-04-16 16:36:52,355 - DEBUG - HTTP请求详情: {"request_id": "f7b1cac8", "timestamp": "2025-04-16T16:36:52.251018", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.103s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:36:52,368 - DEBUG - [e7814622] HTTP请求开始 - POST None
2025-04-16 16:36:52,449 - WARNING - [e7814622] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.081s
2025-04-16 16:36:52,450 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_register() got an unexpected keyword argument 'args'"
}
2025-04-16 16:36:52,453 - DEBUG - HTTP请求详情: {"request_id": "e7814622", "timestamp": "2025-04-16T16:36:52.368194", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744792612", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 500, "elapsed_time": "0.081s", "response": "{\n  \"detail\": \"Unexpected error: mobile_register() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:36:52,467 - DEBUG - [71ad7f9e] HTTP请求开始 - POST None
2025-04-16 16:36:52,516 - WARNING - [71ad7f9e] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.049s
2025-04-16 16:36:52,517 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:36:52,532 - DEBUG - HTTP请求详情: {"request_id": "71ad7f9e", "timestamp": "2025-04-16T16:36:52.467077", "method": "POST", "url": null, "json": {"username": "testuser_1744792612", "password_hash": "********", "timestamp": 1744792612}, "status_code": 422, "elapsed_time": "0.049s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:36:52,551 - DEBUG - [dbd2df26] HTTP请求开始 - GET None
2025-04-16 16:36:52,597 - INFO - [dbd2df26] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.046s
2025-04-16 16:36:52,599 - DEBUG - HTTP请求详情: {"request_id": "dbd2df26", "timestamp": "2025-04-16T16:36:52.551485", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.046s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:36:52,611 - DEBUG - [fc254b7c] HTTP请求开始 - POST None
2025-04-16 16:36:52,671 - WARNING - [fc254b7c] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.060s
2025-04-16 16:36:52,673 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:36:52,679 - DEBUG - HTTP请求详情: {"request_id": "fc254b7c", "timestamp": "2025-04-16T16:36:52.611357", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.060s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:37:44,243 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:37:44,245 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:37:44,262 - DEBUG - [071ff5af] HTTP请求开始 - GET None
2025-04-16 16:37:44,343 - INFO - [071ff5af] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.081s
2025-04-16 16:37:44,344 - DEBUG - HTTP请求详情: {"request_id": "071ff5af", "timestamp": "2025-04-16T16:37:44.262753", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.081s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:37:44,349 - DEBUG - [d9030849] HTTP请求开始 - GET None
2025-04-16 16:37:44,467 - INFO - [d9030849] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.118s
2025-04-16 16:37:44,469 - DEBUG - HTTP请求详情: {"request_id": "d9030849", "timestamp": "2025-04-16T16:37:44.348987", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.118s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:37:44,480 - DEBUG - [99c4b063] HTTP请求开始 - POST None
2025-04-16 16:37:44,611 - WARNING - [99c4b063] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.131s
2025-04-16 16:37:44,612 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:37:44,622 - DEBUG - HTTP请求详情: {"request_id": "99c4b063", "timestamp": "2025-04-16T16:37:44.480400", "method": "POST", "url": null, "data": {"username": "testuser_1744792664", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.131s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:37:44,637 - DEBUG - [57b4d919] HTTP请求开始 - POST None
2025-04-16 16:37:44,703 - WARNING - [57b4d919] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.066s
2025-04-16 16:37:44,704 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:37:44,712 - DEBUG - HTTP请求详情: {"request_id": "57b4d919", "timestamp": "2025-04-16T16:37:44.637883", "method": "POST", "url": null, "data": {"username": "testuser_1744792664", "password_hash": "********", "timestamp": 1744792664}, "status_code": 422, "elapsed_time": "0.066s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:37:44,732 - DEBUG - [e2778161] HTTP请求开始 - GET None
2025-04-16 16:37:44,801 - INFO - [e2778161] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.068s
2025-04-16 16:37:44,803 - DEBUG - HTTP请求详情: {"request_id": "e2778161", "timestamp": "2025-04-16T16:37:44.732338", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.068s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:37:44,808 - DEBUG - [14ead278] HTTP请求开始 - POST None
2025-04-16 16:37:45,152 - WARNING - [14ead278] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.344s
2025-04-16 16:37:45,154 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:37:45,156 - DEBUG - HTTP请求详情: {"request_id": "14ead278", "timestamp": "2025-04-16T16:37:44.808688", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.344s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:39:29,941 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:39:30,029 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:39:30,485 - DEBUG - [32e15942] HTTP请求开始 - GET None
2025-04-16 16:39:30,649 - INFO - [32e15942] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.164s
2025-04-16 16:39:30,659 - DEBUG - HTTP请求详情: {"request_id": "32e15942", "timestamp": "2025-04-16T16:39:30.485149", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.164s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:39:30,664 - DEBUG - [a570f55f] HTTP请求开始 - GET None
2025-04-16 16:39:30,715 - INFO - [a570f55f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.051s
2025-04-16 16:39:30,716 - DEBUG - HTTP请求详情: {"request_id": "a570f55f", "timestamp": "2025-04-16T16:39:30.664471", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.051s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:39:30,732 - DEBUG - [415cd1e4] HTTP请求开始 - POST None
2025-04-16 16:39:30,801 - WARNING - [415cd1e4] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.069s
2025-04-16 16:39:30,804 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_register() got an unexpected keyword argument 'args'"
}
2025-04-16 16:39:30,807 - DEBUG - HTTP请求详情: {"request_id": "415cd1e4", "timestamp": "2025-04-16T16:39:30.732061", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744792770", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 500, "elapsed_time": "0.069s", "response": "{\n  \"detail\": \"Unexpected error: mobile_register() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:39:30,818 - DEBUG - [7ef47919] HTTP请求开始 - POST None
2025-04-16 16:39:30,864 - WARNING - [7ef47919] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.047s
2025-04-16 16:39:30,866 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_login() got an unexpected keyword argument 'args'"
}
2025-04-16 16:39:30,868 - DEBUG - HTTP请求详情: {"request_id": "7ef47919", "timestamp": "2025-04-16T16:39:30.818055", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744792770", "password_hash": "********", "timestamp": 1744792770}, "status_code": 500, "elapsed_time": "0.047s", "response": "{\n  \"detail\": \"Unexpected error: mobile_login() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:39:30,888 - DEBUG - [05cb39d9] HTTP请求开始 - GET None
2025-04-16 16:39:30,927 - INFO - [05cb39d9] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.039s
2025-04-16 16:39:30,929 - DEBUG - HTTP请求详情: {"request_id": "05cb39d9", "timestamp": "2025-04-16T16:39:30.888854", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.039s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:39:30,934 - DEBUG - [461f93ad] HTTP请求开始 - POST None
2025-04-16 16:39:30,981 - WARNING - [461f93ad] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.046s
2025-04-16 16:39:30,982 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:39:30,984 - DEBUG - HTTP请求详情: {"request_id": "461f93ad", "timestamp": "2025-04-16T16:39:30.934673", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.046s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:40:19,651 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:40:19,717 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:40:20,459 - DEBUG - [34a203de] HTTP请求开始 - GET None
2025-04-16 16:40:20,590 - INFO - [34a203de] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.130s
2025-04-16 16:40:20,612 - DEBUG - HTTP请求详情: {"request_id": "34a203de", "timestamp": "2025-04-16T16:40:20.459603", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.130s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:40:20,735 - DEBUG - [3904e5c4] HTTP请求开始 - GET None
2025-04-16 16:40:20,789 - INFO - [3904e5c4] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.054s
2025-04-16 16:40:20,790 - DEBUG - HTTP请求详情: {"request_id": "3904e5c4", "timestamp": "2025-04-16T16:40:20.735120", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.054s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:40:20,802 - DEBUG - [9f05738b] HTTP请求开始 - POST None
2025-04-16 16:40:20,911 - WARNING - [9f05738b] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.109s
2025-04-16 16:40:20,912 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:40:20,921 - DEBUG - HTTP请求详情: {"request_id": "9f05738b", "timestamp": "2025-04-16T16:40:20.802189", "method": "POST", "url": null, "json": {"username": "testuser_1744792820", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.109s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:40:20,935 - DEBUG - [5c29619a] HTTP请求开始 - POST None
2025-04-16 16:40:21,018 - WARNING - [5c29619a] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.083s
2025-04-16 16:40:21,020 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:40:21,027 - DEBUG - HTTP请求详情: {"request_id": "5c29619a", "timestamp": "2025-04-16T16:40:20.935248", "method": "POST", "url": null, "json": {"username": "testuser_1744792820", "password_hash": "********", "timestamp": 1744792820}, "status_code": 422, "elapsed_time": "0.083s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:40:21,043 - DEBUG - [682c10be] HTTP请求开始 - GET None
2025-04-16 16:40:21,089 - INFO - [682c10be] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.046s
2025-04-16 16:40:21,090 - DEBUG - HTTP请求详情: {"request_id": "682c10be", "timestamp": "2025-04-16T16:40:21.043954", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.046s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:40:21,095 - DEBUG - [5cf459c3] HTTP请求开始 - POST None
2025-04-16 16:40:21,154 - WARNING - [5cf459c3] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.059s
2025-04-16 16:40:21,155 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:40:21,157 - DEBUG - HTTP请求详情: {"request_id": "5cf459c3", "timestamp": "2025-04-16T16:40:21.095612", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.059s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:53:44,759 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:53:44,779 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:53:45,433 - DEBUG - [1529b428] HTTP请求开始 - GET None
2025-04-16 16:53:46,380 - INFO - [1529b428] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.947s
2025-04-16 16:53:46,381 - DEBUG - HTTP请求详情: {"request_id": "1529b428", "timestamp": "2025-04-16T16:53:45.433676", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.947s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:53:46,386 - DEBUG - [8bf76651] HTTP请求开始 - GET None
2025-04-16 16:53:46,475 - INFO - [8bf76651] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.089s
2025-04-16 16:53:46,486 - DEBUG - [85e50d55] HTTP请求开始 - GET None
2025-04-16 16:53:46,487 - DEBUG - HTTP请求详情: {"request_id": "8bf76651", "timestamp": "2025-04-16T16:53:46.385984", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.089s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:53:46,503 - DEBUG - [b400e1e6] HTTP请求开始 - POST None
2025-04-16 16:53:46,571 - WARNING - [b400e1e6] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.067s
2025-04-16 16:53:46,572 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:53:46,582 - DEBUG - HTTP请求详情: {"request_id": "b400e1e6", "timestamp": "2025-04-16T16:53:46.503855", "method": "POST", "url": null, "json": {"username": "testuser_1744793625", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "headers": {"Content-Type": "application/json"}, "status_code": 422, "elapsed_time": "0.067s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:53:46,586 - INFO - [85e50d55] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.100s
2025-04-16 16:53:46,587 - DEBUG - HTTP请求详情: {"request_id": "85e50d55", "timestamp": "2025-04-16T16:53:46.486389", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.100s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:53:46,599 - DEBUG - [482a40fd] HTTP请求开始 - POST None
2025-04-16 16:53:46,652 - WARNING - [482a40fd] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.053s
2025-04-16 16:53:46,654 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:53:46,667 - DEBUG - HTTP请求详情: {"request_id": "482a40fd", "timestamp": "2025-04-16T16:53:46.599417", "method": "POST", "url": null, "json": {"username": "testuser_1744793625", "password_hash": "********", "timestamp": **********}, "headers": {"Content-Type": "application/json"}, "status_code": 422, "elapsed_time": "0.053s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:53:46,687 - DEBUG - [9d3e9576] HTTP请求开始 - GET None
2025-04-16 16:53:46,774 - INFO - [9d3e9576] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.087s
2025-04-16 16:53:46,777 - DEBUG - HTTP请求详情: {"request_id": "9d3e9576", "timestamp": "2025-04-16T16:53:46.687219", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.087s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:53:46,785 - DEBUG - [b0f6dcfb] HTTP请求开始 - POST None
2025-04-16 16:53:46,838 - WARNING - [b0f6dcfb] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.053s
2025-04-16 16:53:46,839 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:53:46,841 - DEBUG - HTTP请求详情: {"request_id": "b0f6dcfb", "timestamp": "2025-04-16T16:53:46.785187", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.053s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:55:19,716 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:55:19,733 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:55:19,807 - DEBUG - [37c5b880] HTTP请求开始 - GET None
2025-04-16 16:55:19,871 - INFO - [37c5b880] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.064s
2025-04-16 16:55:19,873 - DEBUG - HTTP请求详情: {"request_id": "37c5b880", "timestamp": "2025-04-16T16:55:19.807304", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.064s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:55:19,877 - DEBUG - [330ce860] HTTP请求开始 - GET None
2025-04-16 16:55:19,920 - INFO - [330ce860] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.044s
2025-04-16 16:55:19,921 - DEBUG - HTTP请求详情: {"request_id": "330ce860", "timestamp": "2025-04-16T16:55:19.876962", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.044s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:55:19,933 - DEBUG - [017a41c6] HTTP请求开始 - POST None
2025-04-16 16:55:19,991 - WARNING - [017a41c6] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.058s
2025-04-16 16:55:19,992 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:55:20,001 - DEBUG - HTTP请求详情: {"request_id": "017a41c6", "timestamp": "2025-04-16T16:55:19.932894", "method": "POST", "url": null, "json": {"username": "testuser_1744793719", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "headers": {"Content-Type": "application/json"}, "status_code": 422, "elapsed_time": "0.058s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:55:20,016 - DEBUG - [9ba69d75] HTTP请求开始 - POST None
2025-04-16 16:55:20,085 - WARNING - [9ba69d75] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.069s
2025-04-16 16:55:20,087 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 16:55:20,094 - DEBUG - HTTP请求详情: {"request_id": "9ba69d75", "timestamp": "2025-04-16T16:55:20.015826", "method": "POST", "url": null, "json": {"username": "testuser_1744793719", "password_hash": "********", "timestamp": 1744793720}, "headers": {"Content-Type": "application/json"}, "status_code": 422, "elapsed_time": "0.069s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 16:55:20,116 - DEBUG - [1cb1394e] HTTP请求开始 - GET None
2025-04-16 16:55:20,162 - INFO - [1cb1394e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.046s
2025-04-16 16:55:20,165 - DEBUG - HTTP请求详情: {"request_id": "1cb1394e", "timestamp": "2025-04-16T16:55:20.116264", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.046s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:55:20,171 - DEBUG - [293cc77d] HTTP请求开始 - POST None
2025-04-16 16:55:20,220 - WARNING - [293cc77d] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.049s
2025-04-16 16:55:20,221 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:55:20,222 - DEBUG - HTTP请求详情: {"request_id": "293cc77d", "timestamp": "2025-04-16T16:55:20.171475", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.049s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 16:55:58,591 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 16:55:58,630 - INFO - 已启用HTTP请求日志记录
2025-04-16 16:55:59,006 - DEBUG - [f1df0aab] HTTP请求开始 - GET None
2025-04-16 16:55:59,089 - INFO - [f1df0aab] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.084s
2025-04-16 16:55:59,103 - DEBUG - HTTP请求详情: {"request_id": "f1df0aab", "timestamp": "2025-04-16T16:55:59.006070", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.084s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:55:59,128 - DEBUG - [74d3970f] HTTP请求开始 - GET None
2025-04-16 16:55:59,188 - INFO - [74d3970f] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.059s
2025-04-16 16:55:59,189 - DEBUG - HTTP请求详情: {"request_id": "74d3970f", "timestamp": "2025-04-16T16:55:59.128836", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.059s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:55:59,202 - DEBUG - [47d741da] HTTP请求开始 - POST None
2025-04-16 16:55:59,257 - WARNING - [47d741da] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.055s
2025-04-16 16:55:59,259 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_register() got an unexpected keyword argument 'args'"
}
2025-04-16 16:55:59,262 - DEBUG - HTTP请求详情: {"request_id": "47d741da", "timestamp": "2025-04-16T16:55:59.202601", "method": "POST", "url": null, "json": {"username": "testuser_1744793759", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "headers": {"Content-Type": "application/json"}, "status_code": 500, "elapsed_time": "0.055s", "response": "{\n  \"detail\": \"Unexpected error: mobile_register() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:55:59,272 - DEBUG - [7e58664b] HTTP请求开始 - POST None
2025-04-16 16:55:59,330 - WARNING - [7e58664b] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.058s
2025-04-16 16:55:59,331 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_login() got an unexpected keyword argument 'args'"
}
2025-04-16 16:55:59,333 - DEBUG - HTTP请求详情: {"request_id": "7e58664b", "timestamp": "2025-04-16T16:55:59.272303", "method": "POST", "url": null, "json": {"username": "testuser_1744793759", "password_hash": "********", "timestamp": 1744793759}, "headers": {"Content-Type": "application/json"}, "status_code": 500, "elapsed_time": "0.058s", "response": "{\n  \"detail\": \"Unexpected error: mobile_login() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 16:55:59,349 - DEBUG - [3fc30cff] HTTP请求开始 - GET None
2025-04-16 16:55:59,433 - INFO - [3fc30cff] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.083s
2025-04-16 16:55:59,434 - DEBUG - HTTP请求详情: {"request_id": "3fc30cff", "timestamp": "2025-04-16T16:55:59.349596", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.083s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 16:55:59,439 - DEBUG - [65300645] HTTP请求开始 - POST None
2025-04-16 16:55:59,499 - WARNING - [65300645] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.060s
2025-04-16 16:55:59,500 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 16:55:59,502 - DEBUG - HTTP请求详情: {"request_id": "65300645", "timestamp": "2025-04-16T16:55:59.439416", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.060s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 17:00:56,517 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 17:00:56,520 - INFO - 已启用HTTP请求日志记录
2025-04-16 17:00:56,547 - DEBUG - [b26d63cd] HTTP请求开始 - GET None
2025-04-16 17:00:56,608 - INFO - [b26d63cd] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.061s
2025-04-16 17:00:56,609 - DEBUG - HTTP请求详情: {"request_id": "b26d63cd", "timestamp": "2025-04-16T17:00:56.546954", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.061s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:00:56,613 - DEBUG - [f264036b] HTTP请求开始 - GET None
2025-04-16 17:00:56,681 - INFO - [f264036b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.068s
2025-04-16 17:00:56,682 - DEBUG - HTTP请求详情: {"request_id": "f264036b", "timestamp": "2025-04-16T17:00:56.613495", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.068s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:00:56,695 - DEBUG - [2e01a4ea] HTTP请求开始 - POST None
2025-04-16 17:00:56,746 - WARNING - [2e01a4ea] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.050s
2025-04-16 17:00:56,747 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 17:00:56,757 - DEBUG - HTTP请求详情: {"request_id": "2e01a4ea", "timestamp": "2025-04-16T17:00:56.695723", "method": "POST", "url": null, "data": {"username": "testuser_1744794056", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.050s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 17:00:56,770 - DEBUG - [52685900] HTTP请求开始 - POST None
2025-04-16 17:00:56,848 - WARNING - [52685900] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.077s
2025-04-16 17:00:56,849 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 17:00:56,858 - DEBUG - HTTP请求详情: {"request_id": "52685900", "timestamp": "2025-04-16T17:00:56.770612", "method": "POST", "url": null, "data": {"username": "testuser_1744794056", "password_hash": "********", "timestamp": 1744794056}, "status_code": 422, "elapsed_time": "0.077s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 17:00:56,876 - DEBUG - [0d56ea57] HTTP请求开始 - GET None
2025-04-16 17:00:56,945 - INFO - [0d56ea57] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.070s
2025-04-16 17:00:56,947 - DEBUG - HTTP请求详情: {"request_id": "0d56ea57", "timestamp": "2025-04-16T17:00:56.876226", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.070s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:00:56,952 - DEBUG - [7cacda5c] HTTP请求开始 - POST None
2025-04-16 17:00:57,030 - WARNING - [7cacda5c] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.077s
2025-04-16 17:00:57,031 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 17:00:57,033 - DEBUG - HTTP请求详情: {"request_id": "7cacda5c", "timestamp": "2025-04-16T17:00:56.952742", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.077s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 17:04:17,385 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 17:04:17,395 - INFO - 已启用HTTP请求日志记录
2025-04-16 17:04:17,648 - DEBUG - [d5e58b8c] HTTP请求开始 - GET None
2025-04-16 17:04:17,756 - INFO - [d5e58b8c] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.108s
2025-04-16 17:04:17,773 - DEBUG - HTTP请求详情: {"request_id": "d5e58b8c", "timestamp": "2025-04-16T17:04:17.647926", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.108s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:04:17,786 - DEBUG - [b262d621] HTTP请求开始 - GET None
2025-04-16 17:04:17,876 - INFO - [b262d621] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.090s
2025-04-16 17:04:17,878 - DEBUG - HTTP请求详情: {"request_id": "b262d621", "timestamp": "2025-04-16T17:04:17.786816", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.090s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:04:17,888 - DEBUG - [4b52235d] HTTP请求开始 - POST None
2025-04-16 17:04:18,018 - WARNING - [4b52235d] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.130s
2025-04-16 17:04:18,019 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 17:04:18,028 - DEBUG - HTTP请求详情: {"request_id": "4b52235d", "timestamp": "2025-04-16T17:04:17.887985", "method": "POST", "url": null, "json": {"username": "testuser_**********", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "5ad10df79f012313cb94ab4589929bd6d259fa199618d336691ac31e0305f7ca"}, "status_code": 422, "elapsed_time": "0.130s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 17:04:18,041 - DEBUG - [4ede285f] HTTP请求开始 - POST None
2025-04-16 17:04:18,146 - WARNING - [4ede285f] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.105s
2025-04-16 17:04:18,147 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 17:04:18,155 - DEBUG - HTTP请求详情: {"request_id": "4ede285f", "timestamp": "2025-04-16T17:04:18.041694", "method": "POST", "url": null, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "0a52ad6d71f9d4d599b7d84bcb1c43840f913a910e6c9dd7ad8a498a9e54f2af"}, "status_code": 422, "elapsed_time": "0.105s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 17:04:18,170 - DEBUG - [21576e6d] HTTP请求开始 - GET None
2025-04-16 17:04:18,223 - INFO - [21576e6d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.052s
2025-04-16 17:04:18,224 - DEBUG - HTTP请求详情: {"request_id": "21576e6d", "timestamp": "2025-04-16T17:04:18.170734", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.052s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:04:18,228 - DEBUG - [b75c01af] HTTP请求开始 - POST None
2025-04-16 17:04:18,365 - WARNING - [b75c01af] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.136s
2025-04-16 17:04:18,366 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 17:04:18,367 - DEBUG - HTTP请求详情: {"request_id": "b75c01af", "timestamp": "2025-04-16T17:04:18.228735", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.136s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 17:05:25,955 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 17:05:25,970 - INFO - 已启用HTTP请求日志记录
2025-04-16 17:05:26,184 - DEBUG - [388496b3] HTTP请求开始 - GET None
2025-04-16 17:05:27,166 - DEBUG - [7c8a9839] HTTP请求开始 - GET None
2025-04-16 17:05:27,302 - INFO - [7c8a9839] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.135s
2025-04-16 17:05:27,303 - DEBUG - HTTP请求详情: {"request_id": "7c8a9839", "timestamp": "2025-04-16T17:05:27.166505", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.135s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:05:27,395 - INFO - [388496b3] HTTP响应 - GET None - 状态码: 200 - 耗时: 1.210s
2025-04-16 17:05:27,396 - DEBUG - HTTP请求详情: {"request_id": "388496b3", "timestamp": "2025-04-16T17:05:26.184817", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "1.210s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:05:27,404 - DEBUG - [ceb94a6d] HTTP请求开始 - GET None
2025-04-16 17:05:27,462 - INFO - [ceb94a6d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.059s
2025-04-16 17:05:27,464 - DEBUG - HTTP请求详情: {"request_id": "ceb94a6d", "timestamp": "2025-04-16T17:05:27.403973", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.059s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:05:27,484 - DEBUG - [773f44fa] HTTP请求开始 - POST None
2025-04-16 17:05:27,561 - WARNING - [773f44fa] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.077s
2025-04-16 17:05:27,562 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 17:05:27,577 - DEBUG - HTTP请求详情: {"request_id": "773f44fa", "timestamp": "2025-04-16T17:05:27.483931", "method": "POST", "url": null, "json": {"username": "testuser_1744794326", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "a7425980a817e9fcf827c591f7c2a1267dd90d6049067d3bb9964865c695baa7"}, "status_code": 422, "elapsed_time": "0.077s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 17:05:27,601 - DEBUG - [79d8f54b] HTTP请求开始 - POST None
2025-04-16 17:05:27,693 - WARNING - [79d8f54b] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.092s
2025-04-16 17:05:27,694 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 17:05:27,708 - DEBUG - HTTP请求详情: {"request_id": "79d8f54b", "timestamp": "2025-04-16T17:05:27.601360", "method": "POST", "url": null, "json": {"username": "testuser_1744794326", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "a7425980a817e9fcf827c591f7c2a1267dd90d6049067d3bb9964865c695baa7"}, "status_code": 422, "elapsed_time": "0.092s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 17:05:27,734 - DEBUG - [527491e7] HTTP请求开始 - GET None
2025-04-16 17:05:27,904 - INFO - [527491e7] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.170s
2025-04-16 17:05:27,905 - DEBUG - HTTP请求详情: {"request_id": "527491e7", "timestamp": "2025-04-16T17:05:27.734885", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.170s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 17:05:27,909 - DEBUG - [b61b13da] HTTP请求开始 - POST None
2025-04-16 17:05:27,984 - WARNING - [b61b13da] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.075s
2025-04-16 17:05:27,985 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 17:05:27,986 - DEBUG - HTTP请求详情: {"request_id": "b61b13da", "timestamp": "2025-04-16T17:05:27.909722", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.075s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:09:11,141 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:09:11,145 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:09:11,251 - DEBUG - [00db14e7] HTTP请求开始 - GET None
2025-04-16 18:09:11,297 - INFO - [00db14e7] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 18:09:11,304 - DEBUG - HTTP请求详情: {"request_id": "00db14e7", "timestamp": "2025-04-16T18:09:11.251093", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:09:11,313 - DEBUG - [abd66ad2] HTTP请求开始 - GET None
2025-04-16 18:09:11,380 - INFO - [abd66ad2] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.067s
2025-04-16 18:09:11,381 - DEBUG - HTTP请求详情: {"request_id": "abd66ad2", "timestamp": "2025-04-16T18:09:11.313305", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.067s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:09:11,396 - DEBUG - [3bfe2878] HTTP请求开始 - POST None
2025-04-16 18:09:11,447 - WARNING - [3bfe2878] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.051s
2025-04-16 18:09:11,448 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:09:11,463 - DEBUG - HTTP请求详情: {"request_id": "3bfe2878", "timestamp": "2025-04-16T18:09:11.396011", "method": "POST", "url": null, "json": {"username": "testuser_**********", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "7fc044c988f5b151b608f49b1ff9f98fe35278801393f6923913a2a094ef795f"}, "status_code": 422, "elapsed_time": "0.051s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:09:11,482 - DEBUG - [a203e0e7] HTTP请求开始 - POST None
2025-04-16 18:09:11,532 - WARNING - [a203e0e7] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.049s
2025-04-16 18:09:11,534 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:09:11,545 - DEBUG - HTTP请求详情: {"request_id": "a203e0e7", "timestamp": "2025-04-16T18:09:11.482729", "method": "POST", "url": null, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "7fc044c988f5b151b608f49b1ff9f98fe35278801393f6923913a2a094ef795f"}, "status_code": 422, "elapsed_time": "0.049s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:09:11,564 - DEBUG - [060cba94] HTTP请求开始 - GET None
2025-04-16 18:09:11,605 - INFO - [060cba94] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 18:09:11,606 - DEBUG - HTTP请求详情: {"request_id": "060cba94", "timestamp": "2025-04-16T18:09:11.564237", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:09:11,612 - DEBUG - [88087102] HTTP请求开始 - POST None
2025-04-16 18:09:11,738 - WARNING - [88087102] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.126s
2025-04-16 18:09:11,740 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:09:11,742 - DEBUG - HTTP请求详情: {"request_id": "88087102", "timestamp": "2025-04-16T18:09:11.612151", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.126s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:43:16,007 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:43:16,014 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:43:16,482 - DEBUG - [73a100bc] HTTP请求开始 - GET None
2025-04-16 18:43:16,563 - INFO - [73a100bc] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.081s
2025-04-16 18:43:16,582 - DEBUG - HTTP请求详情: {"request_id": "73a100bc", "timestamp": "2025-04-16T18:43:16.481687", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.081s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:43:16,587 - DEBUG - [dad9f54d] HTTP请求开始 - GET None
2025-04-16 18:43:16,659 - INFO - [dad9f54d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.073s
2025-04-16 18:43:16,666 - DEBUG - HTTP请求详情: {"request_id": "dad9f54d", "timestamp": "2025-04-16T18:43:16.587266", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.073s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:43:16,680 - DEBUG - [fd5011b6] HTTP请求开始 - POST None
2025-04-16 18:43:16,733 - WARNING - [fd5011b6] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.054s
2025-04-16 18:43:16,735 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:43:16,747 - DEBUG - HTTP请求详情: {"request_id": "fd5011b6", "timestamp": "2025-04-16T18:43:16.679914", "method": "POST", "url": null, "params": {"args": [], "kwargs": {}}, "json": {"username": "testuser_**********", "name": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "2935de32ad0820948f259aac9e46afc5c8c66e1e06b4f29f0a982771e8038fcf"}, "status_code": 422, "elapsed_time": "0.054s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:43:16,768 - DEBUG - [f5d250e3] HTTP请求开始 - POST None
2025-04-16 18:43:16,834 - WARNING - [f5d250e3] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.066s
2025-04-16 18:43:16,835 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:43:16,847 - DEBUG - HTTP请求详情: {"request_id": "f5d250e3", "timestamp": "2025-04-16T18:43:16.768121", "method": "POST", "url": null, "params": {"args": [], "kwargs": {}}, "json": {"username": "testuser_**********", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "2935de32ad0820948f259aac9e46afc5c8c66e1e06b4f29f0a982771e8038fcf"}, "status_code": 422, "elapsed_time": "0.066s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:43:16,869 - DEBUG - [e81dd1b8] HTTP请求开始 - GET None
2025-04-16 18:43:16,950 - INFO - [e81dd1b8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.081s
2025-04-16 18:43:16,952 - DEBUG - HTTP请求详情: {"request_id": "e81dd1b8", "timestamp": "2025-04-16T18:43:16.869820", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.081s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:43:16,957 - DEBUG - [b39c9f9c] HTTP请求开始 - POST None
2025-04-16 18:43:17,045 - WARNING - [b39c9f9c] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.088s
2025-04-16 18:43:17,048 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:43:17,050 - DEBUG - HTTP请求详情: {"request_id": "b39c9f9c", "timestamp": "2025-04-16T18:43:16.957778", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.088s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:46:06,215 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:46:06,216 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:46:06,237 - DEBUG - [8b3159f3] HTTP请求开始 - GET None
2025-04-16 18:46:06,269 - INFO - [8b3159f3] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.032s
2025-04-16 18:46:06,270 - DEBUG - HTTP请求详情: {"request_id": "8b3159f3", "timestamp": "2025-04-16T18:46:06.237434", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.032s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:46:06,279 - DEBUG - [d9ef6655] HTTP请求开始 - GET None
2025-04-16 18:46:06,306 - INFO - [d9ef6655] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.027s
2025-04-16 18:46:06,309 - DEBUG - HTTP请求详情: {"request_id": "d9ef6655", "timestamp": "2025-04-16T18:46:06.279151", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.027s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:46:06,322 - DEBUG - [6c28ed79] HTTP请求开始 - POST None
2025-04-16 18:46:06,361 - WARNING - [6c28ed79] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.039s
2025-04-16 18:46:06,363 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:46:06,373 - DEBUG - HTTP请求详情: {"request_id": "6c28ed79", "timestamp": "2025-04-16T18:46:06.322269", "method": "POST", "url": null, "json": {"username": "testuser_1744800366", "name": "测试用户", "password_hash": "********", "email": "<EMAIL>", "role": "personal_user"}, "status_code": 422, "elapsed_time": "0.039s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:46:06,386 - DEBUG - [f74f4893] HTTP请求开始 - POST None
2025-04-16 18:46:06,416 - WARNING - [f74f4893] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.029s
2025-04-16 18:46:06,417 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:46:06,428 - DEBUG - HTTP请求详情: {"request_id": "f74f4893", "timestamp": "2025-04-16T18:46:06.386759", "method": "POST", "url": null, "json": {"username": "testuser_1744800366", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.029s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:46:06,448 - DEBUG - [7cb6ebe8] HTTP请求开始 - GET None
2025-04-16 18:46:06,476 - INFO - [7cb6ebe8] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.029s
2025-04-16 18:46:06,478 - DEBUG - HTTP请求详情: {"request_id": "7cb6ebe8", "timestamp": "2025-04-16T18:46:06.448112", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.029s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:46:06,484 - DEBUG - [a51a8b79] HTTP请求开始 - POST None
2025-04-16 18:46:06,527 - WARNING - [a51a8b79] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.043s
2025-04-16 18:46:06,528 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:46:06,530 - DEBUG - HTTP请求详情: {"request_id": "a51a8b79", "timestamp": "2025-04-16T18:46:06.484454", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.043s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:47:18,981 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:47:18,983 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:47:19,004 - DEBUG - [5b88f238] HTTP请求开始 - GET None
2025-04-16 18:47:19,036 - INFO - [5b88f238] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.032s
2025-04-16 18:47:19,037 - DEBUG - HTTP请求详情: {"request_id": "5b88f238", "timestamp": "2025-04-16T18:47:19.004482", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.032s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:47:19,041 - DEBUG - [8a10c151] HTTP请求开始 - GET None
2025-04-16 18:47:19,075 - INFO - [8a10c151] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.034s
2025-04-16 18:47:19,076 - DEBUG - HTTP请求详情: {"request_id": "8a10c151", "timestamp": "2025-04-16T18:47:19.041198", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.034s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:47:19,089 - DEBUG - [af642661] HTTP请求开始 - POST None
2025-04-16 18:47:19,120 - WARNING - [af642661] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.031s
2025-04-16 18:47:19,122 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:47:19,123 - DEBUG - HTTP请求详情: {"request_id": "af642661", "timestamp": "2025-04-16T18:47:19.089678", "method": "POST", "url": null, "json": {"username": "testuser_1744800439", "name": "测试用户", "password": "********", "email": "<EMAIL>", "role": "personal_user"}, "status_code": 405, "elapsed_time": "0.031s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:47:19,135 - DEBUG - [8d4c5922] HTTP请求开始 - POST None
2025-04-16 18:47:19,198 - WARNING - [8d4c5922] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.063s
2025-04-16 18:47:19,200 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:47:19,202 - DEBUG - HTTP请求详情: {"request_id": "8d4c5922", "timestamp": "2025-04-16T18:47:19.135587", "method": "POST", "url": null, "json": {"username": "testuser_1744800439", "password": "********"}, "status_code": 405, "elapsed_time": "0.063s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:47:19,220 - DEBUG - [d214b5f5] HTTP请求开始 - GET None
2025-04-16 18:47:19,295 - INFO - [d214b5f5] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.075s
2025-04-16 18:47:19,298 - DEBUG - HTTP请求详情: {"request_id": "d214b5f5", "timestamp": "2025-04-16T18:47:19.220133", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.075s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:47:19,303 - DEBUG - [2ef36e0c] HTTP请求开始 - POST None
2025-04-16 18:47:19,403 - WARNING - [2ef36e0c] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.100s
2025-04-16 18:47:19,405 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:47:19,407 - DEBUG - HTTP请求详情: {"request_id": "2ef36e0c", "timestamp": "2025-04-16T18:47:19.303859", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.100s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:49:01,270 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:49:01,307 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:49:01,689 - DEBUG - [81bfd73d] HTTP请求开始 - GET None
2025-04-16 18:49:01,786 - INFO - [81bfd73d] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.097s
2025-04-16 18:49:01,794 - DEBUG - HTTP请求详情: {"request_id": "81bfd73d", "timestamp": "2025-04-16T18:49:01.688994", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.097s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:01,798 - DEBUG - [7f1b09b2] HTTP请求开始 - GET None
2025-04-16 18:49:01,840 - INFO - [7f1b09b2] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.041s
2025-04-16 18:49:01,841 - DEBUG - HTTP请求详情: {"request_id": "7f1b09b2", "timestamp": "2025-04-16T18:49:01.798700", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.041s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:01,855 - DEBUG - [6bd5c7c2] HTTP请求开始 - POST None
2025-04-16 18:49:01,907 - WARNING - [6bd5c7c2] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.052s
2025-04-16 18:49:01,909 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:49:01,917 - DEBUG - HTTP请求详情: {"request_id": "6bd5c7c2", "timestamp": "2025-04-16T18:49:01.855249", "method": "POST", "url": null, "json": {"username": "testuser_1744800541", "name": "测试用户", "password_hash": "********", "email": "<EMAIL>", "role": "personal_user"}, "status_code": 422, "elapsed_time": "0.052s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:49:01,934 - DEBUG - [63906492] HTTP请求开始 - POST None
2025-04-16 18:49:01,976 - WARNING - [63906492] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.041s
2025-04-16 18:49:01,977 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:49:01,988 - DEBUG - HTTP请求详情: {"request_id": "63906492", "timestamp": "2025-04-16T18:49:01.934505", "method": "POST", "url": null, "json": {"username": "testuser_1744800541", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.041s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:49:02,008 - DEBUG - [4f3e32e3] HTTP请求开始 - GET None
2025-04-16 18:49:02,056 - INFO - [4f3e32e3] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.048s
2025-04-16 18:49:02,057 - DEBUG - HTTP请求详情: {"request_id": "4f3e32e3", "timestamp": "2025-04-16T18:49:02.008196", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.048s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:02,062 - DEBUG - [0227a004] HTTP请求开始 - POST None
2025-04-16 18:49:02,125 - WARNING - [0227a004] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.063s
2025-04-16 18:49:02,127 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:49:02,129 - DEBUG - HTTP请求详情: {"request_id": "0227a004", "timestamp": "2025-04-16T18:49:02.062844", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.063s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:49:46,071 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:49:46,116 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:49:47,201 - DEBUG - [d5668988] HTTP请求开始 - GET None
2025-04-16 18:49:47,363 - INFO - [d5668988] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.162s
2025-04-16 18:49:47,372 - DEBUG - HTTP请求详情: {"request_id": "d5668988", "timestamp": "2025-04-16T18:49:47.201435", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.162s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:47,378 - DEBUG - [9e685d7b] HTTP请求开始 - GET None
2025-04-16 18:49:47,420 - INFO - [9e685d7b] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.042s
2025-04-16 18:49:47,421 - DEBUG - HTTP请求详情: {"request_id": "9e685d7b", "timestamp": "2025-04-16T18:49:47.378112", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.042s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:47,434 - DEBUG - [2aeb28c9] HTTP请求开始 - POST None
2025-04-16 18:49:47,483 - WARNING - [2aeb28c9] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.049s
2025-04-16 18:49:47,485 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_register() got an unexpected keyword argument 'args'"
}
2025-04-16 18:49:47,487 - DEBUG - HTTP请求详情: {"request_id": "2aeb28c9", "timestamp": "2025-04-16T18:49:47.434483", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744800586", "name": "测试用户", "password_hash": "********", "email": "<EMAIL>", "role": "personal_user"}, "status_code": 500, "elapsed_time": "0.049s", "response": "{\n  \"detail\": \"Unexpected error: mobile_register() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 18:49:47,500 - DEBUG - [f5cef8bc] HTTP请求开始 - POST None
2025-04-16 18:49:47,572 - WARNING - [f5cef8bc] HTTP响应 - POST None - 状态码: 500 - 耗时: 0.072s
2025-04-16 18:49:47,573 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 500 - 响应: {
  "detail": "Unexpected error: mobile_login() got an unexpected keyword argument 'args'"
}
2025-04-16 18:49:47,575 - DEBUG - HTTP请求详情: {"request_id": "f5cef8bc", "timestamp": "2025-04-16T18:49:47.500231", "method": "POST", "url": null, "params": {"args": "", "kwargs": ""}, "json": {"username": "testuser_1744800586", "password_hash": "********"}, "status_code": 500, "elapsed_time": "0.072s", "response": "{\n  \"detail\": \"Unexpected error: mobile_login() got an unexpected keyword argument 'args'\"\n}"}
2025-04-16 18:49:47,864 - DEBUG - [40311534] HTTP请求开始 - GET None
2025-04-16 18:49:47,882 - DEBUG - [07b0f567] HTTP请求开始 - GET None
2025-04-16 18:49:47,952 - INFO - [40311534] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.088s
2025-04-16 18:49:47,954 - DEBUG - HTTP请求详情: {"request_id": "40311534", "timestamp": "2025-04-16T18:49:47.864414", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.088s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:47,976 - INFO - [07b0f567] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.093s
2025-04-16 18:49:47,978 - DEBUG - HTTP请求详情: {"request_id": "07b0f567", "timestamp": "2025-04-16T18:49:47.882898", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.093s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:49:47,984 - DEBUG - [136e0ec9] HTTP请求开始 - POST None
2025-04-16 18:49:48,040 - WARNING - [136e0ec9] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.056s
2025-04-16 18:49:48,041 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:49:48,045 - DEBUG - HTTP请求详情: {"request_id": "136e0ec9", "timestamp": "2025-04-16T18:49:47.984250", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.056s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:51:33,477 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:51:33,482 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:51:33,683 - DEBUG - [15f2d796] HTTP请求开始 - GET None
2025-04-16 18:51:33,729 - INFO - [15f2d796] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.045s
2025-04-16 18:51:33,731 - DEBUG - HTTP请求详情: {"request_id": "15f2d796", "timestamp": "2025-04-16T18:51:33.683851", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.045s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:51:33,751 - DEBUG - [78de95a1] HTTP请求开始 - GET None
2025-04-16 18:51:33,787 - INFO - [78de95a1] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.036s
2025-04-16 18:51:33,788 - DEBUG - HTTP请求详情: {"request_id": "78de95a1", "timestamp": "2025-04-16T18:51:33.751150", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.036s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:51:33,803 - DEBUG - [9e491258] HTTP请求开始 - POST None
2025-04-16 18:51:33,840 - WARNING - [9e491258] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.036s
2025-04-16 18:51:33,841 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:51:33,850 - DEBUG - HTTP请求详情: {"request_id": "9e491258", "timestamp": "2025-04-16T18:51:33.803536", "method": "POST", "url": null, "json": {"username": "testuser_1744800693", "name": "测试用户", "password_hash": "********", "email": "<EMAIL>", "role": "personal_user"}, "status_code": 422, "elapsed_time": "0.036s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:51:33,867 - DEBUG - [5bd45b23] HTTP请求开始 - POST None
2025-04-16 18:51:33,910 - WARNING - [5bd45b23] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.043s
2025-04-16 18:51:33,911 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:51:33,921 - DEBUG - HTTP请求详情: {"request_id": "5bd45b23", "timestamp": "2025-04-16T18:51:33.867491", "method": "POST", "url": null, "json": {"username": "testuser_1744800693", "password_hash": "********"}, "status_code": 422, "elapsed_time": "0.043s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:51:33,938 - DEBUG - [dc70f33e] HTTP请求开始 - GET None
2025-04-16 18:51:33,973 - INFO - [dc70f33e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.035s
2025-04-16 18:51:33,974 - DEBUG - HTTP请求详情: {"request_id": "dc70f33e", "timestamp": "2025-04-16T18:51:33.938642", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.035s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:51:33,980 - DEBUG - [4a238f04] HTTP请求开始 - POST None
2025-04-16 18:51:34,103 - WARNING - [4a238f04] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.123s
2025-04-16 18:51:34,104 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:51:34,106 - DEBUG - HTTP请求详情: {"request_id": "4a238f04", "timestamp": "2025-04-16T18:51:33.980393", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.123s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 18:58:30,592 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 18:58:30,598 - INFO - 已启用HTTP请求日志记录
2025-04-16 18:58:30,632 - DEBUG - [015e058e] HTTP请求开始 - GET None
2025-04-16 18:58:30,677 - INFO - [015e058e] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.044s
2025-04-16 18:58:30,683 - DEBUG - HTTP请求详情: {"request_id": "015e058e", "timestamp": "2025-04-16T18:58:30.632778", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.044s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:58:30,687 - DEBUG - [df227233] HTTP请求开始 - GET None
2025-04-16 18:58:30,716 - INFO - [df227233] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.029s
2025-04-16 18:58:30,717 - DEBUG - HTTP请求详情: {"request_id": "df227233", "timestamp": "2025-04-16T18:58:30.687089", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.029s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:58:30,729 - DEBUG - [2b2eb654] HTTP请求开始 - POST None
2025-04-16 18:58:30,765 - WARNING - [2b2eb654] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.036s
2025-04-16 18:58:30,767 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:58:30,778 - DEBUG - HTTP请求详情: {"request_id": "2b2eb654", "timestamp": "2025-04-16T18:58:30.729604", "method": "POST", "url": null, "json": {"username": "testuser_1744801110", "fullName": "测试用户", "password_hash": "********", "email": "<EMAIL>", "role": "personal_user", "timestamp": 1744801110, "signature": "549daacf9714d96b42b34d4155001d440dd4c861bfd7398b07f9ed3fdff18939"}, "status_code": 422, "elapsed_time": "0.036s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:58:30,798 - DEBUG - [ae5bb739] HTTP请求开始 - POST None
2025-04-16 18:58:30,833 - WARNING - [ae5bb739] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.035s
2025-04-16 18:58:30,835 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 18:58:30,846 - DEBUG - HTTP请求详情: {"request_id": "ae5bb739", "timestamp": "2025-04-16T18:58:30.797920", "method": "POST", "url": null, "json": {"username": "testuser_1744801110", "password_hash": "********", "timestamp": 1744801110, "signature": "549daacf9714d96b42b34d4155001d440dd4c861bfd7398b07f9ed3fdff18939"}, "status_code": 422, "elapsed_time": "0.035s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 18:58:30,869 - DEBUG - [15adfcf7] HTTP请求开始 - GET None
2025-04-16 18:58:30,901 - INFO - [15adfcf7] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.032s
2025-04-16 18:58:30,902 - DEBUG - HTTP请求详情: {"request_id": "15adfcf7", "timestamp": "2025-04-16T18:58:30.869243", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.032s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 18:58:30,907 - DEBUG - [d5e1faa6] HTTP请求开始 - POST None
2025-04-16 18:58:30,947 - WARNING - [d5e1faa6] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.041s
2025-04-16 18:58:30,950 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 18:58:30,953 - DEBUG - HTTP请求详情: {"request_id": "d5e1faa6", "timestamp": "2025-04-16T18:58:30.907248", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.041s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
2025-04-16 20:10:34,988 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 20:10:34,990 - INFO - 已启用HTTP请求日志记录
2025-04-16 20:10:35,087 - DEBUG - [4bc0e8ac] HTTP请求开始 - GET None
2025-04-16 20:10:35,136 - WARNING - [4bc0e8ac] HTTP响应 - GET None - 状态码: 502 - 耗时: 0.049s
2025-04-16 20:10:35,138 - WARNING - HTTP请求返回错误状态码 - GET None - 状态码: 502 - 响应: <html>

<head><title>502 Bad Gateway</title></head>

<body>

<center><h1>502 Bad Gateway</h1></center>

<hr><center>nginx</center>

</body>

</html>


2025-04-16 20:10:35,143 - DEBUG - HTTP请求详情: {"request_id": "4bc0e8ac", "timestamp": "2025-04-16T20:10:35.087724", "method": "GET", "url": null, "status_code": 502, "elapsed_time": "0.049s", "response": "<html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"}
2025-04-16 20:10:35,146 - DEBUG - [95f94768] HTTP请求开始 - GET None
2025-04-16 20:10:36,042 - DEBUG - [5411c6ae] HTTP请求开始 - GET None
2025-04-16 20:10:40,433 - ERROR - [95f94768] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.017s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 20:10:40,468 - DEBUG - HTTP请求详情: {"request_id": "95f94768", "timestamp": "2025-04-16T20:10:35.146908", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.017s"}
2025-04-16 20:10:41,065 - ERROR - [5411c6ae] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.013s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 20:10:41,130 - DEBUG - HTTP请求详情: {"request_id": "5411c6ae", "timestamp": "2025-04-16T20:10:36.042617", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.013s"}
2025-04-16 20:10:41,519 - DEBUG - [ba3c852e] HTTP请求开始 - GET None
2025-04-16 20:10:46,604 - ERROR - [ba3c852e] HTTP请求异常 - GET None - 异常: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5) - 耗时: 5.076s
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\health-Trea\mobile\utils\http_logger.py", line 287, in wrapper
    response = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)

2025-04-16 20:10:46,654 - DEBUG - HTTP请求详情: {"request_id": "ba3c852e", "timestamp": "2025-04-16T20:10:41.519590", "method": "GET", "url": null, "exception": "HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=5)", "elapsed_time": "5.076s"}
2025-04-16 20:10:46,745 - DEBUG - [3a09e6ec] HTTP请求开始 - POST None
2025-04-16 20:10:51,765 - WARNING - [3a09e6ec] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.021s
2025-04-16 20:10:51,768 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 20:10:51,770 - DEBUG - HTTP请求详情: {"request_id": "3a09e6ec", "timestamp": "2025-04-16T20:10:46.745179", "method": "POST", "url": null, "json": {"username": "testuser_1744805435", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "a47a574201f87783f0fcb66ea98cafe55c75200f1e3374ed4e6d81c4988eac78"}, "status_code": 502, "elapsed_time": "5.021s"}
2025-04-16 20:10:51,799 - DEBUG - [f0fed1ab] HTTP请求开始 - POST None
2025-04-16 20:10:56,840 - WARNING - [f0fed1ab] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.041s
2025-04-16 20:10:56,841 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 20:10:56,844 - DEBUG - HTTP请求详情: {"request_id": "f0fed1ab", "timestamp": "2025-04-16T20:10:51.798937", "method": "POST", "url": null, "json": {"username": "testuser_1744805435", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "84b6cd863f2b5b767e5f119a07b4d846b1515955e8d284af0f8bee70a2cbbc24"}, "status_code": 502, "elapsed_time": "5.041s"}
2025-04-16 20:10:56,870 - DEBUG - [a7ce5217] HTTP请求开始 - POST None
2025-04-16 20:11:01,960 - WARNING - [a7ce5217] HTTP响应 - POST None - 状态码: 502 - 耗时: 5.090s
2025-04-16 20:11:01,963 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 502 - 响应: None
2025-04-16 20:11:01,966 - DEBUG - HTTP请求详情: {"request_id": "a7ce5217", "timestamp": "2025-04-16T20:10:56.870830", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 502, "elapsed_time": "5.090s"}
2025-04-16 20:14:54,851 - INFO - HTTP请求日志初始化完成，日志文件: c:\Users\<USER>\Desktop\health-Trea\mobile\logs\http_requests_20250416.log
2025-04-16 20:14:54,853 - INFO - 已启用HTTP请求日志记录
2025-04-16 20:14:54,891 - DEBUG - [371123d9] HTTP请求开始 - GET None
2025-04-16 20:14:54,940 - INFO - [371123d9] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.049s
2025-04-16 20:14:54,941 - DEBUG - HTTP请求详情: {"request_id": "371123d9", "timestamp": "2025-04-16T20:14:54.891210", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.049s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 20:14:54,950 - DEBUG - [e2049415] HTTP请求开始 - GET None
2025-04-16 20:14:55,023 - INFO - [e2049415] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.073s
2025-04-16 20:14:55,024 - DEBUG - HTTP请求详情: {"request_id": "e2049415", "timestamp": "2025-04-16T20:14:54.950484", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.073s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 20:14:55,039 - DEBUG - [d0b124ce] HTTP请求开始 - POST None
2025-04-16 20:14:55,093 - WARNING - [d0b124ce] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.053s
2025-04-16 20:14:55,100 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 20:14:55,108 - DEBUG - HTTP请求详情: {"request_id": "d0b124ce", "timestamp": "2025-04-16T20:14:55.039617", "method": "POST", "url": null, "json": {"username": "testuser_1744805694", "fullName": "测试用户", "email": "<EMAIL>", "role": "personal_user", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "af04ab32a5e33abdf4c6eb40ce095be82dee917803dd7e33e05268b9380d884c"}, "status_code": 422, "elapsed_time": "0.053s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 20:14:55,130 - DEBUG - [b813d21e] HTTP请求开始 - POST None
2025-04-16 20:14:55,183 - WARNING - [b813d21e] HTTP响应 - POST None - 状态码: 422 - 耗时: 0.053s
2025-04-16 20:14:55,184 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 422 - 响应: {
  "detail": [
    {
      "type": "missing",
      "loc": [
        "query",
        "args"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    },
    {
      "type": "missing",
      "loc": [
        "query",
        "kwargs"
      ],
      "msg": "Field required",
      "input": null,
      "url": "https://errors.pydantic.dev/2.6/v/missing"
    }
  ]
}
2025-04-16 20:14:55,192 - DEBUG - HTTP请求详情: {"request_id": "b813d21e", "timestamp": "2025-04-16T20:14:55.130202", "method": "POST", "url": null, "json": {"username": "testuser_1744805694", "password_hash": "********", "timestamp": **********, "app_id": "health_trea_app", "signature": "af04ab32a5e33abdf4c6eb40ce095be82dee917803dd7e33e05268b9380d884c"}, "status_code": 422, "elapsed_time": "0.053s", "response": "{\n  \"detail\": [\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"args\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    },\n    {\n      \"type\": \"missing\",\n      \"loc\": [\n        \"query\",\n        \"kwargs\"\n      ],\n      \"msg\": \"Field required\",\n      \"input\": null,\n      \"url\": \"https://errors.pydantic.dev/2.6/v/missing\"\n    }\n  ]\n}"}
2025-04-16 20:14:55,212 - DEBUG - [4e7703c2] HTTP请求开始 - GET None
2025-04-16 20:14:55,246 - INFO - [4e7703c2] HTTP响应 - GET None - 状态码: 200 - 耗时: 0.033s
2025-04-16 20:14:55,249 - DEBUG - HTTP请求详情: {"request_id": "4e7703c2", "timestamp": "2025-04-16T20:14:55.212289", "method": "GET", "url": null, "status_code": 200, "elapsed_time": "0.033s", "response": "{\n  \"status\": \"healthy\"\n}"}
2025-04-16 20:14:55,255 - DEBUG - [a2130e0e] HTTP请求开始 - POST None
2025-04-16 20:14:55,289 - WARNING - [a2130e0e] HTTP响应 - POST None - 状态码: 405 - 耗时: 0.034s
2025-04-16 20:14:55,290 - WARNING - HTTP请求返回错误状态码 - POST None - 状态码: 405 - 响应: {
  "detail": "Method Not Allowed"
}
2025-04-16 20:14:55,292 - DEBUG - HTTP请求详情: {"request_id": "a2130e0e", "timestamp": "2025-04-16T20:14:55.255233", "method": "POST", "url": null, "headers": {"Authorization": "********"}, "status_code": 405, "elapsed_time": "0.034s", "response": "{\n  \"detail\": \"Method Not Allowed\"\n}"}
