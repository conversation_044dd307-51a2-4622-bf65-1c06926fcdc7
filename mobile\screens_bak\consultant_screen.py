# screens/consultant_screen.py
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, ListProperty
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.clock import Clock
import json
import os

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButton, MDButtonText
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.menu import MDDropdownMenu

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入Logo组件
from widgets.logo import HealthLogo

# 导入API客户端
from api.api_client import APIClient

# 定义KV语言字符串
KV = '''
<ConsultantScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'

        # 滚动内容区域
        MDScrollView:
            id: scroll_view
            do_scroll_x: False
            do_scroll_y: True

            # 主内容容器
            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(10), dp(16), dp(16)]
                spacing: dp(15)

                # Logo区域 - 与homepage_screen.py保持一致
                MDBoxLayout:
                    id: logo_container_wrap
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(200)  # 减小高度
                    padding: [0, dp(10), 0, dp(5)]

                    # Logo主容器
                    MDBoxLayout:
                        id: logo_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(100)  # 减小高度

                        # 使用统一的HealthLogo组件
                        HealthLogo:
                            id: health_logo

                # 顾问信息区域
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(50)
                    spacing: dp(5)

                    # 当前顾问信息
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(25)

                        MDLabel:
                            text: "当前健康顾问: 张三 医生"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.PRIMARY_COLOR
                            size_hint_y: None
                            height: dp(25)

                    # 正在咨询患者信息
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(25)

                        MDLabel:
                            text: "正在查询 王五 先生 健康资料"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_SECONDARY
                            size_hint_y: None
                            height: dp(25)

                # 顾问头像区域
                MDBoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: dp(80)
                    spacing: dp(15)

                    # 第一个成员头像
                    MDBoxLayout:
                        orientation: 'vertical'
                        size_hint_x: None
                        width: dp(60)
                        spacing: dp(5)

                        MDFloatLayout:
                            size_hint: None, None
                            size: dp(60), dp(60)

                            MDCard:
                                size_hint: None, None
                                size: dp(60), dp(60)
                                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                                radius: [dp(30)]
                                md_bg_color: app.theme.PRIMARY_MEDIUM

                                MDLabel:
                                    text: "王五"
                                    halign: "center"
                                    font_style: "Body"
                                    role: "large"
                                    bold: True
                                    theme_text_color: "Custom"
                                    text_color: app.theme.TEXT_PRIMARY
                                    pos_hint: {"center_x": 0.5, "center_y": 0.5}

                        MDLabel:
                            text: "王五"
                            halign: "center"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_PRIMARY
                            size_hint_y: None
                            height: dp(15)

                    # 第二个成员头像
                    MDBoxLayout:
                        orientation: 'vertical'
                        size_hint_x: None
                        width: dp(60)
                        spacing: dp(5)

                        MDFloatLayout:
                            size_hint: None, None
                            size: dp(60), dp(60)

                            MDCard:
                                size_hint: None, None
                                size: dp(60), dp(60)
                                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                                radius: [dp(30)]
                                md_bg_color: app.theme.PRIMARY_MEDIUM

                                MDLabel:
                                    text: "李四"
                                    halign: "center"
                                    font_style: "Body"
                                    role: "large"
                                    bold: True
                                    theme_text_color: "Custom"
                                    text_color: app.theme.TEXT_PRIMARY
                                    pos_hint: {"center_x": 0.5, "center_y": 0.5}

                        MDLabel:
                            text: "李四"
                            halign: "center"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_PRIMARY
                            size_hint_y: None
                            height: dp(15)

                    # 第三个成员头像
                    MDBoxLayout:
                        orientation: 'vertical'
                        size_hint_x: None
                        width: dp(60)
                        spacing: dp(5)

                        MDFloatLayout:
                            size_hint: None, None
                            size: dp(60), dp(60)

                            MDCard:
                                size_hint: None, None
                                size: dp(60), dp(60)
                                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                                radius: [dp(30)]
                                md_bg_color: app.theme.PRIMARY_MEDIUM

                                MDLabel:
                                    text: "张三"
                                    halign: "center"
                                    font_style: "Body"
                                    role: "large"
                                    bold: True
                                    theme_text_color: "Custom"
                                    text_color: app.theme.TEXT_PRIMARY
                                    pos_hint: {"center_x": 0.5, "center_y": 0.5}

                        MDLabel:
                            text: "张三"
                            halign: "center"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_PRIMARY
                            size_hint_y: None
                            height: dp(15)

                    # 添加成员按钮
                    MDBoxLayout:
                        orientation: 'vertical'
                        size_hint_x: None
                        width: dp(60)
                        spacing: dp(5)

                        MDFloatLayout:
                            size_hint: None, None
                            size: dp(60), dp(60)

                            MDCard:
                                size_hint: None, None
                                size: dp(60), dp(60)
                                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                                radius: [dp(30)]
                                md_bg_color: app.theme.BACKGROUND_COLOR
                                on_release: root.scan_qr_code()

                                MDIconButton:
                                    icon: "plus"
                                    pos_hint: {"center_x": 0.5, "center_y": 0.5}
                                    theme_icon_color: "Custom"
                                    icon_color: app.theme.TEXT_SECONDARY

                        MDLabel:
                            text: "添加成员"
                            halign: "center"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_SECONDARY
                            size_hint_y: None
                            height: dp(15)

                # QR码区域
                MDCard:
                    id: qr_code_card
                    orientation: "vertical"
                    size_hint_y: None
                    height: dp(130)  # 减小高度
                    md_bg_color: app.theme.CARD_BACKGROUND
                    radius: [dp(app.metrics.CORNER_RADIUS)]
                    on_release: root.scan_qr_code()

                    MDBoxLayout:
                        orientation: "vertical"
                        padding: [0, dp(10), 0, dp(5)]
                        spacing: dp(5)

                        # QR码图像
                        MDCard:
                            size_hint: None, None
                            size: dp(80), dp(80)  # 减小大小
                            pos_hint: {"center_x": 0.5}
                            md_bg_color: app.theme.BACKGROUND_COLOR
                            elevation: 0

                            MDLabel:
                                text: "QR"
                                halign: "center"
                                font_style: "Body"
                                role: "large"
                                bold: True
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_SECONDARY
                                pos_hint: {"center_x": 0.5, "center_y": 0.5}

                        MDLabel:
                            text: "扫描二维码添加成员"
                            halign: "center"
                            font_style: "Label"
                            theme_text_color: "Custom"
                            text_color: app.theme.TEXT_SECONDARY
                            size_hint_y: None
                            height: dp(25)

                # 功能按钮区域 - 第一行
                MDBoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(90)
                    spacing: dp(15)

                    # 健康资料收集
                    MDCard:
                        orientation: "vertical"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(app.metrics.CORNER_RADIUS)]
                        padding: [dp(5), dp(10), dp(5), dp(10)]
                        on_release: root.navigate_to_data_collection()

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(5)

                            MDIconButton:
                                icon: "folder"
                                pos_hint: {"center_x": 0.5}
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "健康资料收集"
                                halign: "center"
                                font_style: "Label"
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                size_hint_y: None
                                height: dp(20)

                    # 健康资料查询
                    MDCard:
                        orientation: "vertical"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(app.metrics.CORNER_RADIUS)]
                        padding: [dp(5), dp(10), dp(5), dp(10)]
                        on_release: root.navigate_to_search()

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(5)

                            MDIconButton:
                                icon: "magnify"
                                pos_hint: {"center_x": 0.5}
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "健康资料查询"
                                halign: "center"
                                font_style: "Label"
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                size_hint_y: None
                                height: dp(20)

                # 功能按钮区域 - 第二行
                MDBoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(90)
                    spacing: dp(15)

                    # 健康资料分析
                    MDCard:
                        orientation: "vertical"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(app.metrics.CORNER_RADIUS)]
                        padding: [dp(5), dp(10), dp(5), dp(10)]
                        on_release: root.navigate_to_data_analysis()

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(5)

                            MDIconButton:
                                icon: "chart-bar"
                                pos_hint: {"center_x": 0.5}
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "健康资料分析"
                                halign: "center"
                                font_style: "Label"
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                size_hint_y: None
                                height: dp(20)

                    # 成员管理
                    MDCard:
                        orientation: "vertical"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(app.metrics.CORNER_RADIUS)]
                        padding: [dp(5), dp(10), dp(5), dp(10)]
                        on_release: root.navigate_to_member_management()

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(5)

                            MDIconButton:
                                icon: "account-group"
                                pos_hint: {"center_x": 0.5}
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "成员管理"
                                halign: "center"
                                font_style: "Label"
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                size_hint_y: None
                                height: dp(20)

                # 功能按钮区域 - 第三行
                MDBoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(90)
                    spacing: dp(15)

                    # 管理日志
                    MDCard:
                        orientation: "vertical"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(app.metrics.CORNER_RADIUS)]
                        padding: [dp(5), dp(10), dp(5), dp(10)]
                        on_release: root.navigate_to_log_management()

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(5)

                            MDIconButton:
                                icon: "file-document"
                                pos_hint: {"center_x": 0.5}
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "管理日志"
                                halign: "center"
                                font_style: "Label"
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                size_hint_y: None
                                height: dp(20)

                    # 健康通知推送
                    MDCard:
                        orientation: "vertical"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(app.metrics.CORNER_RADIUS)]
                        padding: [dp(5), dp(10), dp(5), dp(10)]
                        on_release: root.navigate_to_notifications()

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(5)

                            MDIconButton:
                                icon: "bell"
                                pos_hint: {"center_x": 0.5}
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "健康通知推送"
                                halign: "center"
                                font_style: "Label"
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                size_hint_y: None
                                height: dp(20)

        # 底部导航栏
        MDBoxLayout:
            orientation: "horizontal"
            size_hint_y: None
            height: dp(60)
            md_bg_color: (1, 1, 1, 1)
            padding: [0, dp(5), 0, dp(5)]

            # 资料管理
            MDBoxLayout:
                orientation: "vertical"
                size_hint_x: 0.25

                MDIconButton:
                    icon: "folder-outline"
                    pos_hint: {"center_x": 0.5}
                    theme_icon_color: "Custom"
                    icon_color: app.theme.TEXT_SECONDARY
                    on_release: root.navigate_to_data_collection()

                MDLabel:
                    text: "健康分析"
                    halign: "center"
                    font_style: "Label"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_SECONDARY
                    size_hint_y: None
                    height: dp(15)

            # 健康资料管理
            MDBoxLayout:
                orientation: "vertical"
                size_hint_x: 0.25

                MDIconButton:
                    icon: "magnify"
                    pos_hint: {"center_x": 0.5}
                    theme_icon_color: "Custom"
                    icon_color: app.theme.TEXT_SECONDARY
                    on_release: root.navigate_to_search()

                MDLabel:
                    text: "资料管理"
                    halign: "center"
                    font_style: "Label"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_SECONDARY
                    size_hint_y: None
                    height: dp(15)

            # 成员管理
            MDBoxLayout:
                orientation: "vertical"
                size_hint_x: 0.25

                MDIconButton:
                    icon: "account-group-outline"
                    pos_hint: {"center_x": 0.5}
                    theme_icon_color: "Custom"
                    icon_color: app.theme.TEXT_SECONDARY
                    on_release: root.navigate_to_member_management()

                MDLabel:
                    text: "成员管理"
                    halign: "center"
                    font_style: "Label"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_SECONDARY
                    size_hint_y: None
                    height: dp(15)

            # 更多功能
            MDBoxLayout:
                orientation: "vertical"
                size_hint_x: 0.25

                MDIconButton:
                    icon: "dots-horizontal"
                    pos_hint: {"center_x": 0.5}
                    theme_icon_color: "Custom"
                    icon_color: app.theme.TEXT_SECONDARY
                    on_release: root.show_more_functions()

                MDLabel:
                    text: "更多"
                    halign: "center"
                    font_style: "Label"
                    theme_text_color: "Custom"
                    text_color: app.theme.TEXT_SECONDARY
                    size_hint_y: None
                    height: dp(15)
'''

Builder.load_string(KV)

class ConsultantScreen(Screen):
    """健康顾问页面

    显示健康顾问的基本信息，当前咨询患者，顾问列表和功能按钮等。
    """
    # 属性定义
    consultant_name = StringProperty("张三 医生")
    api_client = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(ConsultantScreen, self).__init__(**kwargs)
        self.api_client = APIClient()
        # 在初始化完成后加载顾问数据
        Clock.schedule_once(self.init_ui)

    def init_ui(self, dt=0):
        """初始化UI元素"""
        # Logo已在KV模板中定义，不需要再次添加

        # 加载顾问数据
        self.load_consultant_data()

    def on_enter(self):
        """当屏幕进入时调用"""
        # 重新加载顾问数据以确保显示最新信息
        self.load_consultant_data()

    def load_consultant_data(self):
        """从本地存储加载顾问数据"""
        try:
            # 尝试从data目录读取用户数据
            data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../data")
            user_data_file = os.path.join(data_dir, "user_data.json")

            if os.path.exists(user_data_file):
                with open(user_data_file, "r") as f:
                    data = json.load(f)
                    user_info = data.get("user_info", {})

                    # 设置顾问名称
                    if "name" in user_info:
                        self.consultant_name = user_info["name"]
        except Exception as e:
            print(f"加载顾问数据失败: {str(e)}")

    def navigate_to_data_collection(self):
        """导航到健康数据采集页面"""
        self.manager.current = "upload"

    def navigate_to_consultation_history(self):
        """导航到咨询历史页面"""
        # 在实际应用中，这里应该跳转到咨询历史页面
        pass

    def scan_qr_code(self):
        """扫描二维码添加成员"""
        try:
            # 在实际应用中，这里应该跳转到相机扫描二维码
            # 调用已有的相机模块或二维码扫描模块
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(
                    text="正在启动二维码扫描...",
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()

            # 如果有相机扫描页面，可以跳转到相机页面
            if self.manager.has_screen("camera_screen"):
                self.manager.current = "camera_screen"
            else:
                # 否则提示用户
                Clock.schedule_once(lambda dt: self.show_info("二维码扫描功能开发中..."), 1)
        except Exception as e:
            print(f"启动二维码扫描失败: {str(e)}")
            self.show_error("启动扫描失败，请稍后重试")

    def navigate_to_member_management(self):
        """导航到成员管理页面"""
        try:
            # 在实际应用中，这里应该跳转到成员管理页面
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(
                    text="正在进入成员管理...",
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()

            # 如果有成员管理页面，跳转
            if self.manager.has_screen("member_management_screen"):
                self.manager.current = "member_management_screen"
            else:
                # 否则提示用户
                Clock.schedule_once(lambda dt: self.show_info("成员管理功能开发中..."), 1)
        except Exception as e:
            print(f"导航到成员管理页面失败: {str(e)}")
            self.show_error("导航失败，请稍后重试")

    def show_more_functions(self):
        """显示更多功能菜单"""
        try:
            # 定义菜单项
            menu_items = [
                {
                    "text": "管理日志",
                    "on_release": lambda x=None: self.navigate_to_log_management(),
                },
                {
                    "text": "数据备份",
                    "on_release": lambda x=None: self.show_info("数据备份功能开发中..."),
                },
                {
                    "text": "系统设置",
                    "on_release": lambda x=None: self.show_info("系统设置功能开发中..."),
                },
                {
                    "text": "帮助中心",
                    "on_release": lambda x=None: self.show_info("帮助中心功能开发中..."),
                },
            ]

            # 创建并显示下拉菜单
            dropdown_menu = MDDropdownMenu(
                caller=self.ids.scroll_view,  # 使用滚动视图作为调用者
                items=menu_items,
                width=dp(200),
                position="center",
            )
            dropdown_menu.open()
        except Exception as e:
            print(f"显示更多功能菜单失败: {str(e)}")
            self.show_info("更多功能选择开发中...")

    def navigate_to_log_management(self):
        """导航到日志管理页面"""
        try:
            # 在实际应用中，这里应该跳转到日志管理页面
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(
                    text="正在进入管理日志...",
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()

            # 如果有日志管理页面，跳转
            if self.manager.has_screen("log_management_screen"):
                self.manager.current = "log_management_screen"
            else:
                # 否则提示用户
                Clock.schedule_once(lambda dt: self.show_info("管理日志功能开发中..."), 1)
        except Exception as e:
            print(f"导航到日志管理页面失败: {str(e)}")
            self.show_error("导航失败，请稍后重试")

    def show_info(self, message):
        """显示信息提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()
        except Exception as e:
            print(f"显示信息提示失败: {str(e)}")

    def show_error(self, message):
        """显示错误提示"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                    theme_text_color="Custom",
                    text_color=(1, 0, 0, 1),  # 红色错误提示
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()
        except Exception as e:
            print(f"显示错误提示失败: {str(e)}")

    def navigate_to_homepage(self):
        """导航到主页"""
        if self.manager.has_screen("homepage_screen"):
            self.manager.current = "homepage_screen"

    def navigate_to_search(self):
        """导航到搜索页面"""
        self.show_info("搜索功能开发中...")

    def navigate_to_profile(self):
        """导航到用户资料页面"""
        if self.manager.has_screen("profile"):
            self.manager.current = "profile"
        else:
            self.show_info("用户资料页面开发中...")

    def navigate_to_notifications(self):
        """导航到通知页面"""
        self.show_info("通知功能开发中...")

    def navigate_to_data_analysis(self):
        """导航到健康资料分析页面"""
        try:
            # 显示提示
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(
                    text="正在进入健康资料分析...",
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()

            # 如果有健康资料分析页面，跳转
            if self.manager.has_screen("data_analysis_screen"):
                self.manager.current = "data_analysis_screen"
            else:
                # 否则提示用户
                Clock.schedule_once(lambda dt: self.show_info("健康资料分析功能开发中..."), 1)
        except Exception as e:
            print(f"导航到健康资料分析页面失败: {str(e)}")
            self.show_error("导航失败，请稍后重试")

# 测试代码
if __name__ == "__main__":
    from kivymd.app import MDApp
    from kivy.uix.screenmanager import ScreenManager
    from kivy.clock import Clock

    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles

        def build(self):
            sm = ScreenManager()
            sm.add_widget(ConsultantScreen(name="consultant_screen"))
            return sm

    TestApp().run()