#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库工具模块
提供数据库操作的便捷方法和工具
"""

import asyncio
import json
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union, Type, Tuple
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass
from enum import Enum

from sqlalchemy import create_engine, text, inspect, MetaData, Table
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session, declarative_base
from sqlalchemy.pool import StaticPool
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import select, update, delete, func, and_, or_, desc, asc
from sqlalchemy.sql import Select

from .error_handler import DatabaseException, BusinessException
from ..db.base_session import Base as DBBaseModel

class DatabaseType(str, Enum):
    """数据库类型枚举"""
    SQLITE = "sqlite"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    ORACLE = "oracle"

class QueryType(str, Enum):
    """查询类型枚举"""
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    COUNT = "count"
    EXISTS = "exists"

@dataclass
class DatabaseConfig:
    """数据库配置"""
    database_type: DatabaseType
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    database: Optional[str] = None
    sqlite_path: Optional[str] = None
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False

@dataclass
class QueryResult:
    """查询结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    affected_rows: int = 0
    execution_time: float = 0.0

@dataclass
class TransactionResult:
    """事务结果"""
    success: bool
    results: List[QueryResult] = None
    error: Optional[str] = None
    rollback_executed: bool = False

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.engine = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        self._initialized = False
    
    def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        # 创建数据库URL
        database_url = self._build_database_url()
        
        # 创建同步引擎
        engine_kwargs = {
            "echo": self.config.echo
        }
        
        if self.config.database_type == DatabaseType.SQLITE:
            engine_kwargs.update({
                "poolclass": StaticPool,
                "connect_args": {"check_same_thread": False}
            })
        else:
            # 非SQLite数据库使用连接池参数
            engine_kwargs.update({
                "pool_size": self.config.pool_size,
                "max_overflow": self.config.max_overflow,
                "pool_timeout": self.config.pool_timeout,
                "pool_recycle": self.config.pool_recycle
            })
        
        self.engine = create_engine(database_url, **engine_kwargs)
        self.session_factory = sessionmaker(bind=self.engine)
        
        # 创建异步引擎
        async_database_url = self._build_database_url(async_mode=True)
        self.async_engine = create_async_engine(async_database_url, **engine_kwargs)
        self.async_session_factory = async_sessionmaker(bind=self.async_engine)
        
        self._initialized = True
    
    def _build_database_url(self, async_mode: bool = False) -> str:
        """构建数据库URL"""
        if self.config.database_type == DatabaseType.SQLITE:
            prefix = "sqlite+aiosqlite" if async_mode else "sqlite"
            path = self.config.sqlite_path or "./app.db"
            return f"{prefix}:///{path}"
        
        elif self.config.database_type == DatabaseType.POSTGRESQL:
            prefix = "postgresql+asyncpg" if async_mode else "postgresql+psycopg2"
            return (
                f"{prefix}://{self.config.username}:{self.config.password}@"
                f"{self.config.host}:{self.config.port}/{self.config.database}"
            )
        
        elif self.config.database_type == DatabaseType.MYSQL:
            prefix = "mysql+aiomysql" if async_mode else "mysql+pymysql"
            return (
                f"{prefix}://{self.config.username}:{self.config.password}@"
                f"{self.config.host}:{self.config.port}/{self.config.database}"
            )
        
        else:
            raise ValueError(f"不支持的数据库类型: {self.config.database_type}")
    
    @contextmanager
    def get_session(self):
        """获取同步会话"""
        if not self._initialized:
            self.initialize()
        
        session = self.session_factory()
        try:
            yield session
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    @asynccontextmanager
    async def get_async_session(self):
        """获取异步会话"""
        if not self._initialized:
            self.initialize()
        
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                raise e
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
        if self.async_engine:
            asyncio.create_task(self.async_engine.aclose())
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        if not self._initialized:
            self.initialize()
        
        inspector = inspect(self.engine)
        
        if table_name not in inspector.get_table_names():
            raise ValueError(f"表 {table_name} 不存在")
        
        columns = inspector.get_columns(table_name)
        indexes = inspector.get_indexes(table_name)
        foreign_keys = inspector.get_foreign_keys(table_name)
        primary_keys = inspector.get_pk_constraint(table_name)
        
        return {
            "table_name": table_name,
            "columns": columns,
            "indexes": indexes,
            "foreign_keys": foreign_keys,
            "primary_keys": primary_keys
        }
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        if not self._initialized:
            self.initialize()
        
        inspector = inspect(self.engine)
        table_names = inspector.get_table_names()
        
        database_info = {
            "database_type": self.config.database_type,
            "table_count": len(table_names),
            "tables": []
        }
        
        for table_name in table_names:
            table_info = self.get_table_info(table_name)
            database_info["tables"].append({
                "name": table_name,
                "column_count": len(table_info["columns"]),
                "has_primary_key": bool(table_info["primary_keys"]["constrained_columns"]),
                "index_count": len(table_info["indexes"]),
                "foreign_key_count": len(table_info["foreign_keys"])
            })
        
        return database_info

class QueryBuilder:
    """查询构建器"""
    
    def __init__(self, model_class: Type[DBBaseModel]):
        self.model_class = model_class
        self.query = select(model_class)
        self._joins = []
        self._filters = []
        self._order_by = []
        self._group_by = []
        self._having = []
        self._limit = None
        self._offset = None
    
    def filter(self, *conditions):
        """添加过滤条件"""
        self._filters.extend(conditions)
        return self
    
    def filter_by(self, **kwargs):
        """根据字段值过滤"""
        for field, value in kwargs.items():
            if hasattr(self.model_class, field):
                self._filters.append(getattr(self.model_class, field) == value)
        return self
    
    def where(self, condition):
        """添加WHERE条件"""
        self._filters.append(condition)
        return self
    
    def join(self, target, condition=None):
        """添加JOIN"""
        self._joins.append((target, condition))
        return self
    
    def order_by(self, *columns):
        """添加排序"""
        self._order_by.extend(columns)
        return self
    
    def order_by_desc(self, column):
        """降序排序"""
        self._order_by.append(desc(column))
        return self
    
    def order_by_asc(self, column):
        """升序排序"""
        self._order_by.append(asc(column))
        return self
    
    def group_by(self, *columns):
        """添加分组"""
        self._group_by.extend(columns)
        return self
    
    def having(self, condition):
        """添加HAVING条件"""
        self._having.append(condition)
        return self
    
    def limit(self, count: int):
        """限制结果数量"""
        self._limit = count
        return self
    
    def offset(self, count: int):
        """设置偏移量"""
        self._offset = count
        return self
    
    def paginate(self, page: int, page_size: int):
        """分页"""
        self._offset = (page - 1) * page_size
        self._limit = page_size
        return self
    
    def build(self) -> Select:
        """构建查询"""
        query = self.query
        
        # 添加JOIN
        for target, condition in self._joins:
            if condition:
                query = query.join(target, condition)
            else:
                query = query.join(target)
        
        # 添加过滤条件
        if self._filters:
            query = query.where(and_(*self._filters))
        
        # 添加分组
        if self._group_by:
            query = query.group_by(*self._group_by)
        
        # 添加HAVING
        if self._having:
            query = query.having(and_(*self._having))
        
        # 添加排序
        if self._order_by:
            query = query.order_by(*self._order_by)
        
        # 添加限制和偏移
        if self._offset is not None:
            query = query.offset(self._offset)
        
        if self._limit is not None:
            query = query.limit(self._limit)
        
        return query
    
    def count_query(self) -> Select:
        """构建计数查询"""
        query = select(func.count()).select_from(self.model_class)
        
        # 添加JOIN
        for target, condition in self._joins:
            if condition:
                query = query.join(target, condition)
            else:
                query = query.join(target)
        
        # 添加过滤条件
        if self._filters:
            query = query.where(and_(*self._filters))
        
        return query

class DatabaseOperations:
    """数据库操作类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def execute_query(
        self,
        query: Union[str, Select],
        params: Optional[Dict[str, Any]] = None,
        fetch_mode: str = "all"
    ) -> QueryResult:
        """执行查询"""
        start_time = datetime.now()
        
        try:
            async with self.db_manager.get_async_session() as session:
                if isinstance(query, str):
                    # 原生SQL查询
                    result = await session.execute(text(query), params or {})
                else:
                    # SQLAlchemy查询
                    result = await session.execute(query)
                
                if fetch_mode == "all":
                    data = result.fetchall()
                elif fetch_mode == "one":
                    data = result.fetchone()
                elif fetch_mode == "scalar":
                    data = result.scalar()
                else:
                    data = result
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return QueryResult(
                    success=True,
                    data=data,
                    execution_time=execution_time
                )
        
        except SQLAlchemyError as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def execute_transaction(
        self,
        operations: List[Tuple[Union[str, Select], Optional[Dict[str, Any]]]]
    ) -> TransactionResult:
        """执行事务"""
        results = []
        rollback_executed = False
        
        try:
            async with self.db_manager.get_async_session() as session:
                async with session.begin():
                    for query, params in operations:
                        result = await self.execute_query(query, params)
                        results.append(result)
                        
                        if not result.success:
                            raise DatabaseException(f"查询执行失败: {result.error}")
                
                return TransactionResult(
                    success=True,
                    results=results
                )
        
        except Exception as e:
            rollback_executed = True
            return TransactionResult(
                success=False,
                results=results,
                error=str(e),
                rollback_executed=rollback_executed
            )
    
    async def bulk_insert(
        self,
        model_class: Type[DBBaseModel],
        data_list: List[Dict[str, Any]],
        batch_size: int = 1000
    ) -> QueryResult:
        """批量插入"""
        start_time = datetime.now()
        total_inserted = 0
        
        try:
            async with self.db_manager.get_async_session() as session:
                for i in range(0, len(data_list), batch_size):
                    batch_data = data_list[i:i + batch_size]
                    entities = [model_class(**data) for data in batch_data]
                    
                    session.add_all(entities)
                    await session.commit()
                    total_inserted += len(entities)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return QueryResult(
                    success=True,
                    affected_rows=total_inserted,
                    execution_time=execution_time
                )
        
        except SQLAlchemyError as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def bulk_update(
        self,
        model_class: Type[DBBaseModel],
        updates: List[Dict[str, Any]],
        id_field: str = "id"
    ) -> QueryResult:
        """批量更新"""
        start_time = datetime.now()
        total_updated = 0
        
        try:
            async with self.db_manager.get_async_session() as session:
                for update_data in updates:
                    entity_id = update_data.pop(id_field)
                    
                    stmt = (
                        update(model_class)
                        .where(getattr(model_class, id_field) == entity_id)
                        .values(**update_data)
                    )
                    
                    result = await session.execute(stmt)
                    total_updated += result.rowcount
                
                await session.commit()
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return QueryResult(
                    success=True,
                    affected_rows=total_updated,
                    execution_time=execution_time
                )
        
        except SQLAlchemyError as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def bulk_delete(
        self,
        model_class: Type[DBBaseModel],
        conditions: List[Any]
    ) -> QueryResult:
        """批量删除"""
        start_time = datetime.now()
        total_deleted = 0
        
        try:
            async with self.db_manager.get_async_session() as session:
                for condition in conditions:
                    stmt = delete(model_class).where(condition)
                    result = await session.execute(stmt)
                    total_deleted += result.rowcount
                
                await session.commit()
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return QueryResult(
                    success=True,
                    affected_rows=total_deleted,
                    execution_time=execution_time
                )
        
        except SQLAlchemyError as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return QueryResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )

class DatabaseMigration:
    """数据库迁移工具"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def create_tables(self, base_model):
        """创建所有表"""
        try:
            async with self.db_manager.async_engine.begin() as conn:
                await conn.run_sync(base_model.metadata.create_all)
            return True
        except SQLAlchemyError as e:
            print(f"创建表失败: {e}")
            return False
    
    async def drop_tables(self, base_model):
        """删除所有表"""
        try:
            async with self.db_manager.async_engine.begin() as conn:
                await conn.run_sync(base_model.metadata.drop_all)
            return True
        except SQLAlchemyError as e:
            print(f"删除表失败: {e}")
            return False
    
    async def backup_table(self, table_name: str, backup_path: str) -> bool:
        """备份表数据"""
        try:
            query = f"SELECT * FROM {table_name}"
            result = await self.execute_query(query)
            
            if result.success:
                # 将数据保存为JSON文件
                backup_data = {
                    "table_name": table_name,
                    "backup_time": datetime.now().isoformat(),
                    "data": [dict(row) for row in result.data]
                }
                
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
                
                return True
            else:
                print(f"备份表 {table_name} 失败: {result.error}")
                return False
        
        except Exception as e:
            print(f"备份表 {table_name} 失败: {e}")
            return False
    
    async def restore_table(self, backup_path: str) -> bool:
        """恢复表数据"""
        try:
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            table_name = backup_data["table_name"]
            data = backup_data["data"]
            
            # 清空表
            await self.execute_query(f"DELETE FROM {table_name}")
            
            # 插入数据
            if data:
                columns = list(data[0].keys())
                placeholders = ", ".join([f":{col}" for col in columns])
                query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                
                async with self.db_manager.get_async_session() as session:
                    for row in data:
                        await session.execute(text(query), row)
                    await session.commit()
            
            return True
        
        except Exception as e:
            print(f"恢复表数据失败: {e}")
            return False

# 便捷函数
def create_database_config(
    database_type: DatabaseType,
    **kwargs
) -> DatabaseConfig:
    """创建数据库配置"""
    return DatabaseConfig(database_type=database_type, **kwargs)

def create_query_builder(model_class: Type[DBBaseModel]) -> QueryBuilder:
    """创建查询构建器"""
    return QueryBuilder(model_class)

async def execute_raw_query(
    db_manager: DatabaseManager,
    query: str,
    params: Optional[Dict[str, Any]] = None
) -> QueryResult:
    """执行原生SQL查询"""
    operations = DatabaseOperations(db_manager)
    return await operations.execute_query(query, params)

async def check_database_connection(db_manager: DatabaseManager) -> bool:
    """检查数据库连接"""
    try:
        result = await execute_raw_query(db_manager, "SELECT 1")
        return result.success
    except Exception:
        return False

def get_model_fields(model_class: Type[DBBaseModel]) -> List[str]:
    """获取模型字段列表"""
    return [column.name for column in model_class.__table__.columns]

def get_model_relationships(model_class: Type[DBBaseModel]) -> List[str]:
    """获取模型关系列表"""
    return [rel.key for rel in model_class.__mapper__.relationships]

# 数据库性能监控
class DatabaseMonitor:
    """数据库性能监控"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.query_stats = []
    
    async def monitor_query_performance(
        self,
        query: Union[str, Select],
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """监控查询性能"""
        start_time = datetime.now()
        
        operations = DatabaseOperations(self.db_manager)
        result = await operations.execute_query(query, params)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        query_stat = {
            "query": str(query),
            "execution_time": execution_time,
            "success": result.success,
            "timestamp": start_time.isoformat(),
            "error": result.error
        }
        
        self.query_stats.append(query_stat)
        
        return query_stat
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.query_stats:
            return {"message": "暂无查询统计数据"}
        
        execution_times = [stat["execution_time"] for stat in self.query_stats if stat["success"]]
        
        if not execution_times:
            return {"message": "暂无成功查询统计数据"}
        
        return {
            "total_queries": len(self.query_stats),
            "successful_queries": len(execution_times),
            "failed_queries": len(self.query_stats) - len(execution_times),
            "avg_execution_time": sum(execution_times) / len(execution_times),
            "min_execution_time": min(execution_times),
            "max_execution_time": max(execution_times),
            "success_rate": len(execution_times) / len(self.query_stats) * 100
        }
    
    def clear_stats(self):
        """清除统计数据"""
        self.query_stats.clear()


# 全局数据库管理器实例
_db_manager = None

def get_db_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        from .env_config import env_config
        # 转换为DatabaseManager期望的DatabaseConfig格式
        db_config = DatabaseConfig(
            database_type=DatabaseType.SQLITE,  # 默认使用SQLite
            sqlite_path=env_config.DATABASE_URL.replace('sqlite:///', ''),
            host=getattr(env_config, 'POSTGRES_HOST', None),
            port=getattr(env_config, 'POSTGRES_PORT', None),
            username=getattr(env_config, 'POSTGRES_USER', None),
            password=getattr(env_config, 'POSTGRES_PASSWORD', None),
            database=getattr(env_config, 'POSTGRES_DB', None),
            pool_size=getattr(env_config, 'POOL_SIZE', 10),
            max_overflow=getattr(env_config, 'MAX_OVERFLOW', 20),
            pool_timeout=getattr(env_config, 'POOL_TIMEOUT', 30),
            pool_recycle=getattr(env_config, 'POOL_RECYCLE', 3600),
            echo=getattr(env_config, 'DEBUG', False)
        )
        _db_manager = DatabaseManager(db_config)
        _db_manager.initialize()
    return _db_manager

def get_session():
    """获取同步数据库会话"""
    return get_db_manager().get_session()

async def get_async_session():
    """获取异步数据库会话"""
    async with get_db_manager().get_async_session() as session:
        yield session