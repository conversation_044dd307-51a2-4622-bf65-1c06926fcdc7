#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用药管理对话框测试脚本

测试停药对话框和提醒设置对话框的布局和功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kivy.app import App
from kivy.metrics import dp
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.screen import MDScreen

# 导入主题和屏幕
from theme import AppTheme, AppMetrics
from screens.medication_management_screen import MedicationManagementScreen

class TestMedicationDialogsApp(MDApp):
    """测试用药管理对话框的应用"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.theme = AppTheme()
        
    def build(self):
        """构建应用界面"""
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
        
        # 创建主屏幕
        main_screen = MDScreen()
        
        # 创建布局
        layout = MDBoxLayout(
            orientation='vertical',
            spacing=dp(20),
            padding=[dp(20), dp(40), dp(20), dp(20)]
        )
        
        # 创建用药管理屏幕实例
        self.medication_screen = MedicationManagementScreen()
        
        # 模拟选中的药物数据
        self.medication_screen.selected_medications = []
        
        # 创建模拟药物卡片
        class MockMedicationCard:
            def __init__(self, name):
                self.medication_data = {
                    'id': 1,
                    'name': name,
                    'dosage': '10mg',
                    'frequency': '每日三次',
                    'start_date': '2024-01-01',
                    'reason': '高血压治疗'
                }
        
        # 添加模拟选中的药物
        mock_card = MockMedicationCard("阿司匹林")
        self.medication_screen.selected_medications.append(mock_card)
        
        # 测试停药对话框按钮
        stop_button = MDButton(
            style="filled",
            md_bg_color=self.theme_cls.errorColor,
            size_hint_y=None,
            height=dp(48)
        )
        stop_button.add_widget(MDButtonText(text="测试停药对话框"))
        stop_button.bind(on_release=self.test_stop_dialog)
        layout.add_widget(stop_button)
        
        # 测试提醒设置对话框按钮
        reminder_button = MDButton(
            style="filled",
            md_bg_color=self.theme_cls.primaryColor,
            size_hint_y=None,
            height=dp(48)
        )
        reminder_button.add_widget(MDButtonText(text="测试提醒设置对话框"))
        reminder_button.bind(on_release=self.test_reminder_dialog)
        layout.add_widget(reminder_button)
        
        main_screen.add_widget(layout)
        return main_screen
    
    def test_stop_dialog(self, *args):
        """测试停药对话框"""
        try:
            self.medication_screen.show_stop_dialog()
        except Exception as e:
            print(f"测试停药对话框失败: {e}")
    
    def test_reminder_dialog(self, *args):
        """测试提醒设置对话框"""
        try:
            self.medication_screen.show_batch_reminder_dialog()
        except Exception as e:
            print(f"测试提醒设置对话框失败: {e}")

if __name__ == '__main__':
    # 设置环境变量
    os.environ['KIVY_METRICS_DENSITY'] = '1'
    
    # 运行测试应用
    TestMedicationDialogsApp().run()
