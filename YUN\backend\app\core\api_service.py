#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务基类
提供统一的API处理模式和工具方法
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Type, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

from fastapi import HTTPException, Depends, Request, Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, update, delete, func
from pydantic import BaseModel

from .response_handler import ResponseHandler, ApiResponse
from .error_handler import Error<PERSON>andler, BusinessException, ValidationException
from .validators import ValidationResult, BaseValidator
from .business_logic import UserUtils, AssessmentUtils, QuestionnaireUtils
from ..db.session import get_db
from ..db.base_session import Base as DBBaseModel

class ServiceType(str, Enum):
    """服务类型枚举"""
    USER = "user"
    ASSESSMENT = "assessment"
    QUESTIONNAIRE = "questionnaire"
    HEALTH_RECORD = "health_record"
    MEDICAL_RECORD = "medical_record"
    NOTIFICATION = "notification"
    FILE = "file"
    REPORT = "report"

class OperationType(str, Enum):
    """操作类型枚举"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    LIST = "list"
    SEARCH = "search"
    EXPORT = "export"
    IMPORT = "import"

@dataclass
class ServiceContext:
    """服务上下文"""
    user_id: Optional[str] = None
    user_role: Optional[str] = None
    request_id: Optional[str] = None
    client_ip: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: Optional[datetime] = None
    session: Optional[Union[Session, AsyncSession]] = None

@dataclass
class PaginationParams:
    """分页参数"""
    page: int = 1
    page_size: int = 20
    sort_by: Optional[str] = None
    sort_order: str = "asc"

@dataclass
class FilterParams:
    """过滤参数"""
    filters: Dict[str, Any] = None
    search_query: Optional[str] = None
    date_range: Optional[Dict[str, datetime]] = None

class BaseApiService(ABC):
    """API服务基类"""
    
    def __init__(self, service_type: ServiceType):
        self.service_type = service_type
        self.response_handler = ResponseHandler()
        self.error_handler = ErrorHandler()
        self.validator = BaseValidator()
        
    async def execute_with_context(
        self,
        operation: OperationType,
        handler: Callable,
        context: ServiceContext,
        *args,
        **kwargs
    ) -> ApiResponse:
        """在上下文中执行操作"""
        try:
            # 记录操作开始
            start_time = datetime.now()
            
            # 权限检查
            await self._check_permissions(operation, context)
            
            # 执行操作
            result = await handler(context, *args, **kwargs)
            
            # 记录操作日志
            await self._log_operation(
                operation, context, True, 
                execution_time=(datetime.now() - start_time).total_seconds()
            )
            
            return result
            
        except ValidationException as e:
            await self._log_operation(operation, context, False, error=str(e))
            return self.response_handler.validation_error(e.errors)
        except BusinessException as e:
            await self._log_operation(operation, context, False, error=str(e))
            return self.response_handler.error(e.message, e.error_code)
        except HTTPException as e:
            await self._log_operation(operation, context, False, error=str(e))
            raise e
        except Exception as e:
            await self._log_operation(operation, context, False, error=str(e))
            return self.response_handler.internal_error("操作执行失败")
    
    async def _check_permissions(self, operation: OperationType, context: ServiceContext):
        """检查权限"""
        # 基础权限检查逻辑
        if not context.user_id and operation != OperationType.READ:
            raise BusinessException("需要登录才能执行此操作", "AUTHENTICATION_REQUIRED")
        
        # 子类可以重写此方法实现具体的权限检查
        await self._check_specific_permissions(operation, context)
    
    async def _check_specific_permissions(self, operation: OperationType, context: ServiceContext):
        """检查特定权限（子类重写）"""
        pass
    
    async def _log_operation(
        self,
        operation: OperationType,
        context: ServiceContext,
        success: bool,
        execution_time: float = None,
        error: str = None
    ):
        """记录操作日志"""
        log_data = {
            "service_type": self.service_type,
            "operation": operation,
            "user_id": context.user_id,
            "request_id": context.request_id,
            "success": success,
            "timestamp": datetime.now(),
            "execution_time": execution_time,
            "error": error
        }
        
        # 这里可以集成日志系统
        print(f"Operation Log: {log_data}")
    
    def validate_data(self, data: Dict[str, Any], rules: Dict[str, Any]) -> ValidationResult:
        """验证数据"""
        return self.validator.validate(data, rules)
    
    async def get_paginated_results(
        self,
        query,
        pagination: PaginationParams,
        session: Union[Session, AsyncSession]
    ) -> Dict[str, Any]:
        """获取分页结果"""
        # 计算偏移量
        offset = (pagination.page - 1) * pagination.page_size
        
        # 添加排序
        if pagination.sort_by:
            if pagination.sort_order.lower() == "desc":
                query = query.order_by(getattr(query.column_descriptions[0]['type'], pagination.sort_by).desc())
            else:
                query = query.order_by(getattr(query.column_descriptions[0]['type'], pagination.sort_by))
        
        # 获取总数
        if isinstance(session, AsyncSession):
            total_result = await session.execute(select(func.count()).select_from(query.subquery()))
            total = total_result.scalar()
            
            # 获取分页数据
            paginated_query = query.offset(offset).limit(pagination.page_size)
            result = await session.execute(paginated_query)
            items = result.scalars().all()
        else:
            total = session.execute(select(func.count()).select_from(query.subquery())).scalar()
            
            # 获取分页数据
            paginated_query = query.offset(offset).limit(pagination.page_size)
            items = session.execute(paginated_query).scalars().all()
        
        # 计算分页信息
        total_pages = (total + pagination.page_size - 1) // pagination.page_size
        
        return {
            "items": items,
            "pagination": {
                "page": pagination.page,
                "page_size": pagination.page_size,
                "total": total,
                "total_pages": total_pages,
                "has_next": pagination.page < total_pages,
                "has_prev": pagination.page > 1
            }
        }
    
    def apply_filters(self, query, filters: FilterParams, model_class: Type[DBBaseModel]):
        """应用过滤条件"""
        if not filters:
            return query
        
        # 应用字段过滤
        if filters.filters:
            for field, value in filters.filters.items():
                if hasattr(model_class, field) and value is not None:
                    if isinstance(value, list):
                        query = query.filter(getattr(model_class, field).in_(value))
                    elif isinstance(value, dict):
                        # 支持范围查询
                        if "min" in value:
                            query = query.filter(getattr(model_class, field) >= value["min"])
                        if "max" in value:
                            query = query.filter(getattr(model_class, field) <= value["max"])
                    else:
                        query = query.filter(getattr(model_class, field) == value)
        
        # 应用搜索查询
        if filters.search_query:
            search_fields = getattr(model_class, '__search_fields__', [])
            if search_fields:
                search_conditions = []
                for field in search_fields:
                    if hasattr(model_class, field):
                        search_conditions.append(
                            getattr(model_class, field).ilike(f"%{filters.search_query}%")
                        )
                if search_conditions:
                    from sqlalchemy import or_
                    query = query.filter(or_(*search_conditions))
        
        # 应用日期范围过滤
        if filters.date_range:
            date_field = getattr(model_class, 'created_at', None)
            if date_field is not None:
                if "start" in filters.date_range:
                    query = query.filter(date_field >= filters.date_range["start"])
                if "end" in filters.date_range:
                    query = query.filter(date_field <= filters.date_range["end"])
        
        return query
    
    async def create_entity(
        self,
        model_class: Type[DBBaseModel],
        data: Dict[str, Any],
        session: Union[Session, AsyncSession],
        context: ServiceContext
    ) -> DBBaseModel:
        """创建实体"""
        # 添加创建者信息
        if hasattr(model_class, 'created_by') and context.user_id:
            data['created_by'] = context.user_id
        
        if hasattr(model_class, 'created_at'):
            data['created_at'] = datetime.now()
        
        # 创建实体
        entity = model_class(**data)
        
        if isinstance(session, AsyncSession):
            session.add(entity)
            await session.commit()
            await session.refresh(entity)
        else:
            session.add(entity)
            session.commit()
            session.refresh(entity)
        
        return entity
    
    async def update_entity(
        self,
        model_class: Type[DBBaseModel],
        entity_id: Union[str, int],
        data: Dict[str, Any],
        session: Union[Session, AsyncSession],
        context: ServiceContext
    ) -> Optional[DBBaseModel]:
        """更新实体"""
        # 添加更新者信息
        if hasattr(model_class, 'updated_by') and context.user_id:
            data['updated_by'] = context.user_id
        
        if hasattr(model_class, 'updated_at'):
            data['updated_at'] = datetime.now()
        
        if isinstance(session, AsyncSession):
            # 异步更新
            stmt = update(model_class).where(model_class.id == entity_id).values(**data)
            await session.execute(stmt)
            await session.commit()
            
            # 获取更新后的实体
            result = await session.execute(select(model_class).where(model_class.id == entity_id))
            return result.scalar_one_or_none()
        else:
            # 同步更新
            stmt = update(model_class).where(model_class.id == entity_id).values(**data)
            session.execute(stmt)
            session.commit()
            
            # 获取更新后的实体
            return session.execute(select(model_class).where(model_class.id == entity_id)).scalar_one_or_none()
    
    async def delete_entity(
        self,
        model_class: Type[DBBaseModel],
        entity_id: Union[str, int],
        session: Union[Session, AsyncSession],
        context: ServiceContext,
        soft_delete: bool = True
    ) -> bool:
        """删除实体"""
        if soft_delete and hasattr(model_class, 'is_deleted'):
            # 软删除
            data = {
                'is_deleted': True,
                'deleted_at': datetime.now()
            }
            if hasattr(model_class, 'deleted_by') and context.user_id:
                data['deleted_by'] = context.user_id
            
            result = await self.update_entity(model_class, entity_id, data, session, context)
            return result is not None
        else:
            # 硬删除
            if isinstance(session, AsyncSession):
                stmt = delete(model_class).where(model_class.id == entity_id)
                result = await session.execute(stmt)
                await session.commit()
                return result.rowcount > 0
            else:
                stmt = delete(model_class).where(model_class.id == entity_id)
                result = session.execute(stmt)
                session.commit()
                return result.rowcount > 0
    
    async def get_entity_by_id(
        self,
        model_class: Type[DBBaseModel],
        entity_id: Union[str, int],
        session: Union[Session, AsyncSession]
    ) -> Optional[DBBaseModel]:
        """根据ID获取实体"""
        if isinstance(session, AsyncSession):
            result = await session.execute(select(model_class).where(model_class.id == entity_id))
            return result.scalar_one_or_none()
        else:
            return session.execute(select(model_class).where(model_class.id == entity_id)).scalar_one_or_none()
    
    def serialize_entity(self, entity: DBBaseModel, fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """序列化实体"""
        if entity is None:
            return None
        
        result = {}
        
        if fields:
            # 只序列化指定字段
            for field in fields:
                if hasattr(entity, field):
                    value = getattr(entity, field)
                    result[field] = self._serialize_value(value)
        else:
            # 序列化所有字段
            for column in entity.__table__.columns:
                field = column.name
                value = getattr(entity, field)
                result[field] = self._serialize_value(value)
        
        return result
    
    def _serialize_value(self, value: Any) -> Any:
        """序列化值"""
        if isinstance(value, datetime):
            return value.isoformat()
        elif isinstance(value, Enum):
            return value.value
        elif hasattr(value, '__dict__'):
            # 处理关联对象
            return self.serialize_entity(value)
        else:
            return value
    
    async def batch_create(
        self,
        model_class: Type[DBBaseModel],
        data_list: List[Dict[str, Any]],
        session: Union[Session, AsyncSession],
        context: ServiceContext,
        batch_size: int = 100
    ) -> List[DBBaseModel]:
        """批量创建"""
        created_entities = []
        
        for i in range(0, len(data_list), batch_size):
            batch_data = data_list[i:i + batch_size]
            batch_entities = []
            
            for data in batch_data:
                # 添加创建者信息
                if hasattr(model_class, 'created_by') and context.user_id:
                    data['created_by'] = context.user_id
                
                if hasattr(model_class, 'created_at'):
                    data['created_at'] = datetime.now()
                
                entity = model_class(**data)
                batch_entities.append(entity)
            
            if isinstance(session, AsyncSession):
                session.add_all(batch_entities)
                await session.commit()
                
                for entity in batch_entities:
                    await session.refresh(entity)
            else:
                session.add_all(batch_entities)
                session.commit()
                
                for entity in batch_entities:
                    session.refresh(entity)
            
            created_entities.extend(batch_entities)
        
        return created_entities
    
    async def export_data(
        self,
        query,
        session: Union[Session, AsyncSession],
        format_type: str = "json",
        fields: Optional[List[str]] = None
    ) -> Union[List[Dict[str, Any]], str]:
        """导出数据"""
        if isinstance(session, AsyncSession):
            result = await session.execute(query)
            entities = result.scalars().all()
        else:
            entities = session.execute(query).scalars().all()
        
        # 序列化数据
        serialized_data = [self.serialize_entity(entity, fields) for entity in entities]
        
        if format_type == "json":
            return serialized_data
        elif format_type == "csv":
            return self._convert_to_csv(serialized_data)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
    
    def _convert_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """转换为CSV格式"""
        if not data:
            return ""
        
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
        
        return output.getvalue()

class CacheableApiService(BaseApiService):
    """支持缓存的API服务"""
    
    def __init__(self, service_type: ServiceType, cache_ttl: int = 300):
        super().__init__(service_type)
        self.cache_ttl = cache_ttl
        self._cache = {}
    
    async def get_cached_result(
        self,
        cache_key: str,
        fetch_func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """获取缓存结果"""
        # 检查缓存
        if cache_key in self._cache:
            cached_data, timestamp = self._cache[cache_key]
            if datetime.now() - timestamp < timedelta(seconds=self.cache_ttl):
                return cached_data
        
        # 获取新数据
        result = await fetch_func(*args, **kwargs)
        
        # 缓存结果
        self._cache[cache_key] = (result, datetime.now())
        
        return result
    
    def invalidate_cache(self, pattern: str = None):
        """清除缓存"""
        if pattern:
            # 清除匹配模式的缓存
            keys_to_remove = [key for key in self._cache.keys() if pattern in key]
            for key in keys_to_remove:
                del self._cache[key]
        else:
            # 清除所有缓存
            self._cache.clear()

# 便捷函数
def create_service_context(
    request: Request,
    user_id: Optional[str] = None,
    user_role: Optional[str] = None,
    session: Optional[Union[Session, AsyncSession]] = None
) -> ServiceContext:
    """创建服务上下文"""
    return ServiceContext(
        user_id=user_id,
        user_role=user_role,
        request_id=getattr(request.state, 'request_id', None),
        client_ip=request.client.host if request.client else None,
        user_agent=request.headers.get('user-agent'),
        timestamp=datetime.now(),
        session=session
    )

def create_pagination_params(
    page: int = 1,
    page_size: int = 20,
    sort_by: Optional[str] = None,
    sort_order: str = "asc"
) -> PaginationParams:
    """创建分页参数"""
    # 限制页面大小
    page_size = min(page_size, 100)
    page = max(page, 1)
    
    return PaginationParams(
        page=page,
        page_size=page_size,
        sort_by=sort_by,
        sort_order=sort_order
    )

def create_filter_params(
    filters: Optional[Dict[str, Any]] = None,
    search_query: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None
) -> FilterParams:
    """创建过滤参数"""
    date_range = None
    if start_date or end_date:
        date_range = {}
        if start_date:
            date_range["start"] = start_date
        if end_date:
            date_range["end"] = end_date
    
    return FilterParams(
        filters=filters or {},
        search_query=search_query,
        date_range=date_range
    )