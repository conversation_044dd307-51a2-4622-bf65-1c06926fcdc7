# 迁移报告

**迁移时间**: 2025-07-02 23:07:53
**备份目录**: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230746

## 迁移摘要

- **总操作数**: 24
- **成功操作**: 20
- **警告操作**: 3
- **失败操作**: 1

## 详细日志

**✓ 备份文件**
- 时间: 2025-07-02T23:07:47.050210
- 状态: SUCCESS
- 详情: main.py

**✓ 备份文件**
- 时间: 2025-07-02T23:07:47.067088
- 状态: SUCCESS
- 详情: theme.py

**⚠ 备份文件**
- 时间: 2025-07-02T23:07:47.070716
- 状态: WARNING
- 详情: api/api_config.py 不存在

**✓ 备份文件**
- 时间: 2025-07-02T23:07:47.094052
- 状态: SUCCESS
- 详情: api/api_client.py

**✓ 备份文件**
- 时间: 2025-07-02T23:07:47.118364
- 状态: SUCCESS
- 详情: screens/homepage_screen.py

**⚠ 备份文件**
- 时间: 2025-07-02T23:07:47.123660
- 状态: WARNING
- 详情: config.json 不存在

**✓ 备份目录**
- 时间: 2025-07-02T23:07:47.501047
- 状态: SUCCESS
- 详情: data/

**✓ 备份目录**
- 时间: 2025-07-02T23:07:47.584952
- 状态: SUCCESS
- 详情: logs/

**✓ 备份完成**
- 时间: 2025-07-02T23:07:47.585743
- 状态: SUCCESS
- 详情: 共备份 6 个文件/目录

**✓ 配置迁移**
- 时间: 2025-07-02T23:07:47.590185
- 状态: SUCCESS
- 详情: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json

**⚠ 数据库迁移**
- 时间: 2025-07-02T23:07:47.597533
- 状态: WARNING
- 详情: 未找到可迁移的数据库文件

**✓ 用户数据迁移**
- 时间: 2025-07-02T23:07:47.622754
- 状态: SUCCESS
- 详情: data/users/

**✓ 用户数据迁移**
- 时间: 2025-07-02T23:07:47.630814
- 状态: SUCCESS
- 详情: cache/

**✓ 用户数据迁移完成**
- 时间: 2025-07-02T23:07:47.633675
- 状态: SUCCESS
- 详情: 共迁移 0 个文件

**✓ 更新导入**
- 时间: 2025-07-02T23:07:49.356720
- 状态: SUCCESS
- 详情: migration_tool.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:49.362912
- 状态: SUCCESS
- 详情: run_tests.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:49.378129
- 状态: SUCCESS
- 详情: test_mobile_fixes.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:49.461825
- 状态: SUCCESS
- 详情: logo.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:49.697793
- 状态: SUCCESS
- 详情: font_definitions.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:49.819127
- 状态: SUCCESS
- 详情: theming.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:51.780613
- 状态: SUCCESS
- 详情: label.py

**✓ 更新导入**
- 时间: 2025-07-02T23:07:51.953704
- 状态: SUCCESS
- 详情: textfield.py

**✓ 导入更新完成**
- 时间: 2025-07-02T23:07:53.241235
- 状态: SUCCESS
- 详情: 共更新 8 个文件

**✗ 创建迁移脚本**
- 时间: 2025-07-02T23:07:53.349238
- 状态: ERROR
- 详情: name 'missing_files' is not defined

## 错误详情

**创建迁移脚本**: name 'missing_files' is not defined

## 后续步骤

1. 检查上述错误信息
2. 手动修复相关问题
3. 重新运行迁移工具
4. 如需要，可从备份目录恢复文件

⚠️ 迁移过程中遇到问题，请根据错误信息进行修复。
