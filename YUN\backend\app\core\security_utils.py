#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全工具模块
提供统一的安全功能，包括加密、认证、授权、令牌管理等
"""

import os
import jwt
import bcrypt
import secrets
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import re
import ipaddress
from urllib.parse import urlparse
import json
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from passlib.context import CryptContext
from passlib.hash import bcrypt as passlib_bcrypt

from .env_config import env_config
from .logging_utils import get_security_logger

class TokenType(str, Enum):
    """令牌类型枚举"""
    ACCESS = "access"
    REFRESH = "refresh"
    RESET_PASSWORD = "reset_password"
    EMAIL_VERIFICATION = "email_verification"
    API_KEY = "api_key"
    SESSION = "session"

class EncryptionType(str, Enum):
    """加密类型枚举"""
    SYMMETRIC = "symmetric"
    ASYMMETRIC = "asymmetric"
    HASH = "hash"
    HMAC = "hmac"

class SecurityLevel(str, Enum):
    """安全级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class TokenPayload:
    """令牌载荷"""
    user_id: str
    token_type: TokenType
    issued_at: datetime
    expires_at: datetime
    permissions: List[str] = None
    roles: List[str] = None
    session_id: Optional[str] = None
    device_id: Optional[str] = None
    ip_address: Optional[str] = None
    extra_claims: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.permissions is None:
            self.permissions = []
        if self.roles is None:
            self.roles = []
        if self.extra_claims is None:
            self.extra_claims = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换datetime为timestamp
        data['issued_at'] = self.issued_at.timestamp()
        data['expires_at'] = self.expires_at.timestamp()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TokenPayload':
        """从字典创建"""
        # 转换timestamp为datetime
        data['issued_at'] = datetime.fromtimestamp(data['issued_at'])
        data['expires_at'] = datetime.fromtimestamp(data['expires_at'])
        return cls(**data)

@dataclass
class SecurityConfig:
    """安全配置"""
    # JWT配置
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # 密码配置
    password_min_length: int = 8
    password_require_uppercase: bool = True
    password_require_lowercase: bool = True
    password_require_digits: bool = True
    password_require_special: bool = True
    password_max_age_days: int = 90
    
    # 加密配置
    encryption_key: Optional[str] = None
    salt_rounds: int = 12
    
    # 安全策略
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 30
    session_timeout_minutes: int = 60
    require_2fa: bool = False
    
    # IP白名单/黑名单
    ip_whitelist: List[str] = None
    ip_blacklist: List[str] = None
    
    def __post_init__(self):
        if self.ip_whitelist is None:
            self.ip_whitelist = []
        if self.ip_blacklist is None:
            self.ip_blacklist = []
        if self.encryption_key is None:
            self.encryption_key = Fernet.generate_key().decode()

class PasswordManager:
    """密码管理器"""
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        self.config = config or self._get_default_config()
        self.pwd_context = CryptContext(
            schemes=["bcrypt"],
            deprecated="auto",
            bcrypt__rounds=self.config.salt_rounds
        )
        self.logger = get_security_logger()
    
    def _get_default_config(self) -> SecurityConfig:
        """获取默认配置"""
        # env_config已经在文件顶部导入
        return SecurityConfig(
            jwt_secret_key=env_config.secret_key,
            jwt_algorithm=env_config.algorithm,
            access_token_expire_minutes=env_config.access_token_expire_minutes
        )
    
    def hash_password(self, password: str) -> str:
        """哈希密码"""
        try:
            hashed = self.pwd_context.hash(password)
            self.logger.debug("Password hashed successfully")
            return hashed
        except Exception as e:
            self.logger.error(f"Password hashing failed: {e}")
            raise
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        try:
            result = self.pwd_context.verify(plain_password, hashed_password)
            self.logger.debug(f"Password verification: {'success' if result else 'failed'}")
            return result
        except Exception as e:
            self.logger.error(f"Password verification failed: {e}")
            return False
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """验证密码强度"""
        errors = []
        
        # 长度检查
        if len(password) < self.config.password_min_length:
            errors.append(f"密码长度至少{self.config.password_min_length}位")
        
        # 大写字母检查
        if self.config.password_require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("密码必须包含大写字母")
        
        # 小写字母检查
        if self.config.password_require_lowercase and not re.search(r'[a-z]', password):
            errors.append("密码必须包含小写字母")
        
        # 数字检查
        if self.config.password_require_digits and not re.search(r'\d', password):
            errors.append("密码必须包含数字")
        
        # 特殊字符检查
        if self.config.password_require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含特殊字符")
        
        # 常见密码检查
        common_passwords = [
            "password", "123456", "123456789", "qwerty", "abc123",
            "password123", "admin", "root", "user", "guest"
        ]
        if password.lower() in common_passwords:
            errors.append("不能使用常见密码")
        
        is_valid = len(errors) == 0
        
        self.logger.debug(
            f"Password strength validation: {'passed' if is_valid else 'failed'}",
            errors=errors
        )
        
        return is_valid, errors
    
    def generate_secure_password(self, length: int = 12) -> str:
        """生成安全密码"""
        import string
        
        # 确保包含所有必需的字符类型
        chars = []
        
        if self.config.password_require_uppercase:
            chars.extend(string.ascii_uppercase)
        if self.config.password_require_lowercase:
            chars.extend(string.ascii_lowercase)
        if self.config.password_require_digits:
            chars.extend(string.digits)
        if self.config.password_require_special:
            chars.extend("!@#$%^&*(),.?:{}|<>\"")
        
        if not chars:
            chars = string.ascii_letters + string.digits
        
        # 生成密码
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 验证生成的密码
        is_valid, _ = self.validate_password_strength(password)
        if not is_valid:
            # 如果不符合要求，递归重新生成
            return self.generate_secure_password(length)
        
        self.logger.debug("Secure password generated")
        return password

class TokenManager:
    """令牌管理器"""
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        self.config = config or self._get_default_config()
        self.logger = get_security_logger()
    
    def _get_default_config(self) -> SecurityConfig:
        """获取默认配置"""
        # env_config已经在文件顶部导入
        return SecurityConfig(
            jwt_secret_key=env_config.secret_key,
            jwt_algorithm=env_config.algorithm,
            access_token_expire_minutes=env_config.access_token_expire_minutes
        )
    
    def create_token(
        self,
        user_id: str,
        token_type: TokenType,
        expires_delta: Optional[timedelta] = None,
        **extra_claims
    ) -> str:
        """创建令牌"""
        now = datetime.utcnow()
        
        # 设置过期时间
        if expires_delta:
            expires_at = now + expires_delta
        elif token_type == TokenType.ACCESS:
            expires_at = now + timedelta(minutes=self.config.access_token_expire_minutes)
        elif token_type == TokenType.REFRESH:
            expires_at = now + timedelta(days=self.config.refresh_token_expire_days)
        else:
            expires_at = now + timedelta(hours=24)  # 默认24小时
        
        # 创建载荷
        payload = TokenPayload(
            user_id=user_id,
            token_type=token_type,
            issued_at=now,
            expires_at=expires_at,
            extra_claims=extra_claims
        )
        
        # 转换为JWT载荷格式
        jwt_payload = {
            "sub": user_id,
            "type": token_type.value,
            "iat": int(now.timestamp()),
            "exp": int(expires_at.timestamp()),
            "jti": secrets.token_urlsafe(16),  # JWT ID
            **extra_claims
        }
        
        try:
            token = jwt.encode(
                jwt_payload,
                self.config.jwt_secret_key,
                algorithm=self.config.jwt_algorithm
            )
            
            self.logger.debug(
                f"Token created for user {user_id}",
                user_id=user_id,
                token_type=token_type.value,
                expires_at=expires_at.isoformat()
            )
            
            return token
        
        except Exception as e:
            self.logger.error(f"Token creation failed: {e}", user_id=user_id)
            raise
    
    def verify_token(self, token: str) -> Optional[TokenPayload]:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                self.config.jwt_secret_key,
                algorithms=[self.config.jwt_algorithm]
            )
            
            # 转换为TokenPayload
            token_payload = TokenPayload(
                user_id=payload["sub"],
                token_type=TokenType(payload["type"]),
                issued_at=datetime.fromtimestamp(payload["iat"]),
                expires_at=datetime.fromtimestamp(payload["exp"]),
                extra_claims={k: v for k, v in payload.items() 
                            if k not in ["sub", "type", "iat", "exp", "jti"]}
            )
            
            self.logger.debug(
                f"Token verified for user {token_payload.user_id}",
                user_id=token_payload.user_id,
                token_type=token_payload.token_type.value
            )
            
            return token_payload
        
        except jwt.ExpiredSignatureError:
            self.logger.warning("Token expired")
            return None
        except jwt.InvalidTokenError as e:
            self.logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Token verification failed: {e}")
            return None
    
    def refresh_token(self, refresh_token: str) -> Optional[Tuple[str, str]]:
        """刷新令牌"""
        payload = self.verify_token(refresh_token)
        
        if not payload or payload.token_type != TokenType.REFRESH:
            self.logger.warning("Invalid refresh token")
            return None
        
        # 创建新的访问令牌和刷新令牌
        new_access_token = self.create_token(
            payload.user_id,
            TokenType.ACCESS,
            permissions=payload.permissions,
            roles=payload.roles
        )
        
        new_refresh_token = self.create_token(
            payload.user_id,
            TokenType.REFRESH,
            permissions=payload.permissions,
            roles=payload.roles
        )
        
        self.logger.info(
            f"Tokens refreshed for user {payload.user_id}",
            user_id=payload.user_id
        )
        
        return new_access_token, new_refresh_token
    
    def revoke_token(self, token: str) -> bool:
        """撤销令牌（需要配合黑名单实现）"""
        # 这里可以实现令牌黑名单逻辑
        # 例如将令牌ID存储到Redis中
        payload = self.verify_token(token)
        if payload:
            self.logger.info(
                f"Token revoked for user {payload.user_id}",
                user_id=payload.user_id,
                token_type=payload.token_type.value
            )
            return True
        return False

class EncryptionManager:
    """加密管理器"""
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        self.config = config or SecurityConfig(jwt_secret_key=secrets.token_urlsafe(32))
        self.fernet = Fernet(self.config.encryption_key.encode() if isinstance(self.config.encryption_key, str) else self.config.encryption_key)
        self.logger = get_security_logger()
    
    def encrypt_data(self, data: Union[str, bytes]) -> str:
        """加密数据"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted = self.fernet.encrypt(data)
            result = base64.urlsafe_b64encode(encrypted).decode('utf-8')
            
            self.logger.debug("Data encrypted successfully")
            return result
        
        except Exception as e:
            self.logger.error(f"Data encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted = self.fernet.decrypt(encrypted_bytes)
            result = decrypted.decode('utf-8')
            
            self.logger.debug("Data decrypted successfully")
            return result
        
        except Exception as e:
            self.logger.error(f"Data decryption failed: {e}")
            raise
    
    def hash_data(self, data: str, salt: Optional[str] = None) -> Tuple[str, str]:
        """哈希数据"""
        try:
            if salt is None:
                salt = secrets.token_hex(16)
            
            # 使用PBKDF2进行哈希
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt.encode('utf-8'),
                iterations=100000,
            )
            
            hashed = kdf.derive(data.encode('utf-8'))
            hashed_hex = hashed.hex()
            
            self.logger.debug("Data hashed successfully")
            return hashed_hex, salt
        
        except Exception as e:
            self.logger.error(f"Data hashing failed: {e}")
            raise
    
    def verify_hash(self, data: str, hashed_data: str, salt: str) -> bool:
        """验证哈希"""
        try:
            new_hash, _ = self.hash_data(data, salt)
            result = hmac.compare_digest(new_hash, hashed_data)
            
            self.logger.debug(f"Hash verification: {'success' if result else 'failed'}")
            return result
        
        except Exception as e:
            self.logger.error(f"Hash verification failed: {e}")
            return False
    
    def generate_hmac(self, data: str, key: Optional[str] = None) -> str:
        """生成HMAC"""
        try:
            if key is None:
                key = self.config.jwt_secret_key
            
            mac = hmac.new(
                key.encode('utf-8'),
                data.encode('utf-8'),
                hashlib.sha256
            )
            
            result = mac.hexdigest()
            self.logger.debug("HMAC generated successfully")
            return result
        
        except Exception as e:
            self.logger.error(f"HMAC generation failed: {e}")
            raise
    
    def verify_hmac(self, data: str, mac: str, key: Optional[str] = None) -> bool:
        """验证HMAC"""
        try:
            expected_mac = self.generate_hmac(data, key)
            result = hmac.compare_digest(expected_mac, mac)
            
            self.logger.debug(f"HMAC verification: {'success' if result else 'failed'}")
            return result
        
        except Exception as e:
            self.logger.error(f"HMAC verification failed: {e}")
            return False

class SecurityValidator:
    """安全验证器"""
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        self.config = config or SecurityConfig(jwt_secret_key=secrets.token_urlsafe(32))
        self.logger = get_security_logger()
    
    def validate_ip_address(self, ip_address: str) -> bool:
        """验证IP地址"""
        try:
            # 检查IP格式
            ip = ipaddress.ip_address(ip_address)
            
            # 检查黑名单
            if self.config.ip_blacklist:
                for blocked_ip in self.config.ip_blacklist:
                    if ip in ipaddress.ip_network(blocked_ip, strict=False):
                        self.logger.warning(
                            f"IP address {ip_address} is blacklisted",
                            ip_address=ip_address
                        )
                        return False
            
            # 检查白名单（如果配置了白名单）
            if self.config.ip_whitelist:
                for allowed_ip in self.config.ip_whitelist:
                    if ip in ipaddress.ip_network(allowed_ip, strict=False):
                        return True
                
                self.logger.warning(
                    f"IP address {ip_address} is not in whitelist",
                    ip_address=ip_address
                )
                return False
            
            return True
        
        except ValueError:
            self.logger.warning(
                f"Invalid IP address format: {ip_address}",
                ip_address=ip_address
            )
            return False
    
    def validate_url(self, url: str, allowed_schemes: List[str] = None) -> bool:
        """验证URL"""
        if allowed_schemes is None:
            allowed_schemes = ['http', 'https']
        
        try:
            parsed = urlparse(url)
            
            # 检查协议
            if parsed.scheme not in allowed_schemes:
                self.logger.warning(
                    f"URL scheme {parsed.scheme} not allowed",
                    url=url,
                    scheme=parsed.scheme
                )
                return False
            
            # 检查主机名
            if not parsed.netloc:
                self.logger.warning(f"URL missing hostname: {url}", url=url)
                return False
            
            return True
        
        except Exception as e:
            self.logger.warning(f"URL validation failed: {e}", url=url)
            return False
    
    def sanitize_input(self, input_data: str, max_length: int = 1000) -> str:
        """清理输入数据"""
        if not input_data:
            return ""
        
        # 限制长度
        if len(input_data) > max_length:
            input_data = input_data[:max_length]
        
        # 移除危险字符
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|', '`']
        for char in dangerous_chars:
            input_data = input_data.replace(char, '')
        
        # 移除SQL注入关键词
        sql_keywords = [
            'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE',
            'ALTER', 'EXEC', 'UNION', 'SCRIPT', 'JAVASCRIPT'
        ]
        
        for keyword in sql_keywords:
            input_data = re.sub(rf'\b{keyword}\b', '', input_data, flags=re.IGNORECASE)
        
        return input_data.strip()
    
    def validate_file_upload(
        self,
        filename: str,
        file_content: bytes,
        allowed_extensions: List[str] = None,
        max_size_mb: int = 10
    ) -> Tuple[bool, List[str]]:
        """验证文件上传"""
        errors = []
        
        # 检查文件名
        if not filename or '..' in filename or '/' in filename or '\\' in filename:
            errors.append("无效的文件名")
        
        # 检查文件扩展名
        if allowed_extensions:
            file_ext = filename.split('.')[-1].lower() if '.' in filename else ''
            if file_ext not in [ext.lower() for ext in allowed_extensions]:
                errors.append(f"不允许的文件类型: {file_ext}")
        
        # 检查文件大小
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > max_size_mb:
            errors.append(f"文件大小超过限制: {file_size_mb:.2f}MB > {max_size_mb}MB")
        
        # 检查文件内容（简单的恶意文件检测）
        dangerous_signatures = [
            b'<?php',
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'onload=',
            b'onerror='
        ]
        
        for signature in dangerous_signatures:
            if signature in file_content.lower():
                errors.append("检测到潜在的恶意文件内容")
                break
        
        is_valid = len(errors) == 0
        
        self.logger.debug(
            f"File upload validation: {'passed' if is_valid else 'failed'}",
            filename=filename,
            file_size_mb=file_size_mb,
            errors=errors
        )
        
        return is_valid, errors

class RateLimiter:
    """速率限制器"""
    
    def __init__(self):
        self._requests = {}  # 存储请求记录
        self.logger = get_security_logger()
    
    def is_allowed(
        self,
        identifier: str,
        max_requests: int,
        time_window_seconds: int
    ) -> bool:
        """检查是否允许请求"""
        now = datetime.now()
        
        # 清理过期记录
        if identifier in self._requests:
            self._requests[identifier] = [
                req_time for req_time in self._requests[identifier]
                if (now - req_time).total_seconds() < time_window_seconds
            ]
        else:
            self._requests[identifier] = []
        
        # 检查请求数量
        if len(self._requests[identifier]) >= max_requests:
            self.logger.warning(
                f"Rate limit exceeded for {identifier}",
                identifier=identifier,
                request_count=len(self._requests[identifier]),
                max_requests=max_requests
            )
            return False
        
        # 记录当前请求
        self._requests[identifier].append(now)
        return True
    
    def get_remaining_requests(
        self,
        identifier: str,
        max_requests: int,
        time_window_seconds: int
    ) -> int:
        """获取剩余请求数"""
        if identifier not in self._requests:
            return max_requests
        
        now = datetime.now()
        valid_requests = [
            req_time for req_time in self._requests[identifier]
            if (now - req_time).total_seconds() < time_window_seconds
        ]
        
        return max(0, max_requests - len(valid_requests))

class SessionManager:
    """会话管理器"""
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        self.config = config or SecurityConfig(jwt_secret_key=secrets.token_urlsafe(32))
        self._sessions = {}  # 存储会话信息
        self.logger = get_security_logger()
    
    def create_session(
        self,
        user_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> str:
        """创建会话"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            'user_id': user_id,
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'ip_address': ip_address,
            'user_agent': user_agent,
            'is_active': True
        }
        
        self._sessions[session_id] = session_data
        
        self.logger.info(
            f"Session created for user {user_id}",
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address
        )
        
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """验证会话"""
        if session_id not in self._sessions:
            return None
        
        session = self._sessions[session_id]
        
        # 检查会话是否过期
        timeout = timedelta(minutes=self.config.session_timeout_minutes)
        if datetime.now() - session['last_activity'] > timeout:
            self.invalidate_session(session_id)
            return None
        
        # 更新最后活动时间
        session['last_activity'] = datetime.now()
        
        return session
    
    def invalidate_session(self, session_id: str) -> bool:
        """使会话失效"""
        if session_id in self._sessions:
            session = self._sessions[session_id]
            session['is_active'] = False
            
            self.logger.info(
                f"Session invalidated for user {session['user_id']}",
                user_id=session['user_id'],
                session_id=session_id
            )
            
            del self._sessions[session_id]
            return True
        
        return False
    
    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的所有会话"""
        return [
            {**session, 'session_id': sid}
            for sid, session in self._sessions.items()
            if session['user_id'] == user_id and session['is_active']
        ]
    
    def invalidate_user_sessions(self, user_id: str) -> int:
        """使用户的所有会话失效"""
        count = 0
        sessions_to_remove = []
        
        for session_id, session in self._sessions.items():
            if session['user_id'] == user_id and session['is_active']:
                sessions_to_remove.append(session_id)
                count += 1
        
        for session_id in sessions_to_remove:
            self.invalidate_session(session_id)
        
        self.logger.info(
            f"Invalidated {count} sessions for user {user_id}",
            user_id=user_id,
            session_count=count
        )
        
        return count

# 全局实例
_password_manager: Optional[PasswordManager] = None
_token_manager: Optional[TokenManager] = None
_encryption_manager: Optional[EncryptionManager] = None
_security_validator: Optional[SecurityValidator] = None
_rate_limiter: Optional[RateLimiter] = None
_session_manager: Optional[SessionManager] = None

def get_password_manager() -> PasswordManager:
    """获取密码管理器"""
    global _password_manager
    if _password_manager is None:
        _password_manager = PasswordManager()
    return _password_manager

def get_token_manager() -> TokenManager:
    """获取令牌管理器"""
    global _token_manager
    if _token_manager is None:
        _token_manager = TokenManager()
    return _token_manager

def get_encryption_manager() -> EncryptionManager:
    """获取加密管理器"""
    global _encryption_manager
    if _encryption_manager is None:
        _encryption_manager = EncryptionManager()
    return _encryption_manager

def get_security_validator() -> SecurityValidator:
    """获取安全验证器"""
    global _security_validator
    if _security_validator is None:
        _security_validator = SecurityValidator()
    return _security_validator

def get_rate_limiter() -> RateLimiter:
    """获取速率限制器"""
    global _rate_limiter
    if _rate_limiter is None:
        _rate_limiter = RateLimiter()
    return _rate_limiter

def get_session_manager() -> SessionManager:
    """获取会话管理器"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager

# 便捷函数
def hash_password(password: str) -> str:
    """哈希密码"""
    return get_password_manager().hash_password(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return get_password_manager().verify_password(plain_password, hashed_password)

def create_access_token(user_id: str, **extra_claims) -> str:
    """创建访问令牌"""
    return get_token_manager().create_token(user_id, TokenType.ACCESS, **extra_claims)

def create_refresh_token(user_id: str, **extra_claims) -> str:
    """创建刷新令牌"""
    return get_token_manager().create_token(user_id, TokenType.REFRESH, **extra_claims)

def verify_token(token: str) -> Optional[TokenPayload]:
    """验证令牌"""
    return get_token_manager().verify_token(token)

def encrypt_sensitive_data(data: str) -> str:
    """加密敏感数据"""
    return get_encryption_manager().encrypt_data(data)

def decrypt_sensitive_data(encrypted_data: str) -> str:
    """解密敏感数据"""
    return get_encryption_manager().decrypt_data(encrypted_data)

def generate_secure_token(length: int = 32) -> str:
    """生成安全令牌"""
    return secrets.token_urlsafe(length)

def generate_api_key() -> str:
    """生成API密钥"""
    return f"hm_{secrets.token_urlsafe(32)}"

def mask_sensitive_data(data: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """掩码敏感数据"""
    if len(data) <= visible_chars * 2:
        return mask_char * len(data)
    
    start = data[:visible_chars]
    end = data[-visible_chars:]
    middle = mask_char * (len(data) - visible_chars * 2)
    
    return f"{start}{middle}{end}"

# 安全装饰器
def require_auth(func):
    """需要认证的装饰器"""
    def wrapper(*args, **kwargs):
        # 这里可以实现认证逻辑
        # 例如检查请求头中的令牌
        return func(*args, **kwargs)
    return wrapper

def rate_limit(max_requests: int, time_window_seconds: int):
    """速率限制装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 获取标识符（例如IP地址或用户ID）
            identifier = kwargs.get('identifier', 'default')
            
            limiter = get_rate_limiter()
            if not limiter.is_allowed(identifier, max_requests, time_window_seconds):
                raise Exception("Rate limit exceeded")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

class SecurityManager:
    """安全管理器 - 统一的安全管理接口"""
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        self.config = config or self._get_default_config()
        self.password_manager = PasswordManager(self.config)
        self.token_manager = TokenManager(self.config)
        self.encryption_manager = EncryptionManager(self.config)
        self.security_validator = SecurityValidator(self.config)
        self.rate_limiter = RateLimiter()
        self.session_manager = SessionManager(self.config)
        self.logger = get_security_logger()
    
    def _get_default_config(self) -> SecurityConfig:
        """获取默认配置"""
        return SecurityConfig(
            jwt_secret_key=env_config.SECRET_KEY,
            jwt_algorithm=getattr(env_config, 'ALGORITHM', 'HS256'),
            access_token_expire_minutes=env_config.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    def hash_password(self, password: str) -> str:
        """哈希密码"""
        return self.password_manager.hash_password(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.password_manager.verify_password(plain_password, hashed_password)
    
    def create_access_token(self, user_id: str, **extra_claims) -> str:
        """创建访问令牌"""
        return self.token_manager.create_token(user_id, TokenType.ACCESS, **extra_claims)
    
    def verify_token(self, token: str) -> Optional[TokenPayload]:
        """验证令牌"""
        return self.token_manager.verify_token(token)
    
    def encrypt_data(self, data: str) -> str:
        """加密数据"""
        return self.encryption_manager.encrypt_data(data)
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        return self.encryption_manager.decrypt_data(encrypted_data)

# 全局SecurityManager实例
_security_manager: Optional[SecurityManager] = None

def get_security_manager() -> SecurityManager:
    """获取安全管理器"""
    global _security_manager
    if _security_manager is None:
        _security_manager = SecurityManager()
    return _security_manager

# 初始化函数
def init_security(config: Optional[SecurityConfig] = None):
    """初始化安全模块"""
    global _password_manager, _token_manager, _encryption_manager
    global _security_validator, _rate_limiter, _session_manager, _security_manager
    
    _password_manager = PasswordManager(config)
    _token_manager = TokenManager(config)
    _encryption_manager = EncryptionManager(config)
    _security_validator = SecurityValidator(config)
    _rate_limiter = RateLimiter()
    _session_manager = SessionManager(config)
    _security_manager = SecurityManager(config)
    
    logger = get_security_logger()
    logger.info("Security module initialized")