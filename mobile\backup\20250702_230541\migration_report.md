# 迁移报告

**迁移时间**: 2025-07-02 23:06:00
**备份目录**: C:\Users\<USER>\Desktop\health-Trea\mobile\backup\20250702_230541

## 迁移摘要

- **总操作数**: 62
- **成功操作**: 58
- **警告操作**: 3
- **失败操作**: 1

## 详细日志

**✓ 备份文件**
- 时间: 2025-07-02T23:05:42.090295
- 状态: SUCCESS
- 详情: main.py

**✓ 备份文件**
- 时间: 2025-07-02T23:05:42.129761
- 状态: SUCCESS
- 详情: theme.py

**⚠ 备份文件**
- 时间: 2025-07-02T23:05:42.131995
- 状态: WARNING
- 详情: api/api_config.py 不存在

**✓ 备份文件**
- 时间: 2025-07-02T23:05:42.136543
- 状态: SUCCESS
- 详情: api/api_client.py

**✓ 备份文件**
- 时间: 2025-07-02T23:05:42.177057
- 状态: SUCCESS
- 详情: screens/homepage_screen.py

**⚠ 备份文件**
- 时间: 2025-07-02T23:05:42.186556
- 状态: WARNING
- 详情: config.json 不存在

**✓ 备份目录**
- 时间: 2025-07-02T23:05:43.931332
- 状态: SUCCESS
- 详情: data/

**✓ 备份目录**
- 时间: 2025-07-02T23:05:44.326994
- 状态: SUCCESS
- 详情: logs/

**✓ 备份完成**
- 时间: 2025-07-02T23:05:44.349294
- 状态: SUCCESS
- 详情: 共备份 6 个文件/目录

**✓ 配置迁移**
- 时间: 2025-07-02T23:05:44.650559
- 状态: SUCCESS
- 详情: 配置已保存到 C:\Users\<USER>\Desktop\health-Trea\mobile\api\api_config.json

**⚠ 数据库迁移**
- 时间: 2025-07-02T23:05:44.868792
- 状态: WARNING
- 详情: 未找到可迁移的数据库文件

**✓ 用户数据迁移**
- 时间: 2025-07-02T23:05:45.058594
- 状态: SUCCESS
- 详情: data/users/

**✓ 用户数据迁移**
- 时间: 2025-07-02T23:05:45.233133
- 状态: SUCCESS
- 详情: cache/

**✓ 用户数据迁移完成**
- 时间: 2025-07-02T23:05:45.332118
- 状态: SUCCESS
- 详情: 共迁移 0 个文件

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.323974
- 状态: SUCCESS
- 详情: fix_kivymd_compatibility.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.425162
- 状态: SUCCESS
- 详情: main.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.575683
- 状态: SUCCESS
- 详情: migration_tool.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.619509
- 状态: SUCCESS
- 详情: run_tests.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.648079
- 状态: SUCCESS
- 详情: test_management_log.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.665255
- 状态: SUCCESS
- 详情: test_mobile_fixes.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:48.835208
- 状态: SUCCESS
- 详情: assessment_form_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.097050
- 状态: SUCCESS
- 详情: assessment_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.308698
- 状态: SUCCESS
- 详情: basic_health_info_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.552409
- 状态: SUCCESS
- 详情: companion_service_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.633781
- 状态: SUCCESS
- 详情: consultant_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.677194
- 状态: SUCCESS
- 详情: document_list_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.681388
- 状态: SUCCESS
- 详情: health_diary_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.685480
- 状态: SUCCESS
- 详情: health_document_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.692270
- 状态: SUCCESS
- 详情: health_overview_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.830568
- 状态: SUCCESS
- 详情: homepage_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.896375
- 状态: SUCCESS
- 详情: hospital_records_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:49.995663
- 状态: SUCCESS
- 详情: lab_report_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.081654
- 状态: SUCCESS
- 详情: login_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.148476
- 状态: SUCCESS
- 详情: log_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.261752
- 状态: SUCCESS
- 详情: management_log_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.362379
- 状态: SUCCESS
- 详情: medical_records_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.582468
- 状态: SUCCESS
- 详情: medication_management_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.692621
- 状态: SUCCESS
- 详情: other_records_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.762148
- 状态: SUCCESS
- 详情: outpatient_records_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.808349
- 状态: SUCCESS
- 详情: personalized_checkup_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.927927
- 状态: SUCCESS
- 详情: physical_exam_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:50.985732
- 状态: SUCCESS
- 详情: profile_page.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.101280
- 状态: SUCCESS
- 详情: public_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.301150
- 状态: SUCCESS
- 详情: questionnaire_form_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.414864
- 状态: SUCCESS
- 详情: register_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.541100
- 状态: SUCCESS
- 详情: supermanager_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.662635
- 状态: SUCCESS
- 详情: survey_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.759855
- 状态: SUCCESS
- 详情: tech_diagnosis_report_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.809347
- 状态: SUCCESS
- 详情: unit_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.826966
- 状态: SUCCESS
- 详情: voice_triage_screen.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.840976
- 状态: SUCCESS
- 详情: app_metrics.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.863862
- 状态: SUCCESS
- 详情: common_components.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.877997
- 状态: SUCCESS
- 详情: health_data_aggregator.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.906433
- 状态: SUCCESS
- 详情: triage_manager.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.913017
- 状态: SUCCESS
- 详情: camera_view.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:51.926203
- 状态: SUCCESS
- 详情: logo.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:52.760709
- 状态: SUCCESS
- 详情: font_definitions.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:52.942951
- 状态: SUCCESS
- 详情: theming.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:57.579815
- 状态: SUCCESS
- 详情: label.py

**✓ 更新导入**
- 时间: 2025-07-02T23:05:57.812303
- 状态: SUCCESS
- 详情: textfield.py

**✓ 导入更新完成**
- 时间: 2025-07-02T23:05:59.894697
- 状态: SUCCESS
- 详情: 共更新 46 个文件

**✗ 创建迁移脚本**
- 时间: 2025-07-02T23:06:00.062660
- 状态: ERROR
- 详情: name 'missing_files' is not defined

## 错误详情

**创建迁移脚本**: name 'missing_files' is not defined

## 后续步骤

1. 检查上述错误信息
2. 手动修复相关问题
3. 重新运行迁移工具
4. 如需要，可从备份目录恢复文件

⚠️ 迁移过程中遇到问题，请根据错误信息进行修复。
