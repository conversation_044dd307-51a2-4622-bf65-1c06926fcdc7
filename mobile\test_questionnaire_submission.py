#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动端问卷提交测试脚本
测试问卷和量表提交后是否正确保存到数据库并能在前端查询
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime
import time

# 配置
BASE_URL = "http://localhost:8006"  # 后端服务器地址
USERNAME = "markey"
PASSWORD = "markey0308@163"

class QuestionnaireSubmissionTest:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.user_id = None
        
    def login(self):
        """登录获取token"""
        try:
            login_data = {
                "username": USERNAME,
                "password": PASSWORD
            }
            
            response = self.session.post(
                f"{BASE_URL}/api/auth/register/login",
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"登录响应状态: {response.status_code}")
            print(f"登录响应内容: {response.text[:500]}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('access_token'):
                    self.token = result['access_token']
                    self.user_data = result.get('user', {})
                    self.user_id = self.user_data.get('id')
                    print(f"✓ 登录成功，用户ID: {self.user_id}, Custom ID: {self.user_data.get('custom_id')}")
                    return True
                else:
                    print(f"✗ 登录失败: {result.get('detail', '未知错误')}")
                    return False
            else:
                print(f"✗ 登录请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 登录异常: {str(e)}")
            return False
    
    def get_headers(self):
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
    
    def get_questionnaire_templates(self):
        """获取问卷模板"""
        try:
            response = self.session.get(
                f"{BASE_URL}/api/mobile/questionnaires",
                headers=self.get_headers()
            )
            
            print(f"获取问卷模板响应: {response.status_code}")
            if response.status_code == 200:
                templates = response.json()
                print(f"问卷模板数据类型: {type(templates)}")
                print(f"问卷模板数据内容: {templates}")
                if isinstance(templates, list):
                    print(f"✓ 获取到 {len(templates)} 个问卷模板")
                elif isinstance(templates, dict):
                    print(f"✓ 获取到问卷模板字典，键: {list(templates.keys())}")
                return templates
            else:
                print(f"✗ 获取问卷模板失败: {response.text}")
                return []
                
        except Exception as e:
            print(f"✗ 获取问卷模板异常: {str(e)}")
            return []
    
    def get_assessment_templates(self):
        """获取评估量表模板"""
        try:
            response = self.session.get(
                f"{BASE_URL}/api/mobile/assessments",
                headers=self.get_headers()
            )
            
            print(f"获取评估模板响应: {response.status_code}")
            if response.status_code == 200:
                templates = response.json()
                print(f"评估模板数据类型: {type(templates)}")
                print(f"评估模板数据内容: {templates}")
                if isinstance(templates, list):
                    print(f"✓ 获取到 {len(templates)} 个评估模板")
                elif isinstance(templates, dict):
                    print(f"✓ 获取到评估模板字典，键: {list(templates.keys())}")
                return templates
            else:
                print(f"✗ 获取评估模板失败: {response.text}")
                return []
                
        except Exception as e:
            print(f"✗ 获取评估模板异常: {str(e)}")
            return []
    
    def submit_questionnaire(self, questionnaire_id, answers):
        """提交问卷"""
        try:
            submit_data = {
                "questionnaire_id": questionnaire_id,
                "answers": answers
            }
            
            response = self.session.post(
                f"{BASE_URL}/api/mobile/questionnaires/{questionnaire_id}/submit",
                json=submit_data,
                headers=self.get_headers()
            )
            
            print(f"提交问卷响应: {response.status_code}")
            print(f"提交问卷内容: {response.text[:500]}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 问卷提交成功: {result.get('message')}")
                return result
            else:
                print(f"✗ 问卷提交失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"✗ 问卷提交异常: {str(e)}")
            return None
    
    def submit_assessment(self, assessment_id, answers):
        """提交评估量表"""
        try:
            submit_data = {
                "assessment_id": assessment_id,
                "answers": answers
            }
            
            response = self.session.post(
                f"{BASE_URL}/api/mobile/assessments/{assessment_id}/submit",
                json=submit_data,
                headers=self.get_headers()
            )
            
            print(f"提交评估响应: {response.status_code}")
            print(f"提交评估内容: {response.text[:500]}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 评估提交成功: {result.get('message')}")
                return result
            else:
                print(f"✗ 评估提交失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"✗ 评估提交异常: {str(e)}")
            return None
    
    def check_health_records(self):
        """检查健康记录中是否有问卷数据"""
        try:
            response = self.session.get(
                f"{BASE_URL}/api/user-health-records/{self.user_data['custom_id']}?record_type=questionnaire",
                headers=self.get_headers()
            )
            
            print(f"查询健康记录响应: {response.status_code}")
            if response.status_code == 200:
                records = response.json()
                questionnaire_records = []
                
                if isinstance(records, dict) and 'records' in records:
                    questionnaire_records = [r for r in records['records'] if r.get('type') == 'questionnaire']
                elif isinstance(records, list):
                    questionnaire_records = [r for r in records if r.get('type') == 'questionnaire']
                
                print(f"✓ 找到 {len(questionnaire_records)} 条问卷健康记录")
                for record in questionnaire_records:
                    print(f"  - {record.get('title', '未知标题')} ({record.get('date', '未知时间')})")
                
                return questionnaire_records
            else:
                print(f"✗ 查询健康记录失败: {response.text}")
                return []
                
        except Exception as e:
            print(f"✗ 查询健康记录异常: {str(e)}")
            return []
    
    def run_test(self):
        """运行完整测试"""
        print("=== 移动端问卷提交测试 ===")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"后端地址: {BASE_URL}")
        print(f"用户名: {USERNAME}")
        print()
        
        # 1. 登录
        print("1. 测试登录...")
        if not self.login():
            print("登录失败，测试终止")
            return False
        
        time.sleep(1)
        
        # 2. 获取问卷模板
        print("\n2. 获取问卷模板...")
        questionnaires = self.get_questionnaire_templates()
        
        # 3. 获取评估模板
        print("\n3. 获取评估模板...")
        assessments = self.get_assessment_templates()
        
        # 4. 提交问卷（如果有的话）
        if questionnaires:
            print("\n4. 测试问卷提交...")
            # 处理questionnaires可能是字典或列表的情况
            if isinstance(questionnaires, list) and len(questionnaires) > 0:
                questionnaire = questionnaires[0]
            elif isinstance(questionnaires, dict):
                # 如果是字典，尝试获取第一个值
                questionnaire_keys = list(questionnaires.keys())
                if questionnaire_keys:
                    questionnaire = questionnaires[questionnaire_keys[0]]
                else:
                    print("✗ 问卷数据为空字典")
                    questionnaire = None
            else:
                print("✗ 问卷数据格式不正确")
                questionnaire = None
                
            if questionnaire:
                # 如果questionnaire是字符串（键名），需要从questionnaires中获取实际数据
                if isinstance(questionnaire, str):
                    if isinstance(questionnaires, dict) and 'data' in questionnaires:
                        questionnaire_data = questionnaires['data']
                        if isinstance(questionnaire_data, list) and len(questionnaire_data) > 0:
                            questionnaire = questionnaire_data[0]
                        else:
                            print("✗ 问卷数据为空")
                            questionnaire = None
                    else:
                        print("✗ 无法获取问卷数据")
                        questionnaire = None
                        
                if questionnaire and isinstance(questionnaire, dict):
                    questionnaire_id = questionnaire.get('id')
                    print(f"问卷ID: {questionnaire_id}")
                    
                    # 构造示例答案
                    answers = []
                    questions = questionnaire.get('questions', [])
                    print(f"问卷包含 {len(questions)} 个问题")
                    for i, question in enumerate(questions[:3]):  # 只回答前3个问题
                        answers.append({
                            "question_id": question.get('id', i+1),
                            "answer": "测试答案",
                            "score": 1
                        })
                else:
                    print("✗ 问卷数据格式错误")
                    questionnaire = None
            
            if answers:
                result = self.submit_questionnaire(questionnaire_id, answers)
                if result:
                    print(f"问卷提交成功，健康记录ID: {result.get('data', {}).get('health_record_id')}")
        
        # 5. 提交评估量表（如果有的话）
        if assessments:
            print("\n5. 测试评估量表提交...")
            # 处理assessments可能是字典或列表的情况
            if isinstance(assessments, list) and len(assessments) > 0:
                assessment = assessments[0]
            elif isinstance(assessments, dict):
                # 如果是字典，尝试获取第一个值
                assessment_keys = list(assessments.keys())
                if assessment_keys:
                    assessment = assessments[assessment_keys[0]]
                else:
                    print("✗ 评估数据为空字典")
                    assessment = None
            else:
                print("✗ 评估数据格式不正确")
                assessment = None
                
            if assessment:
                 # 如果assessment是字符串（键名），需要从assessments中获取实际数据
                 if isinstance(assessment, str):
                     if isinstance(assessments, dict) and 'data' in assessments:
                         assessment_data = assessments['data']
                         if isinstance(assessment_data, list) and len(assessment_data) > 0:
                             assessment = assessment_data[0]
                         else:
                             print("✗ 评估数据为空")
                             assessment = None
                     else:
                         print("✗ 无法获取评估数据")
                         assessment = None
                         
                 if assessment and isinstance(assessment, dict):
                     assessment_id = assessment.get('id')
                     print(f"评估ID: {assessment_id}")
                     
                     # 构造示例答案
                     answers = []
                     questions = assessment.get('questions', [])
                     print(f"评估包含 {len(questions)} 个问题")
                     for i, question in enumerate(questions[:3]):  # 只回答前3个问题
                         answers.append({
                             "question_id": question.get('id', i+1),
                             "answer": "测试答案",
                             "score": 2
                         })
                 else:
                     print("✗ 评估数据格式错误")
                     assessment = None
            
            if answers:
                result = self.submit_assessment(assessment_id, answers)
                if result:
                    print(f"评估提交成功，健康记录ID: {result.get('data', {}).get('health_record_id')}")
        
        # 6. 检查健康记录
        print("\n6. 检查健康记录...")
        time.sleep(2)  # 等待数据保存
        health_records = self.check_health_records()
        
        print("\n=== 测试完成 ===")
        if health_records:
            print("✓ 测试成功：问卷数据已正确保存到健康记录")
            return True
        else:
            print("⚠️ 警告：未在健康记录中找到问卷数据")
            return False

def main():
    test = QuestionnaireSubmissionTest()
    success = test.run_test()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查配置")
        sys.exit(1)

if __name__ == "__main__":
    main()