#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端环境配置管理模块
统一管理不同环境下的配置参数
"""

import os
import secrets
from enum import Enum
from typing import Dict, Any, Optional, List
from functools import lru_cache
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from pydantic import Field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class DatabaseConfig(BaseSettings):
    """数据库配置"""
    model_config = {"extra": "ignore"}
    
    # SQLite配置
    sqlite_url: str = Field(default="sqlite:///./app.db", description="SQLite数据库URL")
    sqlite_echo: bool = Field(default=False, description="是否打印SQL语句")
    
    # PostgreSQL配置
    postgres_host: str = Field(default="localhost", description="PostgreSQL主机")
    postgres_port: int = Field(default=5432, description="PostgreSQL端口")
    postgres_user: str = Field(default="postgres", description="PostgreSQL用户名")
    postgres_password: str = Field(default="", description="PostgreSQL密码")
    postgres_db: str = Field(default="health_system", description="PostgreSQL数据库名")
    
    # 连接池配置
    pool_size: int = Field(default=10, description="连接池大小")
    max_overflow: int = Field(default=20, description="最大溢出连接数")
    pool_timeout: int = Field(default=30, description="连接池超时时间")
    pool_recycle: int = Field(default=3600, description="连接回收时间")
    
    @property
    def postgres_url(self) -> str:
        """构建PostgreSQL连接URL"""
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"

class RedisConfig(BaseSettings):
    """Redis配置"""
    model_config = {"extra": "ignore"}
    
    enabled: bool = Field(default=False, description="是否启用Redis")
    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    password: str = Field(default="", description="Redis密码")
    db: int = Field(default=0, description="Redis数据库索引")
    max_connections: int = Field(default=10, description="最大连接数")
    socket_timeout: int = Field(default=5, description="Socket超时时间")
    socket_connect_timeout: int = Field(default=5, description="连接超时时间")
    
    @property
    def url(self) -> str:
        """构建Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"

class SecurityConfig(BaseSettings):
    """安全配置"""
    model_config = {"extra": "ignore"}
    
    # JWT配置
    secret_key: str = Field(default_factory=lambda: secrets.token_urlsafe(32), description="JWT密钥")
    algorithm: str = Field(default="HS256", description="JWT算法")
    access_token_expire_minutes: int = Field(default=60 * 24 * 8, description="访问令牌过期时间（分钟）")
    refresh_token_expire_days: int = Field(default=30, description="刷新令牌过期时间（天）")
    
    # 密码策略
    password_min_length: int = Field(default=8, description="密码最小长度")
    password_require_uppercase: bool = Field(default=True, description="密码是否需要大写字母")
    password_require_lowercase: bool = Field(default=True, description="密码是否需要小写字母")
    password_require_numbers: bool = Field(default=True, description="密码是否需要数字")
    password_require_special: bool = Field(default=True, description="密码是否需要特殊字符")
    
    # 登录限制
    max_login_attempts: int = Field(default=5, description="最大登录尝试次数")
    login_lockout_duration: int = Field(default=300, description="登录锁定时间（秒）")
    
    # CORS配置
    cors_origins: List[str] = Field(default=["http://localhost:8080", "http://localhost:3000"], description="允许的CORS源")
    cors_methods: List[str] = Field(default=["GET", "POST", "PUT", "DELETE", "OPTIONS"], description="允许的HTTP方法")
    cors_headers: List[str] = Field(default=["*"], description="允许的请求头")

class APIConfig(BaseSettings):
    """API配置"""
    model_config = {"extra": "ignore"}
    
    # 基础配置
    title: str = Field(default="健康评估系统API", description="API标题")
    description: str = Field(default="健康评估系统后端API服务", description="API描述")
    version: str = Field(default="1.0.0", description="API版本")
    
    # 路径配置
    api_prefix: str = Field(default="/api", description="API路径前缀")
    docs_url: str = Field(default="/docs", description="文档URL")
    redoc_url: str = Field(default="/redoc", description="ReDoc URL")
    openapi_url: str = Field(default="/openapi.json", description="OpenAPI JSON URL")
    
    # 限流配置
    rate_limit_enabled: bool = Field(default=True, description="是否启用限流")
    rate_limit_requests: int = Field(default=100, description="限流请求数")
    rate_limit_window: int = Field(default=60, description="限流时间窗口（秒）")
    
    # 分页配置
    default_page_size: int = Field(default=20, description="默认分页大小")
    max_page_size: int = Field(default=100, description="最大分页大小")
    
    # 文件上传配置
    max_file_size: int = Field(default=10 * 1024 * 1024, description="最大文件大小（字节）")
    allowed_file_types: List[str] = Field(default=[".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"], description="允许的文件类型")
    upload_path: str = Field(default="./uploads", description="文件上传路径")

class LoggingConfig(BaseSettings):
    """日志配置"""
    model_config = {"extra": "ignore"}
    
    level: LogLevel = Field(default=LogLevel.INFO, description="日志级别")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", description="日志格式")
    
    # 文件日志配置
    file_enabled: bool = Field(default=True, description="是否启用文件日志")
    file_path: str = Field(default="./logs", description="日志文件路径")
    file_max_size: int = Field(default=10 * 1024 * 1024, description="日志文件最大大小")
    file_backup_count: int = Field(default=5, description="日志文件备份数量")
    
    # 控制台日志配置
    console_enabled: bool = Field(default=True, description="是否启用控制台日志")
    
    # 远程日志配置
    remote_enabled: bool = Field(default=False, description="是否启用远程日志")
    remote_url: str = Field(default="", description="远程日志服务URL")
    remote_api_key: str = Field(default="", description="远程日志API密钥")

class MonitoringConfig(BaseSettings):
    """监控配置"""
    model_config = {"extra": "ignore"}
    
    enabled: bool = Field(default=True, description="是否启用监控")
    
    # 性能监控
    performance_enabled: bool = Field(default=True, description="是否启用性能监控")
    slow_query_threshold: float = Field(default=1.0, description="慢查询阈值（秒）")
    
    # 健康检查
    health_check_enabled: bool = Field(default=True, description="是否启用健康检查")
    health_check_interval: int = Field(default=30, description="健康检查间隔（秒）")
    
    # 指标收集
    metrics_enabled: bool = Field(default=True, description="是否启用指标收集")
    metrics_endpoint: str = Field(default="/metrics", description="指标端点")
    
    # 告警配置
    alert_enabled: bool = Field(default=True, description="是否启用告警")
    alert_email: str = Field(default="", description="告警邮箱")
    alert_webhook: str = Field(default="", description="告警Webhook")

class CacheConfig(BaseSettings):
    """缓存配置"""
    model_config = {"extra": "ignore"}
    
    enabled: bool = Field(default=True, description="是否启用缓存")
    
    # 内存缓存
    memory_enabled: bool = Field(default=True, description="是否启用内存缓存")
    memory_max_size: int = Field(default=1000, description="内存缓存最大条目数")
    memory_ttl: int = Field(default=300, description="内存缓存TTL（秒）")
    
    # Redis缓存
    redis_enabled: bool = Field(default=False, description="是否启用Redis缓存")
    redis_ttl: int = Field(default=3600, description="Redis缓存TTL（秒）")
    
    # 查询缓存
    query_cache_enabled: bool = Field(default=True, description="是否启用查询缓存")
    query_cache_ttl: int = Field(default=300, description="查询缓存TTL（秒）")

class EmailConfig(BaseSettings):
    """邮件配置"""
    model_config = {"extra": "ignore"}
    
    enabled: bool = Field(default=False, description="是否启用邮件服务")
    
    # SMTP配置
    smtp_host: str = Field(default="", description="SMTP主机")
    smtp_port: int = Field(default=587, description="SMTP端口")
    smtp_username: str = Field(default="", description="SMTP用户名")
    smtp_password: str = Field(default="", description="SMTP密码")
    smtp_use_tls: bool = Field(default=True, description="是否使用TLS")
    
    # 发件人配置
    from_email: str = Field(default="", description="发件人邮箱")
    from_name: str = Field(default="健康评估系统", description="发件人名称")
    
    # 模板配置
    template_path: str = Field(default="./templates/email", description="邮件模板路径")

class SMSConfig(BaseSettings):
    """短信配置"""
    model_config = {"extra": "ignore"}
    
    enabled: bool = Field(default=False, description="是否启用短信服务")
    
    # 服务商配置
    provider: str = Field(default="twilio", description="短信服务商")
    api_key: str = Field(default="", description="API密钥")
    api_secret: str = Field(default="", description="API密钥")
    from_number: str = Field(default="", description="发送号码")
    
    # 限制配置
    daily_limit: int = Field(default=100, description="每日发送限制")
    rate_limit: int = Field(default=10, description="每分钟发送限制")

class EnvironmentSettings(BaseSettings):
    """环境配置主类"""
    # 基础配置
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="运行环境")
    debug: bool = Field(default=True, description="是否开启调试模式")
    testing: bool = Field(default=False, description="是否为测试环境")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8006, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")
    
    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    email: EmailConfig = Field(default_factory=EmailConfig)
    sms: SMSConfig = Field(default_factory=SMSConfig)
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }
        
    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == Environment.DEVELOPMENT
    
    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == Environment.TESTING
    
    @property
    def is_staging(self) -> bool:
        """是否为预发布环境"""
        return self.environment == Environment.STAGING
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == Environment.PRODUCTION
    
    def get_database_url(self, use_postgres: bool = False) -> str:
        """获取数据库连接URL"""
        if use_postgres:
            return self.database.postgres_url
        return self.database.sqlite_url
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        if self.is_development:
            return LogLevel.DEBUG
        elif self.is_testing:
            return LogLevel.INFO
        elif self.is_staging:
            return LogLevel.WARNING
        else:
            return LogLevel.ERROR
    
    def get_cors_origins(self) -> List[str]:
        """获取CORS允许的源"""
        if self.is_production:
            # 生产环境只允许特定域名
            return ["https://yourdomain.com"]
        else:
            # 开发环境允许本地域名
            return self.security.cors_origins
    
    def should_enable_docs(self) -> bool:
        """是否应该启用API文档"""
        return not self.is_production
    
    def get_upload_path(self) -> str:
        """获取文件上传路径"""
        base_path = self.api.upload_path
        if not os.path.isabs(base_path):
            # 相对路径转换为绝对路径
            base_path = os.path.join(os.getcwd(), base_path)
        
        # 确保目录存在
        os.makedirs(base_path, exist_ok=True)
        return base_path
    
    def get_log_path(self) -> str:
        """获取日志文件路径"""
        base_path = self.logging.file_path
        if not os.path.isabs(base_path):
            base_path = os.path.join(os.getcwd(), base_path)
        
        os.makedirs(base_path, exist_ok=True)
        return base_path

@lru_cache()
def get_settings() -> EnvironmentSettings:
    """获取配置实例（单例模式）"""
    return EnvironmentSettings()

# get_config函数作为get_settings的别名，保持向后兼容
def get_config() -> EnvironmentSettings:
    """获取配置实例（向后兼容）"""
    return get_settings()

# 全局配置实例
settings = get_settings()

# 环境检查函数
def is_development() -> bool:
    """是否为开发环境"""
    return settings.is_development

def is_testing() -> bool:
    """是否为测试环境"""
    return settings.is_testing

def is_production() -> bool:
    """是否为生产环境"""
    return settings.is_production

def get_database_url() -> str:
    """获取数据库URL"""
    return settings.get_database_url()

def get_redis_url() -> str:
    """获取Redis URL"""
    return settings.redis.url if settings.redis.enabled else ""

# 配置验证函数
def validate_config() -> Dict[str, Any]:
    """验证配置"""
    issues = []
    
    # 检查必要的配置
    if settings.is_production:
        if not settings.security.secret_key or len(settings.security.secret_key) < 32:
            issues.append("生产环境必须设置强密钥")
        
        if settings.debug:
            issues.append("生产环境不应开启调试模式")
        
        if settings.should_enable_docs():
            issues.append("生产环境不应启用API文档")
    
    # 检查数据库配置
    if settings.database.postgres_password and not settings.database.postgres_user:
        issues.append("PostgreSQL密码已设置但用户名为空")
    
    # 检查Redis配置
    if settings.redis.enabled and not settings.redis.host:
        issues.append("Redis已启用但主机地址为空")
    
    # 检查邮件配置
    if settings.email.enabled:
        if not settings.email.smtp_host:
            issues.append("邮件服务已启用但SMTP主机为空")
        if not settings.email.from_email:
            issues.append("邮件服务已启用但发件人邮箱为空")
    
    # 检查短信配置
    if settings.sms.enabled:
        if not settings.sms.api_key:
            issues.append("短信服务已启用但API密钥为空")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "environment": settings.environment,
        "debug": settings.debug
    }

if __name__ == "__main__":
    # 配置验证
    validation = validate_config()
    print(f"配置验证结果: {validation}")
    
    # 打印主要配置
    print(f"环境: {settings.environment}")
    print(f"调试模式: {settings.debug}")
    print(f"数据库URL: {settings.get_database_url()}")
    print(f"Redis启用: {settings.redis.enabled}")
    print(f"监控启用: {settings.monitoring.enabled}")