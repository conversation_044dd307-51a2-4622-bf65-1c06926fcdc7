"""Add custom_id to health tables

Revision ID: add_custom_id_health
Revises: 
Create Date: 2025-01-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'add_custom_id_health'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add custom_id column to health-related tables"""
    # Get database connection
    connection = op.get_bind()
    
    # Check if we're using SQLite or other database
    if 'sqlite' in str(connection.engine.url):
        upgrade_sqlite(connection)
    else:
        upgrade_other_db(connection)


def upgrade_sqlite(connection):
    """Upgrade SQLite database"""
    # Check if medications table exists and add custom_id if missing
    try:
        # Check if medications table exists
        result = connection.execute(text(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='medications'"
        )).fetchone()
        
        if result:
            # Check if custom_id column exists
            columns = connection.execute(text("PRAGMA table_info(medications)")).fetchall()
            column_names = [col[1] for col in columns]
            
            if 'custom_id' not in column_names:
                print("Adding custom_id to medications table...")
                # For SQLite, we need to recreate the table
                connection.execute(text("""
                    CREATE TABLE medications_new (
                        id INTEGER PRIMARY KEY,
                        custom_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        dosage TEXT,
                        frequency TEXT,
                        start_date TEXT,
                        end_date TEXT,
                        instructions TEXT,
                        prescription_required INTEGER DEFAULT 0,
                        notes TEXT,
                        medication_type TEXT,
                        created_at TEXT,
                        updated_at TEXT,
                        FOREIGN KEY(custom_id) REFERENCES users(custom_id)
                    )
                """))
                
                # Copy data from old table (if it has data)
                try:
                    # Check if old table has user_id column
                    old_columns = connection.execute(text("PRAGMA table_info(medications)")).fetchall()
                    old_column_names = [col[1] for col in old_columns]
                    
                    if 'user_id' in old_column_names:
                        # Migrate data using user_id as custom_id placeholder
                        connection.execute(text("""
                            INSERT INTO medications_new 
                            (id, custom_id, name, dosage, frequency, start_date, end_date, 
                             instructions, prescription_required, notes, medication_type, 
                             created_at, updated_at)
                            SELECT id, COALESCE(user_id, 'unknown'), name, dosage, frequency, 
                                   start_date, end_date, instructions, prescription_required, 
                                   notes, medication_type, created_at, updated_at
                            FROM medications
                        """))
                    else:
                        # Just copy existing data
                        connection.execute(text("""
                            INSERT INTO medications_new 
                            SELECT * FROM medications
                        """))
                except Exception as e:
                    print(f"Warning: Could not migrate existing data: {e}")
                
                # Drop old table and rename new one
                connection.execute(text("DROP TABLE medications"))
                connection.execute(text("ALTER TABLE medications_new RENAME TO medications"))
                
                # Create index
                connection.execute(text("CREATE INDEX idx_medications_custom_id ON medications(custom_id)"))
                print("Successfully added custom_id to medications table")
            else:
                print("custom_id already exists in medications table")
    except Exception as e:
        print(f"Error processing medications table: {e}")
    
    # Check if examination_reports table exists and add custom_id if missing
    try:
        result = connection.execute(text(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='examination_reports'"
        )).fetchone()
        
        if result:
            columns = connection.execute(text("PRAGMA table_info(examination_reports)")).fetchall()
            column_names = [col[1] for col in columns]
            
            if 'custom_id' not in column_names:
                print("Adding custom_id to examination_reports table...")
                # For SQLite, we need to recreate the table
                connection.execute(text("""
                    CREATE TABLE examination_reports_new (
                        id INTEGER PRIMARY KEY,
                        custom_id TEXT NOT NULL,
                        exam_type TEXT NOT NULL,
                        hospital_name TEXT NOT NULL,
                        department TEXT,
                        exam_part TEXT,
                        exam_date TEXT,
                        report_date TEXT,
                        device TEXT,
                        doctor_name TEXT,
                        description TEXT,
                        conclusion TEXT,
                        recommendation TEXT,
                        notes TEXT,
                        is_abnormal INTEGER DEFAULT 0,
                        created_at TEXT,
                        updated_at TEXT,
                        FOREIGN KEY(custom_id) REFERENCES users(custom_id)
                    )
                """))
                
                # Copy data from old table (if it has data)
                try:
                    old_columns = connection.execute(text("PRAGMA table_info(examination_reports)")).fetchall()
                    old_column_names = [col[1] for col in old_columns]
                    
                    if 'user_id' in old_column_names:
                        # Migrate data using user_id as custom_id placeholder
                        connection.execute(text("""
                            INSERT INTO examination_reports_new 
                            (id, custom_id, exam_type, hospital_name, department, exam_part, 
                             exam_date, report_date, device, doctor_name, description, 
                             conclusion, recommendation, notes, is_abnormal, created_at, updated_at)
                            SELECT id, COALESCE(user_id, 'unknown'), exam_type, hospital_name, 
                                   department, exam_part, exam_date, report_date, device, 
                                   doctor_name, description, conclusion, recommendation, 
                                   notes, is_abnormal, created_at, updated_at
                            FROM examination_reports
                        """))
                    else:
                        # Just copy existing data
                        connection.execute(text("""
                            INSERT INTO examination_reports_new 
                            SELECT * FROM examination_reports
                        """))
                except Exception as e:
                    print(f"Warning: Could not migrate existing data: {e}")
                
                # Drop old table and rename new one
                connection.execute(text("DROP TABLE examination_reports"))
                connection.execute(text("ALTER TABLE examination_reports_new RENAME TO examination_reports"))
                
                # Create index
                connection.execute(text("CREATE INDEX idx_examination_reports_custom_id ON examination_reports(custom_id)"))
                print("Successfully added custom_id to examination_reports table")
            else:
                print("custom_id already exists in examination_reports table")
    except Exception as e:
        print(f"Error processing examination_reports table: {e}")


def upgrade_other_db(connection):
    """Upgrade non-SQLite database (PostgreSQL, MySQL, etc.)"""
    # For other databases, we can use ALTER TABLE directly
    try:
        # Add custom_id to medications table if it doesn't exist
        try:
            connection.execute(text("SELECT custom_id FROM medications LIMIT 1"))
            print("custom_id already exists in medications table")
        except:
            print("Adding custom_id to medications table...")
            connection.execute(text(
                "ALTER TABLE medications ADD COLUMN custom_id VARCHAR(20) NOT NULL DEFAULT 'unknown'"
            ))
            connection.execute(text(
                "ALTER TABLE medications ADD CONSTRAINT fk_medications_custom_id "
                "FOREIGN KEY (custom_id) REFERENCES users(custom_id)"
            ))
            connection.execute(text("CREATE INDEX idx_medications_custom_id ON medications(custom_id)"))
            print("Successfully added custom_id to medications table")
    except Exception as e:
        print(f"Error processing medications table: {e}")
    
    try:
        # Add custom_id to examination_reports table if it doesn't exist
        try:
            connection.execute(text("SELECT custom_id FROM examination_reports LIMIT 1"))
            print("custom_id already exists in examination_reports table")
        except:
            print("Adding custom_id to examination_reports table...")
            connection.execute(text(
                "ALTER TABLE examination_reports ADD COLUMN custom_id VARCHAR(20) NOT NULL DEFAULT 'unknown'"
            ))
            connection.execute(text(
                "ALTER TABLE examination_reports ADD CONSTRAINT fk_examination_reports_custom_id "
                "FOREIGN KEY (custom_id) REFERENCES users(custom_id)"
            ))
            connection.execute(text("CREATE INDEX idx_examination_reports_custom_id ON examination_reports(custom_id)"))
            print("Successfully added custom_id to examination_reports table")
    except Exception as e:
        print(f"Error processing examination_reports table: {e}")


def downgrade() -> None:
    """Remove custom_id column from health-related tables"""
    # Get database connection
    connection = op.get_bind()
    
    if 'sqlite' in str(connection.engine.url):
        # For SQLite, we would need to recreate tables without custom_id
        # This is complex and risky, so we'll just log a warning
        print("Warning: Downgrade not implemented for SQLite. Manual intervention required.")
    else:
        # For other databases
        try:
            connection.execute(text("ALTER TABLE medications DROP COLUMN custom_id"))
            print("Removed custom_id from medications table")
        except Exception as e:
            print(f"Error removing custom_id from medications: {e}")
        
        try:
            connection.execute(text("ALTER TABLE examination_reports DROP COLUMN custom_id"))
            print("Removed custom_id from examination_reports table")
        except Exception as e:
            print(f"Error removing custom_id from examination_reports: {e}")