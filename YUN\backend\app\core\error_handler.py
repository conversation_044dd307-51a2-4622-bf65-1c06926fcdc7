#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理和日志模块
提供全局异常处理、错误分类、日志记录和监控功能
"""

import sys
import traceback
import logging
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps
from contextlib import asynccontextmanager

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
from pydantic import ValidationError

from .response_handler import ResponseHandler, ErrorType
from ..config.environment import get_settings

# 获取配置
settings = get_settings()

class ErrorCategory:
    """错误分类"""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    BUSINESS = "business"
    SYSTEM = "system"
    DATABASE = "database"
    NETWORK = "network"
    EXTERNAL = "external"

class ErrorSeverity:
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorContext:
    """错误上下文"""
    def __init__(
        self,
        request: Optional[Request] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        self.request = request
        self.user_id = user_id
        self.session_id = session_id
        self.trace_id = trace_id
        self.additional_data = additional_data or {}
        self.timestamp = datetime.now()

class CustomException(Exception):
    """自定义异常基类"""
    def __init__(
        self,
        message: str,
        error_type: ErrorType = ErrorType.SYSTEM_ERROR,
        error_key: str = "internal_error",
        category: str = ErrorCategory.SYSTEM,
        severity: str = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    ):
        self.message = message
        self.error_type = error_type
        self.error_key = error_key
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.status_code = status_code
        super().__init__(message)

class ValidationException(CustomException):
    """验证异常"""
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.VALIDATION_ERROR,
            error_key="invalid_value",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            **kwargs
        )
        if field:
            self.details["field"] = field

class AuthenticationException(CustomException):
    """认证异常"""
    def __init__(self, message: str = "认证失败", **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.AUTHENTICATION_ERROR,
            error_key="invalid_credentials",
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.MEDIUM,
            status_code=status.HTTP_401_UNAUTHORIZED,
            **kwargs
        )

class AuthorizationException(CustomException):
    """授权异常"""
    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.AUTHORIZATION_ERROR,
            error_key="insufficient_permissions",
            category=ErrorCategory.AUTHORIZATION,
            severity=ErrorSeverity.MEDIUM,
            status_code=status.HTTP_403_FORBIDDEN,
            **kwargs
        )

class BusinessException(CustomException):
    """业务异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.BUSINESS_ERROR,
            error_key="business_rule_violation",
            category=ErrorCategory.BUSINESS,
            severity=ErrorSeverity.MEDIUM,
            status_code=status.HTTP_400_BAD_REQUEST,
            **kwargs
        )

class ResourceNotFoundException(CustomException):
    """资源不存在异常"""
    def __init__(self, resource: str = "资源", **kwargs):
        super().__init__(
            message=f"{resource}不存在",
            error_type=ErrorType.NOT_FOUND_ERROR,
            error_key="resource_not_found",
            category=ErrorCategory.BUSINESS,
            severity=ErrorSeverity.LOW,
            status_code=status.HTTP_404_NOT_FOUND,
            **kwargs
        )

class DatabaseException(CustomException):
    """数据库异常"""
    def __init__(self, message: str = "数据库操作失败", **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.DATABASE_ERROR,
            error_key="query_error",
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            **kwargs
        )

class ExternalAPIException(CustomException):
    """外部API异常"""
    def __init__(self, message: str = "外部服务调用失败", **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.EXTERNAL_API_ERROR,
            error_key="api_unavailable",
            category=ErrorCategory.EXTERNAL,
            severity=ErrorSeverity.MEDIUM,
            status_code=status.HTTP_502_BAD_GATEWAY,
            **kwargs
        )

class CacheException(CustomException):
    """缓存异常"""
    def __init__(self, message: str = "缓存操作失败", **kwargs):
        super().__init__(
            message=message,
            error_type=ErrorType.SYSTEM_ERROR,
            error_key="cache_error",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.MEDIUM,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            **kwargs
        )

class ErrorLogger:
    """错误日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger("error_handler")
        self._setup_logger()
        self.error_stats = {
            "total_errors": 0,
            "by_category": {},
            "by_severity": {},
            "by_hour": {}
        }
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            # 控制台处理器
            if settings.logging.console_enabled:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setLevel(getattr(logging, settings.logging.level))
                console_formatter = logging.Formatter(settings.logging.format)
                console_handler.setFormatter(console_formatter)
                self.logger.addHandler(console_handler)
            
            # 文件处理器
            if settings.logging.file_enabled:
                from logging.handlers import RotatingFileHandler
                import os
                
                log_path = settings.get_log_path()
                error_log_file = os.path.join(log_path, "error.log")
                
                file_handler = RotatingFileHandler(
                    error_log_file,
                    maxBytes=settings.logging.file_max_size,
                    backupCount=settings.logging.file_backup_count
                )
                file_handler.setLevel(logging.ERROR)
                file_formatter = logging.Formatter(
                    "%(asctime)s - %(name)s - %(levelname)s - %(message)s - %(pathname)s:%(lineno)d"
                )
                file_handler.setFormatter(file_formatter)
                self.logger.addHandler(file_handler)
            
            self.logger.setLevel(getattr(logging, settings.logging.level))
    
    def log_error(
        self,
        exception: Exception,
        context: Optional[ErrorContext] = None,
        category: str = ErrorCategory.SYSTEM,
        severity: str = ErrorSeverity.MEDIUM
    ):
        """记录错误"""
        # 更新统计
        self._update_stats(category, severity)
        
        # 构建日志消息
        log_data = {
            "error_type": type(exception).__name__,
            "message": str(exception),
            "category": category,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        }
        
        # 添加上下文信息
        if context:
            if context.request:
                log_data["request"] = {
                    "method": context.request.method,
                    "url": str(context.request.url),
                    "headers": dict(context.request.headers),
                    "client_ip": context.request.client.host if context.request.client else None
                }
            
            if context.user_id:
                log_data["user_id"] = context.user_id
            
            if context.session_id:
                log_data["session_id"] = context.session_id
            
            if context.trace_id:
                log_data["trace_id"] = context.trace_id
            
            if context.additional_data:
                log_data["additional_data"] = context.additional_data
        
        # 添加堆栈跟踪
        if settings.is_development:
            log_data["traceback"] = traceback.format_exc()
        
        # 记录日志
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_data)
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(log_data)
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_data)
        else:
            self.logger.info(log_data)
        
        # 发送告警
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self._send_alert(exception, context, severity)
    
    def _update_stats(self, category: str, severity: str):
        """更新错误统计"""
        self.error_stats["total_errors"] += 1
        
        # 按分类统计
        if category not in self.error_stats["by_category"]:
            self.error_stats["by_category"][category] = 0
        self.error_stats["by_category"][category] += 1
        
        # 按严重程度统计
        if severity not in self.error_stats["by_severity"]:
            self.error_stats["by_severity"][severity] = 0
        self.error_stats["by_severity"][severity] += 1
        
        # 按小时统计
        hour = datetime.now().strftime("%Y-%m-%d %H")
        if hour not in self.error_stats["by_hour"]:
            self.error_stats["by_hour"][hour] = 0
        self.error_stats["by_hour"][hour] += 1
    
    def _send_alert(self, exception: Exception, context: Optional[ErrorContext], severity: str):
        """发送告警"""
        if not settings.monitoring.alert_enabled:
            return
        
        # 这里可以集成邮件、短信、Webhook等告警方式
        alert_data = {
            "severity": severity,
            "error": str(exception),
            "timestamp": datetime.now().isoformat(),
            "environment": settings.environment
        }
        
        if context and context.request:
            alert_data["url"] = str(context.request.url)
            alert_data["method"] = context.request.method
        
        # 发送到日志（实际应用中可以发送到告警系统）
        self.logger.critical(f"ALERT: {alert_data}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return self.error_stats.copy()
    
    def reset_stats(self):
        """重置统计"""
        self.error_stats = {
            "total_errors": 0,
            "by_category": {},
            "by_severity": {},
            "by_hour": {}
        }

class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.logger = ErrorLogger()
        self.response_handler = ResponseHandler()
    
    def handle_exception(
        self,
        exception: Exception,
        context: Optional[ErrorContext] = None
    ) -> JSONResponse:
        """处理异常"""
        # 自定义异常
        if isinstance(exception, CustomException):
            self.logger.log_error(exception, context, exception.category, exception.severity)
            return self.response_handler.error(
                error_type=exception.error_type,
                error_key=exception.error_key,
                message=exception.message,
                details=exception.details,
                status_code=exception.status_code
            )
        
        # HTTP异常
        elif isinstance(exception, (HTTPException, StarletteHTTPException)):
            self.logger.log_error(exception, context, ErrorCategory.SYSTEM, ErrorSeverity.LOW)
            return self.response_handler.error(
                error_type=ErrorType.SYSTEM_ERROR,
                error_key="internal_error",
                message=exception.detail if hasattr(exception, 'detail') else str(exception),
                status_code=exception.status_code
            )
        
        # 验证异常
        elif isinstance(exception, (RequestValidationError, ValidationError)):
            self.logger.log_error(exception, context, ErrorCategory.VALIDATION, ErrorSeverity.LOW)
            errors = []
            if hasattr(exception, 'errors'):
                errors = exception.errors()
            return self.response_handler.validation_error(errors)
        
        # 数据库异常
        elif isinstance(exception, SQLAlchemyError):
            self.logger.log_error(exception, context, ErrorCategory.DATABASE, ErrorSeverity.HIGH)
            
            if isinstance(exception, IntegrityError):
                return self.response_handler.error(
                    error_type=ErrorType.DATABASE_ERROR,
                    error_key="constraint_violation",
                    message="数据完整性约束违反"
                )
            elif isinstance(exception, OperationalError):
                return self.response_handler.error(
                    error_type=ErrorType.DATABASE_ERROR,
                    error_key="connection_error",
                    message="数据库连接错误"
                )
            else:
                return self.response_handler.error(
                    error_type=ErrorType.DATABASE_ERROR,
                    error_key="query_error",
                    message="数据库查询错误"
                )
        
        # 其他异常
        else:
            self.logger.log_error(exception, context, ErrorCategory.SYSTEM, ErrorSeverity.HIGH)
            return self.response_handler.internal_error(
                message="系统发生未知错误",
                details={"error": str(exception)} if settings.is_development else None
            )
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return self.logger.get_stats()
    
    def reset_error_stats(self):
        """重置错误统计"""
        self.logger.reset_stats()

# 全局错误处理器实例
error_handler = ErrorHandler()

# 异常处理装饰器
def handle_errors(func: Callable) -> Callable:
    """异常处理装饰器"""
    if asyncio.iscoroutinefunction(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext()
                # 尝试从参数中获取请求对象
                for arg in args:
                    if isinstance(arg, Request):
                        context.request = arg
                        break
                return error_handler.handle_exception(e, context)
        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = ErrorContext()
                # 尝试从参数中获取请求对象
                for arg in args:
                    if isinstance(arg, Request):
                        context.request = arg
                        break
                return error_handler.handle_exception(e, context)
        return sync_wrapper

# 上下文管理器
@asynccontextmanager
async def error_context(
    request: Optional[Request] = None,
    user_id: Optional[str] = None,
    additional_data: Optional[Dict[str, Any]] = None
):
    """错误上下文管理器"""
    context = ErrorContext(
        request=request,
        user_id=user_id,
        additional_data=additional_data
    )
    
    try:
        yield context
    except Exception as e:
        response = error_handler.handle_exception(e, context)
        raise HTTPException(
            status_code=response.status_code,
            detail=response.body.decode() if hasattr(response, 'body') else str(e)
        )

# 便捷函数
def log_error(exception: Exception, **kwargs):
    """记录错误便捷函数"""
    error_handler.logger.log_error(exception, **kwargs)

def raise_validation_error(message: str, field: Optional[str] = None):
    """抛出验证错误"""
    raise ValidationException(message, field)

def raise_auth_error(message: str = "认证失败"):
    """抛出认证错误"""
    raise AuthenticationException(message)

def raise_permission_error(message: str = "权限不足"):
    """抛出权限错误"""
    raise AuthorizationException(message)

def raise_business_error(message: str):
    """抛出业务错误"""
    raise BusinessException(message)

def raise_not_found_error(resource: str = "资源"):
    """抛出资源不存在错误"""
    raise ResourceNotFoundException(resource)

def raise_database_error(message: str = "数据库操作失败"):
    """抛出数据库错误"""
    raise DatabaseException(message)

def raise_external_api_error(message: str = "外部服务调用失败"):
    """抛出外部API错误"""
    raise ExternalAPIException(message)