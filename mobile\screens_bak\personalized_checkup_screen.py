# -*- coding: utf-8 -*-
"""
个性化体检方案屏幕
基于用户健康状况和AI分析，提供个性化体检方案定制服务

功能特点：
1. 健康状况评估：基于用户基本信息、病史、家族史等
2. AI智能推荐：根据年龄、性别、风险因素推荐体检项目
3. 方案定制：用户可自定义添加或删除体检项目
4. 费用预估：显示各项目费用和总费用
5. 医院推荐：推荐附近的体检机构
6. 预约服务：支持在线预约体检
"""

from kivy.clock import Clock
from kivy.metrics import dp
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDFabButton, MDButton, MDButtonText, MDButtonIcon
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.chip import MDChip
from utils.toast import toast
from kivy.properties import StringProperty

from screens.base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import ThemeManager
from theme import FontManager

# KV语言字符串
KV = '''
<CheckupItemCard>
    orientation: "vertical"
    size_hint_y: None
    height: dp(100)
    padding: dp(16)
    spacing: dp(8)
    elevation: 2
    radius: [dp(12)]
    md_bg_color: app.theme.SURFACE_COLOR
    
    MDBoxLayout:
        orientation: "horizontal"
        spacing: dp(12)
        
        MDCheckbox:
            id: checkbox
            size_hint: None, None
            size: dp(32), dp(32)
            pos_hint: {"center_y": 0.5}
            active: root.is_selected
            on_active: root.on_checkbox_active(self.active)
        
        MDBoxLayout:
            orientation: "vertical"
            spacing: dp(4)
            
            MDLabel:
                text: root.item_name
                theme_text_color: "Primary"
                font_size: app.font_styles.TITLE_MEDIUM['font_size']
                font_name: app.font_styles.TITLE_MEDIUM['font_name']
                bold: app.font_styles.TITLE_MEDIUM['bold']
                adaptive_height: True
            
            MDLabel:
                text: root.description
                theme_text_color: "Secondary"
                font_size: app.font_styles.BODY_MEDIUM['font_size']
                font_name: app.font_styles.BODY_MEDIUM['font_name']
                adaptive_height: True
        
        MDBoxLayout:
            orientation: "vertical"
            spacing: dp(4)
            size_hint_x: None
            width: dp(80)
            
            MDLabel:
                text: f"¥{root.price}"
                theme_text_color: "Primary"
                font_size: app.font_styles.TITLE_SMALL['font_size']
                font_name: app.font_styles.TITLE_SMALL['font_name']
                bold: app.font_styles.TITLE_SMALL['bold']
                halign: "right"
                adaptive_height: True
            
            MDChip:
                text: root.category
                size_hint_x: None
                width: dp(80)
                height: dp(24)
                md_bg_color: root.category_color

<HospitalCard>
    orientation: "vertical"
    size_hint_y: None
    height: dp(120)
    padding: dp(16)
    spacing: dp(8)
    elevation: 2
    radius: [dp(12)]
    md_bg_color: app.theme.SURFACE_COLOR
    
    MDBoxLayout:
        orientation: "horizontal"
        spacing: dp(12)
        
        MDIcon:
            icon: "hospital-building"
            theme_icon_color: "Primary"
            size_hint: None, None
            size: dp(40), dp(40)
            pos_hint: {"center_y": 0.5}
        
        MDBoxLayout:
            orientation: "vertical"
            spacing: dp(4)
            
            MDLabel:
                text: root.hospital_name
                theme_text_color: "Primary"
                font_size: app.font_styles.TITLE_MEDIUM['font_size']
                font_name: app.font_styles.TITLE_MEDIUM['font_name']
                bold: app.font_styles.TITLE_MEDIUM['bold']
                adaptive_height: True
            
            MDLabel:
                text: f"距离: {root.distance} | 评分: {root.rating}★"
                theme_text_color: "Secondary"
                font_size: app.font_styles.TITLE_SMALL['font_size']
                font_name: app.font_styles.TITLE_SMALL['font_name']
                bold: app.font_styles.TITLE_SMALL['bold']
                adaptive_height: True
            
            MDLabel:
                text: root.address
                theme_text_color: "Secondary"
                font_size: app.font_styles.BODY_MEDIUM['font_size']
                font_name: app.font_styles.BODY_MEDIUM['font_name']
                adaptive_height: True
        
        MDButton:
            style: "elevated"
            size_hint: None, None
            size: dp(60), dp(36)
            pos_hint: {"center_y": 0.5}
            on_release: root.on_book()
            
            MDButtonText:
                text: "预约"

<PersonalizedCheckupScreen>
    name: "personalized_checkup"
    
    MDBoxLayout:
        orientation: "vertical"
        
        # 顶部栏
        MDTopAppBar:
            title: "个性化体检方案"
            left_action_items: [["arrow-left", lambda x: root.go_back()]]
            right_action_items: [["refresh", lambda x: root.refresh_recommendations()]]
            md_bg_color: app.theme.PRIMARY_COLOR
        
        # 主内容区域
        MDScrollView:
            MDBoxLayout:
                orientation: "vertical"
                spacing: dp(16)
                padding: dp(16)
                adaptive_height: True
                
                # 健康状况评估卡片
                MDCard:
                    orientation: "vertical"
                    size_hint_y: None
                    height: dp(120)
                    padding: dp(16)
                    spacing: dp(8)
                    elevation: 2
                    radius: [dp(12)]
                    
                    MDLabel:
                        text: "健康状况评估"
                        theme_text_color: "Primary"
                        font_size: app.font_styles.TITLE_LARGE['font_size']
                        font_name: app.font_styles.TITLE_LARGE['font_name']
                        bold: app.font_styles.TITLE_LARGE['bold']
                        adaptive_height: True
                    
                    MDBoxLayout:
                        orientation: "horizontal"
                        spacing: dp(16)
                        adaptive_height: True
                        
                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: dp(4)
                            
                            MDLabel:
                                id: age_gender_label
                                text: "年龄: 35岁 | 性别: 男"
                                theme_text_color: "Secondary"
                                font_size: app.font_styles.BODY_MEDIUM['font_size']
                                font_name: app.font_styles.BODY_MEDIUM['font_name']
                                adaptive_height: True
                            
                            MDLabel:
                                id: risk_factors_label
                                text: "风险因素: 高血压家族史, 吸烟"
                                theme_text_color: "Secondary"
                                font_size: app.font_styles.BODY_MEDIUM['font_size']
                                font_name: app.font_styles.BODY_MEDIUM['font_name']
                                adaptive_height: True
                        
                        MDButton:
                            style: "elevated"
                            size_hint: None, None
                            size: dp(100), dp(36)
                            on_release: root.update_health_info()
                            
                            MDButtonText:
                                text: "更新信息"
                
                # AI推荐方案
                MDLabel:
                    text: "AI智能推荐方案"
                    theme_text_color: "Primary"
                    font_size: app.font_styles.TITLE_LARGE['font_size']
                    font_name: app.font_styles.TITLE_LARGE['font_name']
                    bold: app.font_styles.TITLE_LARGE['bold']
                    adaptive_height: True
                
                MDGridLayout:
                    id: recommended_items_grid
                    cols: 1
                    spacing: dp(8)
                    adaptive_height: True
                
                # 可选项目
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: dp(16)
                    adaptive_height: True
                    
                    MDLabel:
                        text: "可选项目"
                        theme_text_color: "Primary"
                        font_size: app.font_styles.TITLE_LARGE['font_size']
                        font_name: app.font_styles.TITLE_LARGE['font_name']
                        bold: app.font_styles.TITLE_LARGE['bold']
                        adaptive_height: True
                    
                    MDButton:
                        style: "elevated"
                        size_hint: None, None
                        size: dp(100), dp(36)
                        on_release: root.show_optional_items()
                        
                        MDButtonText:
                            text: "添加项目"
                
                MDGridLayout:
                    id: optional_items_grid
                    cols: 1
                    spacing: dp(8)
                    adaptive_height: True
                
                # 费用预估
                MDCard:
                    orientation: "vertical"
                    size_hint_y: None
                    height: dp(100)
                    padding: dp(16)
                    spacing: dp(8)
                    elevation: 2
                    radius: [dp(12)]
                    md_bg_color: app.theme.PRIMARY_LIGHT
                    
                    MDLabel:
                        text: "费用预估"
                        theme_text_color: "Primary"
                        font_size: app.font_styles.TITLE_LARGE['font_size']
                        font_name: app.font_styles.TITLE_LARGE['font_name']
                        bold: app.font_styles.TITLE_LARGE['bold']
                        adaptive_height: True
                    
                    MDBoxLayout:
                        orientation: "horizontal"
                        spacing: dp(16)
                        adaptive_height: True
                        
                        MDLabel:
                            id: total_cost_label
                            text: "总费用: ¥1,280"
                            theme_text_color: "Primary"
                            font_size: app.font_styles.TITLE_LARGE['font_size']
                            font_name: app.font_styles.TITLE_LARGE['font_name']
                            bold: app.font_styles.TITLE_LARGE['bold']
                            adaptive_height: True
                        
                        MDLabel:
                            id: items_count_label
                            text: "共8项"
                            theme_text_color: "Secondary"
                            font_size: app.font_styles.BODY_MEDIUM['font_size']
                            font_name: app.font_styles.BODY_MEDIUM['font_name']
                            adaptive_height: True
                
                # 医院推荐
                MDLabel:
                    text: "推荐医院"
                    theme_text_color: "Primary"
                    font_size: app.font_styles.TITLE_LARGE['font_size']
                    font_name: app.font_styles.TITLE_LARGE['font_name']
                    bold: app.font_styles.TITLE_LARGE['bold']
                    adaptive_height: True
                
                MDGridLayout:
                    id: hospitals_grid
                    cols: 1
                    spacing: dp(8)
                    adaptive_height: True
                
                # 底部按钮
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: dp(16)
                    size_hint_y: None
                    height: dp(48)
                    
                    MDButton:
                        style: "elevated"
                        size_hint_x: 0.5
                        on_release: root.save_plan()
                        
                        MDButtonText:
                            text: "保存方案"
                    
                    MDButton:
                        style: "elevated"
                        size_hint_x: 0.5
                        md_bg_color: app.theme.PRIMARY_COLOR
                        on_release: root.book_checkup()
                        
                        MDButtonText:
                            text: "立即预约"
'''

class CheckupItemCard(MDCard):
    """体检项目卡片组件"""
    
    category = StringProperty("其他")  # 添加属性声明
    
    def __init__(self, item_data, **kwargs):
        self.item_data = item_data
        
        # 设置卡片属性
        self.item_name = item_data.get('name', '未知项目')
        self.description = item_data.get('description', '暂无描述')
        self.price = item_data.get('price', 0)
        self.category = item_data.get('category', '其他')
        self.is_selected = item_data.get('selected', False)
        self.category_color = self.get_category_color()
        
        super().__init__(**kwargs)
    
    def get_category_color(self):
        """根据分类获取颜色"""
        app = MDApp.get_running_app()
        colors = {
            '基础检查': app.theme.PRIMARY_COLOR,
            '血液检查': '#E53E3E',
            '影像检查': '#3182CE',
            '专科检查': '#38A169',
            '其他': [0.9, 0.9, 0.9, 1]  # 使用灰色替代surfaceVariant
        }
        return colors.get(self.category, [0.9, 0.9, 0.9, 1])
    
    def on_checkbox_active(self, active):
        """复选框状态改变"""
        self.is_selected = active
        self.item_data['selected'] = active
        
        # 通知父屏幕更新费用
        screen = self.get_screen()
        if screen:
            screen.update_total_cost()
    
    def get_screen(self):
        """获取父屏幕"""
        parent = self.parent
        while parent:
            if hasattr(parent, 'name') and parent.name == 'personalized_checkup':
                return parent
            parent = parent.parent
        return None

class HospitalCard(MDCard):
    """医院卡片组件"""
    
    hospital_name = StringProperty("未知医院")
    distance = StringProperty("未知")
    rating = StringProperty("0")
    address = StringProperty("暂无地址")
    
    def __init__(self, hospital_data, **kwargs):
        self.hospital_data = hospital_data
        
        # 设置卡片属性
        self.hospital_name = hospital_data.get('name', '未知医院')
        self.distance = hospital_data.get('distance', '未知')
        self.rating = str(hospital_data.get('rating', 0))
        self.address = hospital_data.get('address', '暂无地址')
        
        super().__init__(**kwargs)
    
    def on_book(self):
        """预约医院"""
        screen = self.get_screen()
        if screen:
            screen.book_hospital(self.hospital_data)
    
    def get_screen(self):
        """获取父屏幕"""
        parent = self.parent
        while parent:
            if hasattr(parent, 'name') and parent.name == 'personalized_checkup':
                return parent
            parent = parent.parent
        return None

class PersonalizedCheckupScreen(BaseScreen):
    """个性化体检方案屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        
        # 模拟用户健康数据
        self.user_health_data = {
            'age': 35,
            'gender': '男',
            'risk_factors': ['高血压家族史', '吸烟'],
            'medical_history': ['无'],
            'current_medications': []
        }
        
        # 模拟体检项目数据
        self.checkup_items = {
            'recommended': [
                {
                    'id': 'r001',
                    'name': '血常规',
                    'description': '检查血液基本指标',
                    'price': 80,
                    'category': '血液检查',
                    'selected': True,
                    'reason': '基础必检项目'
                },
                {
                    'id': 'r002',
                    'name': '血压测量',
                    'description': '测量血压值',
                    'price': 20,
                    'category': '基础检查',
                    'selected': True,
                    'reason': '有高血压家族史'
                },
                {
                    'id': 'r003',
                    'name': '胸部X光',
                    'description': '检查肺部健康',
                    'price': 150,
                    'category': '影像检查',
                    'selected': True,
                    'reason': '吸烟人群建议检查'
                },
                {
                    'id': 'r004',
                    'name': '心电图',
                    'description': '检查心脏功能',
                    'price': 100,
                    'category': '专科检查',
                    'selected': True,
                    'reason': '心血管风险评估'
                }
            ],
            'optional': [
                {
                    'id': 'o001',
                    'name': '肝功能检查',
                    'description': '检查肝脏功能指标',
                    'price': 120,
                    'category': '血液检查',
                    'selected': False
                },
                {
                    'id': 'o002',
                    'name': '肾功能检查',
                    'description': '检查肾脏功能指标',
                    'price': 100,
                    'category': '血液检查',
                    'selected': False
                },
                {
                    'id': 'o003',
                    'name': '腹部B超',
                    'description': '检查腹部器官',
                    'price': 200,
                    'category': '影像检查',
                    'selected': False
                }
            ]
        }
        
        # 模拟医院数据
        self.hospitals_data = [
            {
                'id': 'h001',
                'name': '市人民医院体检中心',
                'distance': '2.3km',
                'rating': 4.8,
                'address': '健康路123号',
                'phone': '0755-12345678'
            },
            {
                'id': 'h002',
                'name': '康宁体检中心',
                'distance': '1.8km',
                'rating': 4.6,
                'address': '福田区中心大道456号',
                'phone': '0755-87654321'
            },
            {
                'id': 'h003',
                'name': '美年大健康',
                'distance': '3.1km',
                'rating': 4.5,
                'address': '南山区科技园789号',
                'phone': '0755-11223344'
            }
        ]
        
        Clock.schedule_once(self.init_ui, 0.1)
    
    def init_ui(self, dt):
        """初始化UI"""
        self.load_user_health_info()
        self.load_recommended_items()
        self.load_optional_items()
        self.load_hospitals()
        self.update_total_cost()
    
    def load_user_health_info(self):
        """加载用户健康信息"""
        age = self.user_health_data['age']
        gender = self.user_health_data['gender']
        risk_factors = ', '.join(self.user_health_data['risk_factors'])
        
        self.ids.age_gender_label.text = f"年龄: {age}岁 | 性别: {gender}"
        self.ids.risk_factors_label.text = f"风险因素: {risk_factors}"
    
    def load_recommended_items(self):
        """加载推荐项目"""
        grid = self.ids.recommended_items_grid
        grid.clear_widgets()
        
        for item in self.checkup_items['recommended']:
            card = CheckupItemCard(item)
            grid.add_widget(card)
    
    def load_optional_items(self):
        """加载可选项目"""
        grid = self.ids.optional_items_grid
        grid.clear_widgets()
        
        # 只显示已选择的可选项目
        selected_optional = [item for item in self.checkup_items['optional'] if item.get('selected', False)]
        
        for item in selected_optional:
            card = CheckupItemCard(item)
            grid.add_widget(card)
    
    def load_hospitals(self):
        """加载医院列表"""
        grid = self.ids.hospitals_grid
        grid.clear_widgets()
        
        for hospital in self.hospitals_data:
            card = HospitalCard(hospital)
            grid.add_widget(card)
    
    def update_total_cost(self):
        """更新总费用"""
        total_cost = 0
        total_items = 0
        
        # 计算推荐项目费用
        for item in self.checkup_items['recommended']:
            if item.get('selected', False):
                total_cost += item.get('price', 0)
                total_items += 1
        
        # 计算可选项目费用
        for item in self.checkup_items['optional']:
            if item.get('selected', False):
                total_cost += item.get('price', 0)
                total_items += 1
        
        self.ids.total_cost_label.text = f"总费用: ¥{total_cost:,}"
        self.ids.items_count_label.text = f"共{total_items}项"
    
    def refresh_recommendations(self):
        """刷新AI推荐"""
        toast("正在重新分析您的健康状况...")
        # TODO: 实现AI重新推荐逻辑
        Clock.schedule_once(lambda dt: toast("推荐方案已更新"), 2)
    
    def update_health_info(self):
        """更新健康信息"""
        dialog = MDDialog(
            title="更新健康信息",
            text="健康信息更新功能开发中...\n\n将支持更新：\n• 基本信息（年龄、性别）\n• 疾病史\n• 家族史\n• 生活习惯\n• 当前用药",
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="确定"),
                    style="elevated",
                    on_release=lambda x: self.save_health_info(dialog)
                )
            ]
        )
        dialog.open()
    
    def save_health_info(self, dialog):
        """保存健康信息"""
        dialog.dismiss()
        toast("健康信息保存功能开发中...")
        # TODO: 实现健康信息保存逻辑
    
    def show_optional_items(self):
        """显示可选项目列表"""
        # 创建可选项目选择对话框
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8),
            adaptive_height=True
        )
        
        for item in self.checkup_items['optional']:
            item_layout = MDBoxLayout(
                orientation="horizontal",
                spacing=dp(12),
                adaptive_height=True
            )
            
            checkbox = MDCheckbox(
                size_hint=(None, None),
                size=(dp(32), dp(32)),
                active=item.get('selected', False)
            )
            
            label = MDLabel(
                text=f"{item['name']} - ¥{item['price']}",
                theme_text_color="Primary",
                adaptive_height=True
            )
            
            item_layout.add_widget(checkbox)
            item_layout.add_widget(label)
            content.add_widget(item_layout)
            
            # 绑定复选框事件
            checkbox.bind(active=lambda x, active, item=item: self.on_optional_item_selected(item, active))
        
        dialog = MDDialog(
            title="选择可选项目",
            content=content,
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="确定"),
                    style="elevated",
                    on_release=lambda x: self.apply_optional_selection(dialog)
                )
            ]
        )
        dialog.open()
    
    def on_optional_item_selected(self, item, active):
        """可选项目选择状态改变"""
        item['selected'] = active
    
    def apply_optional_selection(self, dialog):
        """应用可选项目选择"""
        dialog.dismiss()
        self.load_optional_items()
        self.update_total_cost()
        toast("已更新体检方案")
    
    def save_plan(self):
        """保存体检方案"""
        # 收集选中的项目
        selected_items = []
        
        for item in self.checkup_items['recommended']:
            if item.get('selected', False):
                selected_items.append(item)
        
        for item in self.checkup_items['optional']:
            if item.get('selected', False):
                selected_items.append(item)
        
        if not selected_items:
            toast("请至少选择一个体检项目")
            return
        
        toast(f"已保存体检方案，共{len(selected_items)}个项目")
        # TODO: 实现方案保存逻辑
    
    def book_checkup(self):
        """立即预约体检"""
        # 检查是否有选中的项目
        selected_items = []
        
        for item in self.checkup_items['recommended']:
            if item.get('selected', False):
                selected_items.append(item)
        
        for item in self.checkup_items['optional']:
            if item.get('selected', False):
                selected_items.append(item)
        
        if not selected_items:
            toast("请至少选择一个体检项目")
            return
        
        # 显示预约确认对话框
        total_cost = sum(item.get('price', 0) for item in selected_items)
        
        dialog = MDDialog(
            title="确认预约",
            text=f"体检项目: {len(selected_items)}项\n总费用: ¥{total_cost:,}\n\n请选择医院进行预约",
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="选择医院"),
                    style="elevated",
                    on_release=lambda x: self.show_hospital_selection(dialog)
                )
            ]
        )
        dialog.open()
    
    def show_hospital_selection(self, dialog):
        """显示医院选择"""
        dialog.dismiss()
        toast("医院预约功能开发中...")
        # TODO: 实现医院预约功能
    
    def book_hospital(self, hospital_data):
        """预约指定医院"""
        hospital_name = hospital_data.get('name', '未知医院')
        toast(f"预约{hospital_name}功能开发中...")
        # TODO: 实现医院预约功能
    
    def go_back(self):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'homepage_screen'

# 注册KV字符串
from kivy.lang import Builder
Builder.load_string(KV)