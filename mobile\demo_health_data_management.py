#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康资料管理页面演示脚本
展示新创建的健康资料管理功能
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from kivy.app import App
from kivy.uix.screenmanager import ScreenManager
from kivymd.app import MDApp
from kivy.core.window import Window

# 导入主题配置
from theme import AppTheme, AppMetrics

# 导入健康资料管理屏幕
from screens.health_data_management_screen import HealthDataManagementScreen

class HealthDataDemoApp(MDApp):
    """健康资料管理演示应用"""
    
    def build(self):
        # 设置窗口大小
        Window.size = (AppMetrics.SCREEN_WIDTH, AppMetrics.SCREEN_HEIGHT)
        
        # 设置背景色
        Window.clearcolor = AppTheme.BACKGROUND_COLOR
        
        # 设置主题颜色
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Light"
        
        # 创建屏幕管理器
        sm = ScreenManager()
        
        # 添加健康资料管理屏幕
        health_data_screen = HealthDataManagementScreen(name="health_data_management_screen")
        sm.add_widget(health_data_screen)
        
        # 设置当前屏幕
        sm.current = "health_data_management_screen"
        
        return sm

if __name__ == "__main__":
    print("启动健康资料管理页面演示...")
    print("功能包括:")
    print("1. 健康状态总览")
    print("2. 基本健康信息")
    print("3. 健康资料传阅")
    print("4. 调查问卷/评估量表")
    print("5. 用药记录")
    print("6. 历年体检报告")
    print("7. 其它记录")
    print("8. 健康日记")
    print("\n点击各个模块可以查看相应功能...")
    
    app = HealthDataDemoApp()
    app.run()