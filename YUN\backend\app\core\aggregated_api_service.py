# -*- coding: utf-8 -*-
"""
聚合API服务
专门处理健康资料、调查问卷和评估量表的前后端通信
提供统一的数据聚合、格式化和响应处理
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Type, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import logging

from fastapi import HTTPException, Request, Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, func, and_, or_
from pydantic import BaseModel, Field

from .api_service import BaseApiService, ServiceType, OperationType, ServiceContext
from .response_handler import ResponseHandler, ApiResponse
from .cache_utils import CacheManager, cache
from .logging_utils import get_logger
from .monitoring_utils import MetricsCollector
from ..utils.performance_monitor import monitor_performance as performance_monitor

logger = get_logger(__name__)
metrics = MetricsCollector()

class DataSourceType(str, Enum):
    """数据源类型枚举"""
    HEALTH_RECORD = "health_record"
    MEDICAL_RECORD = "medical_record"
    LAB_REPORT = "lab_report"
    EXAMINATION_REPORT = "examination_report"
    PHYSICAL_EXAM = "physical_exam"
    QUESTIONNAIRE_DISTRIBUTION = "questionnaire_distribution"  # 未完成的问卷
    QUESTIONNAIRE_RESPONSE = "questionnaire_response"  # 问卷原始回答
    QUESTIONNAIRE_RESULT = "questionnaire_result"  # 问卷分析报告
    ASSESSMENT_DISTRIBUTION = "assessment_distribution"  # 未完成的评估
    ASSESSMENT_RESPONSE = "assessment_response"  # 评估原始回答
    ASSESSMENT_RESULT = "assessment_result"  # 评估分析报告
    QUESTIONNAIRE = "questionnaire"
    ASSESSMENT = "assessment"
    MEDICATION = "medication"
    FOLLOW_UP = "follow_up"
    HEALTH_DIARY = "health_diary"
    OTHER_RECORD = "other_record"

class AggregationStrategy(str, Enum):
    """聚合策略枚举"""
    MERGE_ALL = "merge_all"  # 合并所有数据
    GROUP_BY_TYPE = "group_by_type"  # 按类型分组
    TIMELINE = "timeline"  # 时间线排序
    PRIORITY = "priority"  # 按优先级排序
    LATEST_FIRST = "latest_first"  # 最新优先

class ResponseFormat(str, Enum):
    """响应格式枚举"""
    STANDARD = "standard"  # 标准格式
    MOBILE = "mobile"  # 移动端格式
    FRONTEND = "frontend"  # 前端格式
    EXPORT = "export"  # 导出格式
    SUMMARY = "summary"  # 摘要格式

@dataclass
class DataSourceConfig:
    """数据源配置"""
    source_type: DataSourceType
    enabled: bool = True
    priority: int = 0
    cache_ttl: int = 300
    timeout: int = 30
    retry_count: int = 3
    fields_mapping: Dict[str, str] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AggregationConfig:
    """聚合配置"""
    strategy: AggregationStrategy = AggregationStrategy.MERGE_ALL
    response_format: ResponseFormat = ResponseFormat.STANDARD
    include_metadata: bool = True
    include_statistics: bool = False
    max_records_per_source: int = 1000
    total_max_records: int = 5000
    enable_caching: bool = True
    cache_ttl: int = 300

@dataclass
class AggregationResult:
    """聚合结果"""
    data: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    statistics: Dict[str, Any]
    total_count: int
    source_counts: Dict[str, int]
    execution_time: float
    cache_hit: bool = False

class DataSourceHandler:
    """数据源处理器基类"""
    
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.logger = get_logger(f"{__name__}.{config.source_type}")
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取数据（子类实现）"""
        raise NotImplementedError
    
    def transform_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """转换数据格式"""
        transformed_data = []
        
        for item in raw_data:
            transformed_item = {}
            
            # 应用字段映射
            for source_field, target_field in self.config.fields_mapping.items():
                if source_field in item:
                    transformed_item[target_field] = item[source_field]
            
            # 保留未映射的字段
            for key, value in item.items():
                if key not in self.config.fields_mapping:
                    transformed_item[key] = value
            
            # 添加数据源信息
            transformed_item['_source_type'] = self.config.source_type
            transformed_item['_source_priority'] = self.config.priority
            
            transformed_data.append(transformed_item)
        
        return transformed_data
    
    def apply_filters(self, data: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用过滤条件"""
        if not filters:
            return data
        
        filtered_data = []
        
        for item in data:
            include_item = True
            
            for filter_key, filter_value in filters.items():
                if filter_key in item:
                    item_value = item[filter_key]
                    
                    if isinstance(filter_value, dict):
                        # 范围过滤
                        if 'min' in filter_value and item_value < filter_value['min']:
                            include_item = False
                            break
                        if 'max' in filter_value and item_value > filter_value['max']:
                            include_item = False
                            break
                    elif isinstance(filter_value, list):
                        # 列表过滤
                        if item_value not in filter_value:
                            include_item = False
                            break
                    else:
                        # 精确匹配
                        if item_value != filter_value:
                            include_item = False
                            break
            
            if include_item:
                filtered_data.append(item)
        
        return filtered_data

class HealthRecordHandler(DataSourceHandler):
    """健康记录处理器"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取健康记录数据"""
        try:
            from ..models.health_record import HealthRecord
            
            query = select(HealthRecord).where(HealthRecord.custom_id == user_id)
            
            # 应用过滤条件
            if filters:
                if 'record_type' in filters:
                    query = query.where(HealthRecord.record_type == filters['record_type'])
                # HealthRecord模型中没有status字段，跳过status过滤
                # if 'status' in filters:
                #     query = query.where(HealthRecord.status == filters['status'])
                if 'start_date' in filters:
                    query = query.where(HealthRecord.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(HealthRecord.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                records = result.scalars().all()
            else:
                result = session.execute(query)
                records = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for record in records:
                data.append({
                    'id': record.id,
                    'custom_id': record.custom_id,
                    'record_type': record.record_type,
                    'record_date': record.created_at.isoformat() if record.created_at else None,
                    'title': record.title,
                    'description': record.description,
                    'content': record.content,
                    'metadata': None,
                    'status': 'active',
                    'created_at': record.created_at.isoformat() if record.created_at else None,
                    'updated_at': record.updated_at.isoformat() if record.updated_at else None
                })
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取健康记录数据失败: {str(e)}")
            return []

class QuestionnaireDistributionHandler(DataSourceHandler):
    """问卷分发处理器 - 未完成的问卷"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取问卷分发数据（未完成）"""
        try:
            from ..models.distribution import QuestionnaireDistribution
            from ..models.questionnaire import Questionnaire
            
            # 查询未完成的问卷分发记录
            query = select(QuestionnaireDistribution).join(Questionnaire).where(
                QuestionnaireDistribution.custom_id == user_id,
                QuestionnaireDistribution.status == 'pending'
            )
            
            # 应用过滤条件
            if filters:
                if 'start_date' in filters:
                    query = query.where(QuestionnaireDistribution.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(QuestionnaireDistribution.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                distributions = result.scalars().all()
            else:
                result = session.execute(query)
                distributions = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for dist in distributions:
                item = {
                    'id': dist.id,
                    'type': 'questionnaire',
                    'questionnaire_id': dist.questionnaire_id,
                    'custom_id': dist.custom_id,
                    'distributor_custom_id': dist.distributor_custom_id,
                    'status': dist.status,
                    'due_date': dist.due_date.isoformat() if dist.due_date else None,
                    'message': dist.message,
                    'created_at': dist.created_at.isoformat() if dist.created_at else None,
                    'updated_at': dist.updated_at.isoformat() if dist.updated_at else None,
                    'title': dist.questionnaire.title if dist.questionnaire else None,
                    'description': dist.questionnaire.description if dist.questionnaire else None
                }
                data.append(item)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取问卷分发数据失败: {str(e)}")
            return []

class QuestionnaireResponseHandler(DataSourceHandler):
    """问卷回复处理器 - 原始回答数据"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取问卷回复数据（原始回答）"""
        try:
            from ..models.questionnaire import QuestionnaireResponse, Questionnaire
            
            # 查询问卷回复记录
            query = select(QuestionnaireResponse).join(Questionnaire).where(
                QuestionnaireResponse.custom_id == user_id
            )
            
            # 应用过滤条件
            if filters:
                if 'status' in filters:
                    query = query.where(QuestionnaireResponse.status == filters['status'])
                if 'start_date' in filters:
                    query = query.where(QuestionnaireResponse.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(QuestionnaireResponse.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                responses = result.scalars().all()
            else:
                result = session.execute(query)
                responses = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for response in responses:
                item = {
                    'id': response.id,
                    'type': 'questionnaire_response',
                    'questionnaire_id': response.questionnaire_id,
                    'custom_id': response.custom_id,
                    'total_score': response.total_score,
                    'dimension_scores': response.dimension_scores,
                    'status': response.status,
                    'answers': response.answers,
                    'report': response.report,
                    'created_at': response.created_at.isoformat() if response.created_at else None,
                    'updated_at': response.updated_at.isoformat() if response.updated_at else None,
                    'title': response.questionnaire.title if response.questionnaire else None,
                    'description': response.questionnaire.description if response.questionnaire else None
                }
                data.append(item)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取问卷回复数据失败: {str(e)}")
            return []

class QuestionnaireResultHandler(DataSourceHandler):
    """问卷结果处理器 - 分析报告数据"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取问卷结果数据（分析报告）"""
        try:
            from ..models.result import QuestionnaireResult
            
            # 直接查询问卷结果表
            query = select(QuestionnaireResult).where(QuestionnaireResult.custom_id == user_id)
            
            # 应用过滤条件
            if filters:
                if 'status' in filters:
                    query = query.where(QuestionnaireResult.status == filters['status'])
                if 'start_date' in filters:
                    query = query.where(QuestionnaireResult.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(QuestionnaireResult.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                results = result.scalars().all()
            else:
                result = session.execute(query)
                results = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for result_record in results:
                item = {
                    'id': result_record.id,
                    'type': 'questionnaire_result',
                    'custom_id': result_record.custom_id,
                    'questionnaire_id': result_record.questionnaire_id,
                    'template_id': result_record.template_id,
                    'total_score': result_record.total_score,
                    'max_score': result_record.max_score,
                    'percentage': result_record.percentage,
                    'result_level': result_record.result_level,
                    'result_category': result_record.result_category,
                    'interpretation': result_record.interpretation,
                    'recommendations': result_record.recommendations,
                    'dimension_scores': result_record.dimension_scores,
                    'raw_answers': result_record.raw_answers,
                    'report_generated': result_record.report_generated,
                    'report_content': result_record.report_content,
                    'status': result_record.status,
                    'calculated_at': result_record.calculated_at.isoformat() if result_record.calculated_at else None,
                    'created_at': result_record.created_at.isoformat() if result_record.created_at else None,
                    'updated_at': result_record.updated_at.isoformat() if result_record.updated_at else None,
                    '_source_type': 'questionnaire',
                    'type': 'questionnaire',
                    'date': result_record.calculated_at.isoformat() if result_record.calculated_at else result_record.created_at.isoformat() if result_record.created_at else None
                }
                
                data.append(item)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取问卷调查数据失败: {str(e)}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []

class AssessmentDistributionHandler(DataSourceHandler):
    """评估分发处理器 - 未完成的评估"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取评估分发数据（未完成）"""
        try:
            from ..models.distribution import AssessmentDistribution
            from ..models.assessment import Assessment
            
            # 查询未完成的评估分发记录
            query = select(AssessmentDistribution).join(Assessment).where(
                AssessmentDistribution.custom_id == user_id,
                AssessmentDistribution.status == 'pending'
            )
            
            # 应用过滤条件
            if filters:
                if 'start_date' in filters:
                    query = query.where(AssessmentDistribution.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(AssessmentDistribution.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                distributions = result.scalars().all()
            else:
                result = session.execute(query)
                distributions = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for dist in distributions:
                item = {
                    'id': dist.id,
                    'type': 'assessment',
                    'assessment_id': dist.assessment_id,
                    'custom_id': dist.custom_id,
                    'distributor_custom_id': dist.distributor_custom_id,
                    'status': dist.status,
                    'due_date': dist.due_date.isoformat() if dist.due_date else None,
                    'message': dist.message,
                    'created_at': dist.created_at.isoformat() if dist.created_at else None,
                    'updated_at': dist.updated_at.isoformat() if dist.updated_at else None,
                    'title': dist.assessment.title if dist.assessment else None,
                    'description': dist.assessment.description if dist.assessment else None
                }
                data.append(item)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取评估分发数据失败: {str(e)}")
            return []

class AssessmentResponseHandler(DataSourceHandler):
    """评估回复处理器 - 原始回答数据"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取评估回复数据（原始回答）"""
        try:
            from ..models.assessment import AssessmentResponse, Assessment
            
            # 查询评估回复记录
            query = select(AssessmentResponse).join(Assessment).where(
                AssessmentResponse.custom_id == user_id
            )
            
            # 应用过滤条件
            if filters:
                if 'start_date' in filters:
                    query = query.where(AssessmentResponse.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(AssessmentResponse.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                responses = result.scalars().all()
            else:
                result = session.execute(query)
                responses = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for response in responses:
                item = {
                    'id': response.id,
                    'type': 'assessment_response',
                    'assessment_id': response.assessment_id,
                    'custom_id': response.custom_id,
                    'answers': response.answers,
                    'score': response.score,
                    'dimension_scores': response.dimension_scores,
                    'result': response.result,
                    'notes': response.notes,
                    'created_at': response.created_at.isoformat() if response.created_at else None,
                    'updated_at': response.updated_at.isoformat() if response.updated_at else None,
                    'title': response.assessment.title if response.assessment else None,
                    'description': response.assessment.description if response.assessment else None
                }
                data.append(item)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取评估回复数据失败: {str(e)}")
            return []

class AssessmentResultHandler(DataSourceHandler):
    """评估结果处理器 - 分析报告数据"""
    
    async def fetch_data(
        self,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取评估结果数据（分析报告）"""
        try:
            from ..models.result import AssessmentResult
            
            # 直接查询评估结果表
            query = select(AssessmentResult).where(AssessmentResult.custom_id == user_id)
            
            # 应用过滤条件
            if filters:
                if 'status' in filters:
                    query = query.where(AssessmentResult.status == filters['status'])
                if 'start_date' in filters:
                    query = query.where(AssessmentResult.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(AssessmentResult.created_at <= filters['end_date'])
            
            # 应用分页
            if pagination:
                if 'limit' in pagination:
                    query = query.limit(pagination['limit'])
                if 'offset' in pagination:
                    query = query.offset(pagination['offset'])
            
            # 执行查询
            if isinstance(session, AsyncSession):
                result = await session.execute(query)
                results = result.scalars().all()
            else:
                result = session.execute(query)
                results = result.scalars().all()
            
            # 转换为字典格式
            data = []
            for result_record in results:
                item = {
                    'id': result_record.id,
                    'type': 'assessment_result',
                    'custom_id': result_record.custom_id,
                    'assessment_id': result_record.assessment_id,
                    'template_id': result_record.template_id,
                    'total_score': result_record.total_score,
                    'max_score': result_record.max_score,
                    'percentage': result_record.percentage,
                    'result_level': result_record.result_level,
                    'result_category': result_record.result_category,
                    'interpretation': result_record.interpretation,
                    'recommendations': result_record.recommendations,
                    'dimension_scores': result_record.dimension_scores,
                    'raw_answers': result_record.raw_answers,
                    'report_generated': result_record.report_generated,
                    'report_content': result_record.report_content,
                    'status': result_record.status,
                    'calculated_at': result_record.calculated_at.isoformat() if result_record.calculated_at else None,
                    'created_at': result_record.created_at.isoformat() if result_record.created_at else None,
                    'updated_at': result_record.updated_at.isoformat() if result_record.updated_at else None,
                    '_source_type': 'assessment',
                    'type': 'assessment',
                    'date': result_record.calculated_at.isoformat() if result_record.calculated_at else result_record.created_at.isoformat() if result_record.created_at else None
                }
                
                data.append(item)
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取评估量表数据失败: {str(e)}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []

class AggregatedApiService(BaseApiService):
    """聚合API服务"""
    
    def __init__(self):
        super().__init__(ServiceType.USER)
        self.cache_manager = CacheManager()
        self.data_handlers = {}
        self._initialize_handlers()
    
    def _initialize_handlers(self):
        """初始化数据处理器"""
        # 健康记录处理器
        health_config = DataSourceConfig(
            source_type=DataSourceType.HEALTH_RECORD,
            priority=1,
            fields_mapping={
                'record_date': 'date'
                # 保留record_type字段，不映射为type，避免与_source_type混淆
            }
        )
        self.data_handlers[DataSourceType.HEALTH_RECORD] = HealthRecordHandler(health_config)
        
        # 问卷分发处理器（未完成的问卷）
        questionnaire_dist_config = DataSourceConfig(
            source_type=DataSourceType.QUESTIONNAIRE_DISTRIBUTION,
            priority=2,
            fields_mapping={
                'created_at': 'date',
                'questionnaire_id': 'template_id'
            }
        )
        self.data_handlers[DataSourceType.QUESTIONNAIRE_DISTRIBUTION] = QuestionnaireDistributionHandler(questionnaire_dist_config)
        
        # 问卷回复处理器（原始回答）
        questionnaire_resp_config = DataSourceConfig(
            source_type=DataSourceType.QUESTIONNAIRE_RESPONSE,
            priority=3,
            fields_mapping={
                'created_at': 'date',
                'questionnaire_id': 'template_id'
            }
        )
        self.data_handlers[DataSourceType.QUESTIONNAIRE_RESPONSE] = QuestionnaireResponseHandler(questionnaire_resp_config)
        
        # 问卷结果处理器（分析报告）
        questionnaire_result_config = DataSourceConfig(
            source_type=DataSourceType.QUESTIONNAIRE_RESULT,
            priority=4,
            fields_mapping={
                'calculated_at': 'date',
                'questionnaire_id': 'template_id'
            }
        )
        self.data_handlers[DataSourceType.QUESTIONNAIRE_RESULT] = QuestionnaireResultHandler(questionnaire_result_config)
        
        # 评估分发处理器（未完成的评估）
        assessment_dist_config = DataSourceConfig(
            source_type=DataSourceType.ASSESSMENT_DISTRIBUTION,
            priority=5,
            fields_mapping={
                'created_at': 'date',
                'assessment_id': 'template_id'
            }
        )
        self.data_handlers[DataSourceType.ASSESSMENT_DISTRIBUTION] = AssessmentDistributionHandler(assessment_dist_config)
        
        # 评估回复处理器（原始回答）
        assessment_resp_config = DataSourceConfig(
            source_type=DataSourceType.ASSESSMENT_RESPONSE,
            priority=6,
            fields_mapping={
                'created_at': 'date',
                'assessment_id': 'template_id'
            }
        )
        self.data_handlers[DataSourceType.ASSESSMENT_RESPONSE] = AssessmentResponseHandler(assessment_resp_config)
        
        # 评估结果处理器（分析报告）
        assessment_result_config = DataSourceConfig(
            source_type=DataSourceType.ASSESSMENT_RESULT,
            priority=7,
            fields_mapping={
                'calculated_at': 'date',
                'assessment_id': 'template_id'
            }
        )
        self.data_handlers[DataSourceType.ASSESSMENT_RESULT] = AssessmentResultHandler(assessment_result_config)
    
    @performance_monitor("aggregate_user_data")
    async def aggregate_user_data(
        self,
        user_id: str,
        session: Union[Session, AsyncSession],
        context: ServiceContext,
        config: AggregationConfig = None,
        data_sources: List[DataSourceType] = None,
        filters: Dict[str, Any] = None,
        pagination: Dict[str, Any] = None
    ) -> AggregationResult:
        """聚合用户数据"""
        start_time = datetime.now()
        
        if config is None:
            config = AggregationConfig()
        
        if data_sources is None:
            data_sources = list(self.data_handlers.keys())
        
        # 检查缓存
        cache_key = self._generate_cache_key(user_id, config, data_sources, filters, pagination)
        if config.enable_caching:
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result:
                cached_result.cache_hit = True
                return cached_result
        
        # 聚合数据
        all_data = []
        source_counts = {}
        metadata = {
            'user_id': user_id,
            'aggregation_strategy': config.strategy,
            'response_format': config.response_format,
            'data_sources': [ds.value for ds in data_sources],
            'timestamp': datetime.now().isoformat()
        }
        
        # 并发获取各数据源数据
        tasks = []
        for source_type in data_sources:
            if source_type in self.data_handlers:
                handler = self.data_handlers[source_type]
                task = self._fetch_source_data(
                    handler, session, user_id, filters, 
                    {'limit': config.max_records_per_source}
                )
                tasks.append((source_type, task))
        
        # 等待所有任务完成
        for source_type, task in tasks:
            try:
                source_data = await task
                source_counts[source_type.value] = len(source_data)
                all_data.extend(source_data)
            except Exception as e:
                logger.error(f"获取{source_type}数据失败: {str(e)}")
                source_counts[source_type.value] = 0
        
        # 应用聚合策略
        aggregated_data = self._apply_aggregation_strategy(all_data, config)
        
        # 限制总记录数
        if len(aggregated_data) > config.total_max_records:
            aggregated_data = aggregated_data[:config.total_max_records]
        
        # 应用响应格式
        formatted_data = self._apply_response_format(aggregated_data, config)
        
        # 计算统计信息
        statistics = self._calculate_statistics(aggregated_data, source_counts) if config.include_statistics else {}
        
        # 创建结果
        execution_time = (datetime.now() - start_time).total_seconds()
        result = AggregationResult(
            data=formatted_data,
            metadata=metadata if config.include_metadata else {},
            statistics=statistics,
            total_count=len(formatted_data),
            source_counts=source_counts,
            execution_time=execution_time,
            cache_hit=False
        )
        
        # 缓存结果
        if config.enable_caching:
            await self.cache_manager.set(cache_key, result, ttl=config.cache_ttl)
        
        # 记录指标
        metrics.increment_counter("aggregated_api_requests")
        metrics.record_histogram("aggregated_api_duration", execution_time)
        
        return result
    
    async def _fetch_source_data(
        self,
        handler: DataSourceHandler,
        session: Union[Session, AsyncSession],
        user_id: str,
        filters: Dict[str, Any],
        pagination: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取数据源数据"""
        try:
            # 合并过滤条件
            combined_filters = {**(filters or {}), **handler.config.filters}
            
            # 获取原始数据
            raw_data = await handler.fetch_data(session, user_id, combined_filters, pagination)
            
            # 转换数据格式
            transformed_data = handler.transform_data(raw_data)
            
            # 应用额外过滤
            filtered_data = handler.apply_filters(transformed_data, combined_filters)
            
            return filtered_data
            
        except Exception as e:
            logger.error(f"获取{handler.config.source_type}数据失败: {str(e)}")
            return []
    
    def _apply_aggregation_strategy(
        self,
        data: List[Dict[str, Any]],
        config: AggregationConfig
    ) -> List[Dict[str, Any]]:
        """应用聚合策略"""
        if config.strategy == AggregationStrategy.MERGE_ALL:
            return data
        
        elif config.strategy == AggregationStrategy.GROUP_BY_TYPE:
            grouped_data = {}
            for item in data:
                source_type = item.get('_source_type', 'unknown')
                if source_type not in grouped_data:
                    grouped_data[source_type] = []
                grouped_data[source_type].append(item)
            return [{'type': k, 'items': v} for k, v in grouped_data.items()]
        
        elif config.strategy == AggregationStrategy.TIMELINE:
            return sorted(data, key=lambda x: x.get('date') or x.get('created_at') or '')
        
        elif config.strategy == AggregationStrategy.PRIORITY:
            return sorted(data, key=lambda x: x.get('_source_priority', 999))
        
        elif config.strategy == AggregationStrategy.LATEST_FIRST:
            return sorted(
                data, 
                key=lambda x: x.get('date') or x.get('created_at') or '', 
                reverse=True
            )
        
        return data
    
    def _apply_response_format(
        self,
        data: List[Dict[str, Any]],
        config: AggregationConfig
    ) -> List[Dict[str, Any]]:
        """应用响应格式"""
        if config.response_format == ResponseFormat.MOBILE:
            # 移动端格式：简化字段
            return [
                {
                    'id': item.get('id'),
                    'type': item.get('_source_type'),
                    'title': item.get('title') or item.get('name'),
                    'date': item.get('date') or item.get('created_at'),
                    'status': item.get('status'),
                    'summary': item.get('description') or item.get('interpretation')
                }
                for item in data
            ]
        
        elif config.response_format == ResponseFormat.SUMMARY:
            # 摘要格式：只包含关键信息
            return [
                {
                    'id': item.get('id'),
                    'type': item.get('_source_type'),
                    'date': item.get('date') or item.get('created_at'),
                    'status': item.get('status')
                }
                for item in data
            ]
        
        elif config.response_format == ResponseFormat.EXPORT:
            # 导出格式：包含所有字段
            return data
        
        else:  # STANDARD or FRONTEND
            # 标准格式：移除内部字段
            cleaned_data = []
            for item in data:
                cleaned_item = {k: v for k, v in item.items() if not k.startswith('_')}
                cleaned_data.append(cleaned_item)
            return cleaned_data
    
    def _calculate_statistics(
        self,
        data: List[Dict[str, Any]],
        source_counts: Dict[str, int]
    ) -> Dict[str, Any]:
        """计算统计信息"""
        total_records = len(data)
        
        # 按状态统计
        status_counts = {}
        for item in data:
            status = item.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 按类型统计
        type_counts = {}
        for item in data:
            item_type = item.get('_source_type', 'unknown')
            type_counts[item_type] = type_counts.get(item_type, 0) + 1
        
        # 时间范围统计
        dates = []
        for item in data:
            date_str = item.get('date') or item.get('created_at')
            if date_str:
                try:
                    if isinstance(date_str, str):
                        date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    else:
                        date_obj = date_str
                    dates.append(date_obj)
                except:
                    continue
        
        date_range = {}
        if dates:
            date_range = {
                'earliest': min(dates).isoformat(),
                'latest': max(dates).isoformat(),
                'span_days': (max(dates) - min(dates)).days
            }
        
        return {
            'total_records': total_records,
            'source_counts': source_counts,
            'status_counts': status_counts,
            'type_counts': type_counts,
            'date_range': date_range
        }
    
    def _generate_cache_key(
        self,
        user_id: str,
        config: AggregationConfig,
        data_sources: List[DataSourceType],
        filters: Dict[str, Any],
        pagination: Dict[str, Any]
    ) -> str:
        """生成缓存键"""
        import hashlib
        
        key_data = {
            'user_id': user_id,
            'strategy': config.strategy,
            'format': config.response_format,
            'sources': [ds.value for ds in data_sources],
            'filters': filters or {},
            'pagination': pagination or {}
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return f"aggregated_api:{hashlib.md5(key_str.encode()).hexdigest()}"
    
    async def invalidate_user_cache(self, user_id: str):
        """清除用户相关缓存"""
        pattern = f"aggregated_api:*{user_id}*"
        await self.cache_manager.delete_pattern(pattern)
    
    async def get_data_source_status(self) -> Dict[str, Any]:
        """获取数据源状态"""
        status = {}
        
        for source_type, handler in self.data_handlers.items():
            status[source_type.value] = {
                'enabled': handler.config.enabled,
                'priority': handler.config.priority,
                'cache_ttl': handler.config.cache_ttl,
                'timeout': handler.config.timeout,
                'retry_count': handler.config.retry_count
            }
        
        return status
    
    async def update_data_source_config(
        self,
        source_type: DataSourceType,
        config_updates: Dict[str, Any]
    ):
        """更新数据源配置"""
        if source_type in self.data_handlers:
            handler = self.data_handlers[source_type]
            
            for key, value in config_updates.items():
                if hasattr(handler.config, key):
                    setattr(handler.config, key, value)
            
            logger.info(f"更新{source_type}数据源配置: {config_updates}")

# 全局实例
aggregated_api_service = AggregatedApiService()

# 便捷函数
async def get_user_aggregated_data(
    user_id: str,
    session: Union[Session, AsyncSession],
    context: ServiceContext,
    strategy: AggregationStrategy = AggregationStrategy.LATEST_FIRST,
    response_format: ResponseFormat = ResponseFormat.STANDARD,
    data_sources: List[DataSourceType] = None,
    filters: Dict[str, Any] = None,
    include_statistics: bool = False
) -> AggregationResult:
    """获取用户聚合数据"""
    config = AggregationConfig(
        strategy=strategy,
        response_format=response_format,
        include_statistics=include_statistics
    )
    
    return await aggregated_api_service.aggregate_user_data(
        user_id=user_id,
        session=session,
        context=context,
        config=config,
        data_sources=data_sources,
        filters=filters
    )

async def get_mobile_user_data(
    user_id: str,
    session: Union[Session, AsyncSession],
    context: ServiceContext,
    limit: int = 50
) -> AggregationResult:
    """获取移动端用户数据"""
    config = AggregationConfig(
        strategy=AggregationStrategy.LATEST_FIRST,
        response_format=ResponseFormat.MOBILE,
        total_max_records=limit,
        include_statistics=False
    )
    
    return await aggregated_api_service.aggregate_user_data(
        user_id=user_id,
        session=session,
        context=context,
        config=config
    )

async def get_frontend_user_data(
    user_id: str,
    session: Union[Session, AsyncSession],
    context: ServiceContext,
    data_sources: List[DataSourceType] = None,
    filters: Dict[str, Any] = None,
    include_statistics: bool = True
) -> AggregationResult:
    """获取前端用户数据"""
    config = AggregationConfig(
        strategy=AggregationStrategy.GROUP_BY_TYPE,
        response_format=ResponseFormat.FRONTEND,
        include_statistics=include_statistics
    )
    
    return await aggregated_api_service.aggregate_user_data(
        user_id=user_id,
        session=session,
        context=context,
        config=config,
        data_sources=data_sources,
        filters=filters
    )

# 初始化函数
async def initialize_aggregated_api_service():
    """初始化聚合API服务"""
    logger.info("初始化聚合API服务")
    
    # 预热缓存
    await aggregated_api_service.cache_manager.warm_up()
    
    logger.info("聚合API服务初始化完成")

async def shutdown_aggregated_api_service():
    """关闭聚合API服务"""
    logger.info("关闭聚合API服务")
    
    # 清理缓存
    await aggregated_api_service.cache_manager.clear_all()
    
    logger.info("聚合API服务已关闭")