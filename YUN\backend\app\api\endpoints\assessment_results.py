"""评估量表结果API"""
from typing import Any, Dict, List, Optional
from datetime import datetime
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.db.base_session import get_db
from app.models.user import User
from app.models.result import AssessmentResult, ReportTemplate
from app.models.assessment import Assessment, AssessmentTemplate
from app.api import deps
from app.core.auth import get_current_active_user_custom

router = APIRouter()

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_assessment_results(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    assessment_type: Optional[str] = Query(None, description="评估类型"),
    result_level: Optional[str] = Query(None, description="结果等级"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的评估量表结果列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的评估结果"
        )

    # 构建查询，加入Assessment和AssessmentTemplate的join以获取量表名称
    query = db.query(
        AssessmentResult,
        Assessment.name.label('assessment_name'),
        AssessmentTemplate.name.label('template_name')
    ).join(
        Assessment, AssessmentResult.assessment_id == Assessment.id
    ).outerjoin(
        AssessmentTemplate, Assessment.template_id == AssessmentTemplate.id
    ).filter(AssessmentResult.custom_id == custom_id)
    
    if assessment_type:
        query = query.filter(Assessment.assessment_type == assessment_type)
    if result_level:
        query = query.filter(AssessmentResult.result_level == result_level)
    if start_date:
        query = query.filter(AssessmentResult.calculated_at >= start_date)
    if end_date:
        query = query.filter(AssessmentResult.calculated_at <= end_date)
    
    # 分页
    results = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "success": True,
        "data": [
            {
                "id": result.AssessmentResult.id,
                "assessment_id": result.AssessmentResult.assessment_id,
                "custom_id": result.AssessmentResult.custom_id,
                "template_id": result.AssessmentResult.template_id,
                "total_score": result.AssessmentResult.total_score,
                "max_score": result.AssessmentResult.max_score,
                "percentage": result.AssessmentResult.percentage,
                "result_level": result.AssessmentResult.result_level,
                "result_category": result.AssessmentResult.result_category,
                "interpretation": result.AssessmentResult.interpretation,
                "recommendations": result.AssessmentResult.recommendations,
                "dimension_scores": result.AssessmentResult.dimension_scores,
                "report_generated": result.AssessmentResult.report_generated,
                "status": result.AssessmentResult.status,
                "calculated_at": result.AssessmentResult.calculated_at,
                "created_at": result.AssessmentResult.created_at,
                "updated_at": result.AssessmentResult.updated_at,
                "assessment_name": result.assessment_name or result.template_name or '未命名量表',
                "title": result.assessment_name or result.template_name or '未命名量表'
            }
            for result in results
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_assessment_result(
    *,
    db: Session = Depends(get_db),
    result_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    创建评估量表结果
    """
    # 验证评估存在
    assessment_id = result_data.get("assessment_id")
    assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到评估ID: {assessment_id}"
        )

    # 验证用户存在
    custom_id = result_data.get("custom_id")
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限创建评估结果"
        )

    # 创建评估结果
    result = AssessmentResult(
        assessment_id=assessment_id,
        custom_id=custom_id,
        template_id=result_data.get("template_id"),
        total_score=result_data.get("total_score"),
        max_score=result_data.get("max_score"),
        percentage=result_data.get("percentage"),
        result_level=result_data.get("result_level"),
        result_category=result_data.get("result_category"),
        interpretation=result_data.get("interpretation"),
        recommendations=result_data.get("recommendations"),
        dimension_scores=result_data.get("dimension_scores"),
        calculation_details=result_data.get("calculation_details"),
        raw_answers=result_data.get("raw_answers"),
        report_generated=result_data.get("report_generated", False),
        report_content=result_data.get("report_content"),
        report_format=result_data.get("report_format", "html"),
        report_template=result_data.get("report_template"),
        status=result_data.get("status", "calculated")
    )
    
    db.add(result)
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "data": {
            "id": result.id,
            "assessment_id": result.assessment_id,
            "custom_id": result.custom_id,
            "template_id": result.template_id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "result_category": result.result_category,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "dimension_scores": result.dimension_scores,
            "report_generated": result.report_generated,
            "status": result.status,
            "calculated_at": result.calculated_at,
            "created_at": result.created_at,
            "updated_at": result.updated_at
        }
    }


@router.get("/{result_id}", response_model=Dict[str, Any])
def get_assessment_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取单个评估量表结果详情
    """
    result_query = db.query(
        AssessmentResult,
        Assessment.name.label('assessment_name'),
        AssessmentTemplate.name.label('template_name')
    ).join(
        Assessment, AssessmentResult.assessment_id == Assessment.id
    ).outerjoin(
        AssessmentTemplate, Assessment.template_id == AssessmentTemplate.id
    ).filter(AssessmentResult.id == result_id).first()
    
    if not result_query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到评估结果ID: {result_id}"
        )
    
    result = result_query.AssessmentResult
    assessment_name = result_query.assessment_name or result_query.template_name or '未命名量表'

    # 权限校验
    if current_user.custom_id != result.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此评估结果"
        )

    return {
        "success": True,
        "data": {
            "id": result.id,
            "assessment_id": result.assessment_id,
            "custom_id": result.custom_id,
            "template_id": result.template_id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "result_category": result.result_category,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "dimension_scores": result.dimension_scores,
            "calculation_details": result.calculation_details,
            "raw_answers": result.raw_answers,
            "report_generated": result.report_generated,
            "report_content": result.report_content,
            "report_format": result.report_format,
            "report_template": result.report_template,
            "status": result.status,
            "calculated_at": result.calculated_at,
            "created_at": result.created_at,
            "updated_at": result.updated_at,
            "assessment_name": assessment_name,
            "title": assessment_name
        }
    }


@router.put("/{result_id}", response_model=Dict[str, Any])
def update_assessment_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    result_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新评估量表结果
    """
    result = db.query(AssessmentResult).filter(AssessmentResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到评估结果ID: {result_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改评估结果"
        )

    # 更新字段
    for field, value in result_data.items():
        if hasattr(result, field) and field not in ["id", "assessment_id", "custom_id", "created_at"]:
            setattr(result, field, value)
    
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "data": {
            "id": result.id,
            "assessment_id": result.assessment_id,
            "custom_id": result.custom_id,
            "template_id": result.template_id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "result_category": result.result_category,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "dimension_scores": result.dimension_scores,
            "report_generated": result.report_generated,
            "status": result.status,
            "calculated_at": result.calculated_at,
            "created_at": result.created_at,
            "updated_at": result.updated_at
        }
    }


@router.delete("/{result_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_assessment_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    删除评估量表结果
    """
    result = db.query(AssessmentResult).filter(AssessmentResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到评估结果ID: {result_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除评估结果"
        )

    db.delete(result)
    db.commit()


@router.post("/{result_id}/generate-report", response_model=Dict[str, Any])
def generate_assessment_report(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    template_name: Optional[str] = Query(None, description="报告模板名称"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    生成评估量表报告
    """
    result = db.query(AssessmentResult).filter(AssessmentResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到评估结果ID: {result_id}"
        )

    # 权限校验
    if current_user.custom_id != result.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限生成此评估报告"
        )

    # 查找报告模板
    template_query = db.query(ReportTemplate).filter(
        ReportTemplate.template_type == "assessment",
        ReportTemplate.is_active == True
    )
    
    if template_name:
        template = template_query.filter(ReportTemplate.name == template_name).first()
    else:
        template = template_query.filter(ReportTemplate.is_default == True).first()
    
    if not template:
        # 使用默认报告内容
        report_content = f"""
        <h2>评估量表结果报告</h2>
        <p><strong>总分：</strong>{result.total_score}/{result.max_score}</p>
        <p><strong>得分百分比：</strong>{result.percentage}%</p>
        <p><strong>结果等级：</strong>{result.result_level}</p>
        <p><strong>结果分类：</strong>{result.result_category}</p>
        <p><strong>结果解释：</strong>{result.interpretation}</p>
        <p><strong>建议：</strong>{result.recommendations}</p>
        <p><strong>生成时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        """
    else:
        # 使用模板生成报告（这里简化处理，实际应该有模板引擎）
        report_content = template.template_content.format(
            total_score=result.total_score,
            max_score=result.max_score,
            percentage=result.percentage,
            result_level=result.result_level,
            result_category=result.result_category,
            interpretation=result.interpretation,
            recommendations=result.recommendations,
            generated_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    # 更新结果记录
    result.report_content = report_content
    result.report_generated = True
    result.report_template = template.name if template else "default"
    
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "data": {
            "result_id": result.id,
            "report_content": result.report_content,
            "report_generated": result.report_generated,
            "report_template": result.report_template,
            "generated_at": result.updated_at
        }
    }


@router.get("/export/{result_id}")
def export_assessment_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> StreamingResponse:
    """
    导出评估结果为PDF报告
    """
    # 获取结果记录
    result = db.query(AssessmentResult).filter(AssessmentResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到评估结果"
        )
    
    # 权限校验
    user = db.query(User).filter(User.custom_id == result.custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到用户"
        )
    
    if current_user.custom_id != user.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限导出此评估结果"
        )
    
    # 获取评估信息
    assessment = db.query(Assessment).filter(Assessment.id == result.assessment_id).first()
    
    # 创建PDF
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # 标题
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # 居中
    )
    story.append(Paragraph(f"评估结果报告 - {assessment.name if assessment else '未知评估'}", title_style))
    story.append(Spacer(1, 12))
    
    # 基本信息表格
    basic_info = [
        ['用户ID', user.custom_id],
        ['评估名称', assessment.name if assessment else '未知'],
        ['完成时间', result.created_at.strftime('%Y-%m-%d %H:%M:%S')],
        ['总分', f"{result.total_score}/{result.max_score}"],
        ['百分比', f"{result.percentage:.1f}%"],
        ['结果等级', result.result_level or '无'],
        ['结果类别', result.result_category or '无']
    ]
    
    basic_table = Table(basic_info, colWidths=[2*inch, 4*inch])
    basic_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.grey),
        ('TEXTCOLOR', (0, 0), (0, -1), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('BACKGROUND', (1, 0), (1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(basic_table)
    story.append(Spacer(1, 20))
    
    # 结果解释
    if result.interpretation:
        story.append(Paragraph("结果解释", styles['Heading2']))
        story.append(Paragraph(result.interpretation, styles['Normal']))
        story.append(Spacer(1, 12))
    
    # 建议
    if result.recommendations:
        story.append(Paragraph("建议", styles['Heading2']))
        story.append(Paragraph(result.recommendations, styles['Normal']))
        story.append(Spacer(1, 12))
    
    # 维度得分
    if result.dimension_scores:
        story.append(Paragraph("维度得分", styles['Heading2']))
        dimension_data = [['维度', '得分']]
        for dimension, score in result.dimension_scores.items():
            dimension_data.append([dimension, str(score)])
        
        dimension_table = Table(dimension_data, colWidths=[3*inch, 2*inch])
        dimension_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(dimension_table)
    
    # 生成PDF
    doc.build(story)
    buffer.seek(0)
    
    # 返回文件流
    filename = f"assessment_result_{result_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    return StreamingResponse(
        io.BytesIO(buffer.read()),
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/trend/{custom_id}/{assessment_id}")
def get_assessment_trend(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    assessment_id: int = Path(..., description="评估ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Dict[str, Any]:
    """
    获取用户特定评估的趋势数据
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )
    
    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看此用户的趋势数据"
        )
    
    # 获取趋势数据
    results = db.query(AssessmentResult).filter(
        AssessmentResult.custom_id == custom_id,
        AssessmentResult.assessment_id == assessment_id
    ).order_by(desc(AssessmentResult.created_at)).all()
    
    if not results:
        return {
            "total_count": 0,
            "latest_date": None,
            "details": [],
            "trend_data": []
        }
    
    # 处理趋势数据
    trend_details = []
    trend_data = []
    
    for result in results:
        detail = {
            "date": result.created_at.strftime('%Y-%m-%d'),
            "score": result.total_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "notes": result.interpretation[:100] + "..." if result.interpretation and len(result.interpretation) > 100 else result.interpretation
        }
        trend_details.append(detail)
        
        trend_data.append({
            "date": result.created_at.strftime('%Y-%m-%d'),
            "value": result.percentage
        })
    
    return {
        "total_count": len(results),
        "latest_date": results[0].created_at.strftime('%Y-%m-%d') if results else None,
        "details": trend_details,
        "trend_data": trend_data
    }

@router.get("/list", response_model=Dict[str, Any])
def get_assessment_results_list(
    *,
    db: Session = Depends(get_db),
    assessment_type: Optional[str] = Query(None, description="评估类型"),
    result_level: Optional[str] = Query(None, description="结果等级"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取评估量表结果列表（聚合API）
    """
    # 构建查询
    query = db.query(AssessmentResult).join(Assessment).join(User)
    
    # 添加过滤条件
    if assessment_type:
        query = query.filter(Assessment.assessment_type == assessment_type)
    if result_level:
        query = query.filter(AssessmentResult.result_level == result_level)
    if start_date:
        query = query.filter(AssessmentResult.created_at >= start_date)
    if end_date:
        query = query.filter(AssessmentResult.created_at <= end_date)
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    results = query.order_by(desc(AssessmentResult.created_at)).offset(skip).limit(limit).all()
    
    # 格式化结果
    formatted_results = []
    for result in results:
        formatted_results.append({
            "id": result.id,
            "user_id": result.assessment.user.custom_id,
            "user_name": result.assessment.user.username,
            "assessment_type": result.assessment.assessment_type,
            "template_name": result.assessment.template.name if result.assessment.template else "未知模板",
            "total_score": result.total_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "interpretation": result.interpretation,
            "created_at": result.created_at.isoformat(),
            "completed_at": result.assessment.completed_at.isoformat() if result.assessment.completed_at else None
        })
    
    return {
        "code": 200,
        "message": "获取成功",
        "data": {
            "total": total,
            "results": formatted_results,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total
            }
        }
    }
