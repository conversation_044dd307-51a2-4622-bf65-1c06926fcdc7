#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迁移工具
帮助用户从原版本逐步迁移到优化版本
"""

import os
import sys
import json
import shutil
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MigrationTool:
    """迁移工具类"""
    
    def __init__(self):
        self.project_root = project_root
        self.backup_dir = self.project_root / 'backup' / datetime.now().strftime('%Y%m%d_%H%M%S')
        self.migration_log = []
        self.errors = []
        
        # 创建备份目录
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"备份目录创建: {self.backup_dir}")
    
    def log_action(self, action: str, status: str = "SUCCESS", details: str = ""):
        """记录迁移操作"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.migration_log.append(log_entry)
        
        if status == "SUCCESS":
            logger.info(f"✓ {action}: {details}")
        elif status == "WARNING":
            logger.warning(f"⚠ {action}: {details}")
        else:
            logger.error(f"✗ {action}: {details}")
            self.errors.append(log_entry)
    
    def backup_original_files(self) -> bool:
        """备份原始文件"""
        try:
            logger.info("开始备份原始文件...")
            
            # 需要备份的文件列表
            files_to_backup = [
                'main.py',
                'theme.py',
                'api/api_config.py',
                'api/api_client.py',
                'screens/homepage_screen.py',
                'config.json',
                'data/',  # 数据目录
                'logs/'   # 日志目录
            ]
            
            backed_up_count = 0
            for file_path in files_to_backup:
                source_path = self.project_root / file_path
                if source_path.exists():
                    if source_path.is_file():
                        # 备份文件
                        backup_path = self.backup_dir / file_path
                        backup_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(source_path, backup_path)
                        backed_up_count += 1
                        self.log_action("备份文件", "SUCCESS", f"{file_path}")
                    elif source_path.is_dir():
                        # 备份目录
                        backup_path = self.backup_dir / file_path
                        shutil.copytree(source_path, backup_path, dirs_exist_ok=True)
                        backed_up_count += 1
                        self.log_action("备份目录", "SUCCESS", f"{file_path}")
                else:
                    self.log_action("备份文件", "WARNING", f"{file_path} 不存在")
            
            self.log_action("备份完成", "SUCCESS", f"共备份 {backed_up_count} 个文件/目录")
            return True
            
        except Exception as e:
            self.log_action("备份失败", "ERROR", str(e))
            return False
    
    def migrate_configuration(self) -> bool:
        """迁移配置文件"""
        try:
            logger.info("开始迁移配置文件...")
            
            # 原配置文件路径
            old_config_files = [
                'config.json',
                'api/config.json',
                'settings.json'
            ]
            
            # 新配置文件路径
            new_config_path = self.project_root / 'api' / 'api_config.json'
            
            # 默认配置
            default_config = {
                "base_url": "http://localhost:8000",
                "backup_url": "http://127.0.0.1:8000",
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 1.0,
                "api_version": "v1",
                "endpoints": {
                    "user_login": "/api/v1/auth/login",
                    "user_logout": "/api/v1/auth/logout",
                    "user_register": "/api/v1/auth/register",
                    "user_profile": "/api/v1/user/profile",
                    "health_records": "/api/v1/health/records",
                    "health_data": "/api/v1/health/data",
                    "upload_file": "/api/v1/files/upload",
                    "download_file": "/api/v1/files/download"
                }
            }
            
            # 尝试从旧配置文件读取设置
            merged_config = default_config.copy()
            
            for old_config_file in old_config_files:
                old_config_path = self.project_root / old_config_file
                if old_config_path.exists():
                    try:
                        with open(old_config_path, 'r', encoding='utf-8') as f:
                            old_config = json.load(f)
                        
                        # 合并配置
                        if 'server_url' in old_config:
                            merged_config['base_url'] = old_config['server_url']
                        if 'api_timeout' in old_config:
                            merged_config['timeout'] = old_config['api_timeout']
                        if 'retry_count' in old_config:
                            merged_config['max_retries'] = old_config['retry_count']
                        
                        self.log_action("读取旧配置", "SUCCESS", f"{old_config_file}")
                        
                    except Exception as e:
                        self.log_action("读取旧配置", "WARNING", f"{old_config_file}: {e}")
            
            # 保存新配置文件
            new_config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(new_config_path, 'w', encoding='utf-8') as f:
                json.dump(merged_config, f, indent=2, ensure_ascii=False)
            
            self.log_action("配置迁移", "SUCCESS", f"配置已保存到 {new_config_path}")
            return True
            
        except Exception as e:
            self.log_action("配置迁移", "ERROR", str(e))
            return False
    
    def migrate_database(self) -> bool:
        """迁移数据库"""
        try:
            logger.info("开始迁移数据库...")
            
            # 查找旧数据库文件
            old_db_files = [
                'data/health.db',
                'health.db',
                'app.db',
                'data.db'
            ]
            
            migrated_count = 0
            
            for old_db_file in old_db_files:
                old_db_path = self.project_root / old_db_file
                if old_db_path.exists():
                    try:
                        # 创建新数据库目录
                        new_data_dir = self.project_root / 'data'
                        new_data_dir.mkdir(exist_ok=True)
                        
                        # 复制数据库文件
                        new_db_path = new_data_dir / 'health_optimized.db'
                        shutil.copy2(old_db_path, new_db_path)
                        
                        # 验证数据库
                        if self._validate_database(new_db_path):
                            migrated_count += 1
                            self.log_action("数据库迁移", "SUCCESS", f"{old_db_file} -> {new_db_path}")
                        else:
                            self.log_action("数据库验证", "WARNING", f"{new_db_path} 验证失败")
                        
                    except Exception as e:
                        self.log_action("数据库迁移", "ERROR", f"{old_db_file}: {e}")
            
            if migrated_count == 0:
                self.log_action("数据库迁移", "WARNING", "未找到可迁移的数据库文件")
            
            return True
            
        except Exception as e:
            self.log_action("数据库迁移", "ERROR", str(e))
            return False
    
    def _validate_database(self, db_path: Path) -> bool:
        """验证数据库文件"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查数据库是否可读
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            conn.close()
            
            return len(tables) > 0
            
        except Exception:
            return False
    
    def migrate_user_data(self) -> bool:
        """迁移用户数据"""
        try:
            logger.info("开始迁移用户数据...")
            
            # 用户数据目录
            old_data_dirs = [
                'user_data/',
                'data/users/',
                'profiles/',
                'cache/'
            ]
            
            new_data_dir = self.project_root / 'data' / 'users'
            new_data_dir.mkdir(parents=True, exist_ok=True)
            
            migrated_count = 0
            
            for old_dir in old_data_dirs:
                old_path = self.project_root / old_dir
                if old_path.exists() and old_path.is_dir():
                    try:
                        # 复制用户数据
                        for item in old_path.iterdir():
                            if item.is_file():
                                new_file_path = new_data_dir / item.name
                                shutil.copy2(item, new_file_path)
                                migrated_count += 1
                        
                        self.log_action("用户数据迁移", "SUCCESS", f"{old_dir}")
                        
                    except Exception as e:
                        self.log_action("用户数据迁移", "ERROR", f"{old_dir}: {e}")
            
            self.log_action("用户数据迁移完成", "SUCCESS", f"共迁移 {migrated_count} 个文件")
            return True
            
        except Exception as e:
            self.log_action("用户数据迁移", "ERROR", str(e))
            return False
    
    def update_imports(self) -> bool:
        """更新导入语句"""
        try:
            logger.info("开始更新导入语句...")
            
            # 需要更新的文件
            files_to_update = []
            
            # 查找所有Python文件
            for py_file in self.project_root.rglob('*.py'):
                if 'backup' not in str(py_file) and 'optimized' not in py_file.name:
                    files_to_update.append(py_file)
            
            updated_count = 0
            
            for file_path in files_to_update:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # 更新导入语句
                    import_mappings = {
                        'from theme import': 'from theme import',
                        'import theme as theme': 'import theme as theme_optimized as theme',
                        'from api.api_config_optimized import': 'from api.api_config_optimized import',
                        'from api.api_client import': 'from api.api_client import',
                    }
                    
                    for old_import, new_import in import_mappings.items():
                        if old_import in content:
                            content = content.replace(old_import, new_import)
                    
                    # 如果内容有变化，保存文件
                    if content != original_content:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        updated_count += 1
                        self.log_action("更新导入", "SUCCESS", f"{file_path.name}")
                
                except Exception as e:
                    self.log_action("更新导入", "ERROR", f"{file_path.name}: {e}")
            
            self.log_action("导入更新完成", "SUCCESS", f"共更新 {updated_count} 个文件")
            return True
            
        except Exception as e:
            self.log_action("导入更新", "ERROR", str(e))
            return False
    
    def create_migration_script(self) -> bool:
        """创建迁移脚本"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的迁移脚本
用于从原版本迁移到优化版本
生成时间: {timestamp}
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_migration_status():
    """检查迁移状态"""
    print("检查迁移状态...")
    
    # 检查优化文件是否存在
    optimized_files = [
        'main_optimized.py',
        'theme.py',
        'api/api_config_optimized.py',
        'api/api_client_optimized.py'
    ]
    
    missing_files = []
    for file_path in optimized_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("缺少优化文件:", missing_files)
        return False
    
    print("✓ 所有优化文件都存在")
    return True

def run_optimized_version():
    """运行优化版本"""
    print("启动优化版本...")
    
    try:
        import main_optimized
        print("✓ 优化版本启动成功")
        return True
    except Exception as e:
        print(f"✗ 优化版本启动失败: {e}")
        return False

def main():
    """主函数"""
    print("健康管理应用 - 迁移检查工具")
    print("=" * 40)
    
    # 检查迁移状态
    if not check_migration_status():
        print("\n迁移未完成，请运行完整的迁移工具")
        return 1
    
    # 尝试运行优化版本
    if run_optimized_version():
        print("\n🎉 迁移成功！可以使用优化版本了")
        print("\n使用方法:")
        print("  python main_optimized.py  # 运行优化版本")
        print("  python main.py           # 运行原版本（备用）")
        return 0
    else:
        print("\n⚠️ 迁移可能存在问题，请检查日志")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
'''
            
            script_path = self.project_root / 'check_migration.py'
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.log_action("创建迁移脚本", "SUCCESS", f"{script_path}")
            return True
            
        except Exception as e:
            self.log_action("创建迁移脚本", "ERROR", str(e))
            return False
    
    def generate_migration_report(self) -> str:
        """生成迁移报告"""
        report_content = f"""# 迁移报告

**迁移时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**备份目录**: {self.backup_dir}

## 迁移摘要

- **总操作数**: {len(self.migration_log)}
- **成功操作**: {len([log for log in self.migration_log if log['status'] == 'SUCCESS'])}
- **警告操作**: {len([log for log in self.migration_log if log['status'] == 'WARNING'])}
- **失败操作**: {len(self.errors)}

## 详细日志

"""
        
        for log_entry in self.migration_log:
            status_icon = {
                'SUCCESS': '✓',
                'WARNING': '⚠',
                'ERROR': '✗'
            }.get(log_entry['status'], '?')
            
            report_content += f"**{status_icon} {log_entry['action']}**\n"
            report_content += f"- 时间: {log_entry['timestamp']}\n"
            report_content += f"- 状态: {log_entry['status']}\n"
            if log_entry['details']:
                report_content += f"- 详情: {log_entry['details']}\n"
            report_content += "\n"
        
        if self.errors:
            report_content += "## 错误详情\n\n"
            for error in self.errors:
                report_content += f"**{error['action']}**: {error['details']}\n\n"
        
        report_content += "## 后续步骤\n\n"
        
        if not self.errors:
            report_content += """1. 运行 `python check_migration.py` 验证迁移
2. 运行 `python main_optimized.py` 启动优化版本
3. 测试应用功能是否正常
4. 如有问题，可从备份目录恢复原文件

🎉 迁移完成！享受优化版本的新功能吧！
"""
        else:
            report_content += """1. 检查上述错误信息
2. 手动修复相关问题
3. 重新运行迁移工具
4. 如需要，可从备份目录恢复文件

⚠️ 迁移过程中遇到问题，请根据错误信息进行修复。
"""
        
        return report_content
    
    def save_migration_report(self) -> str:
        """保存迁移报告"""
        report_content = self.generate_migration_report()
        report_path = self.backup_dir / 'migration_report.md'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"迁移报告已保存: {report_path}")
        return str(report_path)
    
    def run_full_migration(self) -> bool:
        """运行完整迁移流程"""
        logger.info("开始完整迁移流程...")
        
        steps = [
            ("备份原始文件", self.backup_original_files),
            ("迁移配置文件", self.migrate_configuration),
            ("迁移数据库", self.migrate_database),
            ("迁移用户数据", self.migrate_user_data),
            ("更新导入语句", self.update_imports),
            ("创建迁移脚本", self.create_migration_script)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            logger.info(f"执行步骤: {step_name}")
            try:
                if step_func():
                    success_count += 1
                    logger.info(f"✓ {step_name} 完成")
                else:
                    logger.warning(f"⚠ {step_name} 部分失败")
            except Exception as e:
                logger.error(f"✗ {step_name} 失败: {e}")
        
        # 生成迁移报告
        report_path = self.save_migration_report()
        
        # 输出结果
        logger.info(f"\n迁移完成: {success_count}/{len(steps)} 个步骤成功")
        logger.info(f"迁移报告: {report_path}")
        logger.info(f"备份目录: {self.backup_dir}")
        
        if success_count == len(steps):
            logger.info("🎉 迁移成功完成！")
            return True
        else:
            logger.warning("⚠️ 迁移部分完成，请检查报告")
            return False


def main():
    """主函数"""
    print("健康管理应用 - 迁移工具")
    print("=" * 40)
    print("此工具将帮助您从原版本迁移到优化版本")
    print("迁移过程中会自动备份原始文件")
    print()
    
    # 确认迁移
    response = input("是否开始迁移？(y/N): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("迁移已取消")
        return 0
    
    # 创建迁移工具实例
    migration_tool = MigrationTool()
    
    # 运行迁移
    success = migration_tool.run_full_migration()
    
    if success:
        print("\n下一步:")
        print("1. 运行 'python check_migration.py' 验证迁移")
        print("2. 运行 'python main_optimized.py' 启动优化版本")
        return 0
    else:
        print("\n迁移过程中遇到问题，请查看日志和报告")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)