from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.widget import Widget
import os
import json
import sys
from datetime import datetime
import threading
import tempfile
import traceback
from kivy.logger import Logger
from kivy.uix.progressbar import ProgressBar

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.list import <PERSON><PERSON>ist, MDListItem
from kivy.factory import Factory

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入Logo组件
from widgets.logo import HealthLogo, add_logo_to_layout

# 导入API客户端
from api.api_client import APIClient

# 定义KV语言字符串
KV = '''
<ModuleCard>:
    orientation: 'vertical'
    size_hint: None, None
    size: dp(160), dp(140)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 4
    padding: [dp(12), dp(12), dp(12), dp(12)]
    pos_hint: {'center_x': 0.5}
    ripple_behavior: True
    on_release: root.on_click()
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        MDIconButton:
            icon: root.icon
            icon_size: dp(32)
            pos_hint: {'center_x': 0.5}
            theme_icon_color: "Custom"
            icon_color: root.icon_color if root.icon_color else app.theme.PRIMARY_DARK
            disabled: True
            
        MDLabel:
            text: root.title
            halign: 'center'
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            bold: True
            size_hint_y: None
            height: self.texture_size[1]
            
        MDLabel:
            text: root.description
            halign: 'center'
            font_style: "Body"
            role: "small"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            size_hint_y: None
            height: self.texture_size[1]
            shorten: True
            shorten_from: 'right'

<QuickActionCard>:
    orientation: 'horizontal'
    size_hint: 1, None
    height: dp(60)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(8)]
    elevation: 2
    padding: [dp(12), dp(8), dp(12), dp(8)]
    ripple_behavior: True
    on_release: root.on_click()
    
    MDIconButton:
        icon: root.icon
        icon_size: dp(24)
        theme_icon_color: "Custom"
        icon_color: root.icon_color if root.icon_color else app.theme.PRIMARY_DARK
        size_hint_x: None
        width: dp(40)
        disabled: True
        
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(2)
        
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            bold: True
            size_hint_y: None
            height: self.texture_size[1]
            
        MDLabel:
            text: root.subtitle
            font_style: "Body"
            role: "small"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            size_hint_y: None
            height: self.texture_size[1]
    
    MDIconButton:
        icon: "chevron-right"
        icon_size: dp(20)
        theme_icon_color: "Custom"
        icon_color: app.theme.TEXT_SECONDARY
        size_hint_x: None
        width: dp(32)
        disabled: True

<HomepageScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size
            
    MDBoxLayout:
        orientation: 'vertical'
        
        # Logo区域 - 与profile页面保持一致
        MDBoxLayout:
            id: logo_container
            orientation: 'vertical'
            size_hint_y: None
            height: dp(200)  # Logo区域高度
            padding: [0, dp(10), 0, dp(5)]
            size_hint_x: 0.8
            pos_hint: {"center_x": 0.5}

            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo
        
        # 删除顶部导航栏，因为底部导航栏已有"我的"按钮
        
        # 主内容区域
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(20), dp(20), dp(20), dp(100)]  # 增加底部padding为底部导航留空间
                spacing: dp(24)
                
                # 欢迎区域
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: [dp(20), dp(16), dp(20), dp(16)]
                    spacing: dp(8)
                    md_bg_color: app.theme.CARD_BACKGROUND
                    radius: [dp(16)]
                    elevation: 2
                    
                    MDBoxLayout:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(8)
                    
                    MDLabel:
                        id: welcome_label
                        text: "欢迎回来！"
                        font_style: "Body"
                        role: "large"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_PRIMARY
                        bold: True
                        halign: "left"
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDLabel:
                        id: welcome_subtitle
                        text: "今天是个美好的一天，祝您健康愉快！"
                        font_style: "Body"
                        role: "medium"
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_SECONDARY
                        halign: "left"
                        size_hint_y: None
                        height: self.texture_size[1]
                
                # 四大功能模块
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: [dp(20), dp(16), dp(20), dp(20)]
                    spacing: dp(16)
                    md_bg_color: app.theme.CARD_BACKGROUND
                    radius: [dp(16)]
                    elevation: 2
                    
                    MDLabel:
                        text: "健康管理服务"
                        font_style: "Body"
                        role: "large"
                        theme_text_color: "Custom"
                        text_color: app.theme.PRIMARY_DARK
                        bold: True
                        halign: "left"
                        size_hint_y: None
                        height: self.texture_size[1]
                    
                    # 模块网格布局
                    MDGridLayout:
                        id: modules_grid
                        cols: 2
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(16)
                        size_hint_y: None
                        height: self.minimum_height
                
                # 快速操作区域
                MDCard:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: [dp(20), dp(16), dp(20), dp(20)]
                    spacing: dp(16)
                    md_bg_color: app.theme.CARD_BACKGROUND
                    radius: [dp(16)]
                    elevation: 2
                    
                    # 快速操作标题栏，包含增加和删除按钮
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(40)
                        spacing: dp(8)
                        
                        MDLabel:
                            text: "快速操作"
                            font_style: "Body"
                            role: "large"
                            theme_text_color: "Custom"
                            text_color: app.theme.PRIMARY_DARK
                            bold: True
                            halign: "left"
                            size_hint_x: 1
                            size_hint_y: None
                            height: self.texture_size[1]
                        
                        MDIconButton:
                            icon: "plus"
                            icon_size: dp(24)
                            theme_icon_color: "Custom"
                            icon_color: app.theme.SUCCESS_COLOR
                            size_hint_x: None
                            width: dp(40)
                            on_release: root.on_add_quick_action()
                            
                        MDIconButton:
                            icon: "minus"
                            icon_size: dp(24)
                            theme_icon_color: "Custom"
                            icon_color: app.theme.ERROR_COLOR
                            size_hint_x: None
                            width: dp(40)
                            on_release: root.on_remove_quick_action()
                    
                    MDBoxLayout:
                        id: quick_actions
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(12)
        # 底部导航栏
        MDBoxLayout:
            id: nav_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(72)
            md_bg_color: app.theme.CARD_BACKGROUND
            elevation: 8
            padding: [dp(8), dp(8), dp(8), dp(8)]
            
            NavBarButton:
                icon: "home"
                text: "首页"
                selected: True
                on_release: root.on_nav_button_press(self)
                
            NavBarButton:
                icon: "heart-pulse"
                text: "健康"
                selected: False
                on_release: root.on_nav_button_press(self)
                
            NavBarButton:
                icon: "calendar-check"
                text: "服务"
                selected: False
                on_release: root.on_nav_button_press(self)
                
            NavBarButton:
                icon: "account"
                text: "我的"
                selected: False
                on_release: root.on_nav_button_press(self)
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class ModuleCard(Factory.MDCard):
    """模块卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("功能")
    description = StringProperty("")
    bg_color = ListProperty(None)
    icon_color = ListProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def on_click(self):
        """点击事件处理"""
        pass

class QuickActionCard(Factory.MDCard):
    """快速操作卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("操作")
    subtitle = StringProperty("")
    icon_color = ListProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
    def on_click(self):
        """点击事件处理"""
        pass

class SectionHeader(MDBoxLayout):
    """分区标题组件"""
    title = StringProperty("分区标题")
    title_color = ListProperty(None)
    __events__ = ('on_more',)  # 注册自定义事件

    def on_more(self, *args):
        pass  # 事件回调占位

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class NavBarButton(MDBoxLayout):
    """导航栏按钮"""
    icon = StringProperty("home")
    text = StringProperty("首页")
    selected = BooleanProperty(False)
    
    # 注册release事件
    __events__ = ('on_release',)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.size_hint_x = 1
        self.spacing = dp(4)
        self.padding = [dp(8), dp(8), dp(8), dp(8)]
        
        # 图标
        self.icon_widget = MDIconButton(
            icon=self.icon,
            icon_size=dp(24),
            pos_hint={'center_x': 0.5},
            theme_icon_color="Custom",
            disabled=True
        )
        self.add_widget(self.icon_widget)
        
        # 文本
        self.text_widget = MDLabel(
            text=self.text,
            font_style="Body",
            role="small",
            halign="center",
            size_hint_y=None,
            height=dp(16),
            theme_text_color="Custom"
        )
        self.add_widget(self.text_widget)
        
        # 绑定属性变化
        self.bind(icon=self.update_icon)
        self.bind(text=self.update_text)
        self.bind(selected=self.update_selected)
        
        # 初始化颜色
        self.update_selected()
    
    def update_icon(self, instance, value):
        if hasattr(self, 'icon_widget'):
            self.icon_widget.icon = value
    
    def update_text(self, instance, value):
        if hasattr(self, 'text_widget'):
            self.text_widget.text = value
    
    def update_selected(self, *args):
        app = MDApp.get_running_app()
        if self.selected:
            # 选中状态
            if hasattr(self, 'icon_widget'):
                self.icon_widget.icon_color = app.theme.PRIMARY_COLOR
            if hasattr(self, 'text_widget'):
                self.text_widget.text_color = app.theme.PRIMARY_COLOR
        else:
            # 未选中状态
            if hasattr(self, 'icon_widget'):
                self.icon_widget.icon_color = app.theme.TEXT_SECONDARY
            if hasattr(self, 'text_widget'):
                self.text_widget.text_color = app.theme.TEXT_SECONDARY
    
    def on_touch_down(self, touch):
        if self.collide_point(*touch.pos):
            # 触发release事件
            self.dispatch('on_release')
            return True
        return super().on_touch_down(touch)

    def on_release(self):
        """按钮点击事件 - 符合KivyMD 2.0.1 dev0标准"""
        # 获取父级屏幕并调用导航方法
        parent = self.parent
        while parent and not hasattr(parent, 'on_nav_button_press'):
            parent = parent.parent
        if parent and hasattr(parent, 'on_nav_button_press'):
            parent.on_nav_button_press(self)

class HomepageScreen(BaseScreen):
    """首页屏幕"""
    # 常量定义
    NAVIGATION_ERROR_MESSAGE = "页面跳转失败"
    
    user_name = StringProperty("XXX")
    user_gender = StringProperty("先生")  # 默认为先生，可以是"先生"或"女士"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        # 延迟初始化UI，确保主题已加载
        Clock.schedule_once(self.init_ui)
    
    def init_ui(self, dt=0):
        """初始化UI"""
        # 加载用户数据
        self.load_user_data()

        # 设置欢迎消息
        self.set_welcome_message()

        # 加载四大功能模块
        self.load_modules()
        
        # 加载快速操作
        self.load_quick_actions()

    def load_modules(self):
        """加载四大功能模块"""
        modules_data = [
            {
                'title': '健康数据管理',
                'icon': 'heart-pulse',
                'description': '记录和管理您的健康数据',
                'color': self.app.theme.HEALTH_DATA_COLOR,
                'action': self.on_health_data
            },
            {
                'title': '健康风险管理', 
                'icon': 'shield-heart',
                'description': '评估和预防健康风险',
                'color': self.app.theme.HEALTH_RISK_COLOR,
                'action': self.on_health_risk
            },
            {
                'title': '慢性病管理',
                'icon': 'medical-bag', 
                'description': '慢性疾病的专业管理',
                'color': self.app.theme.WARNING_COLOR,
                'action': self.on_chronic_disease
            },
            {
                'title': '医疗服务',
                'icon': 'hospital-box',
                'description': '在线医疗咨询服务', 
                'color': self.app.theme.MEDICAL_SERVICE_COLOR,
                'action': self.on_medical_service
            }
        ]
        
        modules_grid = self.ids.modules_grid
        modules_grid.clear_widgets()
        
        for module in modules_data:
            card = ModuleCard(
                title=module['title'],
                icon=module['icon'], 
                description=module['description'],
                icon_color=module['color'],
                on_release=module['action']
            )
            modules_grid.add_widget(card)
            
    def load_quick_actions(self):
        """加载快速操作"""
        quick_actions_data = [
            {
                'title': '健康状态总览',
                'icon': 'chart-line',
                'action': self.navigate_to_health_overview
            },
            {
                'title': '语音分诊',
                'icon': 'microphone',
                'action': self.navigate_to_voice_triage
            },
            {
                'title': '健康日记',
                'icon': 'book-open-variant',
                'action': self.navigate_to_health_diary
            }
        ]
        
        quick_actions_box = self.ids.quick_actions
        quick_actions_box.clear_widgets()
        
        for action in quick_actions_data:
            card = QuickActionCard(
                title=action['title'],
                icon=action['icon'],
                on_release=action['action']
            )
            quick_actions_box.add_widget(card)

    def on_enter(self):
        """进入屏幕时调用"""
        # 强制校验登录状态
        app = MDApp.get_running_app()
        
        # 检查是否已登录
        is_logged_in = False
        if hasattr(app, 'user_data') and app.user_data and app.user_data.get('username'):
            # 检查cloud_api的认证状态
            try:
                from utils.cloud_api import get_cloud_api
                cloud_api = get_cloud_api()
                if cloud_api and cloud_api.is_authenticated():
                    is_logged_in = True
            except Exception as e:
                print(f"检查cloud_api认证状态时出错: {e}")
        
        if not is_logged_in:
            # 未登录或认证无效，强制跳转回登录页并提示
            if self.manager:
                self.manager.current = 'login_screen'
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="请先登录"))
            snackbar.open()
            return
            
        # 已登录，刷新用户数据
        self.load_user_data()

    def load_user_data(self):
        """加载用户数据"""
        try:
            # 获取应用实例
            app = MDApp.get_running_app()

            # 检查是否有用户数据
            if hasattr(app, 'user_data') and app.user_data:
                user_data = app.user_data
                # 获取用户名
                self.user_name = user_data.get('username', 'XXX')
                # 获取用户性别
                gender = user_data.get('gender', '')
                if gender.lower() in ['female', 'f', '女', '女性']:
                    self.user_gender = "女士"
                else:
                    self.user_gender = "先生"
                # 设置欢迎消息
                self.set_welcome_message(self.user_name, self.user_gender)
            else:
                # 没有用户数据，使用默认值
                self.user_name = "访客"
                self.user_gender = "先生"
                self.set_welcome_message(self.user_name, self.user_gender)
        except Exception as e:
            Logger.error(f"加载用户数据失败: {str(e)}")
            # 使用默认值
            self.user_name = "访客"
            self.user_gender = "先生"
            self.set_welcome_message(self.user_name, self.user_gender)
    
    def set_welcome_message(self, name=None, gender=None):
        """设置欢迎消息"""
        try:
            if name is None:
                name = self.user_name
            if gender is None:
                gender = self.user_gender
            # 设置欢迎标签
            welcome_label = self.ids.welcome_label
            # 根据时间段设置不同的问候语
            now = datetime.now()
            hour = now.hour
            greeting = ""
            if 5 <= hour < 12:
                greeting = "早上好"
            elif 12 <= hour < 14:
                greeting = "中午好"
            elif 14 <= hour < 18:
                greeting = "下午好"
            else:
                greeting = "晚上好"
            welcome_label.text = f"{greeting}，{name} {gender}！"
            # 设置欢迎副标签
            welcome_subtitle = self.ids.welcome_subtitle
            welcome_subtitle.text = "今天是个美好的一天，祝您健康愉快！"
        except Exception as e:
            Logger.error(f"设置欢迎消息失败: {str(e)}")
    
    def go_back(self):
        """返回上一级"""
        # 在测试环境中，不执行跳转操作
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
        snackbar = MDSnackbar(MDSnackbarText(text="返回功能在测试环境中暂不可用"))
        snackbar.open()
    
    def on_profile(self):
        """用户资料按钮点击"""
        # 跳转到用户资料页面
        if self.manager and self.manager.has_screen('profile_screen'):
            self.manager.current = 'profile_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="用户资料功能开发中"))
            snackbar.open()
    
    # 四大功能模块事件处理
    def on_health_data(self, *args):
        """健康数据管理"""
        # 导航到健康资料管理页面
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有健康资料管理屏幕
        if not sm.has_screen("health_data_management_screen"):
            # 导入并添加健康资料管理屏幕
            from screens.health_data_management_screen import HealthDataManagementScreen
            sm.add_widget(HealthDataManagementScreen(name="health_data_management_screen"))
        # 导航到健康资料管理屏幕
        sm.transition.direction = 'left'
        sm.current = "health_data_management_screen"
    
    def on_health_risk(self, *args):
        """健康风险管理"""
        if self.manager and self.manager.has_screen('health_risk_screen'):
            self.manager.current = 'health_risk_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="健康风险管理功能开发中"))
            snackbar.open()
    
    def on_chronic_disease(self, *args):
        """慢性病管理"""
        if self.manager and self.manager.has_screen('chronic_disease_screen'):
            self.manager.current = 'chronic_disease_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="慢性病管理功能开发中"))
            snackbar.open()
    
    def on_medical_service(self, *args):
        """医疗服务"""
        if self.manager and self.manager.has_screen('medical_service_screen'):
            self.manager.current = 'medical_service_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="医疗服务功能开发中"))
            snackbar.open()
    
    # 快速操作导航方法
    def navigate_to_health_overview(self, *args):
        """导航到健康状态总览"""
        if self.manager and self.manager.has_screen('health_overview_screen'):
            self.manager.current = 'health_overview_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="健康状态总览功能开发中"))
            snackbar.open()
    
    def navigate_to_voice_triage(self, *args):
        """导航到语音分诊"""
        if self.manager and self.manager.has_screen('voice_triage_screen'):
            self.manager.current = 'voice_triage_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="语音分诊功能开发中"))
            snackbar.open()
    
    def navigate_to_health_diary(self, *args):
        """导航到健康日记"""
        if self.manager and self.manager.has_screen('health_diary_screen'):
            self.manager.current = 'health_diary_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="健康日记功能开发中"))
            snackbar.open()
    
    def add_health_data_modules(self):
        """添加健康资料管理模块"""
        self.ids.health_data_grid.clear_widgets()
        
        # 健康状态总览 - 只能查看，不可编辑
        health_overview = ModuleCard(
            icon="chart-box",
            title="健康状态总览",
            description="查看自动总结的健康信息",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_health_overview()
        )
        self.ids.health_data_grid.add_widget(health_overview)
        
        # 基本健康信息问卷
        basic_health = ModuleCard(
            icon="clipboard-text",
            title="基本健康信息",
            description="录入、修改、提交健康信息",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_basic_health()
        )
        self.ids.health_data_grid.add_widget(basic_health)
        
        # 医疗记录管理 - 整合住院、门诊、检验、技诊报告
        medical_records = ModuleCard(
            icon="folder-medical",
            title="医疗记录管理",
            description="住院、门诊、检验、技诊报告统一管理",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_medical_records()
        )
        self.ids.health_data_grid.add_widget(medical_records)
        
        # 调查问卷/评估量表
        survey = ModuleCard(
            icon="clipboard-check",
            title="调查问卷/评估量表",
            description="搜索、填写、查看结果",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_survey()
        )
        self.ids.health_data_grid.add_widget(survey)
        
        # 用药管理 - 整合用药记录和管理
        medication_management = ModuleCard(
            icon="pill",
            title="用药管理",
            description="药品记录、剂量管理、服药提醒",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_medication_management()
        )
        self.ids.health_data_grid.add_widget(medication_management)
        
        # 健康日记
        health_diary = ModuleCard(
            icon="book-open-variant",
            title="健康日记",
            description="血压、血糖、体重等日常管理",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_health_diary()
        )
        self.ids.health_data_grid.add_widget(health_diary)
        
        # 健康文档管理 - 资料上传和管理
        health_document = ModuleCard(
            icon="folder-upload",
            title="健康文档管理",
            description="直接上传、二维码、拍照上传",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_health_document()
        )
        self.ids.health_data_grid.add_widget(health_document)
        
        # 其它记录
        other_records = ModuleCard(
            icon="text-box",
            title="其它记录",
            description="文字记录其他健康信息",
            bg_color=self.app.theme.HEALTH_DATA_LIGHT,
            icon_color=self.app.theme.HEALTH_DATA_COLOR,
            on_release=lambda x: self.navigate_to_other_records()
        )
        self.ids.health_data_grid.add_widget(other_records)
        
        # 管理日志 - 健康顾问专用
        app = MDApp.get_running_app()
        user_role = getattr(app, 'user_role', 'personal')
        if user_role in ['consultant', 'admin', 'supermanager']:
            management_log = ModuleCard(
                icon="clipboard-list",
                title="管理日志",
                description="健康顾问管理记录",
                bg_color=self.app.theme.HEALTH_DATA_LIGHT,
                icon_color=self.app.theme.HEALTH_DATA_COLOR,
                on_release=lambda x: self.navigate_to_management_log()
            )
            self.ids.health_data_grid.add_widget(management_log)
    
    def add_health_risk_modules(self):
        """添加健康风险管理模块"""
        self.ids.health_risk_grid.clear_widgets()
        
        # 恶性病风险评估
        malignant_risk = ModuleCard(
            icon="alert-circle",
            title="恶性病风险评估",
            description="基于健康状况的AI风险评估",
            bg_color=self.app.theme.HEALTH_RISK_LIGHT,
            icon_color=self.app.theme.HEALTH_RISK_COLOR,
            on_release=lambda x: self.navigate_to_malignant_risk()
        )
        self.ids.health_risk_grid.add_widget(malignant_risk)
        
        # 慢性病风险评估
        chronic_risk = ModuleCard(
            icon="heart-pulse",
            title="慢性病风险评估",
            description="基于健康状况的慢性病风险分析",
            bg_color=self.app.theme.HEALTH_RISK_LIGHT,
            icon_color=self.app.theme.HEALTH_RISK_COLOR,
            on_release=lambda x: self.navigate_to_chronic_risk()
        )
        self.ids.health_risk_grid.add_widget(chronic_risk)
        
        # 个性化体检方案订制
        personalized_checkup = ModuleCard(
            icon="clipboard-check-multiple",
            title="个性化体检方案",
            description="基于健康状况的AI体检方案定制",
            bg_color=self.app.theme.HEALTH_RISK_LIGHT,
            icon_color=self.app.theme.HEALTH_RISK_COLOR,
            on_release=lambda x: self.navigate_to_personalized_checkup()
        )
        self.ids.health_risk_grid.add_widget(personalized_checkup)
    
    def add_medical_service_modules(self):
        """添加医疗服务模块"""
        # 先清空现有内容
        self.ids.medical_service_grid.clear_widgets()
        
        # 添加语音分诊服务
        triage_card = ModuleCard(
            icon="microphone",
            title="语音分诊",
            description="AI语音问诊，生成就诊建议",
            bg_color=self.app.theme.MEDICAL_SERVICE_LIGHT,
            icon_color=self.app.theme.MEDICAL_SERVICE_COLOR,
            on_release=lambda x: self.navigate_to_voice_triage()
        )
        self.ids.medical_service_grid.add_widget(triage_card)
        
        # 添加陪诊服务 - 新功能
        companion_card = ModuleCard(
            icon="account-heart",
            title="陪诊服务",
            description="挂号、接送、住宿、饮食全程服务",
            bg_color=self.app.theme.MEDICAL_SERVICE_LIGHT,
            icon_color=self.app.theme.MEDICAL_SERVICE_COLOR,
            on_release=lambda x: self.navigate_to_medical_companion()
        )
        self.ids.medical_service_grid.add_widget(companion_card)

    def add_navigation_bar(self):
        """添加导航栏"""
        # 清空导航栏
        self.ids.nav_bar.clear_widgets()
        
        # 添加导航按钮
        home_button = NavBarButton(
            icon="home",
            text="首页",
            selected=True
        )
        self.ids.nav_bar.add_widget(home_button)
        
        data_button = NavBarButton(
            icon="folder-multiple",
            text="健康资料"
        )
        self.ids.nav_bar.add_widget(data_button)
        
        service_button = NavBarButton(
            icon="hospital",
            text="医疗服务"
        )
        self.ids.nav_bar.add_widget(service_button)
        
        profile_button = NavBarButton(
            icon="account",
            text="个人中心"
        )
        self.ids.nav_bar.add_widget(profile_button)

    def on_nav_button_press(self, button):
        """导航按钮点击事件"""
        # 重置所有按钮的选中状态
        for child in self.ids.nav_bar.children:
            if isinstance(child, NavBarButton):
                child.selected = False

        # 设置当前按钮为选中状态
        button.selected = True

        # 根据按钮文本导航到相应页面
        if button.text == "首页":
            # 已经在首页，不需要导航
            pass
        elif button.text == "健康":
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="健康页面开发中"))
            snackbar.open()
        elif button.text == "服务":
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="服务页面开发中"))
            snackbar.open()
        elif button.text == "我的":
            self.navigate_to_profile()
    
    def navigate_to_profile(self):
        """导航到个人资料页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到个人资料页面")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有个人资料屏幕
        if not sm.has_screen("profile_page"):
            # 导入并添加个人资料屏幕
            from screens.profile_page import ProfilePage
            sm.add_widget(ProfilePage(name="profile_page"))
        # 导航到个人资料屏幕
        sm.current = "profile_page"

    def on_view_more(self, category):
        """查看更多按钮点击事件"""
        if category == "health_data":
            # 导航到健康资料管理页面
            self.show_info("导航到健康资料管理页面")
        elif category == "health_risk":
            # 导航到健康风险管理页面
            self.show_info("导航到健康风险管理页面")
        elif category == "medical_service":
            # 导航到就医服务页面
            self.show_info("导航到就医服务页面")
    
    def on_profile(self):
        """个人中心按钮点击事件"""
        # 导航到个人中心页面
        self.navigate_to_profile()
    
    def toggle_menu(self):
        """菜单按钮点击事件"""
        # 显示菜单
        self.show_info("菜单功能待实现")
    
    def navigate_to_health_overview(self):
        """导航到健康状态总览页面"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'health_overview_screen'

    def navigate_to_basic_health(self):
        """导航到基本健康信息页面"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'basic_health_info_screen'
    
    def navigate_to_hospital_records(self):
        """导航到住院资料页面"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'hospital_records'
    
    def navigate_to_outpatient_records(self):
        """导航到门诊资料页面"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'outpatient_records'
    
    def navigate_to_lab_report(self):
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'lab_report'
    
    def navigate_to_tech_diagnosis_report(self):
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'tech_diagnosis_report'
    
    def navigate_to_physical_exam(self):
        app = MDApp.get_running_app()
        app.root.transition.direction = 'left'
        app.root.current = 'physical_exam'
    
    def navigate_to_malignant_risk(self):
        """导航到恶性病高风险评估页面"""
        self.show_info("导航到恶性病高风险评估页面")
    
    def navigate_to_chronic_risk(self):
        """导航到慢性病高风险评估页面"""
        self.show_info("导航到慢性病高风险评估页面")

    def navigate_to_voice_triage(self):
        """导航到语音分诊页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到语音分诊页面")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有语音分诊屏幕
        if not sm.has_screen("voice_triage_screen"):
            # 导入并添加语音分诊屏幕
            from screens.voice_triage_screen import VoiceTriageScreen
            sm.add_widget(VoiceTriageScreen(name="voice_triage_screen"))
        # 导航到语音分诊屏幕
        sm.current = "voice_triage_screen"
    
    def navigate_to_medical_companion(self):
        """导航到陪诊服务页面"""
        self.navigate_to_companion_service()
        
    def navigate_to_survey(self):
        """导航到评估量表页面"""
        try:
            # 导航到survey_screen，让用户先选择量表
            self.manager.current = 'survey_screen'
        except Exception as e:
            Logger.error(f"Navigation error: {e}")
            self.show_snackbar(self.NAVIGATION_ERROR_MESSAGE)
    
    def navigate_to_health_diary(self):
        """导航到健康日记页面"""
        try:
            self.manager.current = 'health_diary_screen'
        except Exception as e:
            Logger.error(f"Navigation error: {e}")
            self.show_snackbar(self.NAVIGATION_ERROR_MESSAGE)
    
    def navigate_to_medication_management(self):
        """导航到用药管理页面"""
        try:
            self.manager.current = 'medication_management_screen'
        except Exception as e:
            Logger.error(f"Navigation error: {e}")
            self.show_snackbar(self.NAVIGATION_ERROR_MESSAGE)
    
    def navigate_to_health_document(self, document_type="all"):
        """导航到健康资料管理页面
        Args:
            document_type (str, optional): 文档类型. Defaults to "all".
        """
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info(f"导航到健康资料管理页面，类型: {document_type}")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有健康资料管理屏幕
        if not sm.has_screen("health_document"):
            # 导入并添加健康资料管理屏幕
            from screens.health_document_screen import HealthDocumentScreen
            sm.add_widget(HealthDocumentScreen(name="health_document"))
        # 设置文档类型
        health_document_screen = sm.get_screen("health_document")
        health_document_screen.document_type = document_type
        # 导航到健康资料管理屏幕
        sm.current = "health_document"
    
    def navigate_to_medical_records(self):
        """导航到医疗记录管理页面（整合住院、门诊、检验、技诊报告）"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到医疗记录管理页面")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有医疗记录管理屏幕
        if not sm.has_screen("medical_records"):
            # 导入并添加医疗记录管理屏幕
            from screens.medical_records_screen import MedicalRecordsScreen
            sm.add_widget(MedicalRecordsScreen(name="medical_records"))
        # 导航到医疗记录管理屏幕
        sm.current = "medical_records"
    
    def navigate_to_personalized_checkup(self):
        """导航到个性化体检方案页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到个性化体检方案页面")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有个性化体检方案屏幕
        if not sm.has_screen("personalized_checkup"):
            # 导入并添加个性化体检方案屏幕
            from screens.personalized_checkup_screen import PersonalizedCheckupScreen
            sm.add_widget(PersonalizedCheckupScreen(name="personalized_checkup"))
        # 导航到个性化体检方案屏幕
        sm.current = "personalized_checkup"
    
    def navigate_to_companion_service(self):
        """导航到陪诊服务页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到陪诊服务页面")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有陪诊服务屏幕
        if not sm.has_screen("companion_service"):
            # 导入并添加陪诊服务屏幕
            from screens.companion_service_screen import CompanionServiceScreen
            sm.add_widget(CompanionServiceScreen(name="companion_service"))
        # 导航到陪诊服务屏幕
        sm.current = "companion_service"
    
    def navigate_to_other_records(self):
        """导航到其它记录页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到其它记录页面")
        app = MDApp.get_running_app()
        sm = app.root
        # 检查是否已经有其它记录屏幕
        if not sm.has_screen("other_records"):
            # 导入并添加其它记录屏幕
            from screens.other_records_screen import OtherRecordsScreen
            sm.add_widget(OtherRecordsScreen(name="other_records"))
        # 导航到其它记录屏幕
        sm.current = "other_records"

    def show_snackbar(self, message):
        from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
        snackbar = MDSnackbar(MDSnackbarText(text=message), duration=2)
        snackbar.open()

    def navigate_to_management_log(self, instance=None):
        """导航到管理日志页面"""
        # 导航到管理日志页面
        app = MDApp.get_running_app()
        
        # 检查是否已经注册了管理日志屏幕
        if not app.root.has_screen('management_log'):
            from screens.management_log_screen import ManagementLogScreen
            management_log_screen = ManagementLogScreen(name='management_log')
            app.root.add_widget(management_log_screen)
        
        app.root.transition.direction = 'left'
        app.root.current = 'management_log'
    
    def on_logout(self):
        """退出按钮点击事件"""
        # 显示确认对话框
        dialog = MDDialog(
            title="确认退出",
            text="您确定要退出登录吗？",
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="确定"),
                    style="filled",
                    on_release=lambda x: self.confirm_logout(dialog)
                ),
            ],
        )
        dialog.open()
    
    def confirm_logout(self, dialog):
        """确认退出"""
        # 关闭对话框
        dialog.dismiss()
        # 清除用户数据
        app = MDApp.get_running_app()
        app.clear_user_data()
        # 导航到登录页面
        app.root.transition.direction = 'right'
        app.root.current = 'login_screen'

    def show_info(self, message):
        """显示信息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()
    
    def on_add_quick_action(self):
        """处理添加快速操作按钮点击事件"""
        self.show_info("添加快速操作功能")
        # TODO: 实现添加快速操作的逻辑
        
    def on_remove_quick_action(self):
        """处理删除快速操作按钮点击事件"""
        self.show_info("删除快速操作功能")
        # TODO: 实现删除快速操作的逻辑