#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复 api.api_client_optimized 导入问题
将所有错误的导入替换为正确的 api.api_client
"""

import os
import re
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_api_imports.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class APIImportFixer:
    """API导入修复工具"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.fixed_files = []
        self.total_replacements = 0
        
        # 定义需要替换的模式
        self.patterns = [
            # 匹配 from api.api_client_optimized import ...
            (r'from api\.api_client_optimized import', 'from api.api_client import'),
            # 匹配 import api.api_client_optimized
            (r'import api\.api_client_optimized', 'import api.api_client'),
            # 匹配 api.api_client_optimized.xxx
            (r'api\.api_client_optimized\.', 'api.api_client.'),
        ]
        
        # 需要处理的目录
        self.target_dirs = [
            'screens',
            'screens_bak', 
            'widgets',
            'utils',
            'api',
            '.'  # 根目录
        ]
        
        # 需要处理的文件扩展名
        self.file_extensions = ['.py']
        
        # 排除的文件
        self.exclude_files = [
            'fix_api_imports.py',
            'fix_theme_imports.py',
            'auto_restore.py',
            'restore_from_backup.py'
        ]
    
    def should_process_file(self, file_path):
        """判断是否应该处理该文件"""
        # 检查文件扩展名
        if file_path.suffix not in self.file_extensions:
            return False
            
        # 检查是否在排除列表中
        if file_path.name in self.exclude_files:
            return False
            
        # 检查是否在备份目录中（只处理特定的备份文件）
        if 'backup' in str(file_path) and 'screens_bak' not in str(file_path):
            return False
            
        return True
    
    def fix_file(self, file_path):
        """修复单个文件中的导入问题"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_replacements = 0
            
            # 应用所有替换模式
            for pattern, replacement in self.patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    count = len(matches)
                    file_replacements += count
                    logger.info(f"在 {file_path.relative_to(self.project_root)} 中替换了模式: {pattern} -> {replacement} ({count}次)")
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixed_files.append(str(file_path.relative_to(self.project_root)))
                self.total_replacements += file_replacements
                logger.info(f"✓ 已修复文件: {file_path.relative_to(self.project_root)}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"修复文件 {file_path} 时出错: {str(e)}")
            return False
    
    def scan_and_fix(self):
        """扫描并修复所有文件"""
        logger.info("开始扫描和修复 API 导入问题...")
        
        total_files = 0
        processed_files = 0
        
        # 遍历所有目标目录
        for target_dir in self.target_dirs:
            dir_path = self.project_root / target_dir
            
            if not dir_path.exists():
                logger.warning(f"目录不存在: {dir_path}")
                continue
            
            logger.info(f"处理目录: {target_dir}")
            
            # 递归查找所有Python文件
            if target_dir == '.':
                # 根目录只处理直接的Python文件，不递归
                python_files = [f for f in dir_path.iterdir() if f.is_file() and f.suffix == '.py']
            else:
                # 其他目录递归查找
                python_files = list(dir_path.rglob('*.py'))
            
            for file_path in python_files:
                total_files += 1
                
                if self.should_process_file(file_path):
                    if self.fix_file(file_path):
                        processed_files += 1
        
        logger.info(f"处理完成: 共检查 {total_files} 个文件，修复了 {len(self.fixed_files)} 个文件")
        logger.info(f"批量修复完成！总共修复了 {len(self.fixed_files)} 个文件")
        
        # 生成修复报告
        self.generate_report()
    
    def generate_report(self):
        """生成修复报告"""
        report = {
            'total_fixed_files': len(self.fixed_files),
            'total_replacements': self.total_replacements,
            'fixed_files': self.fixed_files,
            'directories_processed': self.target_dirs,
            'timestamp': str(self.project_root),
            'status': 'completed'
        }
        
        logger.info("修复报告:")
        logger.info(f"  total_fixed_files: {report['total_fixed_files']}")
        logger.info(f"  total_replacements: {report['total_replacements']}")
        logger.info(f"  directories_processed: {report['directories_processed']}")
        logger.info(f"  timestamp: {report['timestamp']}")
        logger.info(f"  status: {report['status']}")
        
        # 保存报告到文件
        import json
        report_file = self.project_root / 'api_import_fix_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"修复报告已保存到: {report_file}")

def main():
    """主函数"""
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    logger.info(f"项目根目录: {project_root}")
    
    # 创建修复工具实例
    fixer = APIImportFixer(project_root)
    
    # 执行修复
    fixer.scan_and_fix()
    
    logger.info("API导入修复完成！")

if __name__ == '__main__':
    main()