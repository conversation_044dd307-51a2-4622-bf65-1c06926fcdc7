#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复theme_optimized引用脚本
将所有文件中的theme_optimized引用替换为theme
"""

import os
import re
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_theme_imports.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def fix_theme_imports_in_file(file_path):
    """修复单个文件中的theme_optimized引用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换所有theme_optimized引用为theme
        patterns = [
            (r'from theme import', 'from theme import'),
            (r'import theme as theme', 'import theme as theme'),
            (r'import theme as theme', 'import theme as theme'),
            (r'import theme', 'import theme'),
            (r'theme_optimized\.', 'theme.'),
        ]
        
        changes_made = False
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                changes_made = True
                logger.info(f"在 {file_path} 中替换了模式: {pattern}")
        
        if changes_made:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✓ 已修复文件: {file_path}")
            return True
        else:
            logger.debug(f"文件无需修改: {file_path}")
            return False
            
    except Exception as e:
        logger.error(f"修复文件 {file_path} 时出错: {e}")
        return False

def fix_theme_imports_in_directory(directory):
    """修复目录中所有Python文件的theme_optimized引用"""
    directory = Path(directory)
    if not directory.exists():
        logger.error(f"目录不存在: {directory}")
        return 0
    
    fixed_count = 0
    total_files = 0
    
    # 遍历所有Python文件
    for py_file in directory.rglob('*.py'):
        total_files += 1
        if fix_theme_imports_in_file(py_file):
            fixed_count += 1
    
    logger.info(f"处理完成: 共检查 {total_files} 个文件，修复了 {fixed_count} 个文件")
    return fixed_count

def main():
    """主函数"""
    logger.info("开始批量修复theme_optimized引用...")
    
    # 需要修复的目录列表
    directories_to_fix = [
        'screens',
        'widgets',
        'utils',
        'api',
        '.'
    ]
    
    total_fixed = 0
    
    for directory in directories_to_fix:
        if os.path.exists(directory):
            logger.info(f"正在处理目录: {directory}")
            fixed = fix_theme_imports_in_directory(directory)
            total_fixed += fixed
        else:
            logger.warning(f"目录不存在，跳过: {directory}")
    
    logger.info(f"批量修复完成！总共修复了 {total_fixed} 个文件")
    
    # 生成修复报告
    report = {
        'total_fixed_files': total_fixed,
        'directories_processed': directories_to_fix,
        'timestamp': str(Path().cwd()),
        'status': 'completed'
    }
    
    logger.info("修复报告:")
    for key, value in report.items():
        logger.info(f"  {key}: {value}")

if __name__ == '__main__':
    main()