# -*- coding: utf-8 -*-
"""
后端模拟数据管理器 - 统一管理所有模拟数据

该模块提供了一个集中的模拟数据管理系统，用于替换分散在各个文件中的硬编码模拟数据。
通过环境变量控制是否启用模拟数据模式。
"""

import os
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from app.core.env_config import env_config

logger = logging.getLogger(__name__)

class BackendMockDataManager:
    """
    后端模拟数据管理器
    
    统一管理所有后端模拟数据，包括：
    - 仪表盘统计数据
    - 服务统计数据
    - 系统性能数据
    - 健康监控数据
    """
    
    def __init__(self):
        """初始化模拟数据管理器"""
        self.enabled = self._check_mock_enabled()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if self.enabled:
            self.logger.info("后端模拟数据模式已启用")
        else:
            self.logger.info("后端模拟数据模式已禁用")
    
    def _check_mock_enabled(self) -> bool:
        """
        检查是否启用模拟数据模式
        
        Returns:
            bool: 是否启用模拟数据
        """
        # 检查环境变量
        mock_enabled = os.getenv('ENABLE_MOCK_DATA', 'false').lower() in ['true', '1', 'yes', 'on']
        
        # 检查开发模式
        dev_mode = env_config.get('ENVIRONMENT', 'production').lower() in ['development', 'dev', 'debug']
        
        # 检查测试模式
        test_mode = os.getenv('TESTING', 'false').lower() in ['true', '1', 'yes']
        
        return mock_enabled or dev_mode or test_mode
    
    def is_enabled(self) -> bool:
        """
        检查模拟数据是否启用
        
        Returns:
            bool: 是否启用模拟数据
        """
        return self.enabled
    
    def generate_mock_dashboard_stats(self, time_range: str = "6months") -> Optional[Dict[str, Any]]:
        """
        生成模拟仪表盘统计数据
        
        Args:
            time_range: 时间范围
            
        Returns:
            Optional[Dict[str, Any]]: 模拟仪表盘统计数据或None
        """
        if not self.enabled:
            return None
        return {
            "total_records": random.randint(50, 200),
            "abnormal_records": random.randint(5, 20),
            "last_checkup_days": random.randint(30, 365),
            "completed_questionnaires": random.randint(10, 50),
            "health_index": round(random.uniform(70, 95), 1),
            "recent_activities": [
                {
                    "date": (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d"),
                    "activity": f"活动 {i+1}",
                    "type": random.choice(["checkup", "questionnaire", "lab", "medical"])
                }
                for i in range(5)
            ]
        }
    
    def generate_mock_weight_data(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟体重数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟体重数据或None
        """
        if not self.enabled:
            return None
        dates = []
        weights = []
        
        today = datetime.now()
        base_weight = random.uniform(60, 80)
        
        for i in range(6):
            date = today - timedelta(days=30 * i)
            dates.insert(0, date.strftime("%Y-%m-%d"))
            
            # 生成有轻微波动的体重数据
            weight_variation = random.uniform(-2, 2)
            weight = round(base_weight + weight_variation, 1)
            weights.insert(0, weight)
        
        return {
            "dates": dates,
            "weights": weights
        }
    
    def generate_mock_bp_data(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟血压数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟血压数据或None
        """
        if not self.enabled:
            return None
        dates = []
        systolic = []
        diastolic = []
        
        today = datetime.now()
        for i in range(6):
            date = today - timedelta(days=30 * i)
            dates.insert(0, date.strftime("%Y-%m-%d"))
            
            # 生成120-140之间的随机收缩压
            sys = random.randint(120, 140)
            systolic.insert(0, sys)
            
            # 生成70-90之间的随机舒张压
            dia = random.randint(70, 90)
            diastolic.insert(0, dia)
        
        return {
            "dates": dates,
            "systolic": systolic,
            "diastolic": diastolic
        }
    
    def generate_mock_exam_dist_data(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟体检分布数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟体检分布数据或None
        """
        if not self.enabled:
            return None
        return {
            "distribution": [
                {"type": "实验室检验", "count": 10, "percentage": 35.7},
                {"type": "影像学检查", "count": 8, "percentage": 28.6},
                {"type": "心电图检查", "count": 5, "percentage": 17.9},
                {"type": "超声检查", "count": 3, "percentage": 10.7},
                {"type": "其他检查", "count": 2, "percentage": 7.1}
            ]
        }
    
    def generate_mock_health_index_data(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟健康指数数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟健康指数数据或None
        """
        if not self.enabled:
            return None
        return {
            "current_index": 85.2,
            "trend": "improving",
            "history": [
                {"date": "2025-06-01", "index": 82.1},
                {"date": "2025-05-01", "index": 79.8},
                {"date": "2025-04-01", "index": 77.5},
                {"date": "2025-03-01", "index": 75.2}
            ],
            "indicators": [
                {"name": "BMI", "max": 30},
                {"name": "血压", "max": 180},
                {"name": "血糖", "max": 10},
                {"name": "胆固醇", "max": 7},
                {"name": "心率", "max": 120},
                {"name": "体脂率", "max": 40}
            ],
            "currentValues": [23.5, 130, 5.2, 4.8, 75, 22],
            "referenceValues": [22, 120, 5.0, 4.5, 70, 20]
        }
    
    def generate_mock_timeline_data(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟时间线数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟时间线数据或None
        """
        if not self.enabled:
            return None
        return {
            "timeline": [
                {
                    "date": (datetime.now() - timedelta(days=15)).strftime("%Y-%m-%d"),
                    "description": "年度体检，各项指标正常",
                    "type": "primary"
                },
                {
                    "date": (datetime.now() - timedelta(days=40)).strftime("%Y-%m-%d"),
                    "description": "完成健康生活方式调查问卷",
                    "type": "info"
                },
                {
                    "date": (datetime.now() - timedelta(days=70)).strftime("%Y-%m-%d"),
                    "description": "血脂检查，总胆固醇偏高",
                    "type": "warning"
                },
                {
                    "date": (datetime.now() - timedelta(days=100)).strftime("%Y-%m-%d"),
                    "description": "感冒就诊，开具抗生素处方",
                    "type": "danger"
                },
                {
                    "date": (datetime.now() - timedelta(days=130)).strftime("%Y-%m-%d"),
                    "description": "胸部X光检查，未见异常",
                    "type": "success"
                }
            ]
        }
    
    def generate_mock_service_stats(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟服务统计数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟服务统计数据或None
        """
        if not self.enabled:
            return None
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "auth_service": {
                "login_attempts": random.randint(1000, 2000),
                "login_success": random.randint(800, 1500),
                "login_failed": random.randint(50, 200)
            },
            "database_service": {
                "connections_successful": random.randint(5000, 10000),
                "connections_failed": random.randint(10, 50),
                "connection_errors": random.randint(5, 20)
            },
            "session_manager": {
                "active_sessions": random.randint(10, 50),
                "active_users": random.randint(5, 20)
            }
        }
    
    def generate_mock_public_service_stats(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟公共服务统计数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟公共服务统计数据或None
        """
        if not self.enabled:
            return None
        return {
            "status": "success",
            "data": {
                "total_requests": random.randint(1000, 5000),
                "total_users": random.randint(50, 200),
                "avg_response_time": round(random.uniform(50, 500), 2),
                "timestamp": datetime.utcnow().isoformat(),
                "note": "模拟数据 (原因: 无统计数据)"
            }
        }
    
    def generate_mock_public_metrics(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟公共指标数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟公共指标数据或None
        """
        if not self.enabled:
            return None
        return {
            "requests_24h": random.randint(1000, 10000),
            "active_users_24h": random.randint(50, 500),
            "avg_response_time": round(random.uniform(50, 500), 1),
            "error_rate": round(random.uniform(0.1, 5), 2),
            "uptime_percentage": round(random.uniform(95, 99.9), 2),
            "cpu_usage": round(random.uniform(10, 80), 1),
            "memory_usage": round(random.uniform(30, 90), 1),
            "disk_usage": round(random.uniform(20, 70), 1),
            "request_rate": round(random.uniform(10, 100), 1),
            "response_time": round(random.uniform(50, 500), 1),
            "timestamp": datetime.utcnow().isoformat(),
            "note": "模拟数据 (原因: 无统计数据)"
        }
    
    def generate_mock_system_metrics(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟系统指标数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟系统指标数据或None
        """
        if not self.enabled:
            return None
        return {
            "cpu_percent": random.randint(10, 70),
            "memory_percent": random.randint(20, 80),
            "disk_percent": random.randint(30, 70),
            "cpu_count": 4,
            "memory_total": 8 * 1024 * 1024 * 1024,  # 8 GB
            "disk_total": 100 * 1024 * 1024 * 1024,  # 100 GB
            "disk_free": 50 * 1024 * 1024 * 1024,  # 50 GB
            "net_bytes_sent": random.randint(1000000, 10000000),
            "net_bytes_recv": random.randint(1000000, 10000000)
        }
    
    def generate_mock_health_status(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟健康状态数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟健康状态数据或None
        """
        if not self.enabled:
            return None
        uptime = random.randint(3600, 86400 * 7)  # 1小时到7天
        
        return {
            "status": "healthy",
            "uptime": uptime,
            "uptime_formatted": self._format_uptime(uptime),
            "timestamp": datetime.now().isoformat(),
            "system": {
                "status": "healthy",
                "platform": "Windows-10-10.0.19041-SP0",
                "python_version": "3.9.0",
                "cpu_percent": random.randint(10, 70),
                "memory_percent": random.randint(20, 80),
                "disk_percent": random.randint(30, 70),
                "psutil_available": False
            },
            "application": {
                "status": "healthy",
                "total_requests": random.randint(100, 1000),
                "success_rate": round(random.uniform(95, 99.9), 1),
                "avg_response_time": round(random.uniform(50, 200), 1),
                "error_count": random.randint(0, 10)
            },
            "dependencies": {
                "status": "healthy",
                "details": {
                    "database": {"available": True},
                    "redis": {"available": True, "has_fallback": True},
                    "psutil": {"available": False, "has_fallback": True}
                }
            }
        }
    
    def _format_uptime(self, seconds: int) -> str:
        """
        格式化运行时间
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化后的运行时间
        """
        days, remainder = divmod(seconds, 86400)
        hours, remainder = divmod(remainder, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}天 {hours}小时 {minutes}分钟"
        elif hours > 0:
            return f"{hours}小时 {minutes}分钟 {seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟 {seconds}秒"
        else:
            return f"{seconds}秒"
    
    def generate_mock_detailed_service_stats(self) -> Optional[Dict[str, Any]]:
        """
        生成模拟详细服务统计数据
        
        Returns:
            Optional[Dict[str, Any]]: 模拟详细服务统计数据或None
        """
        if not self.enabled:
            return None
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "auth_service": {
                "login_attempts": random.randint(1000, 2000),
                "login_success": random.randint(800, 1500),
                "login_failed": random.randint(50, 200),
                "success_rate": round(random.uniform(85, 95), 1),
                "avg_response_time": round(random.uniform(100, 300), 1)
            },
            "db_service": {
                "connections_successful": random.randint(5000, 10000),
                "connections_failed": random.randint(10, 50),
                "connection_errors": random.randint(5, 20),
                "avg_query_time": round(random.uniform(10, 100), 1),
                "active_connections": random.randint(5, 20)
            },
            "session_manager": {
                "active_sessions": random.randint(10, 50),
                "active_users": random.randint(5, 20),
                "expired_sessions": random.randint(100, 500),
                "session_refresh_rate": round(random.uniform(10, 30), 1)
            },
            "api_endpoints": {
                "total_requests": random.randint(10000, 50000),
                "successful_requests": random.randint(9000, 45000),
                "failed_requests": random.randint(100, 1000),
                "avg_response_time": round(random.uniform(50, 200), 1)
            },
            "system_resources": {
                "cpu_usage": round(random.uniform(10, 80), 1),
                "memory_usage": round(random.uniform(30, 90), 1),
                "disk_usage": round(random.uniform(20, 70), 1),
                "network_io": {
                    "bytes_sent": random.randint(1000000, 10000000),
                    "bytes_received": random.randint(1000000, 10000000)
                }
            },
            "note": "模拟数据 (原因: 无统计数据)"
        }
    
    def get_test_endpoints_config(self) -> List[Dict[str, str]]:
        """
        获取测试端点配置
        
        Returns:
            List[Dict[str, str]]: 测试端点配置列表
        """
        return [
            {
                "name": "API文档",
                "url": "/docs",
                "method": "GET"
            },
            {
                "name": "问卷模板列表",
                "url": "/api/templates/questionnaire-templates",
                "method": "GET"
            },
            {
                "name": "评估模板列表",
                "url": "/api/templates/assessment-templates",
                "method": "GET"
            },
            {
                "name": "聚合API健康检查",
                "url": "/api/v1/aggregated/health",
                "method": "GET"
            },
            {
                "name": "数据管理API",
                "url": "/api/v1/data-management/health",
                "method": "GET"
            }
        ]
    
    def get_aggregated_endpoints_config(self) -> List[Dict[str, str]]:
        """
        获取聚合API端点配置
        
        Returns:
            List[Dict[str, str]]: 聚合API端点配置列表
        """
        return [
            {
                "name": "聚合API健康检查",
                "url": "/api/v1/health"
            },
            {
                "name": "问卷模板聚合",
                "url": "/api/v1/questionnaire-templates"
            },
            {
                "name": "评估模板聚合",
                "url": "/api/v1/assessment-templates"
            }
        ]
    
    def get_api_test_cases_config(self) -> List[Dict[str, Any]]:
        """
        获取API测试用例配置
        
        Returns:
            List[Dict[str, Any]]: API测试用例配置列表
        """
        return [
            {
                "name": "只获取问卷数据",
                "params": {"record_type": "questionnaire", "status": "pending"}
            },
            {
                "name": "只获取量表数据", 
                "params": {"record_type": "assessment", "status": "pending"}
            },
            {
                "name": "获取问卷和量表数据",
                "params": {"record_type": "questionnaire,assessment", "status": "pending"}
            },
            {
                "name": "获取所有已完成的问卷和量表",
                "params": {"record_type": "questionnaire,assessment", "status": "completed"}
            },
            {
                "name": "获取所有问卷和量表（不限状态）",
                "params": {"record_type": "questionnaire,assessment"}
            }
        ]
    
    def get_debug_config(self) -> Dict[str, Any]:
        """
        获取调试配置
        
        Returns:
            Dict[str, Any]: 调试配置
        """
        return {
            "base_url": "http://127.0.0.1:8006",
            "username": "admin",
            "password": "admin123",
            "expected_fields": ['id', 'name', 'category', 'item_count', 'target', 'created_at'],
            "field_mapping": {
                'template_id': 'id',
                'template_name': 'name', 
                'question_count': 'item_count',
                'response_count': 'usage_count',
                'category': 'category', 
                'target': 'target',
                'created_at': 'created_at'
            }
        }
    
    def get_test_data_config(self) -> Dict[str, Any]:
        """
        获取测试数据配置
        
        Returns:
            Dict[str, Any]: 测试数据配置
        """
        return {
            "database_path": "c:/Users/<USER>/Desktop/health-Trea/YUN/backend/app.db",
            "admin_username": "admin",
            "test_response_data": '{"test": "data"}',
            "questionnaire_limit": 3,
            "assessment_limit": 3,
            "status_options": ["pending", "completed"]
        }
    
    def reset(self):
        """
        重置模拟数据管理器
        """
        self.enabled = self._check_mock_enabled()
        self.logger.info(f"模拟数据管理器已重置，当前状态: {'启用' if self.enabled else '禁用'}")

# 创建全局实例
backend_mock_manager = BackendMockDataManager()

# 导出函数
def is_mock_enabled() -> bool:
    """
    检查是否启用模拟数据
    
    Returns:
        bool: 是否启用模拟数据
    """
    return backend_mock_manager.is_enabled()

def get_mock_dashboard_stats(time_range: str = "6months") -> Optional[Dict[str, Any]]:
    """
    获取模拟仪表盘统计数据
    
    Args:
        time_range: 时间范围
        
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_dashboard_stats(time_range)
    return None

def get_mock_weight_data() -> Optional[Dict[str, Any]]:
    """
    获取模拟体重数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_weight_data()
    return None

def get_mock_bp_data() -> Optional[Dict[str, Any]]:
    """
    获取模拟血压数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_bp_data()
    return None

def get_mock_exam_dist_data() -> Optional[List[Dict[str, Any]]]:
    """
    获取模拟检查分布数据
    
    Returns:
        Optional[List[Dict[str, Any]]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_exam_dist_data()
    return None

def get_mock_health_index_data() -> Optional[Dict[str, Any]]:
    """
    获取模拟健康指数数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_health_index_data()
    return None

def get_mock_timeline_data() -> Optional[List[Dict[str, Any]]]:
    """
    获取模拟时间线数据
    
    Returns:
        Optional[List[Dict[str, Any]]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_timeline_data()
    return None

def get_mock_service_stats() -> Optional[Dict[str, Any]]:
    """
    获取模拟服务统计数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_service_stats()
    return None

def get_mock_public_service_stats() -> Optional[Dict[str, Any]]:
    """
    获取模拟公共服务统计数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_public_service_stats()
    return None

def get_mock_public_metrics() -> Optional[Dict[str, Any]]:
    """
    获取模拟公共指标数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_public_metrics()
    return None

def get_mock_system_metrics() -> Optional[Dict[str, Any]]:
    """
    获取模拟系统指标数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_system_metrics()
    return None

def get_mock_health_status() -> Optional[Dict[str, Any]]:
    """
    获取模拟健康状态数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        return manager.generate_mock_health_status()
    return None

def get_mock_detailed_service_stats() -> Optional[Dict[str, Any]]:
    """
    获取模拟详细服务统计数据
    
    Returns:
        Optional[Dict[str, Any]]: 模拟数据或None
    """
    manager = BackendMockDataManager()
    if manager.is_enabled():
        data = manager.generate_mock_detailed_service_stats()
        if data:
            return {
                "status": "success",
                "data": data
            }
    return None

def get_test_endpoints_config() -> List[Dict[str, str]]:
    """
    获取测试端点配置
    
    Returns:
        List[Dict[str, str]]: 测试端点配置列表
    """
    manager = BackendMockDataManager()
    return manager.get_test_endpoints_config()

def get_aggregated_endpoints_config() -> List[Dict[str, str]]:
    """
    获取聚合API端点配置
    
    Returns:
        List[Dict[str, str]]: 聚合API端点配置列表
    """
    manager = BackendMockDataManager()
    return manager.get_aggregated_endpoints_config()

def get_api_test_cases_config() -> List[Dict[str, Any]]:
    """
    获取API测试用例配置
    
    Returns:
        List[Dict[str, Any]]: API测试用例配置列表
    """
    manager = BackendMockDataManager()
    return manager.get_api_test_cases_config()

def get_debug_config() -> Dict[str, Any]:
    """
    获取调试配置
    
    Returns:
        Dict[str, Any]: 调试配置
    """
    manager = BackendMockDataManager()
    return manager.get_debug_config()

def get_test_data_config() -> Dict[str, Any]:
    """
    获取测试数据配置
    
    Returns:
        Dict[str, Any]: 测试数据配置
    """
    manager = BackendMockDataManager()
    return manager.get_test_data_config()

# 导出所有公开函数和类
# 为了向后兼容性，创建别名
MockDataManager = BackendMockDataManager

__all__ = [
    'BackendMockDataManager',
    'MockDataManager',  # 别名
    'is_mock_enabled',
    'get_mock_dashboard_stats',
    'get_mock_weight_data',
    'get_mock_bp_data',
    'get_mock_exam_dist_data',
    'get_mock_health_index_data',
    'get_mock_timeline_data',
    'get_mock_service_stats',
    'get_mock_public_service_stats',
    'get_mock_public_metrics',
    'get_mock_system_metrics',
    'get_mock_health_status',
    'get_mock_detailed_service_stats',
    'get_test_endpoints_config',
    'get_aggregated_endpoints_config',
    'get_api_test_cases_config',
    'get_debug_config',
    'get_test_data_config'
]