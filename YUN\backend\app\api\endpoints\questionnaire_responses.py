from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.orm import Session

from app.db.base_session import get_db
from app.models.user import User
from app.models.questionnaire import QuestionnaireResponse, Questionnaire
from app.api import deps
from app.core.auth import get_current_active_user_custom

router = APIRouter()

@router.get("/{response_id}", response_model=Dict[str, Any])
def get_questionnaire_response(
    *,
    db: Session = Depends(get_db),
    response_id: int = Path(..., description="回答ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定问卷回答的详细信息
    """
    # 查找回答记录
    response = db.query(QuestionnaireResponse).filter(
        QuestionnaireResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到回答记录ID: {response_id}"
        )
    
    # 权限校验
    if current_user.custom_id != response.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此回答记录"
        )
    
    # 获取问卷信息 - 根据实际表结构使用正确的字段
    questionnaire_id = getattr(response, 'questionnaire_id', None) or getattr(response, 'questionnaire_instance_id', None)
    questionnaire = None
    if questionnaire_id:
        questionnaire = db.query(Questionnaire).filter(
            Questionnaire.id == questionnaire_id
        ).first()
    
    # 获取问题列表
    questions = []
    if questionnaire:
        if questionnaire.template and questionnaire.template.questions:
            # 从模板获取问题
            questions = [{
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "options": q.options,
                "order": q.order,
                "is_required": q.is_required
            } for q in questionnaire.template.questions]
        elif questionnaire.items:
            # 从问卷项目获取问题
            questions = [{
                "question_id": item.question_id,
                "question_text": item.question_text,
                "question_type": item.question_type,
                "options": item.options,
                "order": item.order,
                "is_required": item.is_required
            } for item in questionnaire.items]
    
    return {
        "status": "success",
        "data": {
            "id": response.id,
            "questionnaire_id": questionnaire_id,
            "custom_id": response.custom_id,
            "answers": response.answers,
            "total_score": response.total_score,
            "report": response.report,
            "status": response.status,
            "created_at": response.created_at.isoformat() if response.created_at else None,
            "updated_at": response.updated_at.isoformat() if response.updated_at else None,
            "questionnaire": {
                "id": questionnaire.id if questionnaire else None,
                "title": questionnaire.title if questionnaire else None,
                "description": questionnaire.description if questionnaire else None,
                "questions": questions,
                "questionnaire_type": questionnaire.questionnaire_type if questionnaire else None
            }
        }
    }

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_questionnaire_responses(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    questionnaire_type: Optional[str] = Query(None, description="问卷类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的问卷回答列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的问卷回答"
        )

    # 构建查询，join questionnaire表获取问卷信息
    # 根据实际表结构使用正确的字段
    if hasattr(QuestionnaireResponse, 'questionnaire_id'):
        query = db.query(QuestionnaireResponse, Questionnaire).join(
            Questionnaire, QuestionnaireResponse.questionnaire_id == Questionnaire.id
        ).filter(QuestionnaireResponse.custom_id == custom_id)
    else:
        # 如果使用questionnaire_instance_id
        query = db.query(QuestionnaireResponse, Questionnaire).join(
            Questionnaire, QuestionnaireResponse.questionnaire_instance_id == Questionnaire.id
        ).filter(QuestionnaireResponse.custom_id == custom_id)

    # 应用过滤条件
    if questionnaire_type:
        query = query.filter(Questionnaire.questionnaire_type == questionnaire_type)
    if start_date:
        query = query.filter(QuestionnaireResponse.created_at >= start_date)
    if end_date:
        query = query.filter(QuestionnaireResponse.created_at <= end_date)

    # 获取总数
    total = query.count()

    # 应用分页并获取结果
    responses = query.order_by(QuestionnaireResponse.created_at.desc()).offset(skip).limit(limit).all()

    # 格式化返回数据
    result_data = []
    for response, questionnaire in responses:
        # 获取正确的questionnaire_id
        response_questionnaire_id = getattr(response, 'questionnaire_id', None) or getattr(response, 'questionnaire_instance_id', None)
        
        result_data.append({
            "id": response.id,
            "questionnaire_id": response_questionnaire_id,
            "custom_id": response.custom_id,
            "answers": response.answers,
            "total_score": response.total_score,
            "report": response.report,
            "status": response.status,
            "created_at": response.created_at.isoformat() if response.created_at else None,
            "updated_at": response.updated_at.isoformat() if response.updated_at else None,
            "questionnaire": {
                "id": questionnaire.id,
                "title": questionnaire.title,
                "description": questionnaire.description,
                "notes": questionnaire.notes,
                "questionnaire_type": questionnaire.questionnaire_type
            }
        })

    return {
        "status": "success",
        "data": result_data,
        "total": total,
        "skip": skip,
        "limit": limit
    }

@router.delete("/{response_id}", response_model=Dict[str, Any])
def delete_questionnaire_response(
    *,
    db: Session = Depends(get_db),
    response_id: int = Path(..., description="回答ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    删除指定的问卷回答记录
    """
    # 查找回答记录
    response = db.query(QuestionnaireResponse).filter(
        QuestionnaireResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到回答记录ID: {response_id}"
        )
    
    # 权限校验
    if current_user.custom_id != response.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此回答记录"
        )
    
    # 删除记录
    db.delete(response)
    db.commit()
    
    return {
        "status": "success",
        "message": f"问卷回答记录 {response_id} 已成功删除"
    }