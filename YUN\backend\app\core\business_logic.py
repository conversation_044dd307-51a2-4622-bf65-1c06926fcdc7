#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一业务逻辑处理模块
提供可复用的业务逻辑函数和工具类
"""

import re
import hashlib
import secrets
import uuid
from datetime import datetime, timedelta, date
from typing import Any, Dict, List, Optional, Union, Tuple
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum
import json
from dataclasses import dataclass

from passlib.context import CryptContext
from email_validator import validate_email, EmailNotValidError

class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    DOCTOR = "doctor"
    NURSE = "nurse"
    PATIENT = "patient"
    GUEST = "guest"

class AssessmentStatus(str, Enum):
    """评估状态枚举"""
    DRAFT = "draft"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REVIEWED = "reviewed"
    ARCHIVED = "archived"

class QuestionnaireType(str, Enum):
    """问卷类型枚举"""
    SCALE = "scale"  # 量表
    SURVEY = "survey"  # 调查问卷
    ASSESSMENT = "assessment"  # 评估问卷
    SCREENING = "screening"  # 筛查问卷

class ScoreLevel(str, Enum):
    """评分等级枚举"""
    NORMAL = "normal"
    MILD = "mild"
    MODERATE = "moderate"
    SEVERE = "severe"
    CRITICAL = "critical"

@dataclass
class ScoreRange:
    """评分范围"""
    min_score: float
    max_score: float
    level: ScoreLevel
    description: str
    recommendations: List[str]

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]

class UserUtils:
    """用户相关工具类"""
    
    # 密码加密上下文
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return UserUtils.pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return UserUtils.pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_password(length: int = 12) -> str:
        """生成随机密码"""
        import string
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    @staticmethod
    def validate_password_strength(password: str) -> ValidationResult:
        """验证密码强度"""
        errors = []
        warnings = []
        
        if len(password) < 8:
            errors.append("密码长度至少8位")
        
        if not re.search(r'[A-Z]', password):
            errors.append("密码必须包含大写字母")
        
        if not re.search(r'[a-z]', password):
            errors.append("密码必须包含小写字母")
        
        if not re.search(r'\d', password):
            errors.append("密码必须包含数字")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            warnings.append("建议包含特殊字符以提高安全性")
        
        if len(password) < 12:
            warnings.append("建议密码长度至少12位")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def validate_email(email: str) -> ValidationResult:
        """验证邮箱格式"""
        try:
            validate_email(email)
            return ValidationResult(is_valid=True, errors=[], warnings=[])
        except EmailNotValidError as e:
            return ValidationResult(
                is_valid=False,
                errors=[str(e)],
                warnings=[]
            )
    
    @staticmethod
    def validate_phone(phone: str) -> ValidationResult:
        """验证手机号格式"""
        # 中国手机号正则
        pattern = r'^1[3-9]\d{9}$'
        
        if re.match(pattern, phone):
            return ValidationResult(is_valid=True, errors=[], warnings=[])
        else:
            return ValidationResult(
                is_valid=False,
                errors=["手机号格式不正确"],
                warnings=[]
            )
    
    @staticmethod
    def validate_id_card(id_card: str) -> ValidationResult:
        """验证身份证号格式"""
        # 18位身份证号正则
        pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
        
        if re.match(pattern, id_card):
            # 验证校验位
            if UserUtils._validate_id_card_checksum(id_card):
                return ValidationResult(is_valid=True, errors=[], warnings=[])
            else:
                return ValidationResult(
                    is_valid=False,
                    errors=["身份证号校验位错误"],
                    warnings=[]
                )
        else:
            return ValidationResult(
                is_valid=False,
                errors=["身份证号格式不正确"],
                warnings=[]
            )
    
    @staticmethod
    def _validate_id_card_checksum(id_card: str) -> bool:
        """验证身份证号校验位"""
        if len(id_card) != 18:
            return False
        
        # 权重因子
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        # 校验码
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        # 计算校验位
        sum_val = sum(int(id_card[i]) * weights[i] for i in range(17))
        check_index = sum_val % 11
        
        return id_card[17].upper() == check_codes[check_index]
    
    @staticmethod
    def extract_info_from_id_card(id_card: str) -> Dict[str, Any]:
        """从身份证号提取信息"""
        if len(id_card) != 18:
            return {}
        
        # 提取出生日期
        birth_year = int(id_card[6:10])
        birth_month = int(id_card[10:12])
        birth_day = int(id_card[12:14])
        birth_date = date(birth_year, birth_month, birth_day)
        
        # 计算年龄
        today = date.today()
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        
        # 提取性别
        gender_code = int(id_card[16])
        gender = "male" if gender_code % 2 == 1 else "female"
        
        # 提取地区代码
        region_code = id_card[:6]
        
        return {
            "birth_date": birth_date,
            "age": age,
            "gender": gender,
            "region_code": region_code
        }
    
    @staticmethod
    def check_user_permissions(user_role: str, required_roles: List[str]) -> bool:
        """检查用户权限"""
        role_hierarchy = {
            UserRole.GUEST: 0,
            UserRole.PATIENT: 1,
            UserRole.NURSE: 2,
            UserRole.DOCTOR: 3,
            UserRole.ADMIN: 4
        }
        
        user_level = role_hierarchy.get(user_role, 0)
        required_level = min(role_hierarchy.get(role, 4) for role in required_roles)
        
        return user_level >= required_level
    
    @staticmethod
    def format_user_display_name(user_data: Dict[str, Any]) -> str:
        """格式化用户显示名称"""
        if user_data.get("nickname"):
            return user_data["nickname"]
        elif user_data.get("real_name"):
            return user_data["real_name"]
        elif user_data.get("username"):
            return user_data["username"]
        else:
            return "未知用户"

class AssessmentUtils:
    """评估相关工具类"""
    
    @staticmethod
    def calculate_total_score(answers: List[Dict[str, Any]], questions: List[Dict[str, Any]]) -> float:
        """计算评估总分"""
        total_score = 0.0
        
        # 创建问题ID到问题的映射
        question_map = {q["id"]: q for q in questions}
        
        for answer in answers:
            question_id = answer.get("question_id")
            question = question_map.get(question_id)
            
            if not question:
                continue
            
            # 获取答案分数
            answer_value = answer.get("answer_value")
            if isinstance(answer_value, (int, float)):
                score = float(answer_value)
            elif isinstance(answer_value, str):
                # 处理选项分数
                options = question.get("options", [])
                option_map = {opt.get("value"): opt.get("score", 0) for opt in options}
                score = float(option_map.get(answer_value, 0))
            else:
                score = 0.0
            
            # 应用权重
            weight = question.get("weight", 1.0)
            total_score += score * weight
        
        return round(total_score, 2)
    
    @staticmethod
    def calculate_dimension_scores(
        answers: List[Dict[str, Any]], 
        questions: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """计算各维度分数"""
        dimension_scores = {}
        dimension_totals = {}
        
        # 创建问题ID到问题的映射
        question_map = {q["id"]: q for q in questions}
        
        for answer in answers:
            question_id = answer.get("question_id")
            question = question_map.get(question_id)
            
            if not question:
                continue
            
            dimension = question.get("dimension", "default")
            
            # 获取答案分数
            answer_value = answer.get("answer_value")
            if isinstance(answer_value, (int, float)):
                score = float(answer_value)
            elif isinstance(answer_value, str):
                options = question.get("options", [])
                option_map = {opt.get("value"): opt.get("score", 0) for opt in options}
                score = float(option_map.get(answer_value, 0))
            else:
                score = 0.0
            
            # 应用权重
            weight = question.get("weight", 1.0)
            weighted_score = score * weight
            
            if dimension not in dimension_scores:
                dimension_scores[dimension] = 0.0
                dimension_totals[dimension] = 0.0
            
            dimension_scores[dimension] += weighted_score
            dimension_totals[dimension] += weight
        
        # 计算平均分
        for dimension in dimension_scores:
            if dimension_totals[dimension] > 0:
                dimension_scores[dimension] = round(
                    dimension_scores[dimension] / dimension_totals[dimension], 2
                )
        
        return dimension_scores
    
    @staticmethod
    def interpret_score(
        score: float, 
        score_ranges: List[ScoreRange]
    ) -> Dict[str, Any]:
        """解释评分结果"""
        for score_range in sorted(score_ranges, key=lambda x: x.min_score):
            if score_range.min_score <= score <= score_range.max_score:
                return {
                    "level": score_range.level,
                    "description": score_range.description,
                    "recommendations": score_range.recommendations
                }
        
        return {
            "level": ScoreLevel.NORMAL,
            "description": "分数超出预定义范围",
            "recommendations": ["请咨询专业医生"]
        }
    
    @staticmethod
    def validate_answers_completeness(
        answers: List[Dict[str, Any]], 
        questions: List[Dict[str, Any]]
    ) -> ValidationResult:
        """验证回答完整性"""
        errors = []
        warnings = []
        
        # 获取必答问题
        required_questions = [q for q in questions if q.get("required", False)]
        answered_question_ids = {answer.get("question_id") for answer in answers}
        
        # 检查必答问题是否都已回答
        for question in required_questions:
            if question["id"] not in answered_question_ids:
                errors.append(f"问题 '{question.get('title', question['id'])}' 为必答题")
        
        # 检查答案有效性
        question_map = {q["id"]: q for q in questions}
        for answer in answers:
            question_id = answer.get("question_id")
            question = question_map.get(question_id)
            
            if not question:
                warnings.append(f"问题 {question_id} 不存在")
                continue
            
            answer_value = answer.get("answer_value")
            if answer_value is None or answer_value == "":
                if question.get("required", False):
                    errors.append(f"问题 '{question.get('title', question_id)}' 的答案不能为空")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def generate_assessment_report(
        assessment_data: Dict[str, Any],
        total_score: float,
        dimension_scores: Dict[str, float],
        interpretation: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成评估报告"""
        return {
            "assessment_id": assessment_data.get("id"),
            "assessment_name": assessment_data.get("name"),
            "user_id": assessment_data.get("user_id"),
            "completed_at": datetime.now().isoformat(),
            "total_score": total_score,
            "dimension_scores": dimension_scores,
            "interpretation": interpretation,
            "summary": {
                "level": interpretation.get("level"),
                "description": interpretation.get("description"),
                "key_findings": AssessmentUtils._extract_key_findings(dimension_scores),
                "recommendations": interpretation.get("recommendations", [])
            },
            "metadata": {
                "version": "1.0",
                "generated_at": datetime.now().isoformat(),
                "total_questions": len(assessment_data.get("questions", [])),
                "answered_questions": len(assessment_data.get("answers", []))
            }
        }
    
    @staticmethod
    def _extract_key_findings(dimension_scores: Dict[str, float]) -> List[str]:
        """提取关键发现"""
        findings = []
        
        if not dimension_scores:
            return findings
        
        # 找出最高和最低分的维度
        max_dimension = max(dimension_scores, key=dimension_scores.get)
        min_dimension = min(dimension_scores, key=dimension_scores.get)
        
        max_score = dimension_scores[max_dimension]
        min_score = dimension_scores[min_dimension]
        
        if max_score > 0:
            findings.append(f"{max_dimension}维度得分最高({max_score}分)")
        
        if min_score >= 0 and max_dimension != min_dimension:
            findings.append(f"{min_dimension}维度得分最低({min_score}分)")
        
        return findings

class QuestionnaireUtils:
    """问卷相关工具类"""
    
    @staticmethod
    def validate_questionnaire_answers(
        answers: List[Dict[str, Any]], 
        questionnaire: Dict[str, Any]
    ) -> ValidationResult:
        """验证问卷回答"""
        errors = []
        warnings = []
        
        questions = questionnaire.get("questions", [])
        question_map = {q["id"]: q for q in questions}
        
        # 检查跳转逻辑
        for answer in answers:
            question_id = answer.get("question_id")
            question = question_map.get(question_id)
            
            if not question:
                continue
            
            # 验证跳转条件
            skip_logic = question.get("skip_logic")
            if skip_logic:
                validation_result = QuestionnaireUtils._validate_skip_logic(
                    answer, skip_logic, answers, question_map
                )
                errors.extend(validation_result.errors)
                warnings.extend(validation_result.warnings)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def _validate_skip_logic(
        current_answer: Dict[str, Any],
        skip_logic: Dict[str, Any],
        all_answers: List[Dict[str, Any]],
        question_map: Dict[str, Any]
    ) -> ValidationResult:
        """验证跳转逻辑"""
        errors = []
        warnings = []
        
        # 这里可以实现复杂的跳转逻辑验证
        # 例如：如果选择了某个选项，则必须回答特定问题
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    @staticmethod
    def calculate_questionnaire_progress(
        answers: List[Dict[str, Any]], 
        total_questions: int
    ) -> Dict[str, Any]:
        """计算问卷进度"""
        answered_count = len(answers)
        progress_percentage = (answered_count / total_questions * 100) if total_questions > 0 else 0
        
        return {
            "answered_count": answered_count,
            "total_questions": total_questions,
            "progress_percentage": round(progress_percentage, 2),
            "is_completed": answered_count >= total_questions
        }
    
    @staticmethod
    def process_skip_logic(
        current_answer: Dict[str, Any],
        questionnaire: Dict[str, Any]
    ) -> List[str]:
        """处理跳转逻辑，返回下一个应该显示的问题ID列表"""
        questions = questionnaire.get("questions", [])
        question_map = {q["id"]: q for q in questions}
        
        current_question = question_map.get(current_answer.get("question_id"))
        if not current_question:
            return []
        
        skip_logic = current_question.get("skip_logic")
        if not skip_logic:
            # 没有跳转逻辑，返回下一个问题
            current_index = next(
                (i for i, q in enumerate(questions) if q["id"] == current_question["id"]), 
                -1
            )
            if current_index >= 0 and current_index < len(questions) - 1:
                return [questions[current_index + 1]["id"]]
            return []
        
        # 处理跳转逻辑
        answer_value = current_answer.get("answer_value")
        for rule in skip_logic.get("rules", []):
            condition = rule.get("condition")
            target_questions = rule.get("target_questions", [])
            
            if QuestionnaireUtils._evaluate_condition(answer_value, condition):
                return target_questions
        
        # 默认跳转
        default_target = skip_logic.get("default_target")
        if default_target:
            return [default_target]
        
        return []
    
    @staticmethod
    def _evaluate_condition(answer_value: Any, condition: Dict[str, Any]) -> bool:
        """评估跳转条件"""
        operator = condition.get("operator")
        expected_value = condition.get("value")
        
        if operator == "equals":
            return answer_value == expected_value
        elif operator == "not_equals":
            return answer_value != expected_value
        elif operator == "greater_than":
            return float(answer_value) > float(expected_value)
        elif operator == "less_than":
            return float(answer_value) < float(expected_value)
        elif operator == "in":
            return answer_value in expected_value
        elif operator == "not_in":
            return answer_value not in expected_value
        
        return False

class FormatUtils:
    """格式化工具类"""
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化日期时间"""
        if dt is None:
            return ""
        return dt.strftime(format_str)
    
    @staticmethod
    def format_date(d: date, format_str: str = "%Y-%m-%d") -> str:
        """格式化日期"""
        if d is None:
            return ""
        return d.strftime(format_str)
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def format_number(number: Union[int, float], decimal_places: int = 2) -> str:
        """格式化数字"""
        if number is None:
            return ""
        
        if isinstance(number, int):
            return f"{number:,}"
        else:
            return f"{number:,.{decimal_places}f}"
    
    @staticmethod
    def format_percentage(value: float, decimal_places: int = 1) -> str:
        """格式化百分比"""
        if value is None:
            return ""
        return f"{value:.{decimal_places}f}%"
    
    @staticmethod
    def format_currency(amount: Union[int, float], currency: str = "¥") -> str:
        """格式化货币"""
        if amount is None:
            return ""
        return f"{currency}{amount:,.2f}"
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """截断文本"""
        if not text or len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
        """掩码敏感数据"""
        if not data or len(data) <= visible_chars:
            return data
        
        visible_part = data[:visible_chars]
        masked_part = mask_char * (len(data) - visible_chars)
        return visible_part + masked_part

class SecurityUtils:
    """安全工具类"""
    
    @staticmethod
    def generate_token(length: int = 32) -> str:
        """生成安全令牌"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_uuid() -> str:
        """生成UUID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def hash_data(data: str, algorithm: str = "sha256") -> str:
        """哈希数据"""
        if algorithm == "md5":
            return hashlib.md5(data.encode()).hexdigest()
        elif algorithm == "sha1":
            return hashlib.sha1(data.encode()).hexdigest()
        elif algorithm == "sha256":
            return hashlib.sha256(data.encode()).hexdigest()
        else:
            raise ValueError(f"不支持的哈希算法: {algorithm}")
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """清理输入数据"""
        if not input_str:
            return ""
        
        # 移除HTML标签
        import html
        cleaned = html.escape(input_str)
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        return cleaned
    
    @staticmethod
    def validate_json(json_str: str) -> ValidationResult:
        """验证JSON格式"""
        try:
            json.loads(json_str)
            return ValidationResult(is_valid=True, errors=[], warnings=[])
        except json.JSONDecodeError as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"JSON格式错误: {str(e)}"],
                warnings=[]
            )

class CalculationUtils:
    """计算工具类"""
    
    @staticmethod
    def calculate_bmi(weight: float, height: float) -> float:
        """计算BMI"""
        if height <= 0:
            raise ValueError("身高必须大于0")
        
        # 身高转换为米
        height_m = height / 100 if height > 3 else height
        bmi = weight / (height_m ** 2)
        return round(bmi, 2)
    
    @staticmethod
    def interpret_bmi(bmi: float) -> Dict[str, str]:
        """解释BMI结果"""
        if bmi < 18.5:
            return {"level": "underweight", "description": "体重过轻"}
        elif 18.5 <= bmi < 24:
            return {"level": "normal", "description": "正常体重"}
        elif 24 <= bmi < 28:
            return {"level": "overweight", "description": "超重"}
        else:
            return {"level": "obese", "description": "肥胖"}
    
    @staticmethod
    def calculate_age(birth_date: date) -> int:
        """计算年龄"""
        today = date.today()
        return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
    
    @staticmethod
    def calculate_percentile(value: float, dataset: List[float]) -> float:
        """计算百分位数"""
        if not dataset:
            return 0.0
        
        sorted_data = sorted(dataset)
        count_below = sum(1 for x in sorted_data if x < value)
        percentile = (count_below / len(sorted_data)) * 100
        return round(percentile, 2)
    
    @staticmethod
    def round_to_precision(value: float, precision: int = 2) -> float:
        """精确舍入"""
        decimal_value = Decimal(str(value))
        rounded_value = decimal_value.quantize(
            Decimal('0.' + '0' * precision), 
            rounding=ROUND_HALF_UP
        )
        return float(rounded_value)

# 便捷函数
def validate_user_data(user_data: Dict[str, Any]) -> ValidationResult:
    """验证用户数据"""
    errors = []
    warnings = []
    
    # 验证邮箱
    if user_data.get("email"):
        email_result = UserUtils.validate_email(user_data["email"])
        errors.extend(email_result.errors)
        warnings.extend(email_result.warnings)
    
    # 验证手机号
    if user_data.get("phone"):
        phone_result = UserUtils.validate_phone(user_data["phone"])
        errors.extend(phone_result.errors)
        warnings.extend(phone_result.warnings)
    
    # 验证身份证号
    if user_data.get("id_card"):
        id_card_result = UserUtils.validate_id_card(user_data["id_card"])
        errors.extend(id_card_result.errors)
        warnings.extend(id_card_result.warnings)
    
    return ValidationResult(
        is_valid=len(errors) == 0,
        errors=errors,
        warnings=warnings
    )

def process_assessment_submission(
    assessment_data: Dict[str, Any],
    answers: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """处理评估提交"""
    questions = assessment_data.get("questions", [])
    
    # 验证回答完整性
    validation_result = AssessmentUtils.validate_answers_completeness(answers, questions)
    if not validation_result.is_valid:
        return {
            "success": False,
            "errors": validation_result.errors,
            "warnings": validation_result.warnings
        }
    
    # 计算分数
    total_score = AssessmentUtils.calculate_total_score(answers, questions)
    dimension_scores = AssessmentUtils.calculate_dimension_scores(answers, questions)
    
    # 解释结果
    score_ranges = assessment_data.get("score_ranges", [])
    interpretation = AssessmentUtils.interpret_score(total_score, score_ranges)
    
    # 生成报告
    report = AssessmentUtils.generate_assessment_report(
        assessment_data, total_score, dimension_scores, interpretation
    )
    
    return {
        "success": True,
        "total_score": total_score,
        "dimension_scores": dimension_scores,
        "interpretation": interpretation,
        "report": report
    }