import os
from datetime import datetime
from kivy.app import App
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivymd.app import MDApp
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

from api.api_client import APIClient
from widgets.logo import HealthLogo
from theme import AppTheme, AppMetrics
from utils.user_manager import get_user_manager
from screens.base_screen import BaseScreen

# 使用KV语言定义UI
Builder.load_string('''
<ServiceCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(180)
    md_bg_color: app.theme.CARD_BACKGROUND
    elevation: 6
    radius: [dp(12)]
    padding: dp(16)
    spacing: dp(8)
    ripple_behavior: True

    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(8)
        
        MDIconButton:
            icon: root.icon
            theme_icon_color: "Custom"
            icon_color: app.theme.MEDICAL_SERVICE_COLOR
            icon_size: dp(36)
            pos_hint: {"center_y": 0.5}
            
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            pos_hint: {"center_y": 0.5}
            
    MDLabel:
        text: root.description
        font_style: "Body"
        role: "medium"
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
        
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(8)
        pos_hint: {"right": 1}
        
        Widget:
            size_hint_x: 1
        
        MDButton:
            style: "elevated"
            size_hint_x: None
            width: dp(100)
            md_bg_color: app.theme.PRIMARY_DARK if hasattr(app.theme, 'PRIMARY_DARK') else app.theme.PRIMARY_COLOR
            on_release: root.on_reserve()
            elevation: 4
            radius: [dp(8)]
            
            MDButtonText:
                text: "预约"
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]
                
<AppointmentCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(160)
    md_bg_color: app.theme.CARD_BACKGROUND
    elevation: 6
    radius: [dp(12)]
    padding: dp(16)
    spacing: dp(8)
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(30)
        
        MDLabel:
            text: root.service_type
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Custom"
            text_color: app.theme.PRIMARY_COLOR
            
        MDLabel:
            text: root.status
            font_style: "Body"
            role: "medium"
            halign: "right"
            theme_text_color: "Custom"
            text_color: app.theme.ACCENT_COLOR if root.status == "待确认" else app.theme.SUCCESS_COLOR
            
    # 使用BoxLayout代替MDSeparator
    BoxLayout:
        size_hint_y: None
        height: dp(1)
        canvas.before:
            Color:
                rgba: app.theme.DIVIDER_COLOR
            Rectangle:
                pos: self.pos
                size: self.size
        
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(25)
        
        MDLabel:
            text: "预约日期:"
            font_style: "Label"
            size_hint_x: 0.3
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            
        MDLabel:
            text: root.appointment_date
            font_style: "Label"
            size_hint_x: 0.7
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(25)
        
        MDLabel:
            text: "医院:"
            font_style: "Label"
            size_hint_x: 0.3
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            
        MDLabel:
            text: root.hospital
            font_style: "Label"
            size_hint_x: 0.7
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(8)
        pos_hint: {"right": 1}
        
        Widget:
            size_hint_x: 1
        
        MDButton:
            style: "text"
            size_hint_x: None
            width: dp(80)
            on_release: root.on_detail()
            radius: [dp(8)]
            
            MDButtonText:
                text: "详情"
                theme_text_color: "Custom"
                text_color: app.theme.PRIMARY_COLOR
        
        MDButton:
            style: "elevated"
            size_hint_x: None
            width: dp(80)
            md_bg_color: app.theme.ERROR_COLOR if root.status == "已确认" else app.theme.PRIMARY_COLOR
            on_release: root.on_action()
            elevation: 4
            radius: [dp(8)]

            MDButtonText:
                text: "取消" if root.status == "已确认" else "确认"
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]

<CompanionServiceScreen>:
    BoxLayout:
        orientation: 'vertical'

        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                Rectangle:
                    pos: self.pos
                    size: self.size

            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo

        # 功能标题栏
        BoxLayout:
            size_hint_y: None
            height: dp(60)
            padding: dp(8)
            spacing: dp(8)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_DARK if hasattr(app.theme, 'PRIMARY_DARK') else app.theme.PRIMARY_COLOR
                Rectangle:
                    pos: self.pos
                    size: self.size

            MDIconButton:
                icon: "arrow-left"
                pos_hint: {"center_y": .5}
                icon_color: [1, 1, 1, 1]
                on_release: root.go_back()

            MDLabel:
                text: "陪诊服务"
                font_size: '18sp'
                font_name: "NotoSansBold"
                halign: "center"
                valign: "middle"
                size_hint_x: 1
                theme_text_color: "Custom"
                text_color: [1, 1, 1, 1]

            MDIconButton:
                icon: "information-outline"
                pos_hint: {"center_y": .5}
                icon_color: [1, 1, 1, 1]
                on_release: root.show_info()

        # 主内容区域
        MDBoxLayout:
            orientation: 'vertical'
            padding: dp(16)
            spacing: dp(16)
            canvas.before:
                Color:
                    rgba: [0.97, 0.97, 0.97, 1]
                Rectangle:
                    pos: self.pos
                    size: self.size
                    
            # 服务选项卡
            MDBoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: dp(48)
                spacing: dp(8)
                
                MDButton:
                    id: service_tab
                    style: "elevated" if root.current_tab == "service" else "text"
                    size_hint_x: 0.5
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == "service" else [1, 1, 1, 0]
                    on_release: root.switch_tab("service")
                    elevation: 2 if root.current_tab == "service" else 0
                    radius: [dp(8)]
                    
                    MDButtonText:
                        text: "服务项目"
                        theme_text_color: "Custom"
                        text_color: [1, 1, 1, 1] if root.current_tab == "service" else app.theme.PRIMARY_COLOR
                        
                MDButton:
                    id: appointment_tab
                    style: "elevated" if root.current_tab == "appointment" else "text"
                    size_hint_x: 0.5
                    md_bg_color: app.theme.PRIMARY_COLOR if root.current_tab == "appointment" else [1, 1, 1, 0]
                    on_release: root.switch_tab("appointment")
                    elevation: 2 if root.current_tab == "appointment" else 0
                    radius: [dp(8)]
                    
                    MDButtonText:
                        text: "我的预约"
                        theme_text_color: "Custom"
                        text_color: [1, 1, 1, 1] if root.current_tab == "appointment" else app.theme.PRIMARY_COLOR
            
            # 服务内容区域
            MDScrollView:
                id: content_scroll
                do_scroll_x: False
                
                MDBoxLayout:
                    id: content_layout
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    padding: dp(8)
                    spacing: dp(16)
''')

class ServiceCard(MDCard):
    """服务卡片组件"""
    icon = StringProperty("")
    title = StringProperty("")
    description = StringProperty("")
    service_id = StringProperty("")
    
    def on_reserve(self):
        """预约服务"""
        screen = self.get_screen()
        if screen:
            screen.show_appointment_form(self.service_id, self.title)
    
    def get_screen(self):
        """获取所属的屏幕实例"""
        parent = self.parent
        while parent:
            if isinstance(parent, CompanionServiceScreen):
                return parent
            parent = parent.parent
        return None

class AppointmentCard(MDCard):
    """预约卡片组件"""
    appointment_id = StringProperty("")
    service_type = StringProperty("")
    appointment_date = StringProperty("")
    hospital = StringProperty("")
    status = StringProperty("待确认")  # 待确认、已确认
    
    def on_detail(self):
        """查看详情"""
        screen = self.get_screen()
        if screen:
            screen.show_appointment_detail(self.appointment_id)
    
    def on_action(self):
        """确认或取消预约"""
        screen = self.get_screen()
        if screen:
            if self.status == "待确认":
                screen.confirm_appointment(self.appointment_id)
            else:
                screen.cancel_appointment(self.appointment_id)
    
    def get_screen(self):
        """获取所属的屏幕实例"""
        parent = self.parent
        while parent:
            if isinstance(parent, CompanionServiceScreen):
                return parent
            parent = parent.parent
        return None

class CompanionServiceScreen(BaseScreen):
    """陪诊服务屏幕"""
    current_tab = StringProperty("service")  # service, appointment
    
    def __init__(self, **kwargs):
        super(CompanionServiceScreen, self).__init__(**kwargs)
        self.api_client = APIClient()
        self.service_data = []
        self.appointment_data = []
        
        # 模拟服务数据
        self.service_data = [
            {
                "id": "1",
                "icon": "hospital-building",
                "title": "挂号陪诊",
                "description": "提供挂号、导医、陪同就诊等服务，专业人员全程陪同，解决看病难题。"
            },
            {
                "id": "2",
                "icon": "ambulance",
                "title": "接送服务",
                "description": "提供往返医院的专车接送服务，舒适安全，适合行动不便患者。"
            },
            {
                "id": "3",
                "icon": "bed",
                "title": "住院陪护",
                "description": "提供专业住院陪护服务，24小时照料患者，减轻家属负担。"
            },
            {
                "id": "4",
                "icon": "food",
                "title": "营养餐饮",
                "description": "根据患者病情提供定制营养餐饮，确保住院期间饮食健康。"
            },
            {
                "id": "5",
                "icon": "home",
                "title": "住宿服务",
                "description": "为外地患者及家属提供医院周边舒适住宿，配套设施齐全，交通便利。"
            }
        ]
        
        # 模拟预约数据
        self.appointment_data = [
            {
                "id": "101",
                "service_type": "挂号陪诊",
                "appointment_date": "2023-12-15 上午",
                "hospital": "北京协和医院",
                "status": "已确认",
                "details": {
                    "department": "心内科",
                    "doctor": "张医生",
                    "contact": "李阿姨 (13800138000)",
                    "notes": "需要携带以往的检查报告"
                }
            },
            {
                "id": "102",
                "service_type": "接送服务",
                "appointment_date": "2023-12-20 下午",
                "hospital": "北京同仁医院",
                "status": "待确认",
                "details": {
                    "pickup_address": "朝阳区建国路88号",
                    "contact": "王师傅 (13900139000)",
                    "vehicle": "舒适型轿车",
                    "notes": "需要轮椅"
                }
            }
        ]

    def init_ui(self, dt=0):
        """初始化UI"""
        super().init_ui()
        self.switch_tab(self.current_tab)
        return True
    
    def on_pre_enter(self, *args):
        """进入屏幕前检查登录状态"""
        self.current_user = get_user_manager().get_current_user()
        if not self.current_user:
            self.show_login_required()
            return
    
    def show_login_required(self):
        """显示登录提示对话框"""
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="请先登录",
                halign="center",
            ),
            MDDialogSupportingText(
                text="请登录后再使用陪诊服务。",
                halign="center",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="确定"), style="text", on_release=lambda x: [dialog.dismiss(), self.go_back()]),
                spacing="8dp",
            ),
        )
        dialog.open()
    
    def switch_tab(self, tab_name):
        """切换标签页"""
        self.current_tab = tab_name
        
        # 清空内容区域
        self.ids.content_layout.clear_widgets()
        
        if tab_name == "service":
            self.load_service_list()
        else:
            self.load_appointment_list()
    
    def load_service_list(self):
        """加载服务列表"""
        for service in self.service_data:
            card = ServiceCard(
                service_id=service["id"],
                icon=service["icon"],
                title=service["title"],
                description=service["description"]
            )
            self.ids.content_layout.add_widget(card)
    
    def load_appointment_list(self):
        """加载预约列表"""
        if not self.appointment_data:
            # 显示无预约提示
            label = MDLabel(
                text="暂无预约记录",
                halign="center",
                theme_text_color="Secondary",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(100)
            )
            self.ids.content_layout.add_widget(label)
            return
            
        for appointment in self.appointment_data:
            card = AppointmentCard(
                appointment_id=appointment["id"],
                service_type=appointment["service_type"],
                appointment_date=appointment["appointment_date"],
                hospital=appointment["hospital"],
                status=appointment["status"]
            )
            self.ids.content_layout.add_widget(card)
    
    def show_appointment_form(self, service_id, service_name):
        """显示预约表单"""
        service = next((s for s in self.service_data if s["id"] == service_id), None)
        if not service:
            return
            
        # 创建表单内容
        form_layout = BoxLayout(orientation='vertical', spacing=dp(16), size_hint_y=None)
        
        # 根据服务类型调整表单高度和字段
        is_accommodation = service["id"] == "5"  # 住宿服务ID为5
        form_layout.height = dp(450) if is_accommodation else dp(350)
        
        # 添加表单字段
        form_layout.add_widget(MDLabel(
            text=f"预约{service['title']}服务",
            font_style="Body",
            role="large",
            bold=True,
            size_hint_y=None,
            height=dp(40)
        ))
        
        date_field = MDTextField(
            hint_text="预约日期 (YYYY-MM-DD)",
            helper_text="请选择服务日期",
            supporting_text_mode="on_focus",
            size_hint_y=None,
            height=dp(60)
        )
        form_layout.add_widget(date_field)
        
        time_options = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(50), spacing=dp(10))
        time_options.add_widget(MDLabel(text="时间段:", size_hint_x=0.3))
        
        self.morning_button = MDButton(
            MDButtonText(text="上午"),
            style="outlined",
            size_hint_x=0.35,
            on_release=lambda x: self.select_time_period("morning")
        )
        time_options.add_widget(self.morning_button)
        
        self.afternoon_button = MDButton(
            MDButtonText(text="下午"),
            style="outlined",
            size_hint_x=0.35,
            on_release=lambda x: self.select_time_period("afternoon")
        )
        time_options.add_widget(self.afternoon_button)
        
        form_layout.add_widget(time_options)
        
        hospital_field = MDTextField(
            hint_text="医院名称",
            helper_text="请输入医院全称",
            supporting_text_mode="on_focus",
            size_hint_y=None,
            height=dp(60)
        )
        form_layout.add_widget(hospital_field)
        
        # 为住宿服务添加特定字段
        accommodation_fields = {}
        if is_accommodation:
            # 入住日期
            check_in_field = MDTextField(
                hint_text="入住日期 (YYYY-MM-DD)",
                helper_text="请选择入住日期",
                supporting_text_mode="on_focus",
                size_hint_y=None,
                height=dp(60)
            )
            form_layout.add_widget(check_in_field)
            
            # 退房日期
            check_out_field = MDTextField(
                hint_text="退房日期 (YYYY-MM-DD)",
                helper_text="请选择退房日期",
                supporting_text_mode="on_focus",
                size_hint_y=None,
                height=dp(60)
            )
            form_layout.add_widget(check_out_field)
            
            # 入住人数
            guests_field = MDTextField(
                hint_text="入住人数",
                helper_text="请输入入住人数",
                supporting_text_mode="on_focus",
                size_hint_y=None,
                height=dp(60),
                input_filter="int"
            )
            form_layout.add_widget(guests_field)
            
            # 保存住宿特定字段
            accommodation_fields = {
                'check_in': check_in_field,
                'check_out': check_out_field,
                'guests': guests_field
            }
        
        notes_field = MDTextField(
            hint_text="备注信息",
            helper_text="请填写其他服务需求",
            supporting_text_mode="on_focus",
            multiline=True,
            size_hint_y=None,
            height=dp(80)
        )
        form_layout.add_widget(notes_field)
        
        # 保存表单字段引用
        self.form_fields = {
            'service_id': service_id,
            'date': date_field,
            'time_period': 'morning',  # 默认上午
            'hospital': hospital_field,
            'notes': notes_field,
            **accommodation_fields  # 添加住宿特定字段
        }
        
        # 创建并显示对话框 - 使用KivyMD 2.0.1 dev0风格
        self.form_dialog = MDDialog(
            MDDialogHeadlineText(
                text="服务预约",
                halign="center",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="取消"), style="text", on_release=lambda x: self.form_dialog.dismiss()),
                MDButton(MDButtonText(text="提交预约"), style="text", on_release=self.submit_appointment),
                spacing="8dp",
            ),
        )
        # 添加内容
        self.form_dialog.ids.container.add_widget(form_layout)
        self.form_dialog.open()
    
    def select_time_period(self, period):
        """选择时间段"""
        self.form_fields['time_period'] = period
        
        # 更新按钮样式
        self.morning_button.style = "filled" if period == "morning" else "outlined"
        self.afternoon_button.style = "filled" if period == "afternoon" else "outlined"
    
    def submit_appointment(self, *args):
        """提交预约"""
        # 验证表单
        date_text = self.form_fields['date'].text.strip()
        hospital_text = self.form_fields['hospital'].text.strip()
        
        if not date_text:
            self.show_snackbar("请选择预约日期")
            return
            
        if not hospital_text:
            self.show_snackbar("请输入医院名称")
            return
        
        # 获取服务信息
        service = next((s for s in self.service_data if s["id"] == self.form_fields['service_id']), None)
        if not service:
            self.form_dialog.dismiss()
            return
        
        # 检查是否为住宿服务，验证特定字段
        is_accommodation = service["id"] == "5"
        if is_accommodation:
            check_in_text = self.form_fields.get('check_in', '').text.strip() if 'check_in' in self.form_fields else ''
            check_out_text = self.form_fields.get('check_out', '').text.strip() if 'check_out' in self.form_fields else ''
            guests_text = self.form_fields.get('guests', '').text.strip() if 'guests' in self.form_fields else ''
            
            if not check_in_text:
                self.show_snackbar("请选择入住日期")
                return
                
            if not check_out_text:
                self.show_snackbar("请选择退房日期")
                return
                
            if not guests_text:
                self.show_snackbar("请输入入住人数")
                return
            
        # 构建预约数据
        time_period = "上午" if self.form_fields['time_period'] == "morning" else "下午"
        appointment = {
            "id": f"10{len(self.appointment_data) + 3}",
            "service_type": service["title"],
            "appointment_date": f"{date_text} {time_period}",
            "hospital": hospital_text,
            "status": "待确认",
            "details": {
                "notes": self.form_fields['notes'].text.strip()
            }
        }
        
        # 为住宿服务添加特定详情
        if is_accommodation:
            appointment["details"].update({
                "check_in": self.form_fields['check_in'].text.strip(),
                "check_out": self.form_fields['check_out'].text.strip(),
                "guests": self.form_fields['guests'].text.strip(),
                "accommodation_type": "标准双人间"  # 默认房型
            })
        
        # 添加到预约列表
        self.appointment_data.append(appointment)
        
        # 关闭对话框
        self.form_dialog.dismiss()
        
        # 显示成功消息
        self.show_snackbar("预约提交成功，请等待确认")
        
        # 切换到预约标签页
        self.switch_tab("appointment")
    
    def show_appointment_detail(self, appointment_id):
        """显示预约详情"""
        appointment = next((a for a in self.appointment_data if a["id"] == appointment_id), None)
        if not appointment:
            return
            
        # 创建详情内容
        detail_layout = BoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None)
        detail_layout.height = dp(300)
        
        # 添加详情字段
        detail_layout.add_widget(MDLabel(
            text=appointment["service_type"],
            font_style="Body",
            role="large",
            bold=True,
            size_hint_y=None,
            height=dp(30)
        ))
        
        fields = [
            ("预约日期", appointment["appointment_date"]),
            ("医院", appointment["hospital"]),
            ("状态", appointment["status"])
        ]
        
        # 添加详情中的其他字段
        details = appointment.get("details", {})
        for key, value in details.items():
            if key == "department" and value:
                fields.append(("科室", value))
            elif key == "doctor" and value:
                fields.append(("医生", value))
            elif key == "contact" and value:
                fields.append(("联系人", value))
            elif key == "pickup_address" and value:
                fields.append(("接送地址", value))
            elif key == "vehicle" and value:
                fields.append(("车型", value))
            elif key == "notes" and value:
                fields.append(("备注", value))
        
        for label, value in fields:
            field_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(30))
            field_layout.add_widget(MDLabel(
                text=f"{label}:",
                font_style="Body",
                role="small",
                bold=True,
                size_hint_x=0.3
            ))
            field_layout.add_widget(MDLabel(
                text=value,
                font_style="Body",
                role="small",
                size_hint_x=0.7
            ))
            detail_layout.add_widget(field_layout)
        
        # 创建并显示对话框 - 使用KivyMD 2.0.1 dev0风格
        detail_dialog = MDDialog(
            MDDialogHeadlineText(
                text="预约详情",
                halign="center",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="关闭"), style="text", on_release=lambda x: detail_dialog.dismiss()),
                spacing="8dp",
            ),
        )
        # 添加内容
        detail_dialog.ids.container.add_widget(detail_layout)
        detail_dialog.open()
    
    def confirm_appointment(self, appointment_id):
        """确认预约"""
        appointment = next((a for a in self.appointment_data if a["id"] == appointment_id), None)
        if not appointment:
            return
            
        # 更新状态
        appointment["status"] = "已确认"
        
        # 刷新UI
        self.switch_tab("appointment")
        
        # 显示确认消息
        self.show_snackbar("预约已确认")
    
    def cancel_appointment(self, appointment_id):
        """取消预约"""
        # 确认取消对话框
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="取消预约",
                halign="center",
            ),
            MDDialogSupportingText(
                text="确定要取消此预约吗？",
                halign="center",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="否"), style="text", on_release=lambda x: dialog.dismiss()),
                MDButton(MDButtonText(text="是"), style="text", on_release=lambda x: [dialog.dismiss(), self._do_cancel_appointment(appointment_id)]),
                spacing="8dp",
            ),
        )
        dialog.open()
    
    def _do_cancel_appointment(self, appointment_id):
        """执行取消预约"""
        # 从列表中移除
        self.appointment_data = [a for a in self.appointment_data if a["id"] != appointment_id]
        
        # 刷新UI
        self.switch_tab("appointment")
        
        # 显示取消消息
        self.show_snackbar("预约已取消")
    
    def show_info(self):
        """显示帮助信息"""
        help_text = """
陪诊服务使用指南:

1. 在"服务项目"标签页可以浏览所有可用的陪诊服务
2. 点击"预约"按钮可以预约相应的服务
3. 在"我的预约"标签页可以查看已预约的服务
4. 预约提交后状态为"待确认"，需要点击"确认"按钮确认预约
5. 已确认的预约可以点击"取消"按钮取消
6. 点击"详情"按钮可以查看预约的详细信息

服务热线: 400-123-4567
工作时间: 周一至周日 8:00-20:00
"""

        dialog = MDDialog(
            MDDialogHeadlineText(
                text="使用指南",
                halign="center",
            ),
            MDDialogSupportingText(
                text=help_text,
                halign="left",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="了解"), style="text", on_release=lambda x: dialog.dismiss()),
                spacing="8dp",
            ),
        )
        dialog.open()
    
    def show_snackbar(self, message):
        """显示Snackbar消息"""
        snackbar = MDSnackbar(
            MDSnackbarText(text=message),
            pos_hint={"center_x": 0.5},
            duration=2,
        )
        snackbar.open()
    
    def go_back(self):
        """返回上一屏幕"""
        app = App.get_running_app()
        app.root.current = "homepage_screen"