#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复脚本：将MDBoxLayout功能区域中的font_style: "Body" + role: "small"组合替换为font_style: "Label"

修复范围：mobile文件夹下所有的Python脚本文件
注意：保持原有的缩进格式
"""

import os
import re
import shutil
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('font_style_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FontStyleFixer:
    def __init__(self, mobile_dir):
        self.mobile_dir = mobile_dir
        self.backup_dir = os.path.join(mobile_dir, 'backup_font_fix_' + datetime.now().strftime('%Y%m%d_%H%M%S'))
        self.fixed_files = []
        self.total_fixes = 0
        
    def create_backup(self, file_path):
        """为文件创建备份"""
        try:
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)
            
            # 保持相对路径结构
            rel_path = os.path.relpath(file_path, self.mobile_dir)
            backup_path = os.path.join(self.backup_dir, rel_path)
            backup_dir = os.path.dirname(backup_path)
            
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
                
            shutil.copy2(file_path, backup_path)
            logger.info(f"已备份文件: {rel_path}")
            return True
        except Exception as e:
            logger.error(f"备份文件失败 {file_path}: {e}")
            return False
    
    def fix_font_style_in_content(self, content):
        """修复文件内容中的font_style问题"""
        lines = content.split('\n')
        fixed_lines = []
        fixes_count = 0
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # 检查当前行是否包含 font_style: "Body"
            if 'font_style:' in line and '"Body"' in line:
                # 获取当前行的缩进
                indent = len(line) - len(line.lstrip())
                
                # 查找接下来的几行中是否有 role: "small"
                found_role_small = False
                role_line_index = -1
                
                # 向前查找（最多查找10行）
                for j in range(max(0, i-10), i):
                    if 'role:' in lines[j] and '"small"' in lines[j]:
                        # 检查缩进是否相似（允许一定的缩进差异）
                        prev_indent = len(lines[j]) - len(lines[j].lstrip())
                        if abs(prev_indent - indent) <= 4:  # 允许4个空格的缩进差异
                            found_role_small = True
                            role_line_index = j
                            break
                
                # 向后查找（最多查找10行）
                if not found_role_small:
                    for j in range(i+1, min(len(lines), i+11)):
                        if 'role:' in lines[j] and '"small"' in lines[j]:
                            # 检查缩进是否相似
                            next_indent = len(lines[j]) - len(lines[j].lstrip())
                            if abs(next_indent - indent) <= 4:  # 允许4个空格的缩进差异
                                found_role_small = True
                                role_line_index = j
                                break
                
                if found_role_small:
                    # 替换 font_style: "Body" 为 font_style: "Label"
                    new_line = re.sub(r'font_style:\s*["\']Body["\']', 'font_style: "Label"', line)
                    fixed_lines.append(new_line)
                    
                    # 移除对应的 role: "small" 行
                    if role_line_index > i:  # role在后面
                        # 先添加当前到role之前的所有行
                        for k in range(i+1, role_line_index):
                            fixed_lines.append(lines[k])
                        # 跳过role行
                        i = role_line_index
                        fixes_count += 1
                        logger.info(f"修复: 第{i+1}行 font_style: 'Body' + role: 'small' -> font_style: 'Label'")
                    elif role_line_index < i:  # role在前面
                        # role行已经被处理过了，只需要跳过当前行
                        fixes_count += 1
                        logger.info(f"修复: 第{i+1}行 font_style: 'Body' + role: 'small' -> font_style: 'Label'")
                    else:
                        fixed_lines.append(new_line)
                        fixes_count += 1
                        logger.info(f"修复: 第{i+1}行 font_style: 'Body' + role: 'small' -> font_style: 'Label'")
                else:
                    # 没有找到对应的role: "small"，保持原样
                    fixed_lines.append(line)
            
            # 检查当前行是否是需要移除的 role: "small" 行
            elif 'role:' in line and '"small"' in line:
                # 检查前面是否有对应的 font_style: "Body"
                indent = len(line) - len(line.lstrip())
                found_font_body = False
                
                # 向前查找
                for j in range(max(0, i-10), i):
                    if 'font_style:' in lines[j] and '"Body"' in lines[j]:
                        prev_indent = len(lines[j]) - len(lines[j].lstrip())
                        if abs(prev_indent - indent) <= 4:
                            found_font_body = True
                            break
                
                # 向后查找
                if not found_font_body:
                    for j in range(i+1, min(len(lines), i+11)):
                        if 'font_style:' in lines[j] and '"Body"' in lines[j]:
                            next_indent = len(lines[j]) - len(lines[j].lstrip())
                            if abs(next_indent - indent) <= 4:
                                found_font_body = True
                                break
                
                if not found_font_body:
                    # 没有找到对应的font_style: "Body"，保持原样
                    fixed_lines.append(line)
                # 如果找到了对应的font_style: "Body"，则跳过这行（已经在处理font_style时处理了）
            else:
                # 普通行，保持原样
                fixed_lines.append(line)
            
            i += 1
        
        return '\n'.join(fixed_lines), fixes_count
    
    def fix_file(self, file_path):
        """修复单个文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复内容
            fixed_content, fixes_count = self.fix_font_style_in_content(content)
            
            if fixes_count > 0:
                # 创建备份
                if self.create_backup(file_path):
                    # 写入修复后的内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    
                    rel_path = os.path.relpath(file_path, self.mobile_dir)
                    self.fixed_files.append(rel_path)
                    self.total_fixes += fixes_count
                    logger.info(f"✅ 修复完成: {rel_path} (修复了 {fixes_count} 处)")
                    return True
                else:
                    logger.error(f"❌ 备份失败，跳过修复: {file_path}")
                    return False
            else:
                logger.debug(f"⏭️  无需修复: {os.path.relpath(file_path, self.mobile_dir)}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 修复文件失败 {file_path}: {e}")
            return False
    
    def scan_and_fix(self):
        """扫描并修复mobile文件夹下的所有Python文件"""
        logger.info(f"开始扫描目录: {self.mobile_dir}")
        
        python_files = []
        
        # 遍历mobile目录下的所有Python文件
        for root, dirs, files in os.walk(self.mobile_dir):
            # 跳过备份目录和虚拟环境目录
            dirs[:] = [d for d in dirs if not d.startswith(('.', '__pycache__', 'backup', 'venv', '.venv'))]
            
            for file in files:
                if file.endswith('.py') and not file.startswith('.'):
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)
        
        logger.info(f"找到 {len(python_files)} 个Python文件")
        
        # 修复每个文件
        success_count = 0
        for file_path in python_files:
            if self.fix_file(file_path):
                success_count += 1
        
        # 输出总结
        logger.info("\n" + "="*60)
        logger.info("修复完成总结:")
        logger.info(f"扫描文件总数: {len(python_files)}")
        logger.info(f"成功处理文件: {success_count}")
        logger.info(f"修复的文件数量: {len(self.fixed_files)}")
        logger.info(f"总修复次数: {self.total_fixes}")
        
        if self.fixed_files:
            logger.info("\n修复的文件列表:")
            for file in self.fixed_files:
                logger.info(f"  - {file}")
            logger.info(f"\n备份目录: {self.backup_dir}")
        else:
            logger.info("\n没有找到需要修复的文件")
        
        logger.info("="*60)
        
        return len(self.fixed_files), self.total_fixes

def main():
    """主函数"""
    # 获取mobile目录路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    mobile_dir = script_dir  # 脚本就在mobile目录下
    
    if not os.path.exists(mobile_dir):
        logger.error(f"Mobile目录不存在: {mobile_dir}")
        return
    
    logger.info("🚀 开始执行font_style修复脚本")
    logger.info(f"目标目录: {mobile_dir}")
    
    # 创建修复器并执行修复
    fixer = FontStyleFixer(mobile_dir)
    fixed_files_count, total_fixes = fixer.scan_and_fix()
    
    if fixed_files_count > 0:
        logger.info(f"\n🎉 修复完成！共修复了 {fixed_files_count} 个文件，总计 {total_fixes} 处修改")
    else:
        logger.info("\n✨ 所有文件都已经是正确的格式，无需修复")

if __name__ == "__main__":
    main()