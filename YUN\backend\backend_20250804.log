2025-08-04 11:18:42,654 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-04 11:18:42,659 - auth_service - INFO - 统一认证服务初始化完成
2025-08-04 11:18:42,768 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-04 11:18:42,770 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-04 11:18:43,280 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-08-04 11:18:44,113 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-08-04 11:18:44,116 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-08-04 11:18:44,118 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-08-04 11:18:44,207 - health_monitor - INFO - 健康监控器初始化完成
2025-08-04 11:18:44,217 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-08-04 11:18:44,221 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-08-04 11:18:44,223 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-08-04 11:18:44,232 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-08-04 11:18:44,233 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-08-04 11:18:44,236 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-04 11:18:44,247 - alert_manager - INFO - 已初始化默认告警规则
2025-08-04 11:18:44,248 - alert_manager - INFO - 已初始化默认通知渠道
2025-08-04 11:18:44,250 - alert_manager - INFO - 告警管理器初始化完成
2025-08-04 11:18:45,084 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-08-04 11:18:45,087 - db_service - INFO - 数据库服务初始化完成
2025-08-04 11:18:45,097 - notification_service - INFO - 通知服务初始化完成
2025-08-04 11:18:45,098 - main - INFO - 错误处理模块导入成功
2025-08-04 11:18:45,151 - main - INFO - 监控模块导入成功
2025-08-04 11:18:45,152 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-08-04 11:18:48,047 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-08-04 11:18:48,080 - main - INFO - 应用启动中...
2025-08-04 11:18:48,083 - error_handling - INFO - 错误处理已设置
2025-08-04 11:18:48,086 - main - INFO - 错误处理系统初始化完成
2025-08-04 11:18:48,087 - monitoring - INFO - 添加指标端点成功: /metrics
2025-08-04 11:18:48,089 - monitoring - INFO - 添加健康检查端点成功: /health
2025-08-04 11:18:48,090 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-08-04 11:18:48,092 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-08-04 11:18:48,099 - monitoring - INFO - 启动资源监控线程成功
2025-08-04 11:18:48,102 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-08-04 11:18:48,104 - monitoring - INFO - 监控系统初始化完成
2025-08-04 11:18:48,105 - main - INFO - 监控系统初始化完成
2025-08-04 11:18:48,107 - app.db.init_db - INFO - 所有模型导入成功
2025-08-04 11:18:48,109 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-04 11:18:48,123 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:18:48,124 - app.db.init_db - INFO - 所有模型导入成功
2025-08-04 11:18:48,128 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-04 11:18:48,131 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-08-04 11:18:48,134 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-08-04 11:18:48,136 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 11:18:48,138 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-08-04 11:18:48,140 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,149 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-08-04 11:18:48,151 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,153 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-08-04 11:18:48,155 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,157 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-08-04 11:18:48,162 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,164 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-08-04 11:18:48,166 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,168 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-08-04 11:18:48,170 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,172 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-08-04 11:18:48,173 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,180 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-08-04 11:18:48,182 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,184 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-08-04 11:18:48,186 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,188 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-08-04 11:18:48,190 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,197 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-08-04 11:18:48,198 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 78.6%
2025-08-04 11:18:48,200 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-08-04 11:18:48,203 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,205 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-08-04 11:18:48,212 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,214 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-08-04 11:18:48,216 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,218 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-08-04 11:18:48,219 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,224 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-08-04 11:18:48,226 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,229 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-08-04 11:18:48,230 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,233 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-08-04 11:18:48,234 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,241 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-08-04 11:18:48,244 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-08-04 11:18:48,249 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,254 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-08-04 11:18:48,256 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,259 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-08-04 11:18:48,261 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,263 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-08-04 11:18:48,265 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,267 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-08-04 11:18:48,272 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,273 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-08-04 11:18:48,275 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,278 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-08-04 11:18:48,280 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,282 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-08-04 11:18:48,286 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,288 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-08-04 11:18:48,290 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,292 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-08-04 11:18:48,294 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,296 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-08-04 11:18:48,298 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,303 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-08-04 11:18:48,305 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,307 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-08-04 11:18:48,308 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,312 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-08-04 11:18:48,313 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,318 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-08-04 11:18:48,320 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,322 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-08-04 11:18:48,323 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,325 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-08-04 11:18:48,327 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,329 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-08-04 11:18:48,334 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,336 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-08-04 11:18:48,337 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,339 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-08-04 11:18:48,340 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,342 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-08-04 11:18:48,345 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,351 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-08-04 11:18:48,352 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,354 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-08-04 11:18:48,355 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 11:18:48,359 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 11:18:48,363 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-08-04 11:18:48,367 - app.db.init_db - INFO - 模型关系初始化完成
2025-08-04 11:18:48,369 - app.db.init_db - INFO - 模型关系设置完成
2025-08-04 11:18:48,370 - main - INFO - 数据库初始化完成（强制重建）
2025-08-04 11:18:48,371 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:18:48,372 - main - INFO - 数据库连接正常
2025-08-04 11:18:48,373 - main - INFO - 开始初始化模板数据
2025-08-04 11:18:48,374 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:18:48,722 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-08-04 11:18:48,765 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-08-04 11:18:48,833 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-08-04 11:18:48,878 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-08-04 11:18:48,880 - main - INFO - 模板数据初始化完成
2025-08-04 11:18:48,882 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-08-04 11:18:48,884 - main - INFO - 应用启动完成
2025-08-04 11:18:56,837 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 11:18:56,838 - main - INFO - 请求没有认证头部
2025-08-04 11:18:56,839 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:18:56,840 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:18:58,893 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 11:18:58,893 - main - INFO - 请求没有认证头部
2025-08-04 11:18:58,894 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:18:58,896 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 11:18:58,897 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 11:18:58,899 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 11:18:58,900 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 11:18:58,901 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:18:58,902 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 11:18:59,666 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 11:18:59,672 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:19:03,306 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 29.2%
2025-08-04 11:19:18,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 17.9%
2025-08-04 11:19:33,514 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 0.0%
2025-08-04 11:19:45,140 - health_monitor - DEBUG - 系统指标 - CPU: 25.4%, 内存: 56.2%, 磁盘: 84.1%
2025-08-04 11:19:48,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 12.5%
2025-08-04 11:20:03,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 91.7%
2025-08-04 11:20:19,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 100.0%
2025-08-04 11:20:34,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 48.3%
2025-08-04 11:20:46,161 - health_monitor - DEBUG - 系统指标 - CPU: 29.1%, 内存: 55.0%, 磁盘: 84.1%
2025-08-04 11:20:49,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 44.0%
2025-08-04 11:21:04,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.5%, CPU使用率 96.4%
2025-08-04 11:21:19,457 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 33.3%
2025-08-04 11:21:34,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 37.0%
2025-08-04 11:21:47,182 - health_monitor - DEBUG - 系统指标 - CPU: 34.5%, 内存: 55.1%, 磁盘: 84.1%
2025-08-04 11:21:49,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 41.7%
2025-08-04 11:22:04,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 41.7%
2025-08-04 11:22:19,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 34.6%
2025-08-04 11:22:35,005 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 85.7%
2025-08-04 11:22:48,203 - health_monitor - DEBUG - 系统指标 - CPU: 42.5%, 内存: 54.9%, 磁盘: 84.1%
2025-08-04 11:22:50,110 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 22.2%
2025-08-04 11:23:05,213 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 45.8%
2025-08-04 11:23:20,319 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 41.7%
2025-08-04 11:23:35,445 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 100.0%
2025-08-04 11:23:49,231 - health_monitor - DEBUG - 系统指标 - CPU: 69.8%, 内存: 55.3%, 磁盘: 84.1%
2025-08-04 11:23:50,550 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 28.6%
2025-08-04 11:24:05,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 32.1%
2025-08-04 11:24:20,827 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.4%, CPU使用率 88.2%
2025-08-04 11:24:35,932 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 64.0%
2025-08-04 11:24:44,253 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-08-04 11:24:50,253 - health_monitor - DEBUG - 系统指标 - CPU: 65.5%, 内存: 55.8%, 磁盘: 84.1%
2025-08-04 11:24:51,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.9%, CPU使用率 92.3%
2025-08-04 11:25:06,403 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 100.0%
2025-08-04 11:25:21,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 100.0%
2025-08-04 11:25:36,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 75.0%
2025-08-04 11:25:51,519 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 58.7%, 磁盘: 84.1%
2025-08-04 11:25:52,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.6%, CPU使用率 100.0%
2025-08-04 11:26:07,456 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 64.3%
2025-08-04 11:26:22,576 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.1%, CPU使用率 78.6%
2025-08-04 11:26:37,688 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 53.3%
2025-08-04 11:26:52,738 - health_monitor - DEBUG - 系统指标 - CPU: 58.5%, 内存: 58.6%, 磁盘: 84.1%
2025-08-04 11:26:52,793 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.6%, CPU使用率 66.7%
2025-08-04 11:27:07,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 66.7%
2025-08-04 11:27:23,006 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 88.0%
2025-08-04 11:27:29,109 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 11:27:29,111 - main - INFO - 请求没有认证头部
2025-08-04 11:27:29,111 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:27:29,113 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:27:31,139 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 11:27:31,142 - main - INFO - 请求没有认证头部
2025-08-04 11:27:31,142 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:27:31,145 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 11:27:31,145 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 11:27:31,147 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 11:27:31,147 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 11:27:31,148 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:27:31,149 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 11:27:33,398 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 11:27:33,401 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:27:38,145 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 92.0%
2025-08-04 11:27:53,251 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 60.7%
2025-08-04 11:27:53,772 - health_monitor - DEBUG - 系统指标 - CPU: 68.5%, 内存: 62.9%, 磁盘: 84.1%
2025-08-04 11:28:08,430 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-08-04 11:28:23,556 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 45.8%
2025-08-04 11:28:38,661 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 37.5%
2025-08-04 11:28:53,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 86.2%
2025-08-04 11:28:54,841 - health_monitor - DEBUG - 系统指标 - CPU: 71.5%, 内存: 62.8%, 磁盘: 84.1%
2025-08-04 11:29:08,890 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 59.3%
2025-08-04 11:29:24,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-08-04 11:29:39,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 16.7%
2025-08-04 11:29:54,620 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 95.8%
2025-08-04 11:29:56,006 - health_monitor - DEBUG - 系统指标 - CPU: 89.7%, 内存: 63.1%, 磁盘: 84.1%
2025-08-04 11:30:09,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 75.0%
2025-08-04 11:30:24,830 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 89.3%
2025-08-04 11:30:39,937 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 48.1%
2025-08-04 11:30:55,041 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 41.7%
2025-08-04 11:30:57,043 - health_monitor - DEBUG - 系统指标 - CPU: 35.8%, 内存: 59.2%, 磁盘: 84.1%
2025-08-04 11:31:10,146 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 20.8%
2025-08-04 11:31:25,249 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 15.4%
2025-08-04 11:31:40,355 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 58.3%
2025-08-04 11:31:55,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 29.2%
2025-08-04 11:31:58,064 - health_monitor - DEBUG - 系统指标 - CPU: 30.0%, 内存: 59.2%, 磁盘: 84.1%
2025-08-04 11:32:10,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 31.0%
2025-08-04 11:32:25,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 35.7%
2025-08-04 11:32:40,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 89.3%
2025-08-04 11:32:55,929 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 45.8%
2025-08-04 11:32:59,129 - health_monitor - DEBUG - 系统指标 - CPU: 47.1%, 内存: 60.4%, 磁盘: 84.1%
2025-08-04 11:33:11,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 96.6%
2025-08-04 11:33:26,140 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 34.5%
2025-08-04 11:33:41,245 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 48.0%
2025-08-04 11:33:56,350 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 41.7%
2025-08-04 11:34:00,151 - health_monitor - DEBUG - 系统指标 - CPU: 61.2%, 内存: 60.7%, 磁盘: 84.1%
2025-08-04 11:34:11,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 98.1%
2025-08-04 11:34:26,716 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 89.7%
2025-08-04 11:34:41,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-08-04 11:34:55,047 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 11:34:55,047 - main - INFO - 请求没有认证头部
2025-08-04 11:34:55,049 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:34:55,052 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:34:56,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 27.6%
2025-08-04 11:34:57,091 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 11:34:57,093 - main - INFO - 请求没有认证头部
2025-08-04 11:34:57,093 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:34:57,095 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 11:34:57,096 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 11:34:57,099 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 11:34:57,100 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 11:34:57,102 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:34:57,102 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 11:34:59,374 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 11:34:59,376 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:35:01,442 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 62.2%, 磁盘: 84.1%
2025-08-04 11:35:12,162 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-08-04 11:35:27,384 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 100.0%
2025-08-04 11:35:42,491 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 50.0%
2025-08-04 11:35:57,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 3.8%
2025-08-04 11:36:02,509 - health_monitor - DEBUG - 系统指标 - CPU: 28.7%, 内存: 62.7%, 磁盘: 84.1%
2025-08-04 11:36:12,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 33.3%
2025-08-04 11:36:27,807 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 4.2%
2025-08-04 11:36:42,912 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 14.3%
2025-08-04 11:36:58,018 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 64.3%
2025-08-04 11:37:03,560 - health_monitor - DEBUG - 系统指标 - CPU: 41.4%, 内存: 59.9%, 磁盘: 84.1%
2025-08-04 11:37:13,123 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 48.1%
2025-08-04 11:37:28,228 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 20.8%
2025-08-04 11:37:43,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 64.0%
2025-08-04 11:37:58,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 40.7%
2025-08-04 11:38:04,583 - health_monitor - DEBUG - 系统指标 - CPU: 43.5%, 内存: 59.8%, 磁盘: 84.1%
2025-08-04 11:38:13,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 40.0%
2025-08-04 11:38:28,647 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 16.7%
2025-08-04 11:38:43,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 27.6%
2025-08-04 11:38:58,858 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 30.8%
2025-08-04 11:39:05,603 - health_monitor - DEBUG - 系统指标 - CPU: 38.9%, 内存: 59.7%, 磁盘: 84.1%
2025-08-04 11:39:13,962 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 16.7%
2025-08-04 11:39:29,071 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 50.0%
2025-08-04 11:39:31,505 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 11:39:31,506 - main - INFO - 请求没有认证头部
2025-08-04 11:39:31,507 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:39:31,509 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:39:33,530 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 11:39:33,531 - main - INFO - 请求没有认证头部
2025-08-04 11:39:33,532 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:39:33,533 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 11:39:33,534 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 11:39:33,535 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 11:39:33,536 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 11:39:33,537 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:39:33,538 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 11:39:34,399 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 11:39:34,401 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:39:44,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 89.3%
2025-08-04 11:39:59,330 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 60.7%
2025-08-04 11:40:06,625 - health_monitor - DEBUG - 系统指标 - CPU: 18.7%, 内存: 64.3%, 磁盘: 84.1%
2025-08-04 11:40:14,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 86.7%
2025-08-04 11:40:29,547 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 25.0%
2025-08-04 11:40:44,651 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 8.3%
2025-08-04 11:40:59,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 33.3%
2025-08-04 11:41:07,647 - health_monitor - DEBUG - 系统指标 - CPU: 32.3%, 内存: 60.6%, 磁盘: 84.1%
2025-08-04 11:41:14,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 29.6%
2025-08-04 11:41:30,074 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 73.8%
2025-08-04 11:41:45,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 27.6%
2025-08-04 11:42:00,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 17.9%
2025-08-04 11:42:08,670 - health_monitor - DEBUG - 系统指标 - CPU: 69.8%, 内存: 61.8%, 磁盘: 84.1%
2025-08-04 11:42:15,399 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 56.0%
2025-08-04 11:42:30,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 42.3%
2025-08-04 11:42:45,697 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 100.0%
2025-08-04 11:43:01,188 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 100.0%
2025-08-04 11:43:09,718 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 62.2%, 磁盘: 84.1%
2025-08-04 11:43:16,397 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 54.2%
2025-08-04 11:43:31,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 78.6%
2025-08-04 11:43:46,687 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 100.0%
2025-08-04 11:44:01,940 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 91.4%
2025-08-04 11:44:10,953 - health_monitor - DEBUG - 系统指标 - CPU: 77.6%, 内存: 62.7%, 磁盘: 84.1%
2025-08-04 11:44:17,051 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 79.2%
2025-08-04 11:44:32,421 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 98.3%
2025-08-04 11:44:47,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 79.3%
2025-08-04 11:45:02,668 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 87.5%
2025-08-04 11:45:12,052 - health_monitor - DEBUG - 系统指标 - CPU: 79.8%, 内存: 62.8%, 磁盘: 84.1%
2025-08-04 11:45:17,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 96.2%
2025-08-04 11:45:33,570 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-08-04 11:45:49,088 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 100.0%
2025-08-04 11:46:04,582 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 100.0%
2025-08-04 11:46:13,414 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 63.6%, 磁盘: 84.1%
2025-08-04 11:46:19,985 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 100.0%
2025-08-04 11:46:35,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 100.0%
2025-08-04 11:46:51,024 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-08-04 11:47:06,265 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 85.7%
2025-08-04 11:47:14,453 - health_monitor - DEBUG - 系统指标 - CPU: 78.8%, 内存: 63.3%, 磁盘: 84.1%
2025-08-04 11:47:21,375 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 64.3%
2025-08-04 11:47:36,538 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 100.0%
2025-08-04 11:47:51,644 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 85.7%
2025-08-04 11:48:06,757 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 89.3%
2025-08-04 11:48:15,479 - health_monitor - DEBUG - 系统指标 - CPU: 89.9%, 内存: 63.2%, 磁盘: 84.1%
2025-08-04 11:48:21,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 89.7%
2025-08-04 11:48:36,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 85.7%
2025-08-04 11:48:52,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 82.1%
2025-08-04 11:49:07,222 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 79.3%
2025-08-04 11:49:16,544 - health_monitor - DEBUG - 系统指标 - CPU: 75.2%, 内存: 63.2%, 磁盘: 84.1%
2025-08-04 11:49:22,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 64.3%
2025-08-04 11:49:37,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 89.7%
2025-08-04 11:49:52,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 100.0%
2025-08-04 11:50:07,832 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 100.0%
2025-08-04 11:50:17,573 - health_monitor - DEBUG - 系统指标 - CPU: 88.7%, 内存: 63.8%, 磁盘: 84.1%
2025-08-04 11:50:22,944 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 83.3%
2025-08-04 11:50:38,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 86.2%
2025-08-04 11:50:53,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 70.8%
2025-08-04 11:51:08,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 85.7%
2025-08-04 11:51:18,875 - health_monitor - DEBUG - 系统指标 - CPU: 82.2%, 内存: 64.1%, 磁盘: 84.1%
2025-08-04 11:51:23,389 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 60.7%
2025-08-04 11:51:38,495 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 76.0%
2025-08-04 11:51:53,608 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 67.9%
2025-08-04 11:52:08,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 88.6%
2025-08-04 11:52:19,949 - health_monitor - DEBUG - 系统指标 - CPU: 89.7%, 内存: 63.4%, 磁盘: 84.1%
2025-08-04 11:52:23,883 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 72.0%
2025-08-04 11:52:38,991 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 79.2%
2025-08-04 11:52:54,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 67.9%
2025-08-04 11:53:09,201 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 78.6%
2025-08-04 11:53:21,070 - health_monitor - DEBUG - 系统指标 - CPU: 92.7%, 内存: 63.6%, 磁盘: 84.1%
2025-08-04 11:53:24,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 78.6%
2025-08-04 11:53:39,421 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 92.9%
2025-08-04 11:53:54,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 93.1%
2025-08-04 11:54:09,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 100.0%
2025-08-04 11:54:22,132 - health_monitor - DEBUG - 系统指标 - CPU: 87.0%, 内存: 63.8%, 磁盘: 84.1%
2025-08-04 11:54:24,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 80.8%
2025-08-04 11:54:39,984 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 72.4%
2025-08-04 11:54:55,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 52.0%
2025-08-04 11:55:10,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 100.0%
2025-08-04 11:55:23,176 - health_monitor - DEBUG - 系统指标 - CPU: 82.3%, 内存: 63.2%, 磁盘: 84.1%
2025-08-04 11:55:25,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 87.8%
2025-08-04 11:55:40,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 28.6%
2025-08-04 11:55:55,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 29.2%
2025-08-04 11:56:10,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 14.3%
2025-08-04 11:56:23,770 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 11:56:23,771 - main - INFO - 请求没有认证头部
2025-08-04 11:56:23,771 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:56:23,773 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:56:24,279 - health_monitor - DEBUG - 系统指标 - CPU: 7.8%, 内存: 62.2%, 磁盘: 84.1%
2025-08-04 11:56:25,794 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 11:56:25,795 - main - INFO - 请求没有认证头部
2025-08-04 11:56:25,796 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 11:56:25,798 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 11:56:25,799 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 11:56:25,800 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 11:56:25,801 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 11:56:25,802 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 11:56:25,803 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 11:56:26,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 53.6%
2025-08-04 11:56:26,763 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 11:56:26,765 - main - INFO - --- 请求结束: 200 ---

2025-08-04 11:56:41,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 4.2%
2025-08-04 11:56:56,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 45.5%
2025-08-04 11:57:11,422 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 0.0%
2025-08-04 11:57:25,304 - health_monitor - DEBUG - 系统指标 - CPU: 12.2%, 内存: 63.1%, 磁盘: 84.1%
2025-08-04 11:57:26,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 8.3%
2025-08-04 11:57:41,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 12.5%
2025-08-04 11:57:56,738 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 19.2%
2025-08-04 11:58:11,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 32.1%
2025-08-04 11:58:26,325 - health_monitor - DEBUG - 系统指标 - CPU: 17.6%, 内存: 62.6%, 磁盘: 84.1%
2025-08-04 11:58:26,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 41.7%
2025-08-04 11:58:42,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 7.7%
2025-08-04 11:58:57,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 0.0%
2025-08-04 11:59:12,261 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 8.3%
2025-08-04 11:59:27,351 - health_monitor - DEBUG - 系统指标 - CPU: 62.3%, 内存: 64.3%, 磁盘: 84.1%
2025-08-04 11:59:27,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 37.5%
2025-08-04 11:59:42,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 17.9%
2025-08-04 11:59:57,580 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 57.1%
2025-08-04 12:00:12,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 71.4%
2025-08-04 12:00:27,973 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 100.0%
2025-08-04 12:00:28,515 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 64.4%, 磁盘: 84.1%
2025-08-04 12:00:43,107 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-08-04 12:00:58,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-08-04 12:01:14,399 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-08-04 12:01:30,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-08-04 12:01:30,046 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 71.1%, 磁盘: 84.1%
2025-08-04 12:01:45,261 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-08-04 12:02:01,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-08-04 12:02:17,162 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-08-04 12:02:33,488 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 63.3%, 磁盘: 84.1%
2025-08-04 12:02:33,488 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-08-04 12:02:50,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 100.0%
2025-08-04 12:03:05,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-08-04 12:03:21,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 100.0%
2025-08-04 12:03:36,802 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 63.9%, 磁盘: 84.1%
2025-08-04 12:03:37,111 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 100.0%
2025-08-04 12:03:52,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 100.0%
2025-08-04 12:04:09,001 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.1%, CPU使用率 100.0%
2025-08-04 12:04:24,693 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-08-04 12:04:38,098 - health_monitor - DEBUG - 系统指标 - CPU: 97.7%, 内存: 63.6%, 磁盘: 84.1%
2025-08-04 12:04:40,070 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-08-04 12:04:55,275 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 93.8%
2025-08-04 12:05:10,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 100.0%
2025-08-04 12:05:25,987 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-08-04 12:05:39,446 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 64.7%, 磁盘: 84.1%
2025-08-04 12:05:41,206 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 97.4%
2025-08-04 12:05:44,796 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 100.0, 阈值: 90
2025-08-04 12:05:56,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 90.3%
2025-08-04 12:06:11,538 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 96.7%
2025-08-04 12:06:27,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 100.0%
2025-08-04 12:06:41,430 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 64.3%, 磁盘: 84.1%
2025-08-04 12:06:42,516 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 100.0%
2025-08-04 12:06:57,684 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 96.0%
2025-08-04 12:07:13,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-08-04 12:07:28,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 12:07:42,758 - health_monitor - DEBUG - 系统指标 - CPU: 77.9%, 内存: 63.7%, 磁盘: 84.1%
2025-08-04 12:07:43,779 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 78.6%
2025-08-04 12:07:59,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 97.6%
2025-08-04 12:08:14,376 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 73.7%
2025-08-04 12:08:29,726 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 58.3%
2025-08-04 12:08:43,799 - health_monitor - DEBUG - 系统指标 - CPU: 86.4%, 内存: 63.7%, 磁盘: 84.1%
2025-08-04 12:08:44,835 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-08-04 12:09:00,255 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 100.0%
2025-08-04 12:09:15,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 96.4%
2025-08-04 12:09:30,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 42.9%
2025-08-04 12:09:44,832 - health_monitor - DEBUG - 系统指标 - CPU: 75.8%, 内存: 64.5%, 磁盘: 84.1%
2025-08-04 12:09:45,811 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 100.0%
2025-08-04 12:10:00,979 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 78.8%
2025-08-04 12:10:16,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 97.2%
2025-08-04 12:10:31,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 83.8%
2025-08-04 12:10:45,924 - health_monitor - DEBUG - 系统指标 - CPU: 83.5%, 内存: 64.1%, 磁盘: 84.1%
2025-08-04 12:10:46,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 96.6%
2025-08-04 12:11:01,647 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 89.7%
2025-08-04 12:11:16,754 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 56.5%
2025-08-04 12:11:32,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-08-04 12:11:47,063 - health_monitor - DEBUG - 系统指标 - CPU: 86.9%, 内存: 65.7%, 磁盘: 84.1%
2025-08-04 12:11:47,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 79.2%
2025-08-04 12:12:02,555 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-08-04 12:12:17,743 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 92.9%
2025-08-04 12:12:32,873 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 79.3%
2025-08-04 12:12:47,979 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 83.3%
2025-08-04 12:12:48,280 - health_monitor - DEBUG - 系统指标 - CPU: 81.3%, 内存: 65.0%, 磁盘: 84.1%
2025-08-04 12:13:03,085 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 72.0%
2025-08-04 12:13:18,362 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 95.8%
2025-08-04 12:13:33,482 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 75.0%
2025-08-04 12:13:48,671 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 94.9%
2025-08-04 12:13:49,326 - health_monitor - DEBUG - 系统指标 - CPU: 91.2%, 内存: 66.0%, 磁盘: 84.1%
2025-08-04 12:14:03,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 72.0%
2025-08-04 12:14:18,911 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 79.2%
2025-08-04 12:14:34,125 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 100.0%
2025-08-04 12:14:49,269 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 91.2%
2025-08-04 12:14:50,350 - health_monitor - DEBUG - 系统指标 - CPU: 80.6%, 内存: 64.5%, 磁盘: 84.1%
2025-08-04 12:15:04,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 67.9%
2025-08-04 12:15:19,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 93.5%
2025-08-04 12:15:34,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 100.0%
2025-08-04 12:15:49,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 66.7%
2025-08-04 12:15:51,411 - health_monitor - DEBUG - 系统指标 - CPU: 92.7%, 内存: 65.9%, 磁盘: 84.1%
2025-08-04 12:16:04,978 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 87.5%
2025-08-04 12:16:20,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 78.6%
2025-08-04 12:16:35,212 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 92.3%
2025-08-04 12:16:50,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 83.3%
2025-08-04 12:16:52,461 - health_monitor - DEBUG - 系统指标 - CPU: 85.8%, 内存: 65.0%, 磁盘: 84.1%
2025-08-04 12:17:05,422 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 82.1%
2025-08-04 12:17:20,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 12:17:35,921 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 94.1%
2025-08-04 12:17:51,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 67.9%
2025-08-04 12:17:53,548 - health_monitor - DEBUG - 系统指标 - CPU: 51.4%, 内存: 67.2%, 磁盘: 84.1%
2025-08-04 12:18:06,137 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 62.5%
2025-08-04 12:18:21,377 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 97.3%
2025-08-04 12:18:36,483 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 50.0%
2025-08-04 12:18:51,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 85.7%
2025-08-04 12:18:54,691 - health_monitor - DEBUG - 系统指标 - CPU: 69.2%, 内存: 65.5%, 磁盘: 84.1%
2025-08-04 12:19:06,819 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 90.3%
2025-08-04 12:19:21,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 56.7%
2025-08-04 12:19:37,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 57.1%
2025-08-04 12:19:52,200 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 12:19:52,200 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 12:19:52,201 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 12:19:52,202 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 70.4%
2025-08-04 12:19:55,718 - health_monitor - DEBUG - 系统指标 - CPU: 79.7%, 内存: 66.2%, 磁盘: 84.1%
2025-08-04 12:20:07,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 84.0%
2025-08-04 12:20:22,426 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 67.9%
2025-08-04 12:20:37,531 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 25.9%
2025-08-04 12:20:52,635 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 20.8%
2025-08-04 12:20:56,739 - health_monitor - DEBUG - 系统指标 - CPU: 9.4%, 内存: 62.9%, 磁盘: 84.1%
2025-08-04 12:21:07,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 8.3%
2025-08-04 12:21:22,845 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 35.7%
2025-08-04 12:21:37,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 17.9%
2025-08-04 12:21:53,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 20.8%
2025-08-04 12:21:57,760 - health_monitor - DEBUG - 系统指标 - CPU: 17.9%, 内存: 63.0%, 磁盘: 84.1%
2025-08-04 12:22:08,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 0.0%
2025-08-04 12:22:23,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 0.0%
2025-08-04 12:22:38,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 50.0%
2025-08-04 12:22:53,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 12.5%
2025-08-04 12:22:58,781 - health_monitor - DEBUG - 系统指标 - CPU: 13.2%, 内存: 62.7%, 磁盘: 84.1%
2025-08-04 12:23:08,580 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 17.9%
2025-08-04 12:23:23,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 11.5%
2025-08-04 12:23:38,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 4.2%
2025-08-04 12:23:53,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 16.7%
2025-08-04 12:23:59,801 - health_monitor - DEBUG - 系统指标 - CPU: 10.5%, 内存: 62.8%, 磁盘: 84.1%
2025-08-04 12:24:09,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 14.3%
2025-08-04 12:24:24,105 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 12.5%
2025-08-04 12:24:39,210 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 29.2%
2025-08-04 12:24:54,315 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 28.6%
2025-08-04 12:25:00,821 - health_monitor - DEBUG - 系统指标 - CPU: 17.2%, 内存: 62.7%, 磁盘: 84.1%
2025-08-04 12:25:09,419 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 17.9%
2025-08-04 12:25:24,525 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 16.7%
2025-08-04 12:25:39,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 33.3%
2025-08-04 12:25:54,735 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 21.4%
2025-08-04 12:26:01,840 - health_monitor - DEBUG - 系统指标 - CPU: 20.3%, 内存: 62.7%, 磁盘: 84.1%
2025-08-04 12:26:09,840 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 25.9%
2025-08-04 12:26:24,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 16.7%
2025-08-04 12:26:40,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 0.0%
2025-08-04 12:26:55,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 27.6%
2025-08-04 12:27:02,862 - health_monitor - DEBUG - 系统指标 - CPU: 22.4%, 内存: 63.2%, 磁盘: 84.1%
2025-08-04 12:27:10,259 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 0.0%
2025-08-04 12:27:25,363 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 3.8%
2025-08-04 12:27:40,468 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 17.9%
2025-08-04 12:27:55,573 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 58.3%
2025-08-04 12:28:03,882 - health_monitor - DEBUG - 系统指标 - CPU: 9.8%, 内存: 63.0%, 磁盘: 84.1%
2025-08-04 12:28:10,677 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 12.5%
2025-08-04 12:28:25,781 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 0.0%
2025-08-04 12:28:40,886 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 20.8%
2025-08-04 12:28:55,993 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.0%, CPU使用率 29.2%
2025-08-04 12:29:04,905 - health_monitor - DEBUG - 系统指标 - CPU: 25.7%, 内存: 63.1%, 磁盘: 84.1%
2025-08-04 12:29:11,098 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 46.4%
2025-08-04 12:29:26,203 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 71.4%
2025-08-04 12:29:41,312 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 82.1%
2025-08-04 12:29:56,549 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 100.0%
2025-08-04 12:30:05,928 - health_monitor - DEBUG - 系统指标 - CPU: 86.6%, 内存: 64.2%, 磁盘: 84.1%
2025-08-04 12:30:11,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 100.0%
2025-08-04 12:30:26,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 60.7%
2025-08-04 12:30:42,038 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 100.0%
2025-08-04 12:30:57,251 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 100.0%
2025-08-04 12:31:07,234 - health_monitor - DEBUG - 系统指标 - CPU: 95.7%, 内存: 63.8%, 磁盘: 84.1%
2025-08-04 12:31:08,321 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 12:31:08,413 - main - INFO - 请求没有认证头部
2025-08-04 12:31:08,414 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 12:31:08,416 - main - INFO - --- 请求结束: 200 ---

2025-08-04 12:31:10,712 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 12:31:10,717 - main - INFO - 请求没有认证头部
2025-08-04 12:31:10,717 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 12:31:10,719 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 12:31:10,721 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 12:31:10,722 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 12:31:10,723 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 12:31:10,726 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 12:31:12,430 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 12:31:13,469 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 12:31:13,473 - main - INFO - --- 请求结束: 200 ---

2025-08-04 12:31:27,662 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-08-04 12:31:42,789 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 50.0%
2025-08-04 12:31:57,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 50.0%
2025-08-04 12:32:08,528 - health_monitor - DEBUG - 系统指标 - CPU: 97.6%, 内存: 66.5%, 磁盘: 84.1%
2025-08-04 12:32:13,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 88.5%
2025-08-04 12:32:28,122 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 25.9%
2025-08-04 12:32:43,227 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 20.8%
2025-08-04 12:32:58,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 46.4%
2025-08-04 12:33:09,697 - health_monitor - DEBUG - 系统指标 - CPU: 85.0%, 内存: 65.3%, 磁盘: 84.1%
2025-08-04 12:33:13,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.7%, CPU使用率 54.2%
2025-08-04 12:33:28,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 91.7%
2025-08-04 12:33:43,737 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 96.9%
2025-08-04 12:33:59,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-08-04 12:34:10,793 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.5%, 磁盘: 84.1%
2025-08-04 12:34:14,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 100.0%
2025-08-04 12:34:29,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 93.9%
2025-08-04 12:34:44,525 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 94.3%
2025-08-04 12:35:00,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 100.0%
2025-08-04 12:35:12,119 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 64.9%, 磁盘: 84.1%
2025-08-04 12:35:15,335 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 97.2%
2025-08-04 12:35:30,504 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 100.0%
2025-08-04 12:35:45,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 79.2%
2025-08-04 12:36:00,739 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 100.0%
2025-08-04 12:36:13,146 - health_monitor - DEBUG - 系统指标 - CPU: 44.6%, 内存: 65.3%, 磁盘: 84.1%
2025-08-04 12:36:15,853 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-08-04 12:36:30,985 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 100.0%
2025-08-04 12:36:43,295 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 12:36:43,351 - main - INFO - 请求没有认证头部
2025-08-04 12:36:43,352 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 12:36:43,354 - main - INFO - --- 请求结束: 200 ---

2025-08-04 12:36:45,496 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 12:36:45,498 - main - INFO - 请求没有认证头部
2025-08-04 12:36:45,499 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 12:36:45,520 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 12:36:45,524 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 12:36:45,525 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 12:36:45,526 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 12:36:45,527 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 12:36:46,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 100.0%
2025-08-04 12:36:49,269 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 12:36:49,273 - main - INFO - --- 请求结束: 200 ---

2025-08-04 12:37:01,750 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-08-04 12:37:14,563 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.1%, 磁盘: 84.1%
2025-08-04 12:37:17,132 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-08-04 12:37:32,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-08-04 12:37:48,231 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-08-04 12:38:03,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 12:38:16,399 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.2%, 磁盘: 84.1%
2025-08-04 12:38:19,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 100.0%
2025-08-04 12:38:34,875 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 80.0%
2025-08-04 12:38:50,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-08-04 12:39:06,043 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 100.0%
2025-08-04 12:39:18,500 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 69.0%, 磁盘: 84.1%
2025-08-04 12:39:21,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 100.0%
2025-08-04 12:39:37,030 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-08-04 12:39:52,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 96.4%
2025-08-04 12:40:07,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 91.7%
2025-08-04 12:40:20,204 - health_monitor - DEBUG - 系统指标 - CPU: 96.6%, 内存: 67.4%, 磁盘: 84.1%
2025-08-04 12:40:22,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 100.0%
2025-08-04 12:40:37,857 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 93.5%
2025-08-04 12:40:53,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-08-04 12:41:08,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 78.6%
2025-08-04 12:41:21,338 - health_monitor - DEBUG - 系统指标 - CPU: 94.8%, 内存: 66.2%, 磁盘: 84.1%
2025-08-04 12:41:23,612 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 88.9%
2025-08-04 12:41:38,953 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 94.9%
2025-08-04 12:41:54,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 97.4%
2025-08-04 12:42:09,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-08-04 12:42:22,827 - health_monitor - DEBUG - 系统指标 - CPU: 95.7%, 内存: 68.3%, 磁盘: 84.1%
2025-08-04 12:42:24,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-08-04 12:42:39,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-08-04 12:42:45,295 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 95.7, 阈值: 90
2025-08-04 12:42:55,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 100.0%
2025-08-04 12:43:10,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 97.0%
2025-08-04 12:43:23,986 - health_monitor - DEBUG - 系统指标 - CPU: 82.9%, 内存: 66.0%, 磁盘: 84.1%
2025-08-04 12:43:25,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 96.7%
2025-08-04 12:43:41,312 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 100.0%
2025-08-04 12:43:46,836 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 12:43:46,936 - main - INFO - 请求没有认证头部
2025-08-04 12:43:46,977 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 12:43:47,029 - main - INFO - --- 请求结束: 200 ---

2025-08-04 12:43:49,481 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 12:43:49,578 - main - INFO - 请求没有认证头部
2025-08-04 12:43:49,611 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 12:43:49,617 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 12:43:49,618 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 12:43:49,619 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 12:43:49,620 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 12:43:49,621 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 12:43:52,461 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 12:43:52,465 - main - INFO - --- 请求结束: 200 ---

2025-08-04 12:43:56,736 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-08-04 12:44:12,144 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-08-04 12:44:25,294 - health_monitor - DEBUG - 系统指标 - CPU: 93.0%, 内存: 68.6%, 磁盘: 84.1%
2025-08-04 12:44:27,272 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 50.0%
2025-08-04 12:44:42,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-08-04 12:44:58,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 67.9%
2025-08-04 12:45:13,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 89.7%
2025-08-04 12:45:26,783 - health_monitor - DEBUG - 系统指标 - CPU: 99.0%, 内存: 65.9%, 磁盘: 84.1%
2025-08-04 12:45:28,451 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 100.0%
2025-08-04 12:45:43,579 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 85.7%
2025-08-04 12:45:58,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 50.0%
2025-08-04 12:46:14,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 12:46:28,088 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 66.1%, 磁盘: 84.1%
2025-08-04 12:46:29,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 96.3%
2025-08-04 12:46:44,959 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-08-04 12:47:00,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 96.6%
2025-08-04 12:47:15,759 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.4%, CPU使用率 100.0%
2025-08-04 12:47:30,493 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.0%, 磁盘: 84.1%
2025-08-04 12:47:31,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-08-04 12:47:47,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-08-04 12:48:03,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-08-04 12:48:19,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-08-04 12:48:32,288 - health_monitor - DEBUG - 系统指标 - CPU: 99.1%, 内存: 66.6%, 磁盘: 84.1%
2025-08-04 12:48:34,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-08-04 12:48:50,796 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-08-04 12:49:06,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-08-04 12:49:22,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-08-04 12:49:33,599 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 69.0%, 磁盘: 84.1%
2025-08-04 12:49:37,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-08-04 12:49:45,640 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 99.2, 阈值: 90
2025-08-04 12:49:53,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-08-04 12:50:08,736 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 12:50:24,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-08-04 12:50:34,979 - health_monitor - DEBUG - 系统指标 - CPU: 93.3%, 内存: 68.1%, 磁盘: 84.1%
2025-08-04 12:50:39,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 84.4%
2025-08-04 12:50:54,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-08-04 12:51:10,544 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-08-04 12:51:26,031 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-08-04 12:51:36,650 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 70.4%, 磁盘: 84.1%
2025-08-04 12:51:41,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-08-04 12:51:57,049 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-08-04 12:52:12,803 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-08-04 12:52:28,542 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-08-04 12:52:37,856 - health_monitor - DEBUG - 系统指标 - CPU: 98.3%, 内存: 69.0%, 磁盘: 84.1%
2025-08-04 12:52:43,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-08-04 12:52:59,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 96.7%
2025-08-04 12:53:14,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 12:53:29,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 12:53:39,038 - health_monitor - DEBUG - 系统指标 - CPU: 95.9%, 内存: 69.8%, 磁盘: 84.1%
2025-08-04 12:53:45,058 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 12:54:00,368 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 12:54:15,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 92.6%
2025-08-04 12:54:30,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 91.7%
2025-08-04 12:54:40,493 - health_monitor - DEBUG - 系统指标 - CPU: 87.2%, 内存: 69.1%, 磁盘: 84.1%
2025-08-04 12:54:45,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 72.4%
2025-08-04 12:55:01,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 12:55:16,639 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 12:55:31,940 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 12:55:41,857 - health_monitor - DEBUG - 系统指标 - CPU: 98.5%, 内存: 70.6%, 磁盘: 84.1%
2025-08-04 12:55:47,074 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 87.5%
2025-08-04 12:56:02,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-08-04 12:56:17,463 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 79.2%
2025-08-04 12:56:32,571 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 92.0%
2025-08-04 12:56:43,234 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 69.0%, 磁盘: 84.1%
2025-08-04 12:56:47,914 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-08-04 12:57:03,296 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 12:57:18,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-08-04 12:57:33,692 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 57.1%
2025-08-04 12:57:44,301 - health_monitor - DEBUG - 系统指标 - CPU: 84.8%, 内存: 70.4%, 磁盘: 84.1%
2025-08-04 12:57:49,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 12:58:04,187 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 89.3%
2025-08-04 12:58:19,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 77.8%
2025-08-04 12:58:34,401 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 54.2%
2025-08-04 12:58:45,347 - health_monitor - DEBUG - 系统指标 - CPU: 84.9%, 内存: 68.0%, 磁盘: 84.1%
2025-08-04 12:58:49,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-08-04 12:59:04,764 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 92.9%
2025-08-04 12:59:19,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 89.7%
2025-08-04 12:59:35,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 96.1%
2025-08-04 12:59:46,615 - health_monitor - DEBUG - 系统指标 - CPU: 89.4%, 内存: 68.8%, 磁盘: 84.1%
2025-08-04 12:59:50,373 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 77.8%
2025-08-04 13:00:05,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 87.5%
2025-08-04 13:00:20,606 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 96.7%
2025-08-04 13:00:35,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 95.8%
2025-08-04 13:00:48,001 - health_monitor - DEBUG - 系统指标 - CPU: 94.9%, 内存: 68.5%, 磁盘: 84.1%
2025-08-04 13:00:50,880 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 100.0%
2025-08-04 13:01:06,123 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 97.8%
2025-08-04 13:01:21,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 78.6%
2025-08-04 13:01:36,489 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 89.2%
2025-08-04 13:01:49,042 - health_monitor - DEBUG - 系统指标 - CPU: 94.2%, 内存: 67.1%, 磁盘: 84.1%
2025-08-04 13:01:51,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 13:02:06,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 85.2%
2025-08-04 13:02:22,299 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-08-04 13:02:37,605 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-08-04 13:02:50,494 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 68.6%, 磁盘: 84.1%
2025-08-04 13:02:52,892 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-08-04 13:03:08,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-04 13:03:23,753 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-08-04 13:03:38,958 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 87.5%
2025-08-04 13:03:52,529 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 69.3%, 磁盘: 84.1%
2025-08-04 13:03:54,712 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 100.0%
2025-08-04 13:04:10,076 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 13:04:25,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-08-04 13:04:41,122 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-04 13:04:53,731 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 69.3%, 磁盘: 84.1%
2025-08-04 13:04:56,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 84.0%
2025-08-04 13:05:11,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 69.0%
2025-08-04 13:05:26,868 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-08-04 13:05:42,308 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-08-04 13:05:54,780 - health_monitor - DEBUG - 系统指标 - CPU: 76.4%, 内存: 70.6%, 磁盘: 84.1%
2025-08-04 13:05:57,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 79.2%
2025-08-04 13:06:12,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 83.3%
2025-08-04 13:06:27,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 80.0%
2025-08-04 13:06:42,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 87.1%
2025-08-04 13:06:56,460 - health_monitor - DEBUG - 系统指标 - CPU: 81.7%, 内存: 68.8%, 磁盘: 84.1%
2025-08-04 13:06:58,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 95.0%
2025-08-04 13:07:13,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 83.3%
2025-08-04 13:07:28,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 54.2%
2025-08-04 13:07:43,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 96.8%
2025-08-04 13:07:57,565 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 69.7%, 磁盘: 84.1%
2025-08-04 13:07:58,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 79.2%
2025-08-04 13:08:13,851 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 87.5%
2025-08-04 13:08:28,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 92.9%
2025-08-04 13:08:44,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-08-04 13:08:58,966 - health_monitor - DEBUG - 系统指标 - CPU: 83.6%, 内存: 70.2%, 磁盘: 84.1%
2025-08-04 13:08:59,383 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 92.9%
2025-08-04 13:09:14,695 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 100.0%
2025-08-04 13:09:29,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 87.5%
2025-08-04 13:09:44,974 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 78.1%
2025-08-04 13:10:00,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 72.0%
2025-08-04 13:10:00,120 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 71.8%, 磁盘: 84.1%
2025-08-04 13:10:15,311 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 13:10:30,461 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 13:10:45,573 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 87.5%
2025-08-04 13:11:00,961 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-08-04 13:11:01,374 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 69.4%, 磁盘: 84.1%
2025-08-04 13:11:16,067 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 65.4%
2025-08-04 13:11:31,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 96.4%
2025-08-04 13:11:46,413 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 100.0%
2025-08-04 13:12:01,697 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-08-04 13:12:02,790 - health_monitor - DEBUG - 系统指标 - CPU: 88.8%, 内存: 70.4%, 磁盘: 84.1%
2025-08-04 13:12:16,860 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 96.4%
2025-08-04 13:12:32,048 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 96.9%
2025-08-04 13:12:47,337 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-08-04 13:13:02,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 76.0%
2025-08-04 13:13:04,054 - health_monitor - DEBUG - 系统指标 - CPU: 83.6%, 内存: 68.5%, 磁盘: 84.1%
2025-08-04 13:13:17,743 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 58.3%
2025-08-04 13:13:32,891 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-08-04 13:13:47,997 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 75.0%
2025-08-04 13:14:03,208 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 75.0%
2025-08-04 13:14:05,338 - health_monitor - DEBUG - 系统指标 - CPU: 92.8%, 内存: 71.3%, 磁盘: 84.1%
2025-08-04 13:14:18,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 45.8%
2025-08-04 13:14:33,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 88.6%
2025-08-04 13:14:48,637 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 85.7%
2025-08-04 13:15:04,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 93.0%
2025-08-04 13:15:06,371 - health_monitor - DEBUG - 系统指标 - CPU: 95.3%, 内存: 69.1%, 磁盘: 84.1%
2025-08-04 13:15:19,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 54.2%
2025-08-04 13:15:34,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 90.3%
2025-08-04 13:15:49,675 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 53.6%
2025-08-04 13:16:04,780 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 25.0%
2025-08-04 13:16:07,395 - health_monitor - DEBUG - 系统指标 - CPU: 16.3%, 内存: 67.4%, 磁盘: 84.1%
2025-08-04 13:16:19,890 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 42.9%
2025-08-04 13:16:34,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 4.2%
2025-08-04 13:16:50,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 13:17:05,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 72.4%
2025-08-04 13:17:08,421 - health_monitor - DEBUG - 系统指标 - CPU: 41.9%, 内存: 66.3%, 磁盘: 84.1%
2025-08-04 13:17:20,325 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 75.0%
2025-08-04 13:17:35,463 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 89.3%
2025-08-04 13:17:50,570 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 79.2%
2025-08-04 13:18:05,675 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 37.9%
2025-08-04 13:18:09,443 - health_monitor - DEBUG - 系统指标 - CPU: 44.3%, 内存: 67.2%, 磁盘: 84.1%
2025-08-04 13:18:20,779 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 39.3%
2025-08-04 13:18:35,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 66.7%
2025-08-04 13:18:50,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 70.8%
2025-08-04 13:19:06,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 21.4%
2025-08-04 13:19:10,464 - health_monitor - DEBUG - 系统指标 - CPU: 45.7%, 内存: 67.2%, 磁盘: 84.1%
2025-08-04 13:19:21,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 53.6%
2025-08-04 13:19:36,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-08-04 13:19:51,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 32.1%
2025-08-04 13:20:06,516 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 50.0%
2025-08-04 13:20:11,488 - health_monitor - DEBUG - 系统指标 - CPU: 49.6%, 内存: 67.4%, 磁盘: 84.1%
2025-08-04 13:20:21,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 79.2%
2025-08-04 13:20:36,725 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 0.0%
2025-08-04 13:20:51,830 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 3.6%
2025-08-04 13:21:06,959 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 48.3%
2025-08-04 13:21:12,509 - health_monitor - DEBUG - 系统指标 - CPU: 7.5%, 内存: 67.1%, 磁盘: 84.1%
2025-08-04 13:21:22,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 21.4%
2025-08-04 13:21:24,300 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 13:21:24,300 - main - INFO - 请求没有认证头部
2025-08-04 13:21:24,301 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 13:21:24,303 - main - INFO - --- 请求结束: 200 ---

2025-08-04 13:21:26,350 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 13:21:26,351 - main - INFO - 请求没有认证头部
2025-08-04 13:21:26,351 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 13:21:26,353 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 13:21:26,354 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 13:21:26,355 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 13:21:26,356 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 13:21:26,357 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 13:21:26,357 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 13:21:29,089 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 13:21:29,091 - main - INFO - --- 请求结束: 200 ---

2025-08-04 13:21:37,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 0.0%
2025-08-04 13:21:52,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 35.7%
2025-08-04 13:22:07,392 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 39.3%
2025-08-04 13:22:13,539 - health_monitor - DEBUG - 系统指标 - CPU: 51.3%, 内存: 67.2%, 磁盘: 84.1%
2025-08-04 13:22:22,501 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 77.8%
2025-08-04 13:22:37,605 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 25.0%
2025-08-04 13:22:52,710 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 32.0%
2025-08-04 13:23:07,976 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 13:23:14,564 - health_monitor - DEBUG - 系统指标 - CPU: 46.9%, 内存: 68.1%, 磁盘: 84.1%
2025-08-04 13:23:23,106 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 25.9%
2025-08-04 13:23:38,228 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 83.3%
2025-08-04 13:23:53,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 50.0%
2025-08-04 13:24:08,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 42.3%
2025-08-04 13:24:15,588 - health_monitor - DEBUG - 系统指标 - CPU: 74.3%, 内存: 68.0%, 磁盘: 84.1%
2025-08-04 13:24:23,545 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 39.3%
2025-08-04 13:24:38,650 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 40.0%
2025-08-04 13:24:53,755 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 48.0%
2025-08-04 13:25:08,875 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 81.2%
2025-08-04 13:25:17,044 - health_monitor - DEBUG - 系统指标 - CPU: 71.6%, 内存: 67.7%, 磁盘: 84.1%
2025-08-04 13:25:24,058 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 78.9%
2025-08-04 13:25:39,887 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 13:25:55,163 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 58.3%
2025-08-04 13:26:10,270 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 80.8%
2025-08-04 13:26:18,204 - health_monitor - DEBUG - 系统指标 - CPU: 94.7%, 内存: 69.4%, 磁盘: 84.1%
2025-08-04 13:26:25,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 96.9%
2025-08-04 13:26:40,946 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-08-04 13:26:56,056 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 83.3%
2025-08-04 13:27:11,426 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 13:27:20,082 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.6%, 磁盘: 84.1%
2025-08-04 13:27:26,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 13:27:41,906 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 97.1%
2025-08-04 13:27:57,067 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 97.2%
2025-08-04 13:28:12,635 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-08-04 13:28:21,690 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 71.7%, 磁盘: 84.1%
2025-08-04 13:28:28,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-08-04 13:28:43,427 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 80.0%
2025-08-04 13:28:58,813 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 13:29:14,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-08-04 13:29:23,902 - health_monitor - DEBUG - 系统指标 - CPU: 99.4%, 内存: 69.5%, 磁盘: 84.1%
2025-08-04 13:29:30,290 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-04 13:29:46,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 13:30:01,916 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 100.0%
2025-08-04 13:30:17,298 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 97.7%
2025-08-04 13:30:25,121 - health_monitor - DEBUG - 系统指标 - CPU: 95.7%, 内存: 70.5%, 磁盘: 84.2%
2025-08-04 13:30:32,956 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 98.6%
2025-08-04 13:30:48,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-08-04 13:31:03,556 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-08-04 13:31:18,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-08-04 13:31:26,390 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 72.0%, 磁盘: 84.2%
2025-08-04 13:31:34,112 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 13:31:46,823 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 99.3, 阈值: 90
2025-08-04 13:31:49,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 93.3%
2025-08-04 13:32:04,588 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 83.3%
2025-08-04 13:32:19,693 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 43.5%
2025-08-04 13:32:27,542 - health_monitor - DEBUG - 系统指标 - CPU: 88.1%, 内存: 71.0%, 磁盘: 84.2%
2025-08-04 13:32:34,801 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 79.2%
2025-08-04 13:32:49,935 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 89.3%
2025-08-04 13:33:05,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 100.0%
2025-08-04 13:33:20,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-08-04 13:33:28,677 - health_monitor - DEBUG - 系统指标 - CPU: 76.3%, 内存: 70.6%, 磁盘: 84.2%
2025-08-04 13:33:35,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 13:33:50,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 85.7%
2025-08-04 13:34:06,185 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 97.1%
2025-08-04 13:34:21,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 88.0%
2025-08-04 13:34:29,915 - health_monitor - DEBUG - 系统指标 - CPU: 92.1%, 内存: 71.7%, 磁盘: 84.2%
2025-08-04 13:34:36,473 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 45.8%
2025-08-04 13:34:51,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 13:35:06,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-08-04 13:35:22,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 70.8%
2025-08-04 13:35:31,309 - health_monitor - DEBUG - 系统指标 - CPU: 79.5%, 内存: 68.8%, 磁盘: 84.2%
2025-08-04 13:35:37,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 44.0%
2025-08-04 13:35:52,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 80.0%
2025-08-04 13:36:07,410 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 87.5%
2025-08-04 13:36:16,584 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 13:36:16,586 - main - INFO - 请求没有认证头部
2025-08-04 13:36:16,587 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 13:36:16,588 - main - INFO - --- 请求结束: 200 ---

2025-08-04 13:36:18,702 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 13:36:18,720 - main - INFO - 请求没有认证头部
2025-08-04 13:36:18,724 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 13:36:18,726 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 13:36:18,728 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 13:36:18,732 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 13:36:18,734 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 13:36:18,735 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 13:36:18,737 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 13:36:18,741 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 13:36:18,742 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 13:36:18,743 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 13:36:18,743 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 13:36:21,336 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 13:36:21,406 - main - INFO - --- 请求结束: 200 ---

2025-08-04 13:36:23,069 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 13:36:33,225 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 70.9%, 磁盘: 84.2%
2025-08-04 13:36:38,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 13:36:54,164 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 98.7%
2025-08-04 13:37:09,454 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 97.1%
2025-08-04 13:37:24,749 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 98.0%
2025-08-04 13:37:34,624 - health_monitor - DEBUG - 系统指标 - CPU: 91.2%, 内存: 69.9%, 磁盘: 84.2%
2025-08-04 13:37:39,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 50.0%
2025-08-04 13:37:55,045 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-08-04 13:38:10,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 33.3%
2025-08-04 13:38:25,254 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 73.1%
2025-08-04 13:38:36,006 - health_monitor - DEBUG - 系统指标 - CPU: 82.3%, 内存: 69.3%, 磁盘: 84.2%
2025-08-04 13:38:40,360 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.8%, CPU使用率 66.7%
2025-08-04 13:38:55,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 57.1%
2025-08-04 13:39:10,695 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-08-04 13:39:25,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 91.7%
2025-08-04 13:39:37,123 - health_monitor - DEBUG - 系统指标 - CPU: 79.4%, 内存: 68.9%, 磁盘: 84.2%
2025-08-04 13:39:40,935 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 76.7%
2025-08-04 13:39:56,079 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 87.5%
2025-08-04 13:40:11,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 88.0%
2025-08-04 13:40:26,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 97.7%
2025-08-04 13:40:38,360 - health_monitor - DEBUG - 系统指标 - CPU: 95.1%, 内存: 69.1%, 磁盘: 84.2%
2025-08-04 13:40:41,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 95.8%
2025-08-04 13:40:57,299 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 100.0%
2025-08-04 13:41:12,787 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-04 13:41:28,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 13:41:39,386 - health_monitor - DEBUG - 系统指标 - CPU: 71.0%, 内存: 71.3%, 磁盘: 84.2%
2025-08-04 13:41:43,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 97.6%
2025-08-04 13:41:58,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 87.1%
2025-08-04 13:42:13,807 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-08-04 13:42:29,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 97.0%
2025-08-04 13:42:40,449 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 70.3%, 磁盘: 84.2%
2025-08-04 13:42:44,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-08-04 13:42:59,982 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-08-04 13:43:15,350 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 13:43:31,266 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-08-04 13:43:42,180 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 72.9%, 磁盘: 84.2%
2025-08-04 13:43:47,008 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 13:44:02,751 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 97.5%
2025-08-04 13:44:18,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 13:44:33,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 96.9%
2025-08-04 13:44:43,478 - health_monitor - DEBUG - 系统指标 - CPU: 96.4%, 内存: 71.9%, 磁盘: 84.2%
2025-08-04 13:44:49,222 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-08-04 13:45:04,567 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 97.4%
2025-08-04 13:45:20,502 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 100.0%
2025-08-04 13:45:36,127 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-04 13:45:45,037 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 72.3%, 磁盘: 84.2%
2025-08-04 13:45:51,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 13:46:07,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-08-04 13:46:22,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 97.8%
2025-08-04 13:46:38,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 96.6%
2025-08-04 13:46:46,464 - health_monitor - DEBUG - 系统指标 - CPU: 95.5%, 内存: 71.4%, 磁盘: 84.2%
2025-08-04 13:46:53,574 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 91.7%
2025-08-04 13:47:09,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-08-04 13:47:24,270 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 69.2%
2025-08-04 13:47:39,426 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.0%, CPU使用率 100.0%
2025-08-04 13:47:47,835 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 74.1%, 磁盘: 84.2%
2025-08-04 13:47:54,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 100.0%
2025-08-04 13:48:10,307 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 70.8%
2025-08-04 13:48:25,707 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 100.0%
2025-08-04 13:48:41,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 13:48:47,223 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 99.7, 阈值: 90
2025-08-04 13:48:48,940 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 75.6%, 磁盘: 84.2%
2025-08-04 13:48:56,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 78.6%
2025-08-04 13:49:11,472 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-08-04 13:49:26,579 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 86.2%
2025-08-04 13:49:41,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 71.4%
2025-08-04 13:49:49,983 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 74.7%, 磁盘: 84.2%
2025-08-04 13:49:56,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-08-04 13:50:11,966 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 90.9%
2025-08-04 13:50:27,074 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 37.5%
2025-08-04 13:50:42,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 95.3%
2025-08-04 13:50:51,023 - health_monitor - DEBUG - 系统指标 - CPU: 86.4%, 内存: 75.0%, 磁盘: 84.2%
2025-08-04 13:50:57,450 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 40.7%
2025-08-04 13:51:12,736 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 100.0%
2025-08-04 13:51:27,849 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 75.0%
2025-08-04 13:51:42,989 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 82.9%
2025-08-04 13:51:52,267 - health_monitor - DEBUG - 系统指标 - CPU: 78.0%, 内存: 72.0%, 磁盘: 84.2%
2025-08-04 13:51:58,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 81.4%
2025-08-04 13:52:13,325 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 89.3%
2025-08-04 13:52:28,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 84.0%
2025-08-04 13:52:43,786 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-08-04 13:52:53,439 - health_monitor - DEBUG - 系统指标 - CPU: 61.6%, 内存: 73.6%, 磁盘: 84.2%
2025-08-04 13:52:58,907 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 60.7%
2025-08-04 13:53:14,100 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-08-04 13:53:29,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 92.9%
2025-08-04 13:53:44,390 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 96.4%
2025-08-04 13:53:54,471 - health_monitor - DEBUG - 系统指标 - CPU: 81.5%, 内存: 69.5%, 磁盘: 84.2%
2025-08-04 13:53:59,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 91.7%
2025-08-04 13:54:14,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 33.3%
2025-08-04 13:54:29,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 83.3%
2025-08-04 13:54:44,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 96.6%
2025-08-04 13:54:45,411 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 13:54:45,428 - main - INFO - 请求没有认证头部
2025-08-04 13:54:45,465 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 13:54:45,509 - main - INFO - --- 请求结束: 200 ---

2025-08-04 13:54:47,838 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 13:54:47,840 - main - INFO - 请求没有认证头部
2025-08-04 13:54:47,840 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 13:54:47,842 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 13:54:47,843 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 13:54:47,844 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 13:54:47,845 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 13:54:47,846 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 13:54:50,375 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 13:54:50,378 - main - INFO - --- 请求结束: 200 ---

2025-08-04 13:54:56,208 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 70.5%, 磁盘: 84.2%
2025-08-04 13:55:00,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 13:55:15,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 88.9%
2025-08-04 13:55:31,123 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-08-04 13:55:46,256 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 75.0%
2025-08-04 13:55:57,359 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 70.4%, 磁盘: 84.2%
2025-08-04 13:56:01,468 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 92.3%
2025-08-04 13:56:16,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-08-04 13:56:31,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 85.7%
2025-08-04 13:56:46,914 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 96.4%
2025-08-04 13:56:58,380 - health_monitor - DEBUG - 系统指标 - CPU: 32.9%, 内存: 65.1%, 磁盘: 84.2%
2025-08-04 13:57:02,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 60.7%
2025-08-04 13:57:17,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 55.2%
2025-08-04 13:57:32,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 84.4%
2025-08-04 13:57:47,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 91.7%
2025-08-04 13:57:59,417 - health_monitor - DEBUG - 系统指标 - CPU: 80.6%, 内存: 67.3%, 磁盘: 84.2%
2025-08-04 13:58:02,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 13:58:17,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 58.3%
2025-08-04 13:58:32,974 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 13:58:48,081 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 85.7%
2025-08-04 13:59:00,467 - health_monitor - DEBUG - 系统指标 - CPU: 55.9%, 内存: 68.7%, 磁盘: 84.2%
2025-08-04 13:59:03,204 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 96.6%
2025-08-04 13:59:18,378 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 90.6%
2025-08-04 13:59:33,509 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 85.7%
2025-08-04 13:59:48,727 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 97.9%
2025-08-04 14:00:01,870 - health_monitor - DEBUG - 系统指标 - CPU: 84.3%, 内存: 68.9%, 磁盘: 84.2%
2025-08-04 14:00:03,902 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 80.0%
2025-08-04 14:00:19,051 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 94.3%
2025-08-04 14:00:34,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 14:00:49,755 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 100.0%
2025-08-04 14:01:03,577 - health_monitor - DEBUG - 系统指标 - CPU: 99.1%, 内存: 69.1%, 磁盘: 84.2%
2025-08-04 14:01:04,950 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 94.6%
2025-08-04 14:01:20,113 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 89.3%
2025-08-04 14:01:35,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 14:01:50,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 14:02:04,654 - health_monitor - DEBUG - 系统指标 - CPU: 78.7%, 内存: 70.3%, 磁盘: 84.2%
2025-08-04 14:02:05,767 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 96.4%
2025-08-04 14:02:20,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 96.4%
2025-08-04 14:02:36,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-08-04 14:02:37,024 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 14:02:37,089 - main - INFO - 请求没有认证头部
2025-08-04 14:02:37,136 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:02:37,160 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:02:39,535 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 14:02:39,536 - main - INFO - 请求没有认证头部
2025-08-04 14:02:39,537 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:02:39,539 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:02:39,540 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:02:39,541 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:02:39,542 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:02:39,543 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:02:42,349 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 14:02:42,815 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:02:52,647 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-08-04 14:03:05,809 - health_monitor - DEBUG - 系统指标 - CPU: 93.3%, 内存: 68.9%, 磁盘: 84.2%
2025-08-04 14:03:08,030 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 100.0%
2025-08-04 14:03:23,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 64.3%
2025-08-04 14:03:38,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-08-04 14:03:53,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 78.6%
2025-08-04 14:04:07,117 - health_monitor - DEBUG - 系统指标 - CPU: 82.6%, 内存: 70.2%, 磁盘: 84.2%
2025-08-04 14:04:08,707 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 97.9%
2025-08-04 14:04:23,815 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 75.9%
2025-08-04 14:04:39,151 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 100.0%
2025-08-04 14:04:54,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 58.3%
2025-08-04 14:05:08,161 - health_monitor - DEBUG - 系统指标 - CPU: 93.5%, 内存: 70.8%, 磁盘: 84.2%
2025-08-04 14:05:09,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 60.7%
2025-08-04 14:05:24,746 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-04 14:05:39,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 25.0%
2025-08-04 14:05:55,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 63.3%
2025-08-04 14:06:09,188 - health_monitor - DEBUG - 系统指标 - CPU: 86.2%, 内存: 65.6%, 磁盘: 84.2%
2025-08-04 14:06:10,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 46.4%
2025-08-04 14:06:25,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 61.5%
2025-08-04 14:06:40,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 80.0%
2025-08-04 14:06:55,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 62.5%
2025-08-04 14:07:10,210 - health_monitor - DEBUG - 系统指标 - CPU: 45.9%, 内存: 68.2%, 磁盘: 84.2%
2025-08-04 14:07:10,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 51.7%
2025-08-04 14:07:25,687 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 32.1%
2025-08-04 14:07:40,795 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 85.7%
2025-08-04 14:07:55,901 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 70.8%
2025-08-04 14:08:11,006 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 39.1%
2025-08-04 14:08:11,232 - health_monitor - DEBUG - 系统指标 - CPU: 49.6%, 内存: 68.7%, 磁盘: 84.2%
2025-08-04 14:08:26,111 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 32.1%
2025-08-04 14:08:41,216 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 34.6%
2025-08-04 14:08:56,324 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 80.8%
2025-08-04 14:09:11,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 75.0%
2025-08-04 14:09:12,295 - health_monitor - DEBUG - 系统指标 - CPU: 73.9%, 内存: 72.6%, 磁盘: 84.2%
2025-08-04 14:09:26,537 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 50.0%
2025-08-04 14:09:41,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-08-04 14:09:56,970 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 97.2%
2025-08-04 14:10:12,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 42.9%
2025-08-04 14:10:13,368 - health_monitor - DEBUG - 系统指标 - CPU: 79.0%, 内存: 72.0%, 磁盘: 84.2%
2025-08-04 14:10:27,261 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 67.7%
2025-08-04 14:10:42,575 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 88.6%
2025-08-04 14:10:57,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 100.0%
2025-08-04 14:11:12,940 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 92.9%
2025-08-04 14:11:14,915 - health_monitor - DEBUG - 系统指标 - CPU: 93.8%, 内存: 68.1%, 磁盘: 84.2%
2025-08-04 14:11:28,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 95.5%
2025-08-04 14:11:43,335 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-08-04 14:11:58,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 97.5%
2025-08-04 14:12:13,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 89.7%
2025-08-04 14:12:16,283 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.0%, 磁盘: 84.2%
2025-08-04 14:12:28,747 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 48.3%
2025-08-04 14:12:44,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 100.0%
2025-08-04 14:12:59,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 96.0%
2025-08-04 14:13:14,556 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 79.2%
2025-08-04 14:13:17,557 - health_monitor - DEBUG - 系统指标 - CPU: 98.1%, 内存: 67.4%, 磁盘: 84.2%
2025-08-04 14:13:30,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 14:13:45,760 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-08-04 14:14:00,924 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-08-04 14:14:16,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-08-04 14:14:19,021 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.8%, 磁盘: 84.2%
2025-08-04 14:14:31,736 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 97.3%
2025-08-04 14:14:47,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 97.7%
2025-08-04 14:15:02,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 92.9%
2025-08-04 14:15:17,398 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 95.8%
2025-08-04 14:15:20,469 - health_monitor - DEBUG - 系统指标 - CPU: 97.6%, 内存: 70.1%, 磁盘: 84.2%
2025-08-04 14:15:32,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 66.7%
2025-08-04 14:15:48,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 100.0%
2025-08-04 14:16:03,167 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 91.7%
2025-08-04 14:16:18,509 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 100.0%
2025-08-04 14:16:21,514 - health_monitor - DEBUG - 系统指标 - CPU: 82.6%, 内存: 69.5%, 磁盘: 84.2%
2025-08-04 14:16:33,953 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-08-04 14:16:49,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-08-04 14:17:04,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 78.6%
2025-08-04 14:17:19,911 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-08-04 14:17:22,604 - health_monitor - DEBUG - 系统指标 - CPU: 98.9%, 内存: 70.0%, 磁盘: 84.2%
2025-08-04 14:17:35,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-08-04 14:17:50,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 92.6%
2025-08-04 14:18:05,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-08-04 14:18:05,701 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 14:18:05,759 - main - INFO - 请求没有认证头部
2025-08-04 14:18:05,775 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:18:05,791 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:18:07,935 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 14:18:07,946 - main - INFO - 请求没有认证头部
2025-08-04 14:18:07,947 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:18:07,950 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:18:07,951 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:18:07,952 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:18:07,953 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:18:07,953 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:18:10,378 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 14:18:10,384 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:18:21,250 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-08-04 14:18:24,101 - health_monitor - DEBUG - 系统指标 - CPU: 95.7%, 内存: 74.9%, 磁盘: 84.2%
2025-08-04 14:18:36,518 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 14:18:51,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-08-04 14:19:07,176 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 98.4%
2025-08-04 14:19:22,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 82.5%
2025-08-04 14:19:25,328 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 70.5%, 磁盘: 84.2%
2025-08-04 14:19:37,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 87.5%
2025-08-04 14:19:52,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 79.2%
2025-08-04 14:20:07,667 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 96.0%
2025-08-04 14:20:23,102 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 100.0%
2025-08-04 14:20:26,483 - health_monitor - DEBUG - 系统指标 - CPU: 97.7%, 内存: 72.9%, 磁盘: 84.2%
2025-08-04 14:20:38,259 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-08-04 14:20:53,580 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 14:21:08,705 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 86.2%
2025-08-04 14:21:23,813 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 42.9%
2025-08-04 14:21:27,513 - health_monitor - DEBUG - 系统指标 - CPU: 82.2%, 内存: 72.0%, 磁盘: 84.2%
2025-08-04 14:21:38,921 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 82.1%
2025-08-04 14:21:54,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.4%, CPU使用率 78.6%
2025-08-04 14:22:09,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 14:22:24,433 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 40.0%
2025-08-04 14:22:28,542 - health_monitor - DEBUG - 系统指标 - CPU: 79.1%, 内存: 73.8%, 磁盘: 84.2%
2025-08-04 14:22:39,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 100.0%
2025-08-04 14:22:54,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 28.6%
2025-08-04 14:23:09,810 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 28.6%
2025-08-04 14:23:24,914 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 41.7%
2025-08-04 14:23:29,563 - health_monitor - DEBUG - 系统指标 - CPU: 39.5%, 内存: 65.5%, 磁盘: 84.2%
2025-08-04 14:23:40,019 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 28.6%
2025-08-04 14:23:55,124 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 17.9%
2025-08-04 14:24:10,229 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 45.5%
2025-08-04 14:24:25,337 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 95.8%
2025-08-04 14:24:30,609 - health_monitor - DEBUG - 系统指标 - CPU: 79.1%, 内存: 69.7%, 磁盘: 84.2%
2025-08-04 14:24:40,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 54.2%
2025-08-04 14:24:55,554 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 66.7%
2025-08-04 14:25:10,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 100.0%
2025-08-04 14:25:25,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 91.7%
2025-08-04 14:25:31,636 - health_monitor - DEBUG - 系统指标 - CPU: 81.8%, 内存: 69.3%, 磁盘: 84.2%
2025-08-04 14:25:41,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 75.0%
2025-08-04 14:25:56,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 14:26:11,539 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 56.0%
2025-08-04 14:26:26,645 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 75.9%
2025-08-04 14:26:32,670 - health_monitor - DEBUG - 系统指标 - CPU: 76.4%, 内存: 72.2%, 磁盘: 84.2%
2025-08-04 14:26:41,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 84.2%
2025-08-04 14:26:57,125 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 97.2%
2025-08-04 14:27:12,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 95.6%
2025-08-04 14:27:28,027 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-08-04 14:27:33,910 - health_monitor - DEBUG - 系统指标 - CPU: 86.0%, 内存: 69.4%, 磁盘: 84.2%
2025-08-04 14:27:43,861 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-08-04 14:27:58,991 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 60.0%
2025-08-04 14:28:14,244 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 97.1%
2025-08-04 14:28:29,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 72.4%
2025-08-04 14:28:34,989 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 71.3%, 磁盘: 84.2%
2025-08-04 14:28:44,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 95.8%
2025-08-04 14:28:59,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 98.1%
2025-08-04 14:29:14,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 97.1%
2025-08-04 14:29:30,215 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-04 14:29:36,092 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 71.4%, 磁盘: 84.2%
2025-08-04 14:29:45,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 97.6%
2025-08-04 14:30:00,896 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 100.0%
2025-08-04 14:30:16,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 58.3%
2025-08-04 14:30:31,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 97.5%
2025-08-04 14:30:37,623 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 71.2%, 磁盘: 84.2%
2025-08-04 14:30:46,480 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 91.4%
2025-08-04 14:31:02,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-08-04 14:31:17,291 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 70.8%
2025-08-04 14:31:32,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 66.7%
2025-08-04 14:31:39,102 - health_monitor - DEBUG - 系统指标 - CPU: 79.1%, 内存: 71.9%, 磁盘: 84.2%
2025-08-04 14:31:47,513 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.7%, CPU使用率 78.6%
2025-08-04 14:32:02,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-08-04 14:32:17,788 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 33.3%
2025-08-04 14:32:33,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 95.2%
2025-08-04 14:32:40,129 - health_monitor - DEBUG - 系统指标 - CPU: 85.2%, 内存: 74.0%, 磁盘: 84.2%
2025-08-04 14:32:48,272 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 92.1%
2025-08-04 14:33:03,390 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 71.4%
2025-08-04 14:33:18,568 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-08-04 14:33:33,678 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 83.3%
2025-08-04 14:33:41,163 - health_monitor - DEBUG - 系统指标 - CPU: 82.3%, 内存: 72.7%, 磁盘: 84.2%
2025-08-04 14:33:48,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 97.8%
2025-08-04 14:34:04,111 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 14:34:19,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 90.6%
2025-08-04 14:34:34,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 98.1%
2025-08-04 14:34:42,243 - health_monitor - DEBUG - 系统指标 - CPU: 98.5%, 内存: 70.5%, 磁盘: 84.2%
2025-08-04 14:34:50,021 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 98.1%
2025-08-04 14:35:05,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 100.0%
2025-08-04 14:35:20,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 97.4%
2025-08-04 14:35:35,505 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 80.8%
2025-08-04 14:35:43,369 - health_monitor - DEBUG - 系统指标 - CPU: 95.6%, 内存: 72.7%, 磁盘: 84.2%
2025-08-04 14:35:50,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 95.2%
2025-08-04 14:36:05,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-04 14:36:21,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 100.0%
2025-08-04 14:36:36,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 100.0%
2025-08-04 14:36:44,472 - health_monitor - DEBUG - 系统指标 - CPU: 98.9%, 内存: 71.7%, 磁盘: 84.2%
2025-08-04 14:36:51,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 33.3%
2025-08-04 14:37:06,742 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 79.2%
2025-08-04 14:37:17,466 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 14:37:17,468 - main - INFO - 请求没有认证头部
2025-08-04 14:37:17,468 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:37:17,471 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:37:19,889 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 14:37:19,891 - main - INFO - 请求没有认证头部
2025-08-04 14:37:19,892 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:37:19,895 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:37:19,897 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:37:19,898 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:37:19,900 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:37:19,901 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:37:22,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 100.0%
2025-08-04 14:37:23,422 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 14:37:23,529 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:37:38,341 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 100.0%
2025-08-04 14:37:45,517 - health_monitor - DEBUG - 系统指标 - CPU: 79.8%, 内存: 72.7%, 磁盘: 84.2%
2025-08-04 14:37:53,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 96.4%
2025-08-04 14:38:09,149 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 100.0%
2025-08-04 14:38:24,308 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.0%, CPU使用率 62.5%
2025-08-04 14:38:39,575 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 86.7%
2025-08-04 14:38:40,742 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:40,772 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:40,773 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:40,775 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:40,839 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:40,975 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:41,114 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:41,130 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:41,139 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:41,153 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:41,155 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:41,156 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:41,157 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,159 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:41,160 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:41,162 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:41,163 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:41,164 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:41,170 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:41,171 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:41,172 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,174 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:41,175 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:41,177 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:41,178 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:41,181 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:41,181 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:41,186 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:41,188 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 14:38:41,190 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:41,190 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:41,191 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:41,192 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,193 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:41,193 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:41,195 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,197 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:41,198 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:41,205 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:41,206 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:41,207 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:41,209 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:41,210 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:41,211 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 14:38:41,212 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:41,213 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:41,214 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,218 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,219 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:41,220 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,222 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,223 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,229 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:41,238 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:41,241 - sqlalchemy.engine.Engine - INFO - [generated in 0.00337s] ('SM_008', 1, 0)
2025-08-04 14:38:41,244 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:41,249 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:41,273 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,274 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,276 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:41,277 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:41,277 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,278 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:41,281 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,285 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:41,287 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:41,292 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:41,294 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:41,295 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:41,298 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:41,301 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:41,304 - sqlalchemy.engine.Engine - INFO - [cached since 0.06628s ago] ('SM_008', 1, 0)
2025-08-04 14:38:41,308 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:41,315 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:41,325 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:41,326 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:41,327 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:41,328 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,329 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:41,330 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:41,332 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:41,340 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:41,345 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:41,345 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:41,347 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:41,347 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:41,351 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,353 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,355 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:41,356 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:41,392 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:41,765 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:41,768 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:41,885 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:41,887 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:41,889 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:41,891 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:41,894 - sqlalchemy.engine.Engine - INFO - [cached since 0.6559s ago] ('SM_008', 1, 0)
2025-08-04 14:38:41,896 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:41,904 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:41,906 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:41,908 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:44,346 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:44,387 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:44,389 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:44,392 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:44,394 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:44,561 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:44,689 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:44,706 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:44,708 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:44,710 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:44,713 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:44,752 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:44,754 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:44,756 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:44,790 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:44,793 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:44,795 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:44,796 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:44,856 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:44,990 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,171 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:45,221 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:45,273 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:45,275 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:45,328 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:45,372 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:45,381 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:45,390 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:45,392 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:45,393 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:45,395 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,397 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:45,398 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:45,407 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:45,409 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:45,410 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:45,411 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:45,413 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:45,415 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,438 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:45,440 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:45,442 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:45,444 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:45,447 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:45,454 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:45,456 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:45,457 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,460 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:45,461 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:45,463 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:45,464 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:45,469 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:45,469 - app.core.db_connection - DEBUG - 当前线程ID: 14664
2025-08-04 14:38:45,470 - app.core.db_connection - DEBUG - 当前线程ID: 12808
2025-08-04 14:38:45,470 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:45,471 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:45,473 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:45,474 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:45,474 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:45,475 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:45,477 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,477 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,478 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:45,479 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:45,480 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:45,480 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:45,482 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:45,488 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:45,489 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:45,491 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:45,493 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:45,494 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:45,497 - sqlalchemy.engine.Engine - INFO - [cached since 4.259s ago] ('SM_008', 1, 0)
2025-08-04 14:38:45,499 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:45,503 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:45,504 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:45,506 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:45,509 - sqlalchemy.engine.Engine - INFO - [cached since 4.271s ago] ('SM_008', 1, 0)
2025-08-04 14:38:45,511 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:45,512 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:45,515 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:45,518 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:45,521 - sqlalchemy.engine.Engine - INFO - [cached since 4.283s ago] ('SM_008', 1, 0)
2025-08-04 14:38:45,525 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:45,538 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:45,538 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:45,539 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:45,544 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:45,545 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:45,547 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:45,547 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:45,551 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:45,555 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:46,646 - health_monitor - DEBUG - 系统指标 - CPU: 92.2%, 内存: 73.4%, 磁盘: 84.2%
2025-08-04 14:38:47,928 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:47,929 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:47,930 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:47,931 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:47,932 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:47,935 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:47,937 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:47,938 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:47,939 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:47,940 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:47,940 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:47,941 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:47,943 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:47,943 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:47,945 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:47,946 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:47,947 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:47,950 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:47,951 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:47,952 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:47,953 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:47,954 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:47,956 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:47,956 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:47,961 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:47,963 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:47,970 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:47,975 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:47,976 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:47,977 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:47,978 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:47,980 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:47,981 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:47,986 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:47,987 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:47,989 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:47,990 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:47,990 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:47,992 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:47,993 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:47,994 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:47,997 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:47,998 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:48,003 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:48,004 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:48,005 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:48,006 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:48,008 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:48,009 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:48,011 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:48,013 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:48,019 - app.core.db_connection - DEBUG - 当前线程ID: 12808
2025-08-04 14:38:48,019 - app.core.db_connection - DEBUG - 当前线程ID: 14664
2025-08-04 14:38:48,020 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:48,122 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:48,202 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:48,630 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:48,638 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:48,639 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:48,641 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:48,642 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:48,745 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:48,808 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:48,888 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:48,912 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:48,925 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:48,925 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:48,931 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:48,935 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:48,938 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:48,939 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:48,942 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:48,944 - sqlalchemy.engine.Engine - INFO - [cached since 7.707s ago] ('SM_008', 1, 0)
2025-08-04 14:38:48,957 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:48,959 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:48,961 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:48,963 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:48,970 - sqlalchemy.engine.Engine - INFO - [cached since 7.732s ago] ('SM_008', 1, 0)
2025-08-04 14:38:48,980 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:48,984 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:48,995 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:48,998 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:49,006 - sqlalchemy.engine.Engine - INFO - [cached since 7.768s ago] ('SM_008', 1, 0)
2025-08-04 14:38:49,008 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:49,020 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:49,037 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:49,036 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:49,040 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:49,043 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:49,045 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:49,072 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:49,077 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:49,079 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:51,543 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:51,544 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:51,545 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:51,545 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,546 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:51,547 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:51,549 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:51,550 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:51,551 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:51,552 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:51,553 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:51,553 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,554 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:51,555 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:51,557 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:51,557 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:51,560 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:51,562 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:51,564 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:51,565 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:51,568 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:51,569 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,570 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:51,571 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:51,572 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:51,573 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:51,575 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:51,576 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:51,576 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:51,577 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,578 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:51,579 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:51,580 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:51,581 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:51,583 - app.core.db_connection - DEBUG - 当前线程ID: 14664
2025-08-04 14:38:51,584 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:51,585 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:51,586 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:51,586 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:51,588 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:51,589 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,589 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,590 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:51,591 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:51,593 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:51,593 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:51,595 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:51,596 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:51,597 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:51,600 - sqlalchemy.engine.Engine - INFO - [cached since 10.36s ago] ('SM_008', 1, 0)
2025-08-04 14:38:51,603 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:51,604 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:51,605 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:51,606 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:51,608 - sqlalchemy.engine.Engine - INFO - [cached since 10.37s ago] ('SM_008', 1, 0)
2025-08-04 14:38:51,610 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:51,668 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:51,669 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:51,671 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:51,672 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:51,674 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:51,675 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:51,757 - main - INFO - 
--- 请求开始: GET /api/documents ---
2025-08-04 14:38:51,761 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:51,762 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:51,763 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,764 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:51,765 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:51,771 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:51,772 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:51,774 - main - INFO - --- 请求结束: 307 ---

2025-08-04 14:38:51,811 - main - INFO - 
--- 请求开始: GET /api/documents/ ---
2025-08-04 14:38:51,848 - main - INFO - 认证头部: Bearer eyJhbGciOiJIU...
2025-08-04 14:38:51,851 - main - INFO - 认证请求 - 令牌: eyJhbGciOi...
2025-08-04 14:38:51,852 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,853 - main - INFO - 令牌验证成功，用户ID: SM_008
2025-08-04 14:38:51,854 - main - INFO - sub 'SM_008' 非数字，直接作为custom_id查询
2025-08-04 14:38:51,856 - main - INFO - 通过custom_id 'SM_008' 获取用户成功: markey
2025-08-04 14:38:51,857 - main - INFO - 获取用户成功: markey, ID: 2, 角色: personal
2025-08-04 14:38:51,859 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:38:51,860 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:38:51,861 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:38:51,862 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:38:51,863 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:38:51,864 - app.core.auth - INFO - 检测到X-User-ID头部: SM_008
2025-08-04 14:38:51,867 - app.core.auth - INFO - 使用X-User-ID认证: SM_008
2025-08-04 14:38:51,869 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 14:38:51,870 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.email AS users_email, users.phone AS users_phone, users.hashed_password AS users_hashed_password, users.full_name AS users_full_name, users.role AS users_role, users.is_active AS users_is_active, users.is_superuser AS users_is_superuser, users.id_number AS users_id_number, users.gender AS users_gender, users.birth_date AS users_birth_date, users.address AS users_address, users.custom_id AS users_custom_id, users.profile_photo AS users_profile_photo, users.emergency_contact AS users_emergency_contact, users.emergency_phone AS users_emergency_phone, users.registration_type AS users_registration_type, users.relationship_type AS users_relationship_type, users.additional_roles AS users_additional_roles, users.verification_status AS users_verification_status, users.is_first_login AS users_is_first_login, users.role_application_status AS users_role_application_status, users.role_application_role AS users_role_application_role, users.password_reset_at AS users_password_reset_at, users.created_at AS users_created_at, users.updated_at AS users_updated_at 
FROM users 
WHERE users.custom_id = ?
 LIMIT ? OFFSET ?
2025-08-04 14:38:51,872 - sqlalchemy.engine.Engine - INFO - [cached since 10.63s ago] ('SM_008', 1, 0)
2025-08-04 14:38:51,873 - app.core.auth - INFO - 通过custom_id找到用户: markey
2025-08-04 14:38:51,878 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-04 14:38:51,880 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 14:38:51,881 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:38:54,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-04 14:39:10,427 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 100.0%
2025-08-04 14:39:25,690 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 100.0%
2025-08-04 14:39:40,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 37.5%, CPU使用率 54.2%
2025-08-04 14:39:47,671 - health_monitor - DEBUG - 系统指标 - CPU: 43.4%, 内存: 35.7%, 磁盘: 84.2%
2025-08-04 14:39:55,943 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.0%, CPU使用率 48.0%
2025-08-04 14:40:12,545 - monitoring - DEBUG - 资源指标更新: 内存使用率 42.3%, CPU使用率 100.0%
2025-08-04 14:40:29,711 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.7%, CPU使用率 100.0%
2025-08-04 14:40:45,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 100.0%
2025-08-04 14:40:49,355 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 48.8%, 磁盘: 84.2%
2025-08-04 14:41:01,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 100.0%
2025-08-04 14:41:16,293 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 100.0%
2025-08-04 14:41:34,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 100.0%
2025-08-04 14:41:50,201 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 100.0%
2025-08-04 14:41:51,449 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 55.4%, 磁盘: 84.2%
2025-08-04 14:42:06,015 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.5%, CPU使用率 100.0%
2025-08-04 14:42:21,456 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 100.0%
2025-08-04 14:42:26,101 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 14:42:26,126 - main - INFO - 请求没有认证头部
2025-08-04 14:42:26,127 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:42:26,144 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:42:28,621 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 14:42:28,675 - main - INFO - 请求没有认证头部
2025-08-04 14:42:28,676 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 14:42:28,678 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 14:42:28,679 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 14:42:28,680 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 14:42:28,681 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 14:42:28,682 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 14:42:32,412 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 14:42:33,474 - main - INFO - --- 请求结束: 200 ---

2025-08-04 14:42:37,794 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-08-04 14:42:53,011 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 59.5%, 磁盘: 84.2%
2025-08-04 14:42:53,146 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 100.0%
2025-08-04 14:43:08,269 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 75.9%
2025-08-04 14:43:23,375 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 85.7%
2025-08-04 14:43:38,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 46.2%
2025-08-04 14:43:53,587 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 58.3%
2025-08-04 14:43:54,211 - health_monitor - DEBUG - 系统指标 - CPU: 68.8%, 内存: 65.7%, 磁盘: 84.2%
2025-08-04 14:44:08,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 94.7%
2025-08-04 14:44:24,098 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 75.0%
2025-08-04 14:44:39,215 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 58.3%
2025-08-04 14:44:54,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 96.9%
2025-08-04 14:44:55,294 - health_monitor - DEBUG - 系统指标 - CPU: 90.3%, 内存: 59.8%, 磁盘: 84.2%
2025-08-04 14:45:09,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 100.0%
2025-08-04 14:45:24,784 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 95.2%
2025-08-04 14:45:39,920 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 96.9%
2025-08-04 14:45:55,136 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 100.0%
2025-08-04 14:45:56,558 - health_monitor - DEBUG - 系统指标 - CPU: 86.6%, 内存: 61.8%, 磁盘: 84.2%
2025-08-04 14:46:10,257 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 91.7%
2025-08-04 14:46:25,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 100.0%
2025-08-04 14:46:40,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-08-04 14:46:56,789 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 100.0%
2025-08-04 14:46:58,046 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.4%, 磁盘: 84.2%
2025-08-04 14:47:12,822 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-08-04 14:47:27,952 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 32.1%
2025-08-04 14:47:43,181 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 94.9%
2025-08-04 14:47:58,288 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 75.0%
2025-08-04 14:47:59,253 - health_monitor - DEBUG - 系统指标 - CPU: 72.3%, 内存: 62.3%, 磁盘: 84.2%
2025-08-04 14:48:13,597 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-08-04 14:48:28,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 100.0%
2025-08-04 14:48:44,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-08-04 14:48:59,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 96.6%
2025-08-04 14:49:00,324 - health_monitor - DEBUG - 系统指标 - CPU: 94.6%, 内存: 61.7%, 磁盘: 84.2%
2025-08-04 14:49:14,596 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 100.0%
2025-08-04 14:49:29,929 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 58.3%
2025-08-04 14:49:45,229 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-08-04 14:50:00,549 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 96.4%
2025-08-04 14:50:01,386 - health_monitor - DEBUG - 系统指标 - CPU: 75.1%, 内存: 63.1%, 磁盘: 84.2%
2025-08-04 14:50:15,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 100.0%
2025-08-04 14:50:31,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 82.4%
2025-08-04 14:50:46,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-08-04 14:51:02,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 100.0%
2025-08-04 14:51:02,743 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 67.7%, 磁盘: 84.2%
2025-08-04 14:51:17,848 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 91.7%
2025-08-04 14:51:33,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 92.9%
2025-08-04 14:51:48,218 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 82.1%
2025-08-04 14:52:03,587 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 100.0%
2025-08-04 14:52:03,797 - health_monitor - DEBUG - 系统指标 - CPU: 97.3%, 内存: 65.9%, 磁盘: 84.2%
2025-08-04 14:52:19,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 100.0%
2025-08-04 14:52:34,271 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 91.7%
2025-08-04 14:52:49,602 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 85.7%
2025-08-04 14:53:04,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 95.8%
2025-08-04 14:53:05,131 - health_monitor - DEBUG - 系统指标 - CPU: 98.6%, 内存: 65.3%, 磁盘: 84.2%
2025-08-04 14:53:19,837 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 58.3%
2025-08-04 14:53:34,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 48.0%
2025-08-04 14:53:50,118 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 76.0%
2025-08-04 14:54:05,224 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 91.7%
2025-08-04 14:54:06,209 - health_monitor - DEBUG - 系统指标 - CPU: 92.1%, 内存: 64.0%, 磁盘: 84.2%
2025-08-04 14:54:20,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 100.0%
2025-08-04 14:54:35,620 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-08-04 14:54:50,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 100.0%
2025-08-04 14:55:06,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 91.8%
2025-08-04 14:55:07,275 - health_monitor - DEBUG - 系统指标 - CPU: 68.2%, 内存: 64.9%, 磁盘: 84.2%
2025-08-04 14:55:21,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 57.1%
2025-08-04 14:55:36,575 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 100.0%
2025-08-04 14:55:51,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-08-04 14:56:07,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 79.2%
2025-08-04 14:56:08,462 - health_monitor - DEBUG - 系统指标 - CPU: 82.4%, 内存: 65.5%, 磁盘: 84.2%
2025-08-04 14:56:22,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.1%, CPU使用率 98.1%
2025-08-04 14:56:37,390 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 89.7%
2025-08-04 14:56:52,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 67.9%
2025-08-04 14:57:07,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-08-04 14:57:09,729 - health_monitor - DEBUG - 系统指标 - CPU: 98.2%, 内存: 65.3%, 磁盘: 84.2%
2025-08-04 14:57:22,811 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.2%, CPU使用率 64.3%
2025-08-04 14:57:37,948 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 84.6%
2025-08-04 14:57:53,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 95.8%
2025-08-04 14:58:08,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 100.0%
2025-08-04 14:58:10,896 - health_monitor - DEBUG - 系统指标 - CPU: 87.1%, 内存: 63.0%, 磁盘: 84.2%
2025-08-04 14:58:23,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 62.5%
2025-08-04 14:58:38,615 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 79.2%
2025-08-04 14:58:53,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 81.2%
2025-08-04 14:59:09,024 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 97.9%
2025-08-04 14:59:12,475 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 63.8%, 磁盘: 84.2%
2025-08-04 14:59:24,132 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 79.2%
2025-08-04 14:59:39,259 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 92.6%
2025-08-04 14:59:54,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 79.2%
2025-08-04 15:00:09,473 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 87.5%
2025-08-04 15:00:13,818 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 64.3%, 磁盘: 84.2%
2025-08-04 15:00:24,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-08-04 15:00:39,886 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 79.2%
2025-08-04 15:00:54,499 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:00:54,520 - main - INFO - 请求没有认证头部
2025-08-04 15:00:54,528 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:00:54,533 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:00:55,277 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 100.0%
2025-08-04 15:00:56,713 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:00:56,716 - main - INFO - 请求没有认证头部
2025-08-04 15:00:56,717 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:00:56,718 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:00:56,719 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:00:56,720 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:00:56,721 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:00:56,722 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:00:58,973 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:00:59,134 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:01:11,072 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 15:01:15,886 - health_monitor - DEBUG - 系统指标 - CPU: 98.4%, 内存: 64.7%, 磁盘: 84.2%
2025-08-04 15:01:26,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-08-04 15:01:41,836 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-08-04 15:01:57,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 100.0%
2025-08-04 15:02:12,341 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 89.3%
2025-08-04 15:02:17,122 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 66.0%, 磁盘: 84.2%
2025-08-04 15:02:27,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 95.8%
2025-08-04 15:02:42,780 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-08-04 15:02:57,934 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 96.7%
2025-08-04 15:03:13,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 100.0%
2025-08-04 15:03:18,256 - health_monitor - DEBUG - 系统指标 - CPU: 91.7%, 内存: 66.2%, 磁盘: 84.2%
2025-08-04 15:03:28,612 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-08-04 15:03:44,099 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-04 15:03:59,613 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 100.0%
2025-08-04 15:04:14,820 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 100.0%
2025-08-04 15:04:19,699 - health_monitor - DEBUG - 系统指标 - CPU: 93.2%, 内存: 68.8%, 磁盘: 84.2%
2025-08-04 15:04:29,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 92.9%
2025-08-04 15:04:45,079 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 82.1%
2025-08-04 15:04:48,827 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 93.2, 阈值: 90
2025-08-04 15:05:00,187 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 75.0%
2025-08-04 15:05:15,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 85.0%
2025-08-04 15:05:20,920 - health_monitor - DEBUG - 系统指标 - CPU: 73.4%, 内存: 66.1%, 磁盘: 84.2%
2025-08-04 15:05:30,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 52.0%
2025-08-04 15:05:45,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 87.8%
2025-08-04 15:06:01,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 79.2%
2025-08-04 15:06:16,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 96.0%
2025-08-04 15:06:22,144 - health_monitor - DEBUG - 系统指标 - CPU: 75.7%, 内存: 63.3%, 磁盘: 84.2%
2025-08-04 15:06:31,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 67.9%
2025-08-04 15:06:46,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 29.2%
2025-08-04 15:07:01,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 87.5%
2025-08-04 15:07:16,924 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-08-04 15:07:23,373 - health_monitor - DEBUG - 系统指标 - CPU: 93.7%, 内存: 66.1%, 磁盘: 84.2%
2025-08-04 15:07:32,032 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 44.4%
2025-08-04 15:07:47,146 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 83.3%
2025-08-04 15:08:02,257 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 66.7%
2025-08-04 15:08:17,472 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.5%, CPU使用率 100.0%
2025-08-04 15:08:24,666 - health_monitor - DEBUG - 系统指标 - CPU: 89.6%, 内存: 63.5%, 磁盘: 84.2%
2025-08-04 15:08:32,621 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 58.3%
2025-08-04 15:08:47,852 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 100.0%
2025-08-04 15:09:03,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.7%, CPU使用率 93.1%
2025-08-04 15:09:18,110 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 48.3%
2025-08-04 15:09:25,690 - health_monitor - DEBUG - 系统指标 - CPU: 30.9%, 内存: 61.4%, 磁盘: 84.2%
2025-08-04 15:09:33,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 29.2%
2025-08-04 15:09:48,319 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 33.3%
2025-08-04 15:10:03,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 41.4%
2025-08-04 15:10:18,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 42.3%
2025-08-04 15:10:26,784 - health_monitor - DEBUG - 系统指标 - CPU: 55.4%, 内存: 59.4%, 磁盘: 84.2%
2025-08-04 15:10:33,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 44.0%
2025-08-04 15:10:48,739 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 39.3%
2025-08-04 15:11:03,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 39.3%
2025-08-04 15:11:18,953 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 70.4%
2025-08-04 15:11:27,804 - health_monitor - DEBUG - 系统指标 - CPU: 30.4%, 内存: 59.1%, 磁盘: 84.2%
2025-08-04 15:11:34,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 84.6%
2025-08-04 15:11:49,174 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-08-04 15:12:04,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 60.7%
2025-08-04 15:12:19,513 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-08-04 15:12:26,813 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:12:26,814 - main - INFO - 请求没有认证头部
2025-08-04 15:12:26,815 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:12:26,817 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:12:28,827 - health_monitor - DEBUG - 系统指标 - CPU: 15.6%, 内存: 61.3%, 磁盘: 84.2%
2025-08-04 15:12:28,832 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:12:28,833 - main - INFO - 请求没有认证头部
2025-08-04 15:12:28,834 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:12:28,835 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:12:28,836 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:12:28,837 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:12:28,838 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:12:28,839 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:12:29,613 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:12:29,615 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:12:34,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 87.2%
2025-08-04 15:12:49,859 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 0.0%
2025-08-04 15:13:04,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 26.9%
2025-08-04 15:13:20,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 37.5%
2025-08-04 15:13:29,852 - health_monitor - DEBUG - 系统指标 - CPU: 32.3%, 内存: 62.0%, 磁盘: 84.2%
2025-08-04 15:13:35,177 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 0.0%
2025-08-04 15:13:50,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 100.0%
2025-08-04 15:14:05,547 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 78.6%
2025-08-04 15:14:20,652 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 48.0%
2025-08-04 15:14:30,874 - health_monitor - DEBUG - 系统指标 - CPU: 19.0%, 内存: 62.1%, 磁盘: 84.2%
2025-08-04 15:14:35,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 4.2%
2025-08-04 15:14:50,861 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 10.7%
2025-08-04 15:14:51,640 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:14:51,640 - main - INFO - 请求没有认证头部
2025-08-04 15:14:51,641 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:14:51,643 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:14:53,696 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:14:53,697 - main - INFO - 请求没有认证头部
2025-08-04 15:14:53,697 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:14:53,699 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:14:53,699 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:14:53,700 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:14:53,702 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:14:53,703 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:14:54,444 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:14:54,446 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:15:05,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 75.0%
2025-08-04 15:15:21,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 7.7%
2025-08-04 15:15:31,894 - health_monitor - DEBUG - 系统指标 - CPU: 25.5%, 内存: 62.0%, 磁盘: 84.2%
2025-08-04 15:15:36,181 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 0.0%
2025-08-04 15:15:51,286 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 0.0%
2025-08-04 15:16:06,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 55.2%
2025-08-04 15:16:21,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 0.0%
2025-08-04 15:16:32,918 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 61.6%, 磁盘: 84.2%
2025-08-04 15:16:36,632 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 70.8%
2025-08-04 15:16:51,738 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 22.2%
2025-08-04 15:17:06,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 3.6%
2025-08-04 15:17:21,948 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 45.8%
2025-08-04 15:17:33,941 - health_monitor - DEBUG - 系统指标 - CPU: 17.3%, 内存: 61.8%, 磁盘: 84.2%
2025-08-04 15:17:37,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 12.5%
2025-08-04 15:17:52,158 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 30.8%
2025-08-04 15:18:07,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 22.2%
2025-08-04 15:18:22,399 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 38.5%
2025-08-04 15:18:35,085 - health_monitor - DEBUG - 系统指标 - CPU: 47.5%, 内存: 61.7%, 磁盘: 84.2%
2025-08-04 15:18:37,506 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 56.0%
2025-08-04 15:18:52,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 0.0%
2025-08-04 15:19:07,717 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 67.9%
2025-08-04 15:19:22,822 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 0.0%
2025-08-04 15:19:36,160 - health_monitor - DEBUG - 系统指标 - CPU: 17.3%, 内存: 61.9%, 磁盘: 84.2%
2025-08-04 15:19:37,927 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 26.9%
2025-08-04 15:19:53,032 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 25.0%
2025-08-04 15:20:08,137 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 0.0%
2025-08-04 15:20:23,242 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 20.8%
2025-08-04 15:20:37,180 - health_monitor - DEBUG - 系统指标 - CPU: 18.8%, 内存: 61.6%, 磁盘: 84.2%
2025-08-04 15:20:38,347 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 12.5%
2025-08-04 15:20:53,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 17.9%
2025-08-04 15:21:08,557 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 4.2%
2025-08-04 15:21:23,670 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 66.7%
2025-08-04 15:21:38,207 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 61.7%, 磁盘: 84.2%
2025-08-04 15:21:38,793 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 92.0%
2025-08-04 15:21:53,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 12.5%
2025-08-04 15:22:07,732 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:22:07,733 - main - INFO - 请求没有认证头部
2025-08-04 15:22:07,733 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:22:07,735 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:22:09,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 0.0%
2025-08-04 15:22:09,780 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:22:09,781 - main - INFO - 请求没有认证头部
2025-08-04 15:22:09,782 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:22:09,783 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:22:09,784 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:22:09,785 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:22:09,785 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:22:09,786 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:22:10,755 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:22:10,757 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:22:24,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 20.7%
2025-08-04 15:22:39,228 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 67.9%
2025-08-04 15:22:39,228 - health_monitor - DEBUG - 系统指标 - CPU: 32.8%, 内存: 62.8%, 磁盘: 84.2%
2025-08-04 15:22:54,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.7%, CPU使用率 25.9%
2025-08-04 15:23:09,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 14.3%
2025-08-04 15:23:24,544 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 25.9%
2025-08-04 15:23:39,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 8.3%
2025-08-04 15:23:40,250 - health_monitor - DEBUG - 系统指标 - CPU: 16.8%, 内存: 62.6%, 磁盘: 84.2%
2025-08-04 15:23:54,753 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 7.1%
2025-08-04 15:24:09,858 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 15.4%
2025-08-04 15:24:24,962 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 0.0%
2025-08-04 15:24:40,088 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 81.5%
2025-08-04 15:24:41,270 - health_monitor - DEBUG - 系统指标 - CPU: 9.3%, 内存: 62.1%, 磁盘: 84.2%
2025-08-04 15:24:55,192 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 18.5%
2025-08-04 15:25:10,296 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 0.0%
2025-08-04 15:25:25,401 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 0.0%
2025-08-04 15:25:40,506 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 42.3%
2025-08-04 15:25:42,300 - health_monitor - DEBUG - 系统指标 - CPU: 32.8%, 内存: 62.4%, 磁盘: 84.2%
2025-08-04 15:25:55,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 3.6%
2025-08-04 15:26:10,716 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 0.0%
2025-08-04 15:26:25,820 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 20.8%
2025-08-04 15:26:40,925 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 12.0%
2025-08-04 15:26:43,320 - health_monitor - DEBUG - 系统指标 - CPU: 28.5%, 内存: 62.0%, 磁盘: 84.2%
2025-08-04 15:26:56,032 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.0%, CPU使用率 14.3%
2025-08-04 15:27:11,137 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 0.0%
2025-08-04 15:27:26,241 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 4.2%
2025-08-04 15:27:41,379 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 90.6%
2025-08-04 15:27:44,342 - health_monitor - DEBUG - 系统指标 - CPU: 37.4%, 内存: 62.6%, 磁盘: 84.2%
2025-08-04 15:27:56,489 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 20.0%
2025-08-04 15:28:11,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 41.4%
2025-08-04 15:28:26,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 23.1%
2025-08-04 15:28:41,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 50.0%
2025-08-04 15:28:45,364 - health_monitor - DEBUG - 系统指标 - CPU: 30.9%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 15:28:56,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 32.1%
2025-08-04 15:29:12,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 92.9%
2025-08-04 15:29:27,140 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 50.0%
2025-08-04 15:29:42,361 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 82.4%
2025-08-04 15:29:46,410 - health_monitor - DEBUG - 系统指标 - CPU: 92.7%, 内存: 59.8%, 磁盘: 84.2%
2025-08-04 15:29:57,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 71.4%
2025-08-04 15:30:12,732 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 100.0%
2025-08-04 15:30:27,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 100.0%
2025-08-04 15:30:43,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 64.3%
2025-08-04 15:30:45,941 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:30:45,947 - main - INFO - 请求没有认证头部
2025-08-04 15:30:45,947 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:30:45,949 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:30:47,437 - health_monitor - DEBUG - 系统指标 - CPU: 64.5%, 内存: 62.1%, 磁盘: 84.2%
2025-08-04 15:30:48,348 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:30:48,349 - main - INFO - 请求没有认证头部
2025-08-04 15:30:48,349 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:30:48,351 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:30:48,352 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:30:48,353 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 15:30:48,354 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:30:48,355 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:30:48,356 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:30:49,809 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:30:49,811 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:30:58,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 100.0%
2025-08-04 15:31:13,503 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 100.0%
2025-08-04 15:31:28,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 54.2%
2025-08-04 15:31:43,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 97.1%
2025-08-04 15:31:48,463 - health_monitor - DEBUG - 系统指标 - CPU: 62.9%, 内存: 61.4%, 磁盘: 84.2%
2025-08-04 15:31:59,222 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 100.0%
2025-08-04 15:32:11,200 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:32:11,200 - main - INFO - 请求没有认证头部
2025-08-04 15:32:11,201 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:32:11,202 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:32:13,222 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:32:13,223 - main - INFO - 请求没有认证头部
2025-08-04 15:32:13,224 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:32:13,225 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:32:13,226 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:32:13,227 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 15:32:13,227 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:32:13,228 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:32:13,229 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:32:14,417 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:32:14,419 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:32:14,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.5%, CPU使用率 75.8%
2025-08-04 15:32:29,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.3%, CPU使用率 50.0%
2025-08-04 15:32:44,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 29.2%
2025-08-04 15:32:49,484 - health_monitor - DEBUG - 系统指标 - CPU: 32.9%, 内存: 64.4%, 磁盘: 84.2%
2025-08-04 15:32:59,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 46.9%
2025-08-04 15:33:14,890 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 31.0%
2025-08-04 15:33:29,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 16.7%
2025-08-04 15:33:45,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 87.5%
2025-08-04 15:33:50,507 - health_monitor - DEBUG - 系统指标 - CPU: 39.1%, 内存: 61.5%, 磁盘: 84.2%
2025-08-04 15:34:00,205 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 28.6%
2025-08-04 15:34:15,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 33.3%
2025-08-04 15:34:30,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 33.3%
2025-08-04 15:34:44,858 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 15:34:44,859 - main - INFO - 请求没有认证头部
2025-08-04 15:34:44,860 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:34:44,861 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:34:45,521 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 28.0%
2025-08-04 15:34:46,900 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 15:34:46,901 - main - INFO - 请求没有认证头部
2025-08-04 15:34:46,902 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 15:34:46,903 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 15:34:46,904 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 15:34:46,905 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 15:34:46,906 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 15:34:46,907 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 15:34:46,908 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 15:34:47,853 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 15:34:47,855 - main - INFO - --- 请求结束: 200 ---

2025-08-04 15:34:51,538 - health_monitor - DEBUG - 系统指标 - CPU: 53.3%, 内存: 63.0%, 磁盘: 84.2%
2025-08-04 15:35:00,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 50.0%
2025-08-04 15:35:15,735 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 13.8%
2025-08-04 15:35:30,840 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 25.0%
2025-08-04 15:35:45,944 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 58.3%
2025-08-04 15:35:52,567 - health_monitor - DEBUG - 系统指标 - CPU: 37.1%, 内存: 61.4%, 磁盘: 84.2%
2025-08-04 15:36:01,071 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 53.6%
2025-08-04 15:36:16,178 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 31.8%
2025-08-04 15:36:31,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 17.9%
2025-08-04 15:36:46,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 62.5%
2025-08-04 15:36:53,591 - health_monitor - DEBUG - 系统指标 - CPU: 29.2%, 内存: 60.7%, 磁盘: 84.2%
2025-08-04 15:37:01,490 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 29.2%
2025-08-04 15:37:16,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 35.7%
2025-08-04 15:37:31,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 65.5%
2025-08-04 15:37:46,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 16.7%
2025-08-04 15:37:54,611 - health_monitor - DEBUG - 系统指标 - CPU: 34.7%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 15:38:01,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 35.7%
2025-08-04 15:38:17,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 45.8%
2025-08-04 15:38:32,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 40.7%
2025-08-04 15:38:47,232 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 38.5%
2025-08-04 15:38:55,632 - health_monitor - DEBUG - 系统指标 - CPU: 38.6%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:39:02,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 25.0%
2025-08-04 15:39:17,442 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 20.8%
2025-08-04 15:39:32,551 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 32.1%
2025-08-04 15:39:47,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 30.8%
2025-08-04 15:39:56,655 - health_monitor - DEBUG - 系统指标 - CPU: 32.9%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 15:40:02,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 29.2%
2025-08-04 15:40:17,866 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 16.7%
2025-08-04 15:40:32,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 66.7%
2025-08-04 15:40:48,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 40.0%
2025-08-04 15:40:57,730 - health_monitor - DEBUG - 系统指标 - CPU: 44.7%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 15:41:03,181 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 33.3%
2025-08-04 15:41:18,286 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 44.4%
2025-08-04 15:41:33,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 35.7%
2025-08-04 15:41:48,496 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 50.0%
2025-08-04 15:41:58,778 - health_monitor - DEBUG - 系统指标 - CPU: 59.4%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:42:03,601 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 29.2%
2025-08-04 15:42:18,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 82.8%
2025-08-04 15:42:33,810 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 34.6%
2025-08-04 15:42:48,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 8.3%
2025-08-04 15:42:59,801 - health_monitor - DEBUG - 系统指标 - CPU: 35.9%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 15:43:04,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 37.0%
2025-08-04 15:43:19,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 42.9%
2025-08-04 15:43:34,245 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 58.3%
2025-08-04 15:43:49,349 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 17.4%
2025-08-04 15:44:00,826 - health_monitor - DEBUG - 系统指标 - CPU: 38.4%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:44:04,454 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 42.9%
2025-08-04 15:44:19,559 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 63.0%
2025-08-04 15:44:34,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 33.3%
2025-08-04 15:44:49,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 44.8%
2025-08-04 15:45:01,848 - health_monitor - DEBUG - 系统指标 - CPU: 35.7%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 15:45:04,872 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 12.0%
2025-08-04 15:45:19,978 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 37.5%
2025-08-04 15:45:35,083 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 30.8%
2025-08-04 15:45:50,187 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 25.0%
2025-08-04 15:46:02,874 - health_monitor - DEBUG - 系统指标 - CPU: 30.4%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:46:05,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 41.7%
2025-08-04 15:46:20,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 30.4%
2025-08-04 15:46:35,503 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 50.0%
2025-08-04 15:46:50,608 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 29.6%
2025-08-04 15:47:03,895 - health_monitor - DEBUG - 系统指标 - CPU: 41.8%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 15:47:05,713 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 45.8%
2025-08-04 15:47:20,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 29.2%
2025-08-04 15:47:35,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 57.1%
2025-08-04 15:47:51,027 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 41.7%
2025-08-04 15:48:04,916 - health_monitor - DEBUG - 系统指标 - CPU: 32.2%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:48:06,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 36.4%
2025-08-04 15:48:21,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 50.0%
2025-08-04 15:48:36,341 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 25.0%
2025-08-04 15:48:51,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 20.8%
2025-08-04 15:49:05,939 - health_monitor - DEBUG - 系统指标 - CPU: 37.3%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 15:49:06,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 37.0%
2025-08-04 15:49:21,676 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 29.2%
2025-08-04 15:49:36,781 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 26.9%
2025-08-04 15:49:51,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 33.3%
2025-08-04 15:50:06,959 - health_monitor - DEBUG - 系统指标 - CPU: 33.5%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:50:06,991 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 50.0%
2025-08-04 15:50:22,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 15.4%
2025-08-04 15:50:37,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 32.1%
2025-08-04 15:50:52,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 16.7%
2025-08-04 15:51:07,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 37.0%
2025-08-04 15:51:07,979 - health_monitor - DEBUG - 系统指标 - CPU: 30.6%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 15:51:22,514 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 50.0%
2025-08-04 15:51:37,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 24.0%
2025-08-04 15:51:52,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 33.3%
2025-08-04 15:52:07,829 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 46.2%
2025-08-04 15:52:09,000 - health_monitor - DEBUG - 系统指标 - CPU: 31.8%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 15:52:22,934 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 34.6%
2025-08-04 15:52:38,039 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 8.3%
2025-08-04 15:52:53,143 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 3.7%
2025-08-04 15:53:08,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 53.6%
2025-08-04 15:53:10,045 - health_monitor - DEBUG - 系统指标 - CPU: 10.5%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:53:23,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 45.8%
2025-08-04 15:53:38,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 11.5%
2025-08-04 15:53:53,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 3.7%
2025-08-04 15:54:08,667 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 8.3%
2025-08-04 15:54:11,067 - health_monitor - DEBUG - 系统指标 - CPU: 20.2%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:54:23,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 14.8%
2025-08-04 15:54:38,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 32.1%
2025-08-04 15:54:53,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 0.0%
2025-08-04 15:55:09,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 4.2%
2025-08-04 15:55:12,091 - health_monitor - DEBUG - 系统指标 - CPU: 35.9%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 15:55:24,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 25.0%
2025-08-04 15:55:39,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 3.6%
2025-08-04 15:55:54,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 39.3%
2025-08-04 15:56:09,513 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 42.3%
2025-08-04 15:56:13,118 - health_monitor - DEBUG - 系统指标 - CPU: 26.2%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 15:56:24,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 15:56:39,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 16.0%
2025-08-04 15:56:54,827 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 15:57:09,935 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 100.0%
2025-08-04 15:57:14,139 - health_monitor - DEBUG - 系统指标 - CPU: 49.6%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 15:57:25,043 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 15:57:40,147 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 8.3%
2025-08-04 15:57:55,252 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 25.0%
2025-08-04 15:58:10,357 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 15:58:15,188 - health_monitor - DEBUG - 系统指标 - CPU: 42.2%, 内存: 60.6%, 磁盘: 84.2%
2025-08-04 15:58:25,462 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 12.5%
2025-08-04 15:58:40,567 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 7.7%
2025-08-04 15:58:55,672 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 28.6%
2025-08-04 15:59:10,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 0.0%
2025-08-04 15:59:16,227 - health_monitor - DEBUG - 系统指标 - CPU: 18.4%, 内存: 60.6%, 磁盘: 84.2%
2025-08-04 15:59:25,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 4.2%
2025-08-04 15:59:40,986 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 14.3%
2025-08-04 15:59:56,090 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 7.7%
2025-08-04 16:00:11,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 12.5%
2025-08-04 16:00:17,257 - health_monitor - DEBUG - 系统指标 - CPU: 9.4%, 内存: 60.4%, 磁盘: 84.2%
2025-08-04 16:00:26,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 19.2%
2025-08-04 16:00:41,430 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 42.9%
2025-08-04 16:00:56,534 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:01:11,639 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 27.6%
2025-08-04 16:01:18,281 - health_monitor - DEBUG - 系统指标 - CPU: 16.7%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:01:26,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 8.3%
2025-08-04 16:01:41,848 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 4.2%
2025-08-04 16:01:56,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 78.6%
2025-08-04 16:02:12,082 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 25.0%
2025-08-04 16:02:19,306 - health_monitor - DEBUG - 系统指标 - CPU: 18.9%, 内存: 60.7%, 磁盘: 84.2%
2025-08-04 16:02:27,188 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 3.6%
2025-08-04 16:02:42,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 45.8%
2025-08-04 16:02:57,396 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:03:12,502 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 7.1%
2025-08-04 16:03:20,334 - health_monitor - DEBUG - 系统指标 - CPU: 6.6%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:03:27,607 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 18.5%
2025-08-04 16:03:42,711 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 20.8%
2025-08-04 16:03:57,817 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:04:12,922 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 30.8%
2025-08-04 16:04:21,356 - health_monitor - DEBUG - 系统指标 - CPU: 10.2%, 内存: 60.7%, 磁盘: 84.2%
2025-08-04 16:04:28,028 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 11.5%
2025-08-04 16:04:43,134 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 16.7%
2025-08-04 16:04:58,240 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 13.6%
2025-08-04 16:05:13,346 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 10.7%
2025-08-04 16:05:22,377 - health_monitor - DEBUG - 系统指标 - CPU: 16.9%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:05:28,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 55.6%
2025-08-04 16:05:43,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 12.5%
2025-08-04 16:05:58,662 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 66.7%
2025-08-04 16:06:13,768 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 82.1%
2025-08-04 16:06:23,397 - health_monitor - DEBUG - 系统指标 - CPU: 12.5%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:06:28,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 51.9%
2025-08-04 16:06:44,002 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 93.8%
2025-08-04 16:06:59,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 67.9%
2025-08-04 16:07:14,215 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 54.2%
2025-08-04 16:07:24,417 - health_monitor - DEBUG - 系统指标 - CPU: 13.6%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:07:29,321 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 0.0%
2025-08-04 16:07:44,425 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:07:59,529 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 17.9%
2025-08-04 16:08:14,634 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 20.8%
2025-08-04 16:08:25,439 - health_monitor - DEBUG - 系统指标 - CPU: 8.6%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:08:29,739 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:08:44,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 7.1%
2025-08-04 16:08:59,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 29.2%
2025-08-04 16:09:15,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 58.3%
2025-08-04 16:09:26,459 - health_monitor - DEBUG - 系统指标 - CPU: 14.2%, 内存: 60.6%, 磁盘: 84.2%
2025-08-04 16:09:30,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 50.0%
2025-08-04 16:09:45,262 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 19.2%
2025-08-04 16:10:00,368 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:10:15,472 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 6.9%
2025-08-04 16:10:27,479 - health_monitor - DEBUG - 系统指标 - CPU: 12.6%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:10:30,576 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 25.9%
2025-08-04 16:10:45,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 0.0%
2025-08-04 16:11:00,786 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 37.5%
2025-08-04 16:11:15,891 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:11:28,501 - health_monitor - DEBUG - 系统指标 - CPU: 11.5%, 内存: 60.6%, 磁盘: 84.2%
2025-08-04 16:11:30,996 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 37.5%
2025-08-04 16:11:46,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 0.0%
2025-08-04 16:12:01,206 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 0.0%
2025-08-04 16:12:16,311 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 19.2%
2025-08-04 16:12:29,522 - health_monitor - DEBUG - 系统指标 - CPU: 40.6%, 内存: 59.9%, 磁盘: 84.2%
2025-08-04 16:12:31,416 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 8.3%
2025-08-04 16:12:46,520 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 12.0%
2025-08-04 16:13:01,624 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 7.1%
2025-08-04 16:13:16,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 20.8%
2025-08-04 16:13:30,543 - health_monitor - DEBUG - 系统指标 - CPU: 10.5%, 内存: 59.9%, 磁盘: 84.2%
2025-08-04 16:13:31,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 24.0%
2025-08-04 16:13:46,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 11.1%
2025-08-04 16:14:02,043 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 20.0%
2025-08-04 16:14:17,148 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 0.0%
2025-08-04 16:14:31,563 - health_monitor - DEBUG - 系统指标 - CPU: 17.9%, 内存: 59.9%, 磁盘: 84.2%
2025-08-04 16:14:32,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 28.6%
2025-08-04 16:14:47,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 74.1%
2025-08-04 16:15:02,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 41.7%
2025-08-04 16:15:17,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 14.3%
2025-08-04 16:15:32,587 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 59.9%, 磁盘: 84.2%
2025-08-04 16:15:32,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 0.0%
2025-08-04 16:15:47,866 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 12.5%
2025-08-04 16:16:02,974 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 33.3%
2025-08-04 16:16:18,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 17.9%
2025-08-04 16:16:33,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 37.5%
2025-08-04 16:16:33,608 - health_monitor - DEBUG - 系统指标 - CPU: 13.2%, 内存: 60.0%, 磁盘: 84.2%
2025-08-04 16:16:48,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 0.0%
2025-08-04 16:17:03,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 0.0%
2025-08-04 16:17:18,497 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 51.9%
2025-08-04 16:17:33,602 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 16.7%
2025-08-04 16:17:34,629 - health_monitor - DEBUG - 系统指标 - CPU: 10.1%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:17:48,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 3.7%
2025-08-04 16:18:03,811 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 18.5%
2025-08-04 16:18:18,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 4.2%
2025-08-04 16:18:34,019 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 16.7%
2025-08-04 16:18:35,649 - health_monitor - DEBUG - 系统指标 - CPU: 18.4%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:18:49,124 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 3.6%
2025-08-04 16:19:04,229 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 0.0%
2025-08-04 16:19:19,334 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 8.3%
2025-08-04 16:19:34,439 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 19.2%
2025-08-04 16:19:36,684 - health_monitor - DEBUG - 系统指标 - CPU: 14.5%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:19:49,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 0.0%
2025-08-04 16:20:04,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:20:19,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 0.0%
2025-08-04 16:20:34,857 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 59.3%
2025-08-04 16:20:37,705 - health_monitor - DEBUG - 系统指标 - CPU: 13.9%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:20:49,961 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:21:05,067 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 12.0%
2025-08-04 16:21:20,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 7.7%
2025-08-04 16:21:35,276 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 4.2%
2025-08-04 16:21:38,727 - health_monitor - DEBUG - 系统指标 - CPU: 15.6%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:21:50,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 12.0%
2025-08-04 16:22:05,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 14.8%
2025-08-04 16:22:20,591 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 19.2%
2025-08-04 16:22:35,695 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 12.5%
2025-08-04 16:22:39,747 - health_monitor - DEBUG - 系统指标 - CPU: 14.1%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:22:50,800 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:23:05,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 17.9%
2025-08-04 16:23:21,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 8.3%
2025-08-04 16:23:36,115 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:23:40,769 - health_monitor - DEBUG - 系统指标 - CPU: 11.7%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:23:51,219 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 28.6%
2025-08-04 16:24:06,324 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 8.3%
2025-08-04 16:24:21,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 0.0%
2025-08-04 16:24:36,533 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 7.7%
2025-08-04 16:24:41,789 - health_monitor - DEBUG - 系统指标 - CPU: 16.4%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:24:51,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 7.1%
2025-08-04 16:25:06,742 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 25.0%
2025-08-04 16:25:21,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 50.0%
2025-08-04 16:25:36,952 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 7.4%
2025-08-04 16:25:42,812 - health_monitor - DEBUG - 系统指标 - CPU: 21.3%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:25:52,074 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 87.5%
2025-08-04 16:26:07,178 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:26:22,283 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 38.5%
2025-08-04 16:26:37,387 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 0.0%
2025-08-04 16:26:43,832 - health_monitor - DEBUG - 系统指标 - CPU: 27.7%, 内存: 60.1%, 磁盘: 84.2%
2025-08-04 16:26:52,492 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 33.3%
2025-08-04 16:27:07,596 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 0.0%
2025-08-04 16:27:22,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 38.5%
2025-08-04 16:27:37,807 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 33.3%
2025-08-04 16:27:44,857 - health_monitor - DEBUG - 系统指标 - CPU: 49.0%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:27:52,911 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 12.5%
2025-08-04 16:28:08,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:28:23,120 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-08-04 16:28:38,225 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:28:45,879 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:28:53,329 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 10.7%
2025-08-04 16:29:08,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 14.8%
2025-08-04 16:29:23,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:29:38,644 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 40.0%
2025-08-04 16:29:46,898 - health_monitor - DEBUG - 系统指标 - CPU: 14.8%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:29:53,750 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 25.0%
2025-08-04 16:30:08,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 0.0%
2025-08-04 16:30:23,960 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 36.0%
2025-08-04 16:30:39,065 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 7.1%
2025-08-04 16:30:47,923 - health_monitor - DEBUG - 系统指标 - CPU: 14.8%, 内存: 60.5%, 磁盘: 84.2%
2025-08-04 16:30:54,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 0.0%
2025-08-04 16:31:09,280 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 61.5%
2025-08-04 16:31:24,384 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 4.2%
2025-08-04 16:31:39,488 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-08-04 16:31:48,943 - health_monitor - DEBUG - 系统指标 - CPU: 22.3%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:31:54,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 46.4%
2025-08-04 16:32:09,699 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-08-04 16:32:24,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 9.1%
2025-08-04 16:32:39,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 10.7%
2025-08-04 16:32:49,970 - health_monitor - DEBUG - 系统指标 - CPU: 17.6%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:32:55,012 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 14.8%
2025-08-04 16:33:10,117 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 4.2%
2025-08-04 16:33:25,222 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 25.0%
2025-08-04 16:33:40,327 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-08-04 16:33:50,993 - health_monitor - DEBUG - 系统指标 - CPU: 13.3%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:33:55,431 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 4.2%
2025-08-04 16:34:10,536 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 12.5%
2025-08-04 16:34:25,640 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 23.1%
2025-08-04 16:34:40,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 16.7%
2025-08-04 16:34:52,016 - health_monitor - DEBUG - 系统指标 - CPU: 7.8%, 内存: 60.2%, 磁盘: 84.2%
2025-08-04 16:34:55,849 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:35:10,954 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 11.1%
2025-08-04 16:35:26,058 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 33.3%
2025-08-04 16:35:41,162 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 0.0%
2025-08-04 16:35:53,040 - health_monitor - DEBUG - 系统指标 - CPU: 6.6%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:35:56,267 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 7.4%
2025-08-04 16:36:11,372 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 18.5%
2025-08-04 16:36:26,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 4.2%
2025-08-04 16:36:41,582 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.3%, CPU使用率 14.8%
2025-08-04 16:36:54,060 - health_monitor - DEBUG - 系统指标 - CPU: 11.3%, 内存: 60.3%, 磁盘: 84.2%
2025-08-04 16:36:56,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 0.0%
2025-08-04 16:37:11,791 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 33.3%
2025-08-04 16:37:26,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 45.8%
2025-08-04 16:37:42,002 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 67.9%
2025-08-04 16:37:55,202 - health_monitor - DEBUG - 系统指标 - CPU: 70.5%, 内存: 62.4%, 磁盘: 84.2%
2025-08-04 16:37:57,107 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 30.8%
2025-08-04 16:38:12,212 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 75.0%
2025-08-04 16:38:27,316 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 46.4%
2025-08-04 16:38:42,421 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 14.3%
2025-08-04 16:38:56,223 - health_monitor - DEBUG - 系统指标 - CPU: 33.6%, 内存: 60.9%, 磁盘: 84.2%
2025-08-04 16:38:57,525 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 0.0%
2025-08-04 16:39:12,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 91.9%
2025-08-04 16:39:27,792 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 92.0%
2025-08-04 16:39:38,229 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 16:39:38,230 - main - INFO - 请求没有认证头部
2025-08-04 16:39:38,231 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 16:39:38,233 - main - INFO - --- 请求结束: 200 ---

2025-08-04 16:39:40,299 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 16:39:40,300 - main - INFO - 请求没有认证头部
2025-08-04 16:39:40,301 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 16:39:40,303 - app.core.db_connection - DEBUG - 当前线程ID: 7956
2025-08-04 16:39:40,304 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 16:39:40,305 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 16:39:40,306 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 16:39:40,306 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 16:39:40,307 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 16:39:40,310 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,311 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,312 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,313 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,313 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,314 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,315 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:40,316 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 16:39:41,088 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 16:39:41,090 - main - INFO - --- 请求结束: 200 ---

2025-08-04 16:39:42,959 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 81.8%
2025-08-04 16:39:57,249 - health_monitor - DEBUG - 系统指标 - CPU: 44.9%, 内存: 64.8%, 磁盘: 84.2%
2025-08-04 16:39:58,065 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 35.7%
2025-08-04 16:40:13,169 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 51.9%
2025-08-04 16:40:28,275 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 45.8%
2025-08-04 16:40:43,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 65.5%
2025-08-04 16:40:58,309 - health_monitor - DEBUG - 系统指标 - CPU: 29.7%, 内存: 61.0%, 磁盘: 84.2%
2025-08-04 16:40:58,489 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 50.0%
2025-08-04 16:41:13,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 37.0%
2025-08-04 16:41:28,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.3%, CPU使用率 87.5%
2025-08-04 16:41:43,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 65.5%
2025-08-04 16:41:58,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.9%, CPU使用率 39.3%
2025-08-04 16:41:59,334 - health_monitor - DEBUG - 系统指标 - CPU: 63.6%, 内存: 65.6%, 磁盘: 84.2%
2025-08-04 16:42:14,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 53.6%
2025-08-04 16:42:29,220 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.1%, CPU使用率 100.0%
2025-08-04 16:42:44,362 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 39.3%
2025-08-04 16:42:59,468 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.1%, CPU使用率 78.6%
2025-08-04 16:43:00,414 - health_monitor - DEBUG - 系统指标 - CPU: 85.9%, 内存: 66.9%, 磁盘: 84.2%
2025-08-04 16:43:14,575 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 89.3%
2025-08-04 16:43:30,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-08-04 16:43:45,692 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 100.0%
2025-08-04 16:44:01,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 100.0%
2025-08-04 16:44:01,667 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 65.5%, 磁盘: 84.3%
2025-08-04 16:44:17,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.8%, CPU使用率 100.0%
2025-08-04 16:44:32,629 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 100.0%
2025-08-04 16:44:48,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 100.0%
2025-08-04 16:45:02,760 - health_monitor - DEBUG - 系统指标 - CPU: 84.7%, 内存: 65.2%, 磁盘: 84.2%
2025-08-04 16:45:03,240 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.5%, CPU使用率 80.6%
2025-08-04 16:45:19,107 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 100.0%
2025-08-04 16:45:34,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.2%, CPU使用率 96.9%
2025-08-04 16:45:49,583 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.4%, CPU使用率 100.0%
2025-08-04 16:46:04,019 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 66.4%, 磁盘: 84.2%
2025-08-04 16:46:05,269 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.0%, CPU使用率 100.0%
2025-08-04 16:46:20,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.2%, CPU使用率 100.0%
2025-08-04 16:46:37,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 100.0%
2025-08-04 16:46:52,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 100.0%
2025-08-04 16:47:05,111 - health_monitor - DEBUG - 系统指标 - CPU: 97.4%, 内存: 67.4%, 磁盘: 84.3%
2025-08-04 16:47:07,944 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.1%, CPU使用率 100.0%
2025-08-04 16:47:23,063 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 16:47:38,881 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 16:47:54,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-08-04 16:48:07,611 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.9%, 磁盘: 84.2%
2025-08-04 16:48:09,619 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 98.2%
2025-08-04 16:48:25,169 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-08-04 16:48:41,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 100.0%
2025-08-04 16:48:56,833 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-08-04 16:49:09,028 - health_monitor - DEBUG - 系统指标 - CPU: 94.5%, 内存: 68.9%, 磁盘: 84.2%
2025-08-04 16:49:12,496 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 93.5%
2025-08-04 16:49:27,798 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 100.0%
2025-08-04 16:49:43,360 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-08-04 16:49:59,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 100.0%
2025-08-04 16:50:10,255 - health_monitor - DEBUG - 系统指标 - CPU: 99.0%, 内存: 68.2%, 磁盘: 84.2%
2025-08-04 16:50:14,334 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 16:50:29,564 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 98.0%
2025-08-04 16:50:44,676 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 96.6%
2025-08-04 16:50:59,796 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-04 16:51:11,396 - health_monitor - DEBUG - 系统指标 - CPU: 93.7%, 内存: 70.4%, 磁盘: 84.2%
2025-08-04 16:51:14,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 16:51:30,080 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 96.8%
2025-08-04 16:51:45,711 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 100.0%
2025-08-04 16:51:49,355 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 93.7, 阈值: 90
2025-08-04 16:52:01,134 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 100.0%
2025-08-04 16:52:12,800 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 70.3%, 磁盘: 84.3%
2025-08-04 16:52:16,551 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 16:52:32,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-04 16:52:47,515 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 85.7%
2025-08-04 16:53:03,397 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 100.0%
2025-08-04 16:53:13,987 - health_monitor - DEBUG - 系统指标 - CPU: 98.5%, 内存: 73.8%, 磁盘: 84.3%
2025-08-04 16:53:19,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 100.0%
2025-08-04 16:53:35,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-08-04 16:53:51,680 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 100.0%
2025-08-04 16:54:07,333 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-08-04 16:54:15,217 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 72.8%, 磁盘: 84.3%
2025-08-04 16:54:22,707 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-08-04 16:54:38,251 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 100.0%
2025-08-04 16:54:53,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 100.0%
2025-08-04 16:55:09,367 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 100.0%
2025-08-04 16:55:16,351 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 71.4%, 磁盘: 84.2%
2025-08-04 16:55:24,686 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 76.7%
2025-08-04 16:55:39,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-08-04 16:55:54,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 88.0%
2025-08-04 16:56:10,291 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 100.0%
2025-08-04 16:56:18,744 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 72.5%, 磁盘: 84.3%
2025-08-04 16:56:25,662 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.6%, CPU使用率 100.0%
2025-08-04 16:56:41,281 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 100.0%
2025-08-04 16:56:56,724 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 100.0%
2025-08-04 16:57:12,272 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-08-04 16:57:20,657 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 74.2%, 磁盘: 84.3%
2025-08-04 16:57:27,683 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-04 16:57:43,185 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 100.0%
2025-08-04 16:57:58,643 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-04 16:58:14,329 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 93.9%
2025-08-04 16:58:21,748 - health_monitor - DEBUG - 系统指标 - CPU: 90.8%, 内存: 75.3%, 磁盘: 84.3%
2025-08-04 16:58:29,530 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.0%, CPU使用率 97.1%
2025-08-04 16:58:44,892 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.4%, CPU使用率 100.0%
2025-08-04 16:59:00,118 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.0%, CPU使用率 100.0%
2025-08-04 16:59:15,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 92.9%
2025-08-04 16:59:24,040 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 74.3%, 磁盘: 84.3%
2025-08-04 16:59:31,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 100.0%
2025-08-04 16:59:46,237 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.2%, CPU使用率 78.1%
2025-08-04 17:00:01,595 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.1%, CPU使用率 100.0%
2025-08-04 17:00:16,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.7%, CPU使用率 96.2%
2025-08-04 17:00:25,530 - health_monitor - DEBUG - 系统指标 - CPU: 84.8%, 内存: 74.2%, 磁盘: 84.3%
2025-08-04 17:00:31,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.9%, CPU使用率 93.5%
2025-08-04 17:00:46,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.7%, CPU使用率 93.1%
2025-08-04 17:01:02,144 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.2%, CPU使用率 100.0%
2025-08-04 17:01:17,269 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.0%, CPU使用率 88.0%
2025-08-04 17:01:26,739 - health_monitor - DEBUG - 系统指标 - CPU: 93.9%, 内存: 76.5%, 磁盘: 84.3%
2025-08-04 17:01:32,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.5%, CPU使用率 72.4%
2025-08-04 17:01:47,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.6%, CPU使用率 100.0%
2025-08-04 17:02:02,787 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 100.0%
2025-08-04 17:02:18,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.1%, CPU使用率 100.0%
2025-08-04 17:02:27,955 - health_monitor - DEBUG - 系统指标 - CPU: 91.7%, 内存: 78.5%, 磁盘: 84.3%
2025-08-04 17:02:33,963 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.7%, CPU使用率 100.0%
2025-08-04 17:02:49,566 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.1%, CPU使用率 100.0%
2025-08-04 17:03:05,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.4%, CPU使用率 100.0%
2025-08-04 17:03:20,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.5%, CPU使用率 100.0%
2025-08-04 17:03:29,210 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 77.6%, 磁盘: 84.3%
2025-08-04 17:03:35,948 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.5%, CPU使用率 100.0%
2025-08-04 17:03:51,407 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.5%, CPU使用率 100.0%
2025-08-04 17:04:06,760 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.6%, CPU使用率 97.4%
2025-08-04 17:04:21,929 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.7%, CPU使用率 91.7%
2025-08-04 17:04:30,436 - health_monitor - DEBUG - 系统指标 - CPU: 82.8%, 内存: 78.4%, 磁盘: 84.3%
2025-08-04 17:04:37,098 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.2%, CPU使用率 100.0%
2025-08-04 17:04:53,008 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.1%, CPU使用率 100.0%
2025-08-04 17:05:08,587 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.6%, CPU使用率 100.0%
2025-08-04 17:05:24,040 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.3%, CPU使用率 100.0%
2025-08-04 17:05:31,723 - health_monitor - DEBUG - 系统指标 - CPU: 87.4%, 内存: 78.6%, 磁盘: 84.3%
2025-08-04 17:05:39,413 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.3%, CPU使用率 100.0%
2025-08-04 17:05:54,635 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.6%, CPU使用率 97.1%
2025-08-04 17:06:10,463 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-08-04 17:06:25,703 - monitoring - DEBUG - 资源指标更新: 内存使用率 82.2%, CPU使用率 71.4%
2025-08-04 17:06:32,837 - health_monitor - DEBUG - 系统指标 - CPU: 96.1%, 内存: 78.9%, 磁盘: 84.3%
2025-08-04 17:06:40,899 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.2%, CPU使用率 100.0%
2025-08-04 17:06:56,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.4%, CPU使用率 66.7%
2025-08-04 17:07:11,215 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.5%, CPU使用率 100.0%
2025-08-04 17:07:26,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.6%, CPU使用率 84.4%
2025-08-04 17:07:34,175 - health_monitor - DEBUG - 系统指标 - CPU: 96.4%, 内存: 80.9%, 磁盘: 84.3%
2025-08-04 17:07:41,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.7%, CPU使用率 93.0%
2025-08-04 17:07:56,841 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.3%, CPU使用率 100.0%
2025-08-04 17:08:11,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.2%, CPU使用率 83.3%
2025-08-04 17:08:27,254 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.6%, CPU使用率 97.9%
2025-08-04 17:08:35,372 - health_monitor - DEBUG - 系统指标 - CPU: 98.5%, 内存: 79.3%, 磁盘: 84.3%
2025-08-04 17:08:42,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.1%, CPU使用率 100.0%
2025-08-04 17:08:57,740 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.8%, CPU使用率 96.4%
2025-08-04 17:09:13,006 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.2%, CPU使用率 97.9%
2025-08-04 17:09:28,687 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.3%, CPU使用率 100.0%
2025-08-04 17:09:36,591 - health_monitor - DEBUG - 系统指标 - CPU: 98.2%, 内存: 81.5%, 磁盘: 84.3%
2025-08-04 17:09:43,987 - monitoring - DEBUG - 资源指标更新: 内存使用率 82.4%, CPU使用率 97.4%
2025-08-04 17:09:59,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.9%, CPU使用率 50.0%
2025-08-04 17:10:14,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.9%, CPU使用率 79.2%
2025-08-04 17:10:29,381 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.5%, CPU使用率 95.2%
2025-08-04 17:10:38,487 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 79.9%, 磁盘: 84.3%
2025-08-04 17:10:45,058 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.5%, CPU使用率 100.0%
2025-08-04 17:11:01,012 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.9%, CPU使用率 100.0%
2025-08-04 17:11:16,284 - monitoring - DEBUG - 资源指标更新: 内存使用率 82.3%, CPU使用率 100.0%
2025-08-04 17:11:31,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 82.6%, CPU使用率 100.0%
2025-08-04 17:11:39,821 - health_monitor - DEBUG - 系统指标 - CPU: 93.1%, 内存: 83.4%, 磁盘: 84.3%
2025-08-04 17:11:46,804 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.8%, CPU使用率 100.0%
2025-08-04 17:11:49,985 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 93.1, 阈值: 90
2025-08-04 17:12:02,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.3%, CPU使用率 100.0%
2025-08-04 17:12:17,739 - monitoring - DEBUG - 资源指标更新: 内存使用率 82.4%, CPU使用率 100.0%
2025-08-04 17:12:32,907 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.8%, CPU使用率 86.2%
2025-08-04 17:12:41,011 - health_monitor - DEBUG - 系统指标 - CPU: 97.8%, 内存: 82.2%, 磁盘: 84.3%
2025-08-04 17:42:03,798 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-04 17:42:03,831 - auth_service - INFO - 统一认证服务初始化完成
2025-08-04 17:42:04,108 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-04 17:42:04,112 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-04 17:42:05,361 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-08-04 17:42:07,153 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-08-04 17:42:07,154 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-08-04 17:42:07,156 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-08-04 17:42:07,209 - health_monitor - INFO - 健康监控器初始化完成
2025-08-04 17:42:07,240 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-08-04 17:42:07,275 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-08-04 17:42:07,280 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-08-04 17:42:07,305 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-08-04 17:42:07,347 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-08-04 17:42:07,375 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-04 17:42:07,389 - alert_manager - INFO - 已初始化默认告警规则
2025-08-04 17:42:07,391 - alert_manager - INFO - 已初始化默认通知渠道
2025-08-04 17:42:07,400 - alert_manager - INFO - 告警管理器初始化完成
2025-08-04 17:42:08,947 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-08-04 17:42:08,950 - db_service - INFO - 数据库服务初始化完成
2025-08-04 17:42:08,966 - notification_service - INFO - 通知服务初始化完成
2025-08-04 17:42:08,999 - main - INFO - 错误处理模块导入成功
2025-08-04 17:42:09,207 - main - INFO - 监控模块导入成功
2025-08-04 17:42:09,317 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-08-04 17:42:13,198 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-08-04 17:42:13,517 - main - INFO - 应用启动中...
2025-08-04 17:42:13,519 - error_handling - INFO - 错误处理已设置
2025-08-04 17:42:13,522 - main - INFO - 错误处理系统初始化完成
2025-08-04 17:42:13,524 - monitoring - INFO - 添加指标端点成功: /metrics
2025-08-04 17:42:13,525 - monitoring - INFO - 添加健康检查端点成功: /health
2025-08-04 17:42:13,559 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-08-04 17:42:13,601 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-08-04 17:42:13,656 - monitoring - INFO - 启动资源监控线程成功
2025-08-04 17:42:13,657 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-08-04 17:42:13,658 - monitoring - INFO - 监控系统初始化完成
2025-08-04 17:42:13,664 - main - INFO - 监控系统初始化完成
2025-08-04 17:42:13,669 - app.db.init_db - INFO - 所有模型导入成功
2025-08-04 17:42:13,673 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-04 17:42:13,691 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 17:42:13,696 - app.db.init_db - INFO - 所有模型导入成功
2025-08-04 17:42:13,698 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-04 17:42:13,699 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-08-04 17:42:13,701 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-08-04 17:42:13,703 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 17:42:13,706 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-08-04 17:42:13,715 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,723 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-08-04 17:42:13,729 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,732 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-08-04 17:42:13,734 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,740 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-08-04 17:42:13,743 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,748 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-08-04 17:42:13,751 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,757 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.9%, CPU使用率 91.7%
2025-08-04 17:42:13,758 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-08-04 17:42:13,763 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,766 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-08-04 17:42:13,771 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,774 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-08-04 17:42:13,777 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,783 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-08-04 17:42:13,789 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,792 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-08-04 17:42:13,796 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,799 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-08-04 17:42:13,805 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,808 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-08-04 17:42:13,810 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,819 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-08-04 17:42:13,821 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,824 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-08-04 17:42:13,830 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,836 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-08-04 17:42:13,839 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,842 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-08-04 17:42:13,851 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,854 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-08-04 17:42:13,857 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,860 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-08-04 17:42:13,868 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,871 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-08-04 17:42:13,873 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,876 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-08-04 17:42:13,883 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,887 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-08-04 17:42:13,889 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,891 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-08-04 17:42:13,898 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,901 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-08-04 17:42:13,903 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,906 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-08-04 17:42:13,913 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,917 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-08-04 17:42:13,918 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,921 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-08-04 17:42:13,925 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,931 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-08-04 17:42:13,933 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,936 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-08-04 17:42:13,938 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,947 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-08-04 17:42:13,950 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,955 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-08-04 17:42:13,964 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,968 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-08-04 17:42:13,973 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,980 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-08-04 17:42:13,983 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:13,989 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-08-04 17:42:13,993 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,000 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-08-04 17:42:14,006 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,010 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-08-04 17:42:14,015 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,021 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-08-04 17:42:14,025 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,032 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-08-04 17:42:14,038 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,041 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-08-04 17:42:14,047 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,054 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-08-04 17:42:14,057 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,064 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-08-04 17:42:14,071 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,074 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-08-04 17:42:14,080 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,087 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-08-04 17:42:14,090 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 17:42:14,100 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 17:42:14,106 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-08-04 17:42:14,114 - app.db.init_db - INFO - 模型关系初始化完成
2025-08-04 17:42:14,117 - app.db.init_db - INFO - 模型关系设置完成
2025-08-04 17:42:14,120 - main - INFO - 数据库初始化完成（强制重建）
2025-08-04 17:42:14,122 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 17:42:14,124 - main - INFO - 数据库连接正常
2025-08-04 17:42:14,134 - main - INFO - 开始初始化模板数据
2025-08-04 17:42:14,155 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 17:42:14,886 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-08-04 17:42:15,042 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-08-04 17:42:15,166 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-08-04 17:42:15,297 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-08-04 17:42:15,301 - main - INFO - 模板数据初始化完成
2025-08-04 17:42:15,348 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-08-04 17:42:15,486 - main - INFO - 应用启动完成
2025-08-04 17:42:23,444 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 17:42:23,482 - main - INFO - 请求没有认证头部
2025-08-04 17:42:23,483 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 17:42:23,485 - main - INFO - --- 请求结束: 200 ---

2025-08-04 17:42:25,633 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 17:42:25,656 - main - INFO - 请求没有认证头部
2025-08-04 17:42:25,765 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 17:42:26,083 - app.core.db_connection - DEBUG - 当前线程ID: 4064
2025-08-04 17:42:26,201 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 17:42:26,250 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 17:42:26,322 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 17:42:26,336 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 17:42:26,339 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 17:42:29,223 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.6%, CPU使用率 100.0%
2025-08-04 17:42:29,869 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 17:42:29,955 - main - INFO - --- 请求结束: 200 ---

2025-08-04 17:42:45,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 94.1%, CPU使用率 100.0%
2025-08-04 17:43:01,146 - monitoring - DEBUG - 资源指标更新: 内存使用率 95.9%, CPU使用率 87.5%
2025-08-04 17:43:08,308 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 93.2%, 磁盘: 85.8%
2025-08-04 17:43:16,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.9%, CPU使用率 100.0%
2025-08-04 17:43:32,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.7%, CPU使用率 100.0%
2025-08-04 17:43:48,145 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.8%, CPU使用率 100.0%
2025-08-04 17:43:50,596 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 17:43:50,605 - main - INFO - 请求没有认证头部
2025-08-04 17:43:50,605 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 17:43:50,607 - main - INFO - --- 请求结束: 200 ---

2025-08-04 17:43:52,887 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 17:43:52,892 - main - INFO - 请求没有认证头部
2025-08-04 17:43:52,893 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 17:43:52,896 - app.core.db_connection - DEBUG - 当前线程ID: 4064
2025-08-04 17:43:52,901 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 17:43:52,903 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 17:43:52,905 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 17:43:52,906 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 17:43:52,907 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 17:43:56,887 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 17:43:57,027 - main - INFO - --- 请求结束: 200 ---

2025-08-04 17:44:04,017 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.9%, CPU使用率 100.0%
2025-08-04 17:44:11,654 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 94.8%, 磁盘: 85.8%
2025-08-04 17:44:19,671 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.5%, CPU使用率 98.9%
2025-08-04 17:44:34,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.6%, CPU使用率 92.0%
2025-08-04 17:44:50,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.5%, CPU使用率 100.0%
2025-08-04 17:45:05,192 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.3%, CPU使用率 70.8%
2025-08-04 17:45:13,705 - health_monitor - DEBUG - 系统指标 - CPU: 97.9%, 内存: 90.1%, 磁盘: 85.8%
2025-08-04 17:45:20,373 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.8%, CPU使用率 87.1%
2025-08-04 17:45:35,637 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.5%, CPU使用率 97.4%
2025-08-04 17:45:51,091 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.4%, CPU使用率 100.0%
2025-08-04 17:46:06,284 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.2%, CPU使用率 88.5%
2025-08-04 17:46:14,820 - health_monitor - DEBUG - 系统指标 - CPU: 96.5%, 内存: 93.4%, 磁盘: 85.8%
2025-08-04 17:46:21,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.1%, CPU使用率 94.6%
2025-08-04 17:46:36,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.1%, CPU使用率 87.8%
2025-08-04 17:46:52,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.2%, CPU使用率 100.0%
2025-08-04 17:47:07,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.5%, CPU使用率 100.0%
2025-08-04 17:47:16,008 - health_monitor - DEBUG - 系统指标 - CPU: 81.7%, 内存: 90.6%, 磁盘: 85.8%
2025-08-04 17:47:22,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.4%, CPU使用率 100.0%
2025-08-04 17:47:37,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.7%, CPU使用率 100.0%
2025-08-04 17:47:53,191 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.9%, CPU使用率 100.0%
2025-08-04 17:48:07,627 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-08-04 17:48:08,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.8%, CPU使用率 100.0%
2025-08-04 17:48:17,201 - health_monitor - DEBUG - 系统指标 - CPU: 84.7%, 内存: 89.1%, 磁盘: 85.8%
2025-08-04 17:48:24,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.4%, CPU使用率 100.0%
2025-08-04 17:48:39,688 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.3%, CPU使用率 100.0%
2025-08-04 17:48:55,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.6%, CPU使用率 96.7%
2025-08-04 17:49:10,326 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.0%, CPU使用率 93.1%
2025-08-04 17:49:18,445 - health_monitor - DEBUG - 系统指标 - CPU: 94.5%, 内存: 91.4%, 磁盘: 85.8%
2025-08-04 17:49:25,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.9%, CPU使用率 100.0%
2025-08-04 17:49:40,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 95.2%, CPU使用率 65.4%
2025-08-04 17:49:56,071 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.1%, CPU使用率 100.0%
2025-08-04 17:50:11,318 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.1%, CPU使用率 100.0%
2025-08-04 17:50:19,549 - health_monitor - DEBUG - 系统指标 - CPU: 94.3%, 内存: 92.1%, 磁盘: 85.8%
2025-08-04 17:50:26,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 94.5%, CPU使用率 100.0%
2025-08-04 17:50:41,789 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.4%, CPU使用率 95.8%
2025-08-04 17:50:57,110 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.1%, CPU使用率 100.0%
2025-08-04 17:51:12,282 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.7%, CPU使用率 59.3%
2025-08-04 17:51:20,900 - health_monitor - DEBUG - 系统指标 - CPU: 86.6%, 内存: 91.2%, 磁盘: 85.8%
2025-08-04 17:51:27,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.0%, CPU使用率 85.7%
2025-08-04 17:51:42,502 - monitoring - DEBUG - 资源指标更新: 内存使用率 94.5%, CPU使用率 100.0%
2025-08-04 17:51:57,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.8%, CPU使用率 92.9%
2025-08-04 17:52:12,767 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.4%, CPU使用率 51.5%
2025-08-04 17:52:22,027 - health_monitor - DEBUG - 系统指标 - CPU: 87.7%, 内存: 94.2%, 磁盘: 85.8%
2025-08-04 17:52:27,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.8%, CPU使用率 26.9%
2025-08-04 17:52:43,223 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.0%, CPU使用率 81.7%
2025-08-04 17:52:58,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.3%, CPU使用率 100.0%
2025-08-04 17:53:13,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.9%, CPU使用率 93.2%
2025-08-04 17:53:23,245 - health_monitor - DEBUG - 系统指标 - CPU: 88.5%, 内存: 92.1%, 磁盘: 85.8%
2025-08-04 17:53:29,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.9%, CPU使用率 100.0%
2025-08-04 17:53:44,514 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.5%, CPU使用率 92.9%
2025-08-04 17:53:59,730 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.8%, CPU使用率 76.0%
2025-08-04 17:54:15,208 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.3%, CPU使用率 97.7%
2025-08-04 17:54:24,360 - health_monitor - DEBUG - 系统指标 - CPU: 89.5%, 内存: 88.7%, 磁盘: 85.5%
2025-08-04 17:54:30,328 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.8%, CPU使用率 96.6%
2025-08-04 17:54:45,452 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.7%, CPU使用率 85.7%
2025-08-04 17:55:00,564 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.2%, CPU使用率 44.8%
2025-08-04 17:55:15,694 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.7%, CPU使用率 93.8%
2025-08-04 17:55:25,393 - health_monitor - DEBUG - 系统指标 - CPU: 85.2%, 内存: 91.5%, 磁盘: 85.4%
2025-08-04 17:55:30,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.6%, CPU使用率 41.7%
2025-08-04 17:55:45,912 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.1%, CPU使用率 67.9%
2025-08-04 17:56:01,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.9%, CPU使用率 100.0%
2025-08-04 17:56:16,243 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.1%, CPU使用率 95.8%
2025-08-04 17:56:26,507 - health_monitor - DEBUG - 系统指标 - CPU: 94.8%, 内存: 87.7%, 磁盘: 85.2%
2025-08-04 17:56:31,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.5%, CPU使用率 75.0%
2025-08-04 17:56:46,459 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.5%, CPU使用率 58.3%
2025-08-04 17:57:01,705 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.4%, CPU使用率 100.0%
2025-08-04 17:57:08,013 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 17:57:08,014 - main - INFO - 请求没有认证头部
2025-08-04 17:57:08,014 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 17:57:08,016 - main - INFO - --- 请求结束: 200 ---

2025-08-04 17:57:10,044 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 17:57:10,111 - main - INFO - 请求没有认证头部
2025-08-04 17:57:10,295 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 17:57:10,367 - app.core.db_connection - DEBUG - 当前线程ID: 4064
2025-08-04 17:57:10,368 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 17:57:10,369 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 17:57:10,370 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 17:57:10,371 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 17:57:10,372 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 17:57:12,481 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 17:57:12,484 - main - INFO - --- 请求结束: 200 ---

2025-08-04 17:57:16,880 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.0%, CPU使用率 75.0%
2025-08-04 17:57:28,430 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 88.6%, 磁盘: 85.3%
2025-08-04 17:57:32,179 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.1%, CPU使用率 96.2%
2025-08-04 17:57:47,284 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.4%, CPU使用率 28.6%
2025-08-04 17:58:02,525 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.8%, CPU使用率 84.8%
2025-08-04 22:17:43,453 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-04 22:17:43,461 - auth_service - INFO - 统一认证服务初始化完成
2025-08-04 22:17:43,597 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-04 22:17:43,600 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-04 22:17:44,122 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-08-04 22:17:45,265 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-08-04 22:17:45,266 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-08-04 22:17:45,268 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-08-04 22:17:45,356 - health_monitor - INFO - 健康监控器初始化完成
2025-08-04 22:17:45,380 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-08-04 22:17:45,396 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-08-04 22:17:45,397 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-08-04 22:17:45,416 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-08-04 22:17:45,430 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-08-04 22:17:45,433 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-04 22:17:45,445 - alert_manager - INFO - 已初始化默认告警规则
2025-08-04 22:17:45,446 - alert_manager - INFO - 已初始化默认通知渠道
2025-08-04 22:17:45,447 - alert_manager - INFO - 告警管理器初始化完成
2025-08-04 22:17:46,416 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-08-04 22:17:46,417 - db_service - INFO - 数据库服务初始化完成
2025-08-04 22:17:46,445 - notification_service - INFO - 通知服务初始化完成
2025-08-04 22:17:46,447 - main - INFO - 错误处理模块导入成功
2025-08-04 22:17:46,506 - main - INFO - 监控模块导入成功
2025-08-04 22:17:46,508 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-08-04 22:17:49,682 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-08-04 22:17:49,725 - main - INFO - 应用启动中...
2025-08-04 22:17:49,726 - error_handling - INFO - 错误处理已设置
2025-08-04 22:17:49,727 - main - INFO - 错误处理系统初始化完成
2025-08-04 22:17:49,727 - monitoring - INFO - 添加指标端点成功: /metrics
2025-08-04 22:17:49,728 - monitoring - INFO - 添加健康检查端点成功: /health
2025-08-04 22:17:49,729 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-08-04 22:17:49,730 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-08-04 22:17:49,733 - monitoring - INFO - 启动资源监控线程成功
2025-08-04 22:17:49,734 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-08-04 22:17:49,735 - monitoring - INFO - 监控系统初始化完成
2025-08-04 22:17:49,735 - main - INFO - 监控系统初始化完成
2025-08-04 22:17:49,737 - app.db.init_db - INFO - 所有模型导入成功
2025-08-04 22:17:49,738 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-04 22:17:49,750 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:17:49,750 - app.db.init_db - INFO - 所有模型导入成功
2025-08-04 22:17:49,751 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-04 22:17:49,752 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-08-04 22:17:49,752 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-08-04 22:17:49,753 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-04 22:17:49,754 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-08-04 22:17:49,755 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,761 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-08-04 22:17:49,762 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,763 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-08-04 22:17:49,764 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,765 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-08-04 22:17:49,766 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,768 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-08-04 22:17:49,769 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,770 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-08-04 22:17:49,770 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,771 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-08-04 22:17:49,772 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,774 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-08-04 22:17:49,775 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,777 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-08-04 22:17:49,778 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,779 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-08-04 22:17:49,780 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,782 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-08-04 22:17:49,783 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,784 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-08-04 22:17:49,785 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,786 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-08-04 22:17:49,787 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,789 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-08-04 22:17:49,791 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-08-04 22:17:49,794 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,796 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-08-04 22:17:49,797 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,798 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-08-04 22:17:49,799 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,800 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-08-04 22:17:49,801 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,802 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-08-04 22:17:49,803 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,805 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-08-04 22:17:49,806 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,808 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-08-04 22:17:49,809 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,810 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-08-04 22:17:49,811 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,812 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-08-04 22:17:49,813 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,815 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-08-04 22:17:49,816 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,817 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-08-04 22:17:49,818 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,819 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-08-04 22:17:49,820 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,821 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-08-04 22:17:49,822 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,825 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-08-04 22:17:49,826 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,828 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-08-04 22:17:49,829 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,830 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-08-04 22:17:49,831 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,832 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-08-04 22:17:49,833 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.7%, CPU使用率 70.4%
2025-08-04 22:17:49,835 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-08-04 22:17:49,836 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,837 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-08-04 22:17:49,838 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,839 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-08-04 22:17:49,841 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,843 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-08-04 22:17:49,844 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,845 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-08-04 22:17:49,846 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,847 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-08-04 22:17:49,848 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,849 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-08-04 22:17:49,850 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,852 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-08-04 22:17:49,853 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,854 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-08-04 22:17:49,855 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,857 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-08-04 22:17:49,860 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,862 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-08-04 22:17:49,864 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-04 22:17:49,869 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-04 22:17:49,870 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-08-04 22:17:49,876 - app.db.init_db - INFO - 模型关系初始化完成
2025-08-04 22:17:49,877 - app.db.init_db - INFO - 模型关系设置完成
2025-08-04 22:17:49,878 - main - INFO - 数据库初始化完成（强制重建）
2025-08-04 22:17:49,879 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:17:49,880 - main - INFO - 数据库连接正常
2025-08-04 22:17:49,881 - main - INFO - 开始初始化模板数据
2025-08-04 22:17:49,882 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:17:50,318 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-08-04 22:17:50,384 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-08-04 22:17:50,498 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-08-04 22:17:50,572 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-08-04 22:17:50,577 - main - INFO - 模板数据初始化完成
2025-08-04 22:17:50,579 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-08-04 22:17:50,580 - main - INFO - 应用启动完成
2025-08-04 22:18:04,940 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.4%, CPU使用率 37.5%
2025-08-04 22:18:20,044 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 36.0%
2025-08-04 22:18:35,148 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 69.2%
2025-08-04 22:18:46,293 - health_monitor - DEBUG - 系统指标 - CPU: 58.4%, 内存: 52.3%, 磁盘: 84.2%
2025-08-04 22:18:50,257 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 44.4%
2025-08-04 22:19:05,366 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 19.2%
2025-08-04 22:19:20,471 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 16.7%
2025-08-04 22:19:35,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 66.7%
2025-08-04 22:19:47,315 - health_monitor - DEBUG - 系统指标 - CPU: 34.4%, 内存: 52.2%, 磁盘: 84.2%
2025-08-04 22:19:50,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 23.1%
2025-08-04 22:20:05,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 42.9%
2025-08-04 22:20:20,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 61.5%
2025-08-04 22:20:33,063 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:20:33,064 - main - INFO - 请求没有认证头部
2025-08-04 22:20:33,065 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:20:33,066 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:20:35,086 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:20:35,087 - main - INFO - 请求没有认证头部
2025-08-04 22:20:35,088 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:20:35,090 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:20:35,091 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:20:35,093 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:20:35,094 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:20:35,095 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:20:35,096 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:20:35,866 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:20:35,869 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:20:36,062 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.3%, CPU使用率 91.7%
2025-08-04 22:20:48,336 - health_monitor - DEBUG - 系统指标 - CPU: 16.6%, 内存: 54.1%, 磁盘: 84.2%
2025-08-04 22:20:51,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 80.0%
2025-08-04 22:21:06,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 44.4%
2025-08-04 22:21:21,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.6%, CPU使用率 33.3%
2025-08-04 22:21:36,496 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.6%, CPU使用率 31.0%
2025-08-04 22:21:49,364 - health_monitor - DEBUG - 系统指标 - CPU: 41.2%, 内存: 52.9%, 磁盘: 84.2%
2025-08-04 22:21:51,608 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 41.4%
2025-08-04 22:22:06,713 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 32.1%
2025-08-04 22:22:21,817 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 33.3%
2025-08-04 22:22:36,922 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.7%, CPU使用率 16.7%
2025-08-04 22:22:50,384 - health_monitor - DEBUG - 系统指标 - CPU: 34.4%, 内存: 52.7%, 磁盘: 84.2%
2025-08-04 22:22:52,034 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.7%, CPU使用率 25.0%
2025-08-04 22:23:07,138 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.7%, CPU使用率 53.6%
2025-08-04 22:23:22,249 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.7%, CPU使用率 40.0%
2025-08-04 22:23:37,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 21.4%
2025-08-04 22:23:45,451 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-08-04 22:23:51,405 - health_monitor - DEBUG - 系统指标 - CPU: 35.3%, 内存: 52.8%, 磁盘: 84.2%
2025-08-04 22:23:52,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 20.7%
2025-08-04 22:24:07,580 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 45.8%
2025-08-04 22:24:22,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 34.6%
2025-08-04 22:24:37,790 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 28.6%
2025-08-04 22:24:52,425 - health_monitor - DEBUG - 系统指标 - CPU: 25.2%, 内存: 52.8%, 磁盘: 84.2%
2025-08-04 22:24:52,897 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 32.1%
2025-08-04 22:25:08,002 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 42.3%
2025-08-04 22:25:23,106 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 46.2%
2025-08-04 22:25:38,210 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 71.4%
2025-08-04 22:25:53,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 53.6%
2025-08-04 22:25:53,447 - health_monitor - DEBUG - 系统指标 - CPU: 39.6%, 内存: 53.0%, 磁盘: 84.2%
2025-08-04 22:26:08,495 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 66.7%
2025-08-04 22:26:23,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 89.3%
2025-08-04 22:26:38,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 54.2%
2025-08-04 22:26:53,818 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 36.0%
2025-08-04 22:26:54,475 - health_monitor - DEBUG - 系统指标 - CPU: 60.3%, 内存: 51.8%, 磁盘: 84.2%
2025-08-04 22:27:08,923 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.7%, CPU使用率 83.3%
2025-08-04 22:27:24,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.5%, CPU使用率 75.0%
2025-08-04 22:27:39,166 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 14.3%
2025-08-04 22:27:54,278 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 100.0%
2025-08-04 22:27:55,578 - health_monitor - DEBUG - 系统指标 - CPU: 75.6%, 内存: 53.1%, 磁盘: 84.2%
2025-08-04 22:28:09,383 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.7%, CPU使用率 53.6%
2025-08-04 22:28:24,494 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 14.3%
2025-08-04 22:28:39,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 92.9%
2025-08-04 22:28:54,759 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.6%, CPU使用率 97.3%
2025-08-04 22:28:56,602 - health_monitor - DEBUG - 系统指标 - CPU: 81.4%, 内存: 53.9%, 磁盘: 84.2%
2025-08-04 22:29:09,866 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 50.0%
2025-08-04 22:29:24,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 56.0%
2025-08-04 22:29:40,083 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.3%, CPU使用率 50.0%
2025-08-04 22:29:55,207 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 100.0%
2025-08-04 22:29:57,659 - health_monitor - DEBUG - 系统指标 - CPU: 78.5%, 内存: 52.5%, 磁盘: 84.2%
2025-08-04 22:30:10,361 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 93.9%
2025-08-04 22:30:25,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.5%, CPU使用率 96.4%
2025-08-04 22:30:40,588 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.8%, CPU使用率 76.7%
2025-08-04 22:30:55,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.3%, CPU使用率 89.3%
2025-08-04 22:30:58,690 - health_monitor - DEBUG - 系统指标 - CPU: 49.1%, 内存: 52.9%, 磁盘: 84.2%
2025-08-04 22:31:10,947 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.6%, CPU使用率 97.7%
2025-08-04 22:31:26,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.0%, CPU使用率 100.0%
2025-08-04 22:31:41,935 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.9%, CPU使用率 100.0%
2025-08-04 22:31:57,221 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 100.0%
2025-08-04 22:31:59,735 - health_monitor - DEBUG - 系统指标 - CPU: 95.8%, 内存: 55.7%, 磁盘: 84.2%
2025-08-04 22:32:12,436 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 60.0%
2025-08-04 22:32:27,850 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.3%, CPU使用率 100.0%
2025-08-04 22:32:42,964 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.6%, CPU使用率 42.9%
2025-08-04 22:32:58,201 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.8%, CPU使用率 100.0%
2025-08-04 22:33:00,767 - health_monitor - DEBUG - 系统指标 - CPU: 84.2%, 内存: 55.1%, 磁盘: 84.2%
2025-08-04 22:33:13,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.3%, CPU使用率 100.0%
2025-08-04 22:33:28,546 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.0%, CPU使用率 60.7%
2025-08-04 22:33:43,653 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 60.7%
2025-08-04 22:33:59,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 100.0%
2025-08-04 22:34:01,795 - health_monitor - DEBUG - 系统指标 - CPU: 86.3%, 内存: 55.1%, 磁盘: 84.2%
2025-08-04 22:34:14,615 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.4%, CPU使用率 38.5%
2025-08-04 22:34:29,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.3%, CPU使用率 89.3%
2025-08-04 22:34:44,840 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.1%, CPU使用率 43.3%
2025-08-04 22:34:59,946 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 27.6%
2025-08-04 22:35:02,824 - health_monitor - DEBUG - 系统指标 - CPU: 39.1%, 内存: 55.2%, 磁盘: 84.2%
2025-08-04 22:35:15,050 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.3%, CPU使用率 29.2%
2025-08-04 22:35:30,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 53.5%, CPU使用率 56.7%
2025-08-04 22:35:45,260 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.1%, CPU使用率 10.7%
2025-08-04 22:35:55,922 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:35:55,923 - main - INFO - 请求没有认证头部
2025-08-04 22:35:55,923 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:35:55,925 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:35:57,952 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:35:57,954 - main - INFO - 请求没有认证头部
2025-08-04 22:35:57,954 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:35:57,956 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:35:57,957 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:35:57,958 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:35:57,959 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:35:57,960 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:35:57,961 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:35:58,774 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:35:58,777 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:36:00,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.0%, CPU使用率 80.0%
2025-08-04 22:36:03,869 - health_monitor - DEBUG - 系统指标 - CPU: 45.7%, 内存: 55.2%, 磁盘: 84.2%
2025-08-04 22:36:15,590 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 94.1%
2025-08-04 22:36:31,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 100.0%
2025-08-04 22:36:46,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 20.8%
2025-08-04 22:37:02,682 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 100.0%
2025-08-04 22:37:05,102 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 58.5%, 磁盘: 84.2%
2025-08-04 22:37:18,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.9%, CPU使用率 100.0%
2025-08-04 22:37:34,747 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.0%, CPU使用率 100.0%
2025-08-04 22:37:49,918 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.5%, CPU使用率 100.0%
2025-08-04 22:38:05,122 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 91.4%
2025-08-04 22:38:06,243 - health_monitor - DEBUG - 系统指标 - CPU: 87.3%, 内存: 57.7%, 磁盘: 84.2%
2025-08-04 22:38:20,232 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 78.6%
2025-08-04 22:38:35,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 97.6%
2025-08-04 22:38:42,370 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:38:42,371 - main - INFO - 请求没有认证头部
2025-08-04 22:38:42,371 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:38:42,373 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:38:44,423 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:38:44,426 - main - INFO - 请求没有认证头部
2025-08-04 22:38:44,428 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:38:44,429 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:38:44,430 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:38:44,433 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:38:44,434 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:38:44,434 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:38:44,435 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:38:45,310 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:38:45,312 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:38:50,785 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 16.7%
2025-08-04 22:39:05,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 88.0%
2025-08-04 22:39:07,395 - health_monitor - DEBUG - 系统指标 - CPU: 94.4%, 内存: 60.0%, 磁盘: 84.2%
2025-08-04 22:39:21,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.5%, CPU使用率 98.1%
2025-08-04 22:39:36,548 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.6%, CPU使用率 100.0%
2025-08-04 22:39:51,662 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 37.5%
2025-08-04 22:40:06,767 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.9%, CPU使用率 62.5%
2025-08-04 22:40:08,444 - health_monitor - DEBUG - 系统指标 - CPU: 58.0%, 内存: 55.9%, 磁盘: 84.2%
2025-08-04 22:40:21,877 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.2%, CPU使用率 39.3%
2025-08-04 22:40:36,985 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 82.1%
2025-08-04 22:40:52,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 88.2%
2025-08-04 22:41:07,388 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 100.0%
2025-08-04 22:41:09,544 - health_monitor - DEBUG - 系统指标 - CPU: 92.9%, 内存: 56.0%, 磁盘: 84.2%
2025-08-04 22:41:22,580 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.6%, CPU使用率 100.0%
2025-08-04 22:41:37,684 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 8.3%
2025-08-04 22:41:38,189 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:41:38,190 - main - INFO - 请求没有认证头部
2025-08-04 22:41:38,191 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:41:38,192 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:41:40,238 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:41:40,240 - main - INFO - 请求没有认证头部
2025-08-04 22:41:40,240 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:41:40,242 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:41:40,243 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:41:40,244 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:41:40,245 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:41:40,246 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:41:40,246 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:41:41,105 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:41:41,107 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:41:52,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 90.6%
2025-08-04 22:42:08,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 100.0%
2025-08-04 22:42:10,668 - health_monitor - DEBUG - 系统指标 - CPU: 78.6%, 内存: 59.7%, 磁盘: 84.2%
2025-08-04 22:42:23,155 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.2%, CPU使用率 96.6%
2025-08-04 22:42:38,670 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.5%, CPU使用率 99.0%
2025-08-04 22:42:53,846 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.3%, CPU使用率 100.0%
2025-08-04 22:43:08,952 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.3%, CPU使用率 60.7%
2025-08-04 22:43:11,714 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 54.5%, 磁盘: 84.2%
2025-08-04 22:43:24,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 80.8%
2025-08-04 22:43:39,195 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.4%, CPU使用率 57.1%
2025-08-04 22:43:54,301 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 60.0%
2025-08-04 22:44:09,406 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 45.8%
2025-08-04 22:44:12,738 - health_monitor - DEBUG - 系统指标 - CPU: 59.2%, 内存: 54.5%, 磁盘: 84.3%
2025-08-04 22:44:24,510 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.5%, CPU使用率 46.7%
2025-08-04 22:44:39,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.6%, CPU使用率 25.0%
2025-08-04 22:44:54,720 - monitoring - DEBUG - 资源指标更新: 内存使用率 54.7%, CPU使用率 37.5%
2025-08-04 22:45:09,823 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 82.8%
2025-08-04 22:45:13,888 - health_monitor - DEBUG - 系统指标 - CPU: 65.2%, 内存: 56.2%, 磁盘: 84.3%
2025-08-04 22:45:24,928 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 80.8%
2025-08-04 22:45:40,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 100.0%
2025-08-04 22:45:55,464 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 100.0%
2025-08-04 22:46:10,903 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 100.0%
2025-08-04 22:46:14,908 - health_monitor - DEBUG - 系统指标 - CPU: 42.2%, 内存: 57.9%, 磁盘: 84.3%
2025-08-04 22:46:26,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 93.5%
2025-08-04 22:46:41,324 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 100.0%
2025-08-04 22:46:54,489 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:46:54,505 - main - INFO - 请求没有认证头部
2025-08-04 22:46:54,506 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:46:54,508 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:46:56,439 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 75.0%
2025-08-04 22:46:56,538 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:46:56,540 - main - INFO - 请求没有认证头部
2025-08-04 22:46:56,541 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:46:56,544 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:46:56,545 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:46:56,546 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:46:56,547 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:46:56,548 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:46:56,549 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:46:57,845 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:46:57,847 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:47:11,557 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 57.1%
2025-08-04 22:47:16,079 - health_monitor - DEBUG - 系统指标 - CPU: 84.2%, 内存: 60.5%, 磁盘: 84.3%
2025-08-04 22:47:26,681 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 100.0%
2025-08-04 22:47:41,827 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 92.0%
2025-08-04 22:47:56,931 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 85.2%
2025-08-04 22:48:12,147 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.6%, CPU使用率 100.0%
2025-08-04 22:48:17,116 - health_monitor - DEBUG - 系统指标 - CPU: 57.9%, 内存: 57.0%, 磁盘: 84.3%
2025-08-04 22:48:27,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.6%, CPU使用率 97.9%
2025-08-04 22:48:42,507 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.9%, CPU使用率 67.9%
2025-08-04 22:48:57,630 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 58.3%
2025-08-04 22:49:12,744 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 78.6%
2025-08-04 22:49:18,235 - health_monitor - DEBUG - 系统指标 - CPU: 88.1%, 内存: 57.0%, 磁盘: 84.3%
2025-08-04 22:49:27,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 92.9%
2025-08-04 22:49:43,161 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 100.0%
2025-08-04 22:49:58,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.4%, CPU使用率 50.0%
2025-08-04 22:50:13,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.6%, CPU使用率 80.0%
2025-08-04 22:50:19,290 - health_monitor - DEBUG - 系统指标 - CPU: 95.0%, 内存: 56.8%, 磁盘: 84.3%
2025-08-04 22:50:28,912 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.3%, CPU使用率 100.0%
2025-08-04 22:50:44,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 100.0%
2025-08-04 22:50:59,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 100.0%
2025-08-04 22:51:15,090 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 89.3%
2025-08-04 22:51:20,328 - health_monitor - DEBUG - 系统指标 - CPU: 75.9%, 内存: 57.1%, 磁盘: 84.3%
2025-08-04 22:51:30,197 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.3%, CPU使用率 35.7%
2025-08-04 22:51:41,544 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:51:41,546 - main - INFO - 请求没有认证头部
2025-08-04 22:51:41,546 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:51:41,548 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:51:43,576 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:51:43,579 - main - INFO - 请求没有认证头部
2025-08-04 22:51:43,580 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:51:43,582 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:51:43,584 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:51:43,586 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:51:43,590 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:51:43,593 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:51:43,594 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:51:45,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 93.8%
2025-08-04 22:51:47,828 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:51:47,831 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:52:00,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 96.9%
2025-08-04 22:52:15,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 92.9%
2025-08-04 22:52:21,359 - health_monitor - DEBUG - 系统指标 - CPU: 66.8%, 内存: 61.0%, 磁盘: 84.3%
2025-08-04 22:52:30,914 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 75.9%
2025-08-04 22:52:46,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.6%, CPU使用率 78.6%
2025-08-04 22:53:01,135 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 42.3%
2025-08-04 22:53:16,257 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 50.0%
2025-08-04 22:53:22,399 - health_monitor - DEBUG - 系统指标 - CPU: 72.8%, 内存: 56.8%, 磁盘: 84.3%
2025-08-04 22:53:31,361 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 70.8%
2025-08-04 22:53:46,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.1%, CPU使用率 60.7%
2025-08-04 22:54:01,579 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 54.2%
2025-08-04 22:54:16,684 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 75.0%
2025-08-04 22:54:23,423 - health_monitor - DEBUG - 系统指标 - CPU: 66.5%, 内存: 56.2%, 磁盘: 84.3%
2025-08-04 22:54:31,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.4%, CPU使用率 84.2%
2025-08-04 22:54:47,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.7%, CPU使用率 54.2%
2025-08-04 22:55:02,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.5%, CPU使用率 100.0%
2025-08-04 22:55:17,462 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 87.5%
2025-08-04 22:55:24,466 - health_monitor - DEBUG - 系统指标 - CPU: 78.2%, 内存: 58.6%, 磁盘: 84.3%
2025-08-04 22:55:32,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 100.0%
2025-08-04 22:55:48,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.8%, CPU使用率 100.0%
2025-08-04 22:56:03,274 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.9%, CPU使用率 96.4%
2025-08-04 22:56:18,423 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 94.4%
2025-08-04 22:56:25,557 - health_monitor - DEBUG - 系统指标 - CPU: 71.0%, 内存: 58.6%, 磁盘: 84.3%
2025-08-04 22:56:33,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 50.0%
2025-08-04 22:56:48,702 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.4%, CPU使用率 100.0%
2025-08-04 22:57:03,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.5%, CPU使用率 37.0%
2025-08-04 22:57:05,734 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 22:57:05,740 - main - INFO - 请求没有认证头部
2025-08-04 22:57:05,741 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:57:05,743 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:57:07,757 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 22:57:07,760 - main - INFO - 请求没有认证头部
2025-08-04 22:57:07,762 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 22:57:07,772 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 22:57:07,773 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 22:57:07,774 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-04 22:57:07,775 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 22:57:07,777 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 22:57:07,778 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 22:57:07,779 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 22:57:07,782 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 22:57:07,783 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-04 22:57:07,784 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 22:57:07,786 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 22:57:10,201 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 22:57:10,335 - main - INFO - --- 请求结束: 200 ---

2025-08-04 22:57:19,120 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 100.0%
2025-08-04 22:57:26,756 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 61.9%, 磁盘: 84.3%
2025-08-04 22:57:34,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.6%, CPU使用率 96.8%
2025-08-04 22:57:49,535 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.7%, CPU使用率 88.5%
2025-08-04 22:58:04,641 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.8%, CPU使用率 40.0%
2025-08-04 22:58:19,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 55.7%, CPU使用率 30.8%
2025-08-04 22:58:27,807 - health_monitor - DEBUG - 系统指标 - CPU: 54.1%, 内存: 55.7%, 磁盘: 84.3%
2025-08-04 22:58:34,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 60.7%
2025-08-04 22:58:50,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 84.4%
2025-08-04 22:59:05,189 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 79.3%
2025-08-04 22:59:20,300 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 79.2%
2025-08-04 22:59:29,124 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 58.2%, 磁盘: 84.3%
2025-08-04 22:59:35,405 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.2%, CPU使用率 71.4%
2025-08-04 22:59:50,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.5%, CPU使用率 100.0%
2025-08-04 23:00:06,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.1%, CPU使用率 100.0%
2025-08-04 23:00:21,423 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.6%, CPU使用率 98.0%
2025-08-04 23:00:30,317 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 59.3%, 磁盘: 84.3%
2025-08-04 23:00:32,625 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 23:00:32,626 - main - INFO - 请求没有认证头部
2025-08-04 23:00:32,627 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:00:32,628 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:00:34,827 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 23:00:34,838 - main - INFO - 请求没有认证头部
2025-08-04 23:00:34,839 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:00:34,863 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 23:00:34,873 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 23:00:34,876 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 23:00:34,877 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 23:00:34,878 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 23:00:36,505 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 23:00:36,507 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:00:36,631 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 85.7%
2025-08-04 23:00:52,241 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 100.0%
2025-08-04 23:01:07,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 81.5%
2025-08-04 23:01:23,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 100.0%
2025-08-04 23:01:31,746 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 59.9%, 磁盘: 84.3%
2025-08-04 23:01:38,308 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 100.0%
2025-08-04 23:01:53,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.6%, CPU使用率 93.5%
2025-08-04 23:02:08,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.6%, CPU使用率 100.0%
2025-08-04 23:02:21,165 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 23:02:21,169 - main - INFO - 请求没有认证头部
2025-08-04 23:02:21,182 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:02:21,184 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:02:23,464 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 23:02:23,519 - main - INFO - 请求没有认证头部
2025-08-04 23:02:23,529 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:02:23,543 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 23:02:23,546 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 23:02:23,550 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 23:02:23,551 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 23:02:23,553 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 23:02:23,869 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 93.2%
2025-08-04 23:02:26,774 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 23:02:26,785 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:02:33,205 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 60.1%, 磁盘: 84.3%
2025-08-04 23:02:39,267 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 100.0%
2025-08-04 23:02:54,405 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.3%, CPU使用率 96.2%
2025-08-04 23:03:09,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 100.0%
2025-08-04 23:03:24,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 100.0%
2025-08-04 23:03:34,248 - health_monitor - DEBUG - 系统指标 - CPU: 91.6%, 内存: 58.5%, 磁盘: 84.3%
2025-08-04 23:03:39,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 80.0%
2025-08-04 23:03:55,247 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 100.0%
2025-08-04 23:04:10,352 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 82.1%
2025-08-04 23:04:25,470 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 64.3%
2025-08-04 23:04:35,279 - health_monitor - DEBUG - 系统指标 - CPU: 69.8%, 内存: 59.6%, 磁盘: 84.3%
2025-08-04 23:04:40,577 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 46.4%
2025-08-04 23:04:55,689 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.3%, CPU使用率 53.6%
2025-08-04 23:05:10,797 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 28.6%
2025-08-04 23:05:25,901 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 35.7%
2025-08-04 23:05:36,305 - health_monitor - DEBUG - 系统指标 - CPU: 37.5%, 内存: 56.1%, 磁盘: 84.3%
2025-08-04 23:05:41,006 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 42.3%
2025-08-04 23:05:56,110 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 38.5%
2025-08-04 23:06:11,233 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.1%, CPU使用率 92.9%
2025-08-04 23:06:26,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.2%, CPU使用率 46.2%
2025-08-04 23:06:37,330 - health_monitor - DEBUG - 系统指标 - CPU: 35.1%, 内存: 56.0%, 磁盘: 84.3%
2025-08-04 23:06:41,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.0%, CPU使用率 32.1%
2025-08-04 23:06:56,830 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 100.0%
2025-08-04 23:07:11,943 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 67.9%
2025-08-04 23:07:26,049 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 23:07:26,050 - main - INFO - 请求没有认证头部
2025-08-04 23:07:26,051 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:07:26,052 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:07:27,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 85.7%
2025-08-04 23:07:28,092 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 23:07:28,138 - main - INFO - 请求没有认证头部
2025-08-04 23:07:28,139 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:07:28,141 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 23:07:28,141 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 23:07:28,142 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 23:07:28,144 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 23:07:28,145 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 23:07:29,749 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 23:07:29,751 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:07:38,359 - health_monitor - DEBUG - 系统指标 - CPU: 46.5%, 内存: 62.7%, 磁盘: 84.3%
2025-08-04 23:07:42,191 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 75.0%
2025-08-04 23:07:57,298 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 8.3%
2025-08-04 23:08:12,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 3.6%
2025-08-04 23:08:27,809 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.7%, CPU使用率 100.0%
2025-08-04 23:08:39,678 - health_monitor - DEBUG - 系统指标 - CPU: 76.8%, 内存: 60.9%, 磁盘: 84.3%
2025-08-04 23:08:42,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.9%, CPU使用率 89.3%
2025-08-04 23:08:58,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-08-04 23:09:13,310 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.2%, CPU使用率 79.2%
2025-08-04 23:09:28,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-08-04 23:09:40,757 - health_monitor - DEBUG - 系统指标 - CPU: 90.9%, 内存: 63.4%, 磁盘: 84.3%
2025-08-04 23:09:43,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.3%, CPU使用率 93.5%
2025-08-04 23:09:59,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 100.0%
2025-08-04 23:10:15,272 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 100.0%
2025-08-04 23:10:30,666 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.8%, CPU使用率 100.0%
2025-08-04 23:10:42,712 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 62.0%, 磁盘: 84.3%
2025-08-04 23:10:46,607 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 100.0%
2025-08-04 23:11:02,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 100.0%
2025-08-04 23:11:17,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 100.0%
2025-08-04 23:11:32,930 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.6%, CPU使用率 100.0%
2025-08-04 23:11:43,788 - health_monitor - DEBUG - 系统指标 - CPU: 95.3%, 内存: 63.8%, 磁盘: 84.3%
2025-08-04 23:11:48,384 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 91.2%
2025-08-04 23:12:03,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.8%, CPU使用率 51.7%
2025-08-04 23:12:18,632 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 48.1%
2025-08-04 23:12:33,868 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 94.6%
2025-08-04 23:12:44,886 - health_monitor - DEBUG - 系统指标 - CPU: 95.5%, 内存: 60.5%, 磁盘: 84.3%
2025-08-04 23:12:48,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 96.6%
2025-08-04 23:12:51,393 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 23:12:51,403 - main - INFO - 请求没有认证头部
2025-08-04 23:12:51,410 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:12:51,412 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:12:53,454 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 23:12:53,456 - main - INFO - 请求没有认证头部
2025-08-04 23:12:53,457 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:12:53,459 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 23:12:53,460 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 23:12:53,461 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 23:12:53,462 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 23:12:53,463 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 23:12:56,864 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 23:12:56,931 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:13:04,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 75.7%
2025-08-04 23:13:20,060 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.9%, CPU使用率 100.0%
2025-08-04 23:13:35,981 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.4%, CPU使用率 100.0%
2025-08-04 23:13:46,061 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 61.4%, 磁盘: 84.3%
2025-08-04 23:13:51,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.8%, CPU使用率 96.3%
2025-08-04 23:14:06,850 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 100.0%
2025-08-04 23:14:21,992 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 91.7%
2025-08-04 23:14:37,229 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.4%, CPU使用率 100.0%
2025-08-04 23:14:46,352 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 100.0, 阈值: 90
2025-08-04 23:14:47,251 - health_monitor - DEBUG - 系统指标 - CPU: 95.0%, 内存: 61.6%, 磁盘: 84.3%
2025-08-04 23:14:52,487 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 94.4%
2025-08-04 23:15:07,618 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 82.1%
2025-08-04 23:15:22,860 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 97.3%
2025-08-04 23:15:38,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 100.0%
2025-08-04 23:15:48,712 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 60.4%, 磁盘: 84.3%
2025-08-04 23:15:53,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 100.0%
2025-08-04 23:16:08,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 90.0%
2025-08-04 23:16:24,010 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 89.3%
2025-08-04 23:16:39,118 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 85.7%
2025-08-04 23:16:49,776 - health_monitor - DEBUG - 系统指标 - CPU: 89.1%, 内存: 58.9%, 磁盘: 84.3%
2025-08-04 23:16:54,224 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.6%, CPU使用率 70.8%
2025-08-04 23:17:09,329 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.2%, CPU使用率 100.0%
2025-08-04 23:17:17,194 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-04 23:17:17,196 - main - INFO - 请求没有认证头部
2025-08-04 23:17:17,197 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:17:17,199 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:17:19,215 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-04 23:17:19,216 - main - INFO - 请求没有认证头部
2025-08-04 23:17:19,217 - main - INFO - 没有认证头部，设置用户为None
2025-08-04 23:17:19,218 - app.core.db_connection - DEBUG - 当前线程ID: 7404
2025-08-04 23:17:19,219 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-04 23:17:19,220 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-04 23:17:19,221 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-04 23:17:19,222 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-04 23:17:19,984 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-04 23:17:19,986 - main - INFO - --- 请求结束: 200 ---

2025-08-04 23:17:24,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 39.3%
2025-08-04 23:17:39,569 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 73.3%
2025-08-04 23:17:50,802 - health_monitor - DEBUG - 系统指标 - CPU: 33.1%, 内存: 60.8%, 磁盘: 84.3%
2025-08-04 23:17:54,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 12.5%
2025-08-04 23:18:09,778 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.2%, CPU使用率 42.9%
2025-08-04 23:18:24,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 34.6%
2025-08-04 23:18:39,992 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 37.0%
2025-08-04 23:18:51,822 - health_monitor - DEBUG - 系统指标 - CPU: 33.5%, 内存: 57.0%, 磁盘: 84.3%
2025-08-04 23:18:55,097 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 79.2%
2025-08-04 23:19:10,204 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 33.3%
2025-08-04 23:19:25,312 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 50.0%
2025-08-04 23:19:40,420 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 28.6%
2025-08-04 23:19:52,851 - health_monitor - DEBUG - 系统指标 - CPU: 33.0%, 内存: 56.8%, 磁盘: 84.3%
2025-08-04 23:19:55,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 0.0%
2025-08-04 23:20:10,632 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 35.7%
2025-08-04 23:20:25,737 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 25.0%
2025-08-04 23:20:40,846 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.9%, CPU使用率 34.6%
2025-08-04 23:20:53,878 - health_monitor - DEBUG - 系统指标 - CPU: 34.8%, 内存: 56.8%, 磁盘: 84.3%
2025-08-04 23:20:55,951 - monitoring - DEBUG - 资源指标更新: 内存使用率 56.8%, CPU使用率 21.4%
2025-08-04 23:21:11,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 57.0%, CPU使用率 67.9%
2025-08-04 23:21:26,161 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 62.5%
2025-08-04 23:21:41,268 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 52.0%
2025-08-04 23:21:54,900 - health_monitor - DEBUG - 系统指标 - CPU: 62.6%, 内存: 59.1%, 磁盘: 84.3%
2025-08-04 23:21:56,373 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 42.9%
2025-08-04 23:22:11,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 95.8%
2025-08-04 23:22:26,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 85.7%
2025-08-04 23:22:41,719 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 79.3%
2025-08-04 23:22:56,032 - health_monitor - DEBUG - 系统指标 - CPU: 99.6%, 内存: 58.2%, 磁盘: 84.3%
2025-08-04 23:22:57,038 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 98.5%
2025-08-04 23:23:12,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-08-04 23:23:27,461 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 100.0%
2025-08-04 23:23:42,805 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 90.9%
2025-08-04 23:23:57,082 - health_monitor - DEBUG - 系统指标 - CPU: 81.2%, 内存: 60.2%, 磁盘: 84.3%
2025-08-04 23:23:57,910 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 87.5%
2025-08-04 23:24:13,351 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 98.9%
2025-08-04 23:24:28,991 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.8%, CPU使用率 100.0%
2025-08-04 23:24:44,321 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 100.0%
2025-08-04 23:24:58,270 - health_monitor - DEBUG - 系统指标 - CPU: 95.6%, 内存: 59.6%, 磁盘: 84.3%
2025-08-04 23:24:59,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 100.0%
2025-08-04 23:25:15,065 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 100.0%
2025-08-04 23:25:30,716 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.7%, CPU使用率 100.0%
2025-08-04 23:25:46,245 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.3%, CPU使用率 100.0%
2025-08-04 23:25:59,390 - health_monitor - DEBUG - 系统指标 - CPU: 98.9%, 内存: 60.7%, 磁盘: 84.3%
2025-08-04 23:26:01,548 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 100.0%
2025-08-04 23:26:16,937 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.7%, CPU使用率 100.0%
2025-08-04 23:26:32,453 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.4%, CPU使用率 100.0%
2025-08-04 23:26:48,058 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.8%, CPU使用率 100.0%
2025-08-04 23:27:01,153 - health_monitor - DEBUG - 系统指标 - CPU: 98.3%, 内存: 60.4%, 磁盘: 84.3%
2025-08-04 23:27:03,302 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.2%, CPU使用率 80.8%
2025-08-04 23:27:18,414 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.5%, CPU使用率 71.4%
2025-08-04 23:27:33,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 92.9%
2025-08-04 23:27:48,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 100.0%
2025-08-04 23:28:02,364 - health_monitor - DEBUG - 系统指标 - CPU: 94.5%, 内存: 63.7%, 磁盘: 84.3%
2025-08-04 23:28:03,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 67.9%
2025-08-04 23:28:18,889 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.5%, CPU使用率 85.7%
2025-08-04 23:28:33,998 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.7%, CPU使用率 100.0%
2025-08-04 23:28:49,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 100.0%
2025-08-04 23:29:03,652 - health_monitor - DEBUG - 系统指标 - CPU: 83.2%, 内存: 63.2%, 磁盘: 84.3%
2025-08-04 23:29:04,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.2%, CPU使用率 65.5%
2025-08-04 23:29:19,671 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.0%, CPU使用率 73.3%
2025-08-04 23:29:34,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.4%, CPU使用率 65.5%
2025-08-04 23:29:50,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.8%, CPU使用率 100.0%
2025-08-04 23:30:04,697 - health_monitor - DEBUG - 系统指标 - CPU: 93.4%, 内存: 66.8%, 磁盘: 84.3%
2025-08-04 23:30:05,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 93.1%
2025-08-04 23:30:20,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 100.0%
2025-08-04 23:30:35,492 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.4%, CPU使用率 93.5%
2025-08-04 23:30:50,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 92.5%
2025-08-04 23:31:05,724 - health_monitor - DEBUG - 系统指标 - CPU: 92.6%, 内存: 66.9%, 磁盘: 84.3%
2025-08-04 23:31:05,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 92.9%
2025-08-04 23:31:21,260 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 100.0%
2025-08-04 23:31:36,717 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.8%, CPU使用率 100.0%
2025-08-04 23:31:52,094 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 98.3%
2025-08-04 23:32:06,754 - health_monitor - DEBUG - 系统指标 - CPU: 88.4%, 内存: 70.4%, 磁盘: 84.3%
2025-08-04 23:32:07,349 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-08-04 23:32:22,678 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 23:32:37,887 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 97.8%
2025-08-04 23:32:53,391 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-04 23:33:08,162 - health_monitor - DEBUG - 系统指标 - CPU: 89.3%, 内存: 69.7%, 磁盘: 84.3%
2025-08-04 23:33:08,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 92.9%
2025-08-04 23:33:23,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 93.8%
2025-08-04 23:33:38,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.5%, CPU使用率 57.1%
2025-08-04 23:33:53,881 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 78.1%
2025-08-04 23:34:09,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 89.8%
2025-08-04 23:34:09,433 - health_monitor - DEBUG - 系统指标 - CPU: 84.0%, 内存: 72.1%, 磁盘: 84.3%
2025-08-04 23:34:24,393 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 100.0%
2025-08-04 23:34:39,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 93.1%
2025-08-04 23:34:54,647 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 50.0%
2025-08-04 23:35:09,764 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 53.6%
2025-08-04 23:35:10,650 - health_monitor - DEBUG - 系统指标 - CPU: 85.4%, 内存: 74.4%, 磁盘: 84.3%
2025-08-04 23:35:24,951 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-08-04 23:35:40,249 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 100.0%
2025-08-04 23:35:55,387 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 65.5%
2025-08-04 23:36:10,809 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 98.0%
2025-08-04 23:36:11,880 - health_monitor - DEBUG - 系统指标 - CPU: 92.9%, 内存: 76.1%, 磁盘: 84.3%
2025-08-04 23:36:25,915 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 71.4%
2025-08-04 23:36:41,037 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.7%, CPU使用率 79.2%
2025-08-04 23:36:56,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.6%, CPU使用率 100.0%
2025-08-04 23:37:12,419 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.2%, CPU使用率 100.0%
2025-08-04 23:37:13,592 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 77.0%, 磁盘: 84.3%
2025-08-04 23:37:28,004 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.0%, CPU使用率 100.0%
2025-08-04 23:37:43,211 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.6%, CPU使用率 100.0%
2025-08-04 23:37:58,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.7%, CPU使用率 96.4%
2025-08-04 23:38:13,445 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.8%, CPU使用率 75.9%
2025-08-04 23:38:15,110 - health_monitor - DEBUG - 系统指标 - CPU: 98.7%, 内存: 80.4%, 磁盘: 84.3%
2025-08-04 23:38:28,679 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.4%, CPU使用率 83.8%
2025-08-04 23:38:43,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.0%, CPU使用率 100.0%
2025-08-04 23:38:58,955 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.6%, CPU使用率 100.0%
2025-08-04 23:39:14,315 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.2%, CPU使用率 100.0%
2025-08-04 23:39:16,650 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 79.3%, 磁盘: 84.3%
2025-08-04 23:39:29,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 79.4%, CPU使用率 79.5%
2025-08-04 23:39:44,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 80.4%, CPU使用率 76.7%
2025-08-04 23:39:59,821 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.8%, CPU使用率 80.8%
2025-08-04 23:40:15,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.6%, CPU使用率 100.0%
2025-08-04 23:40:17,761 - health_monitor - DEBUG - 系统指标 - CPU: 98.9%, 内存: 83.7%, 磁盘: 84.3%
2025-08-04 23:40:30,346 - monitoring - DEBUG - 资源指标更新: 内存使用率 81.4%, CPU使用率 98.1%
2025-08-04 23:40:45,932 - monitoring - DEBUG - 资源指标更新: 内存使用率 83.2%, CPU使用率 100.0%
2025-08-04 23:41:01,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 84.9%, CPU使用率 95.7%
2025-08-04 23:41:16,434 - monitoring - DEBUG - 资源指标更新: 内存使用率 83.2%, CPU使用率 100.0%
2025-08-04 23:41:18,967 - health_monitor - DEBUG - 系统指标 - CPU: 98.9%, 内存: 83.5%, 磁盘: 84.3%
2025-08-04 23:41:32,020 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.2%, CPU使用率 100.0%
2025-08-04 23:41:47,051 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 98.9, 阈值: 90
2025-08-04 23:41:47,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.6%, CPU使用率 100.0%
2025-08-04 23:42:02,844 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.7%, CPU使用率 100.0%
2025-08-04 23:42:18,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.2%, CPU使用率 100.0%
2025-08-04 23:42:20,327 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 86.9%, 磁盘: 84.3%
2025-08-04 23:42:33,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.3%, CPU使用率 100.0%
2025-08-04 23:42:49,021 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.1%, CPU使用率 100.0%
2025-08-04 23:43:04,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.7%, CPU使用率 100.0%
2025-08-04 23:43:19,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.8%, CPU使用率 100.0%
2025-08-04 23:43:21,485 - health_monitor - DEBUG - 系统指标 - CPU: 91.5%, 内存: 86.6%, 磁盘: 84.3%
2025-08-04 23:43:34,798 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.7%, CPU使用率 96.4%
2025-08-04 23:43:50,069 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.2%, CPU使用率 97.7%
2025-08-04 23:44:05,183 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.1%, CPU使用率 50.0%
2025-08-04 23:44:20,630 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.4%, CPU使用率 100.0%
2025-08-04 23:44:22,581 - health_monitor - DEBUG - 系统指标 - CPU: 95.4%, 内存: 88.6%, 磁盘: 84.3%
2025-08-04 23:44:35,735 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.8%, CPU使用率 92.3%
2025-08-04 23:44:50,963 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.6%, CPU使用率 100.0%
2025-08-04 23:45:06,290 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.1%, CPU使用率 98.2%
2025-08-04 23:45:21,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.4%, CPU使用率 100.0%
2025-08-04 23:45:23,907 - health_monitor - DEBUG - 系统指标 - CPU: 92.6%, 内存: 87.6%, 磁盘: 84.3%
2025-08-04 23:45:36,531 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.4%, CPU使用率 75.9%
2025-08-04 23:45:51,779 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.2%, CPU使用率 94.1%
2025-08-04 23:46:07,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.5%, CPU使用率 100.0%
2025-08-04 23:46:22,313 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.2%, CPU使用率 87.5%
2025-08-04 23:46:25,146 - health_monitor - DEBUG - 系统指标 - CPU: 98.6%, 内存: 90.9%, 磁盘: 84.3%
2025-08-04 23:46:37,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 88.9%, CPU使用率 100.0%
2025-08-04 23:46:52,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.1%, CPU使用率 97.4%
2025-08-04 23:47:08,154 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.7%, CPU使用率 92.9%
2025-08-04 23:47:23,278 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.5%, CPU使用率 100.0%
2025-08-04 23:47:26,491 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 89.1%, 磁盘: 84.3%
2025-08-04 23:47:38,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.6%, CPU使用率 100.0%
2025-08-04 23:47:53,838 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.4%, CPU使用率 100.0%
2025-08-04 23:48:09,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.6%, CPU使用率 100.0%
2025-08-04 23:48:24,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.0%, CPU使用率 79.3%
2025-08-04 23:48:27,584 - health_monitor - DEBUG - 系统指标 - CPU: 87.3%, 内存: 92.3%, 磁盘: 84.3%
2025-08-04 23:48:39,861 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.6%, CPU使用率 100.0%
2025-08-04 23:48:55,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.9%, CPU使用率 92.3%
2025-08-04 23:49:10,886 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.7%, CPU使用率 82.1%
2025-08-04 23:49:26,060 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.3%, CPU使用率 100.0%
2025-08-04 23:49:28,747 - health_monitor - DEBUG - 系统指标 - CPU: 91.2%, 内存: 91.8%, 磁盘: 84.3%
2025-08-04 23:49:41,293 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.4%, CPU使用率 100.0%
2025-08-04 23:49:56,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.5%, CPU使用率 82.1%
2025-08-04 23:50:11,699 - monitoring - DEBUG - 资源指标更新: 内存使用率 94.3%, CPU使用率 95.7%
2025-08-04 23:50:26,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 94.2%, CPU使用率 97.4%
2025-08-04 23:50:29,923 - health_monitor - DEBUG - 系统指标 - CPU: 99.2%, 内存: 91.8%, 磁盘: 84.3%
2025-08-04 23:50:41,995 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.5%, CPU使用率 78.6%
2025-08-04 23:50:57,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.5%, CPU使用率 89.7%
2025-08-04 23:51:12,224 - monitoring - DEBUG - 资源指标更新: 内存使用率 93.3%, CPU使用率 96.6%
2025-08-04 23:51:27,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.8%, CPU使用率 89.3%
2025-08-04 23:51:31,157 - health_monitor - DEBUG - 系统指标 - CPU: 85.8%, 内存: 91.7%, 磁盘: 84.3%
2025-08-04 23:51:42,460 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.0%, CPU使用率 100.0%
2025-08-04 23:51:57,569 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.3%, CPU使用率 89.3%
2025-08-04 23:52:12,684 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.9%, CPU使用率 67.9%
2025-08-04 23:52:27,873 - monitoring - DEBUG - 资源指标更新: 内存使用率 90.7%, CPU使用率 100.0%
2025-08-04 23:52:32,345 - health_monitor - DEBUG - 系统指标 - CPU: 84.7%, 内存: 92.0%, 磁盘: 84.3%
2025-08-04 23:52:43,022 - monitoring - DEBUG - 资源指标更新: 内存使用率 89.8%, CPU使用率 77.8%
2025-08-04 23:52:58,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 92.4%, CPU使用率 74.1%
2025-08-04 23:53:13,307 - monitoring - DEBUG - 资源指标更新: 内存使用率 91.4%, CPU使用率 57.1%
2025-08-04 23:53:28,419 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.1%, CPU使用率 78.6%
2025-08-04 23:53:33,428 - health_monitor - DEBUG - 系统指标 - CPU: 89.7%, 内存: 89.7%, 磁盘: 84.3%
2025-08-04 23:53:43,534 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.8%, CPU使用率 89.3%
2025-08-04 23:53:58,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.6%, CPU使用率 87.5%
2025-08-04 23:54:13,755 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.5%, CPU使用率 95.8%
2025-08-04 23:54:28,878 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.7%, CPU使用率 83.3%
2025-08-04 23:54:34,505 - health_monitor - DEBUG - 系统指标 - CPU: 72.5%, 内存: 88.0%, 磁盘: 84.3%
2025-08-04 23:54:44,003 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.4%, CPU使用率 89.3%
2025-08-04 23:54:59,177 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.9%, CPU使用率 65.5%
2025-08-04 23:55:14,479 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.4%, CPU使用率 73.9%
2025-08-04 23:55:29,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.1%, CPU使用率 93.1%
2025-08-04 23:55:35,572 - health_monitor - DEBUG - 系统指标 - CPU: 82.1%, 内存: 87.0%, 磁盘: 84.3%
2025-08-04 23:55:44,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.1%, CPU使用率 100.0%
2025-08-04 23:55:59,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.2%, CPU使用率 87.5%
2025-08-04 23:56:15,132 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.1%, CPU使用率 78.1%
2025-08-04 23:56:30,358 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.0%, CPU使用率 100.0%
2025-08-04 23:56:36,622 - health_monitor - DEBUG - 系统指标 - CPU: 83.6%, 内存: 85.9%, 磁盘: 84.3%
2025-08-04 23:56:45,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 87.8%, CPU使用率 46.4%
2025-08-04 23:57:00,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 86.0%, CPU使用率 93.1%
2025-08-04 23:57:15,726 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.9%, CPU使用率 89.3%
2025-08-04 23:57:31,016 - monitoring - DEBUG - 资源指标更新: 内存使用率 85.0%, CPU使用率 100.0%
