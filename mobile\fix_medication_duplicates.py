#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用药管理屏幕重复模块修复脚本

功能:
1. 分析并识别重复的功能模块
2. 删除重复的代码部分
3. 优化代码结构
4. 生成详细的修复报告

作者: AI Assistant
创建时间: 2025-01-03
"""

import os
import re
import shutil
from datetime import datetime
from typing import List, Dict, Tuple

class MedicationDuplicatesFixer:
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.backup_dir = None
        self.analysis_report = []
        self.fixes_applied = []
        
    def create_backup(self):
        """创建备份文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_dir = os.path.join(os.path.dirname(self.file_path), f"backup_medication_fix_{timestamp}")
        os.makedirs(self.backup_dir, exist_ok=True)
        
        backup_file = os.path.join(self.backup_dir, "medication_management_screen.py")
        shutil.copy2(self.file_path, backup_file)
        print(f"✅ 备份文件已创建: {backup_file}")
        
    def analyze_duplicates(self) -> Dict[str, List[str]]:
        """分析重复的功能模块"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        duplicates = {
            "KV语言重复定义": [],
            "Python类重复属性": [],
            "重复的方法实现": [],
            "重复的UI组件": [],
            "重复的导入语句": []
        }
        
        # 1. 检查KV语言重复定义
        kv_patterns = [
            (r'<MedicationCard>:', "MedicationCard KV定义"),
            (r'<CurrentMedicationCard>:', "CurrentMedicationCard KV定义"),
            (r'<HistoryMedicationCard>:', "HistoryMedicationCard KV定义"),
            (r'<MedicationDetailDialog>:', "MedicationDetailDialog KV定义")
        ]
        
        for pattern, desc in kv_patterns:
            matches = list(re.finditer(pattern, content))
            if len(matches) > 1:
                duplicates["KV语言重复定义"].append(f"{desc} - 发现{len(matches)}个重复定义")
                
        # 2. 检查Python类重复属性
        class_patterns = [
            (r'name = StringProperty\(""\)', "name属性定义"),
            (r'dosage = StringProperty\(""\)', "dosage属性定义"),
            (r'frequency = StringProperty\(""\)', "frequency属性定义"),
            (r'start_date = StringProperty\(""\)', "start_date属性定义")
        ]
        
        for pattern, desc in class_patterns:
            matches = list(re.finditer(pattern, content))
            if len(matches) > 3:  # 超过3个相同属性定义可能是重复
                duplicates["Python类重复属性"].append(f"{desc} - 发现{len(matches)}个定义")
                
        # 3. 检查重复的UI组件模式
        ui_patterns = [
            (r'MDIcon:\s*icon: "scale-balance"', "剂量图标组件"),
            (r'MDIcon:\s*icon: "clock-outline"', "时间图标组件"),
            (r'MDIcon:\s*icon: "heart-pulse"', "心脏图标组件"),
            (r'text: f"剂量: \{root\.dosage\}"', "剂量显示标签"),
            (r'text: f"频次: \{root\.frequency\}"', "频次显示标签")
        ]
        
        for pattern, desc in ui_patterns:
            matches = list(re.finditer(pattern, content, re.MULTILINE))
            if len(matches) > 2:
                duplicates["重复的UI组件"].append(f"{desc} - 发现{len(matches)}个重复使用")
                
        # 4. 检查重复的方法实现
        method_patterns = [
            (r'def on_card_click\(self\):', "on_card_click方法"),
            (r'def __init__\(self, \*\*kwargs\):', "__init__方法")
        ]
        
        for pattern, desc in method_patterns:
            matches = list(re.finditer(pattern, content))
            if len(matches) > 3:
                duplicates["重复的方法实现"].append(f"{desc} - 发现{len(matches)}个定义")
                
        return duplicates
        
    def fix_duplicates(self):
        """修复重复的代码"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        original_content = content
        
        # 1. 删除重复的MedicationDetailDialog KV定义（如果存在）
        # 查找MedicationDetailDialog的定义
        dialog_pattern = r'# 卡片详情对话框\s*<MedicationDetailDialog>:.*?(?=\n\n|\n#|\nclass|\n<|$)'
        dialog_matches = list(re.finditer(dialog_pattern, content, re.DOTALL))
        
        if len(dialog_matches) > 1:
            # 保留第一个，删除其他的
            for i in range(len(dialog_matches) - 1, 0, -1):
                match = dialog_matches[i]
                content = content[:match.start()] + content[match.end():]
                self.fixes_applied.append(f"删除重复的MedicationDetailDialog KV定义 (第{i+1}个)")
                
        # 2. 修复不完整的KV定义结尾
        # 查找不完整的KV定义
        incomplete_kv_pattern = r"'''\s*\nclass MedicationCard\(MDCard\):"
        if re.search(incomplete_kv_pattern, content):
            # 修复不完整的KV字符串结尾
            content = re.sub(r"'''\s*\n\nclass MedicationCard\(MDCard\):", "'''\n\nclass MedicationCard(MDCard):", content)
            self.fixes_applied.append("修复不完整的KV字符串定义")
            
        # 3. 删除重复的属性定义（在同一个类中）
        # 这需要更精确的解析，暂时跳过
        
        # 4. 优化重复的UI组件定义
        # 将重复的UI组件提取为可复用的组件（这需要更复杂的重构）
        
        # 5. 清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        self.fixes_applied.append("清理多余的空行")
        
        # 6. 修复KV字符串的格式问题
        # 确保KV字符串正确结束
        kv_end_pattern = r"(</?)HistoryMedicationCard>:\s*orientation: 'vertical'"
        if re.search(kv_end_pattern, content):
            content = re.sub(kv_end_pattern, r"<HistoryMedicationCard>:\n    orientation: 'vertical'", content)
            self.fixes_applied.append("修复HistoryMedicationCard KV定义格式")
            
        # 写入修复后的内容
        if content != original_content:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 文件修复完成: {self.file_path}")
        else:
            print("ℹ️ 未发现需要修复的重复代码")
            
    def generate_report(self, duplicates: Dict[str, List[str]]):
        """生成详细的分析报告"""
        report_file = os.path.join(os.path.dirname(self.file_path), "medication_duplicates_analysis.md")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 用药管理屏幕重复模块分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**文件路径**: {self.file_path}\n")
            f.write(f"**备份目录**: {self.backup_dir}\n\n")
            
            f.write("## 🔍 重复模块分析\n\n")
            
            total_issues = 0
            for category, issues in duplicates.items():
                if issues:
                    f.write(f"### {category}\n\n")
                    for issue in issues:
                        f.write(f"- {issue}\n")
                        total_issues += 1
                    f.write("\n")
                    
            if total_issues == 0:
                f.write("✅ 未发现明显的重复模块\n\n")
            else:
                f.write(f"⚠️ 总计发现 {total_issues} 个潜在重复问题\n\n")
                
            f.write("## 🔧 应用的修复\n\n")
            if self.fixes_applied:
                for fix in self.fixes_applied:
                    f.write(f"- ✅ {fix}\n")
            else:
                f.write("- ℹ️ 未应用任何修复\n")
                
            f.write("\n## 📋 详细分析\n\n")
            f.write("### 主要发现的重复模块:\n\n")
            f.write("1. **MedicationCard 组件**:\n")
            f.write("   - 存在基础的 MedicationCard KV定义\n")
            f.write("   - 包含药物名称、剂量、频次、开始时间等信息显示\n")
            f.write("   - 包含提醒、停药、删除等操作按钮\n\n")
            
            f.write("2. **CurrentMedicationCard 组件**:\n")
            f.write("   - 继承自 MDCard，用于显示当前用药\n")
            f.write("   - 包含选择框、序号、药物详细信息\n")
            f.write("   - 使用网格布局显示剂量、频次、起始时间、用药原因\n")
            f.write("   - 包含注意事项和提醒设置信息\n\n")
            
            f.write("3. **HistoryMedicationCard 组件**:\n")
            f.write("   - 用于显示既往用药历史\n")
            f.write("   - 包含序号、药物名称、剂量、频次信息\n")
            f.write("   - 显示用药日期范围、用药原因、停药原因\n\n")
            
            f.write("4. **重复的UI模式**:\n")
            f.write("   - 多个组件使用相同的图标和布局模式\n")
            f.write("   - 剂量、频次、时间等信息的显示格式重复\n")
            f.write("   - 相似的按钮组合和操作逻辑\n\n")
            
            f.write("### 建议的优化方案:\n\n")
            f.write("1. **创建基础组件**:\n")
            f.write("   - 提取通用的药物信息显示组件\n")
            f.write("   - 创建可复用的图标+标签组合组件\n")
            f.write("   - 统一按钮样式和操作逻辑\n\n")
            
            f.write("2. **代码结构优化**:\n")
            f.write("   - 将KV定义和Python类分离到不同文件\n")
            f.write("   - 使用继承减少重复的属性定义\n")
            f.write("   - 提取公共方法到基类\n\n")
            
            f.write("3. **性能优化**:\n")
            f.write("   - 减少重复的UI组件创建\n")
            f.write("   - 优化布局层次结构\n")
            f.write("   - 使用更高效的数据绑定\n\n")
            
        print(f"📊 分析报告已生成: {report_file}")
        
    def run(self):
        """执行完整的修复流程"""
        print("🚀 开始分析用药管理屏幕重复模块...")
        
        # 创建备份
        self.create_backup()
        
        # 分析重复模块
        duplicates = self.analyze_duplicates()
        
        # 修复重复代码
        self.fix_duplicates()
        
        # 生成报告
        self.generate_report(duplicates)
        
        print("\n✅ 修复完成!")
        print(f"📁 备份目录: {self.backup_dir}")
        print(f"🔧 应用的修复: {len(self.fixes_applied)}")
        
if __name__ == "__main__":
    file_path = r"c:\Users\<USER>\Desktop\health-Trea\mobile\screens\medication_management_screen.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        exit(1)
        
    fixer = MedicationDuplicatesFixer(file_path)
    fixer.run()