# 评估量表填写与历史记录界面
from kivy.uix.screenmanager import Screen
from kivy.properties import ListProperty, ObjectProperty, StringProperty, BooleanProperty
from kivy.clock import Clock
from kivymd.uix.button import MDButton
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import <PERSON><PERSON>ist, MDListItem, MDListItemHeadlineText, MDListItemSupportingText
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivy.lang import Builder
from kivy.uix.scrollview import ScrollView
from functools import partial
import threading
import json
import logging
from datetime import datetime
from kivymd.app import MDApp as App
from kivy.metrics import dp
from screens.base_screen import BaseScreen

# 导入API客户端
from api.api_client import APIClient

# 导入认证管理器
from utils.auth_manager import get_auth_manager, AuthManager

# 导入健康数据管理器
from utils.health_data_manager import get_health_data_manager

# 导入Logo组件
from widgets.logo import HealthLogo

# 导入主题
from theme import AppTheme, AppMetrics

# 设置日志
logger = logging.getLogger(__name__)

Builder.load_string('''
<AssessmentScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    BoxLayout:
        orientation: 'vertical'
        spacing: dp(10)
        padding: [dp(10), dp(10), dp(10), dp(10)]

        # 顶部栏
        BoxLayout:
            size_hint_y: None
            height: dp(56)
            spacing: dp(10)

            MDIconButton:
                icon: "arrow-left"
                on_release: root.go_back()
                pos_hint: {"center_y": .5}
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_DARK

            MDLabel:
                text: "评估量表"
                font_style: "Title"
                theme_text_color: "Custom"
                text_color: app.theme.PRIMARY_DARK
                size_hint_x: None
                width: dp(120)
                halign: "left"

            Widget:
                size_hint_x: 1

            MDIconButton:
                icon: "refresh"
                on_release: root.refresh_data()
                pos_hint: {"center_y": .5}
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_DARK

        # 内容区域
        MDCard:
            orientation: 'vertical'
            size_hint_y: 1
            md_bg_color: app.theme.CARD_BACKGROUND
            radius: [dp(12)]
            elevation: 2
            padding: [dp(16), dp(16), dp(16), dp(16)]

            # 选项卡标题
            BoxLayout:
                size_hint_y: None
                height: dp(48)
                spacing: dp(10)

                MDButton:
                    text: "待完成"
                    on_release: root.switch_tab("pending")
                    size_hint_x: 0.33
                    style: "text"
                    theme_text_color: 'Custom'
                    text_color: app.theme.PRIMARY_COLOR if root.current_tab == "pending" else app.theme.TEXT_DISABLED

                MDButton:
                    text: "已完成"
                    on_release: root.switch_tab("completed")
                    size_hint_x: 0.33
                    style: "text"
                    theme_text_color: 'Custom'
                    text_color: app.theme.PRIMARY_COLOR if root.current_tab == "completed" else app.theme.TEXT_DISABLED

                MDButton:
                    text: "历史记录"
                    on_release: root.switch_tab("history")
                    size_hint_x: 0.33
                    style: "text"
                    theme_text_color: 'Custom'
                    text_color: app.theme.PRIMARY_COLOR if root.current_tab == "history" else app.theme.TEXT_DISABLED

            # 内容区域
            BoxLayout:
                id: content_area
                orientation: 'vertical'

                # 待完成评估量表内容
                BoxLayout:
                    id: pending_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 1

                    # 刷新按钮
                    MDButton:
                        text: "刷新量表列表"
                        on_release: root.load_pending_assessments()
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK

                    # 量表列表
                    ScrollView:
                        MDList:
                            id: pending_assessment_list

                # 已完成评估量表内容
                BoxLayout:
                    id: completed_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 0
                    size_hint: 1, 0
                    height: 0

                    # 刷新按钮
                    MDButton:
                        text: "刷新已完成量表"
                        on_release: root.load_completed_assessments()
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK

                    # 已完成量表列表
                    ScrollView:
                        MDList:
                            id: completed_assessment_list

                # 历史记录内容
                BoxLayout:
                    id: history_content
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    opacity: 0
                    size_hint: 1, 0
                    height: 0

                    # 刷新按钮
                    MDButton:
                        text: "刷新历史记录"
                        on_release: root.load_assessment_history()
                        size_hint_y: None
                        height: dp(48)
                        pos_hint: {"center_x": .5}
                        style: "filled"
                        md_bg_color: app.theme.PRIMARY_DARK

                    # 历史记录列表
                    ScrollView:
                        MDList:
                            id: history_assessment_list
''')

class AssessmentScreen(BaseScreen):
    """评估量表屏幕"""
    current_tab = StringProperty("pending")
    is_loading = BooleanProperty(False)
    
    def __init__(self, **kwargs):
        super(AssessmentScreen, self).__init__(**kwargs)
        self.app = App.get_running_app()
        self.api_client = APIClient()
        self.auth_manager = get_auth_manager()
        self.health_data_manager = get_health_data_manager()
        # 只加载一次KV
        # 延迟初始化
        Clock.schedule_once(self.init_ui, 0.2)
    
    def init_ui(self, dt=0):
        self.refresh_data()
    
    def on_enter(self):
        super().on_enter()
        self.init_ui()
    
    def refresh_data(self):
        """刷新所有数据"""
        if self.current_tab == "pending":
            self.load_pending_assessments()
        elif self.current_tab == "completed":
            self.load_completed_assessments()
        elif self.current_tab == "history":
            self.load_assessment_history()
    
    def switch_tab(self, tab_name):
        """切换选项卡"""
        self.current_tab = tab_name
        # 只允许在KivyMD 2.x下用0/1切换可见性和高度，不能用None
        if hasattr(self, 'ids'):
            if 'pending_content' in self.ids:
                self.ids.pending_content.opacity = 1 if tab_name == "pending" else 0
                self.ids.pending_content.size_hint_y = 1 if tab_name == "pending" else 0
                self.ids.pending_content.height = self.ids.pending_content.minimum_height if tab_name == "pending" else 0
            if 'completed_content' in self.ids:
                self.ids.completed_content.opacity = 1 if tab_name == "completed" else 0
                self.ids.completed_content.size_hint_y = 1 if tab_name == "completed" else 0
                self.ids.completed_content.height = self.ids.completed_content.minimum_height if tab_name == "completed" else 0
            if 'history_content' in self.ids:
                self.ids.history_content.opacity = 1 if tab_name == "history" else 0
                self.ids.history_content.size_hint_y = 1 if tab_name == "history" else 0
                self.ids.history_content.height = self.ids.history_content.minimum_height if tab_name == "history" else 0
        # 切换时自动刷新
        if tab_name == "pending":
            self.load_pending_assessments()
        elif tab_name == "completed":
            self.load_completed_assessments()
        elif tab_name == "history":
            self.load_assessment_history()
    
    def load_pending_assessments(self):
        """加载待完成的评估量表"""
        if self.is_loading:
            return
        
        self.is_loading = True
        self.ids.pending_assessment_list.clear_widgets()
        
        # 添加加载中提示
        loading_item = MDListItem()
        loading_item.add_widget(MDListItemHeadlineText(text="加载中..."))
        self.ids.pending_assessment_list.add_widget(loading_item)
        
        # 在后台线程中加载数据
        threading.Thread(target=self._load_pending_assessments_thread, daemon=True).start()
    
    def _load_pending_assessments_thread(self, dt):
        try:
            api_client = APIClient()
            def update_ui():
                user = getattr(self, 'current_user', None)
                custom_id = getattr(user, 'custom_id', None) if user else None
                result = api_client.get_assessments(custom_id=custom_id, status='pending')
                # 只在数据变化时打印日志
                if result != getattr(self, '_last_pending_result', None):
                    logger.info(f"AssessmentScreen: 量表API原始响应: {result}")
                    self._last_pending_result = result
                # 兼容两种格式
                if isinstance(result, dict) and 'data' in result:
                    data = result['data']
                elif isinstance(result, list):
                    data = result
                else:
                    logger.warning(f"API响应格式异常: {result}")
                    data = []
                # 只在数据变化时刷新UI
                if data != getattr(self, '_last_pending_list', None):
                    self._last_pending_list = data
                    if hasattr(self, 'ids') and 'pending_assessment_list' in self.ids:
                        self.ids.pending_assessment_list.clear_widgets()
                        if not data:
                            self.ids.pending_assessment_list.add_widget(MDLabel(text='暂无分发的量表', halign='center', theme_text_color='Hint'))
                        else:
                            for item in data:
                                # API已经按status过滤，不需要再次过滤
                                list_item = MDListItem(
                                    MDListItemHeadlineText(text=item.get('name', item.get('title', ''))),
                                    MDListItemSupportingText(text=item.get('description', '')),
                                    on_release=lambda x, i=item: self.open_assessment(i)
                                )
                                self.ids.pending_assessment_list.add_widget(list_item)
            try:
                update_ui()
            except ReferenceError:
                logger.error("AssessmentScreen: 页面已销毁，跳过UI更新")
            except Exception as e:
                logger.error(f"AssessmentScreen: 更新评估量表UI时发生错误: {e}")
        except Exception as e:
            logger.error(f"AssessmentScreen: 加载评估量表线程异常: {e}")
        finally:
            Clock.schedule_once(lambda dt: setattr(self, 'is_loading', False), 0)
    
    def load_completed_assessments(self):
        """加载已完成的评估量表"""
        if self.is_loading:
            return
        
        self.is_loading = True
        self.ids.completed_assessment_list.clear_widgets()
        
        # 添加加载中提示
        loading_item = MDListItem()
        loading_item.add_widget(MDListItemHeadlineText(text="加载中..."))
        self.ids.completed_assessment_list.add_widget(loading_item)
        
        # 在后台线程中加载数据
        threading.Thread(target=self._load_completed_assessments_thread, daemon=True).start()
    
    def _load_completed_assessments_thread(self, dt):
        """在后台线程中加载已完成的评估量表"""
        try:
            api_client = APIClient()
            def update_ui():
                user = getattr(self, 'current_user', None)
                custom_id = getattr(user, 'custom_id', None) if user else None
                result = api_client.get_assessments(custom_id=custom_id, status='completed')
                assessments = result if isinstance(result, list) else []
                if hasattr(self, 'ids') and 'completed_assessment_list' in self.ids:
                    self.ids.completed_assessment_list.clear_widgets()
                    if not assessments:
                        self.ids.completed_assessment_list.add_widget(MDLabel(text='暂无已完成量表', halign='center', theme_text_color='Hint'))
                    else:
                        for item in assessments:
                            # API已经按status过滤，不需要再次过滤
                            list_item = MDListItem(
                                MDListItemHeadlineText(text=item.get('name', item.get('title', ''))),
                                MDListItemSupportingText(text=item.get('description', '')),
                                on_release=lambda x, i=item: self.view_assessment_result(i)
                            )
                            self.ids.completed_assessment_list.add_widget(list_item)
            Clock.schedule_once(lambda dt: update_ui())
        except Exception as e:
            logger.error(f"加载已完成评估量表时出错: {e}")
            Clock.schedule_once(lambda dt: self._show_error(f"加载评估量表失败: {str(e)}"), 0)
        finally:
            Clock.schedule_once(lambda dt: setattr(self, 'is_loading', False), 0)
    
    def load_assessment_history(self):
        """加载评估量表历史记录"""
        if self.is_loading:
            return
        
        self.is_loading = True
        self.ids.history_assessment_list.clear_widgets()
        
        # 添加加载中提示
        loading_item = MDListItem()
        loading_item.add_widget(MDListItemHeadlineText(text="加载中..."))
        self.ids.history_assessment_list.add_widget(loading_item)
        
        # 在后台线程中加载数据
        threading.Thread(target=self._load_assessment_history_thread, daemon=True).start()
    
    def _load_assessment_history_thread(self, dt):
        """在后台线程中加载评估量表历史记录（从assessment_results表）"""
        try:
            from utils.cloud_api import get_cloud_api
            from utils.user_manager import get_user_manager
            
            cloud_api = get_cloud_api()
            user_manager = get_user_manager()
            
            def update_ui():
                user = user_manager.get_current_user()
                custom_id = getattr(user, 'custom_id', None) if user else None
                
                if not custom_id:
                    logger.warning("未获取到有效的用户ID，无法获取历史记录")
                    if hasattr(self, 'ids') and 'history_assessment_list' in self.ids:
                        self.ids.history_assessment_list.clear_widgets()
                        self.ids.history_assessment_list.add_widget(MDLabel(text='无法获取用户信息', halign='center', theme_text_color='Hint'))
                    return
                
                # 从assessment_results表获取量表结果报告
                result = cloud_api.get_assessment_reports(custom_id=custom_id)
                assessments = []
                if result.get("status") == "success":
                    assessments = result.get("data", [])
                
                if hasattr(self, 'ids') and 'history_assessment_list' in self.ids:
                    self.ids.history_assessment_list.clear_widgets()
                    if not assessments:
                        self.ids.history_assessment_list.add_widget(MDLabel(text='暂无历史记录', halign='center', theme_text_color='Hint'))
                    else:
                        for item in assessments:
                            # 显示所有已完成的评估结果
                            title = item.get('assessment_name', '') or item.get('title', '') or item.get('name', '未命名量表')
                            description = item.get('description', '') or f"完成时间: {item.get('created_at', '未知')}"
                            list_item = MDListItem(
                                MDListItemHeadlineText(text=title),
                                MDListItemSupportingText(text=description),
                                on_release=lambda x, i=item: self.view_assessment_history(i)
                            )
                            self.ids.history_assessment_list.add_widget(list_item)
            Clock.schedule_once(lambda dt: update_ui())
        except Exception as e:
            logger.error(f"加载评估量表历史记录时出错: {e}")
            Clock.schedule_once(lambda dt: self._show_error(f"加载历史记录失败: {str(e)}"), 0)
        finally:
            Clock.schedule_once(lambda dt: setattr(self, 'is_loading', False), 0)
    
    def open_assessment(self, assessment):
        """打开评估量表进行填写"""
        # 这里应该添加打开评估量表的逻辑
        logger.info(f"打开评估量表: {assessment.get('name', '未命名量表')}")
        self._show_info(f"打开评估量表: {assessment.get('name', '未命名量表')}")
    
    def view_assessment_result(self, assessment):
        """查看评估量表结果"""
        # 这里应该添加查看评估量表结果的逻辑
        logger.info(f"查看评估量表结果: {assessment.get('name', '未命名量表')}")
        self._show_info(f"查看评估量表结果: {assessment.get('name', '未命名量表')}")
    
    def view_assessment_history(self, record):
        """查看评估量表历史记录"""
        # 这里应该添加查看评估量表历史记录的逻辑
        logger.info(f"查看评估量表历史记录: {record.get('name', '未命名量表')}")
        self._show_info(f"查看评估量表历史记录: {record.get('name', '未命名量表')}")
    
    def go_back(self):
        """返回上一屏幕"""
        self.manager.current = 'homepage_screen'
    
    def _show_error(self, message):
        """显示错误消息"""
        dialog = MDDialog(
            text=message,
            buttons=[
                MDButton(
                    text="确定",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def _show_info(self, message):
        """显示信息消息"""
        dialog = MDDialog(
            text=message,
            buttons=[
                MDButton(
                    text="确定",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()