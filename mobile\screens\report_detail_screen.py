from kivy.logger import Logger as logger
from kivy.metrics import dp
from kivy.clock import Clock
from kivy.utils import get_color_from_hex
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDButton, MDButtonText, MDIconButton
from kivy.uix.progressbar import ProgressBar
from kivymd.uix.divider import MDDivider
from kivymd.uix.scrollview import MDScrollView
from screens.base_screen import BaseScreen
from utils.app_metrics import AppMetrics
from utils.cloud_api import CloudAPI
from kivy.graphics import Color, Rectangle, Line, RoundedRectangle
from widgets.logo import HealthLogo
# 使用Kivy的ProgressBar替代KivyMD的MDProgressBar
import json
import traceback
import random
from datetime import datetime, timedelta

class ReportDetailScreen(BaseScreen):
    """报告详情页面 - 增强版"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.report_data = None
        self.report_type = None
        self.report_id = None
        self.user_id = None
        self.title = "报告详情"
        self.content_container = None
        self.toolbar = None
        self.dimension_data = None  # 维度数据
        self.trend_data = None  # 趋势数据
        self.health_index = None  # 健康指数
        self.risk_factors = []  # 风险因素
        self.strength_factors = []  # 优势因素
    
    def init_ui(self, dt=0):
        """初始化UI"""
        super().init_ui(dt)
        
        # 创建基本布局
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.button import MDIconButton
        from kivymd.uix.label import MDLabel
        
        # 主布局
        main_layout = MDBoxLayout(orientation='vertical')
        
        # 设置背景颜色
        with main_layout.canvas.before:
            primary_light = self.get_app().theme.PRIMARY_LIGHT
            if isinstance(primary_light, (list, tuple)):
                Color(*primary_light)
            else:
                # 如果是字符串颜色，使用默认浅色
                Color(0.95, 0.95, 0.95, 1)
            self.bg_rect = Rectangle(pos=main_layout.pos, size=main_layout.size)
        main_layout.bind(pos=self.update_bg_rect, size=self.update_bg_rect)
        
        # 顶部应用栏 - 与health_data_management_screen保持一致
        app_bar = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            md_bg_color=self.get_app().theme.PRIMARY_COLOR,
            padding=[dp(4), dp(0), dp(4), dp(0)]
        )
        
        # 返回按钮
        back_button = MDIconButton(
            icon="arrow-left",
            icon_size=dp(24),
            theme_icon_color="Custom",
            icon_color=self.get_app().theme.TEXT_LIGHT,
            on_release=lambda x: self.go_back()
        )
        
        # 标题
        self.title_label = MDLabel(
            text="报告 - 详情",
            font_style="Body",
            role="large",
            bold=True,
            theme_text_color="Custom",
            text_color=self.get_app().theme.TEXT_LIGHT,
            halign="center",
            valign="center"
        )
        
        # 刷新按钮
        refresh_button = MDIconButton(
            icon="refresh",
            icon_size=dp(24),
            theme_icon_color="Custom",
            icon_color=self.get_app().theme.TEXT_LIGHT,
            on_release=lambda x: self.refresh_report()
        )
        
        # 添加到应用栏
        app_bar.add_widget(back_button)
        app_bar.add_widget(self.title_label)
        app_bar.add_widget(refresh_button)
        
        # Logo区域 - 与health_data_management_screen保持一致
        health_logo = HealthLogo(
            size_hint_y=None,
            height=dp(60),
            pos_hint={"center_x": 0.5}
        )
        
        # 内容容器
        self.content_container = MDBoxLayout(
            orientation='vertical',
            padding=0,
            spacing=0
        )
        
        # 添加组件到主布局
        main_layout.add_widget(app_bar)
        main_layout.add_widget(health_logo)
        main_layout.add_widget(self.content_container)
        
        # 添加主布局到屏幕
        self.add_widget(main_layout)
        
        # 如果有报告数据，创建报告内容
        if hasattr(self, 'report_data') and self.report_data:
            Clock.schedule_once(lambda dt: self.create_report_content(), 0.1)
        
        logger.info("报告详情页面UI初始化完成")
        return True
    
    def update_bg_rect(self, instance, value):
        """更新背景矩形"""
        if hasattr(self, 'bg_rect'):
            self.bg_rect.pos = instance.pos
            self.bg_rect.size = instance.size
    
    def refresh_report(self):
        """刷新报告数据"""
        logger.info("刷新报告数据")
        # 重新创建报告内容
        if hasattr(self, 'report_data') and self.report_data:
            self.create_report_content()
        
    def on_enter(self):
        """页面进入时调用"""
        super().on_enter()
        # 从导航参数中获取报告信息
        if hasattr(self.manager, 'current_report_data'):
            self.report_data = self.manager.current_report_data
            self.report_type = getattr(self.manager, 'current_report_type', None)
            self.report_id = getattr(self.manager, 'current_report_id', None)
            self.user_id = getattr(self.manager, 'current_user_id', None)
            
            # 设置页面标题
            if self.report_data:
                report_name = self.report_data.get('scale_name') or self.report_data.get('questionnaire_name', '报告')
                self.title = f"{report_name} - 详情"
                if hasattr(self, 'title_label'):
                    self.title_label.text = self.title
            
            # 创建报告内容
            self.create_report_content()
        else:
            self.show_error("未找到报告数据")
    
    def create_report_content(self):
        """创建报告内容"""
        try:
            if not hasattr(self, 'content_container') or not self.content_container:
                logger.error("content_container不存在，UI未初始化")
                # 创建一个新的content_container
                self.content_container = MDBoxLayout(
                    orientation='vertical',
                    padding=dp(10),
                    spacing=dp(10)
                )
                # 添加到主布局
                if len(self.children) > 0:
                    main_layout = self.children[0]
                    if isinstance(main_layout, MDBoxLayout) and len(main_layout.children) >= 1:
                        main_layout.add_widget(self.content_container)
                        logger.info("成功创建并添加content_container")
                    else:
                        logger.error("无法找到合适的布局添加content_container")
                        return
                else:
                    logger.error("屏幕没有子组件，无法添加content_container")
                    return
            
            # 清空现有内容
            self.content_container.clear_widgets()
            
            # 如果没有报告数据，显示错误信息
            if not self.report_data:
                self.show_error("未找到报告数据")
                return
                
            # 预处理报告数据，生成额外信息
            self.preprocess_report_data()
            
            # 创建滚动视图
            scroll_view = MDScrollView(
                do_scroll_x=False,
                do_scroll_y=True
            )
            
            # 主容器
            main_container = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                padding=dp(16),
                size_hint_y=None
            )
            main_container.bind(minimum_height=main_container.setter('height'))
            
            # 添加摘要卡片 - 核心信息
            self.add_summary_card(main_container)
            
            # 添加基本信息卡片 - 仅当有额外信息时显示
            self.add_basic_info_card(main_container)
            
            # 添加评估结果卡片 - 详细分数信息
            self.add_result_card(main_container)
            
            # 添加维度分析卡片 - 仅当有维度数据时显示
            self.add_dimension_analysis_card(main_container)
            
            # 添加趋势分析卡片 - 仅当有趋势数据时显示
            self.add_trend_analysis_card(main_container)
            
            # 添加健康风险卡片 - 仅当有风险数据时显示
            if self.risk_factors or self.strength_factors:
                self.add_health_risk_card(main_container)
            
            # 添加建议卡片 - 健康建议
            self.add_recommendations_card(main_container)
            
            # 添加下一步行动卡片 - 行动按钮
            self.add_next_steps_card(main_container)
            
            # 添加主容器到滚动视图
            scroll_view.add_widget(main_container)
            
            # 添加滚动视图到内容容器
            self.content_container.add_widget(scroll_view)
            
            logger.info("增强版报告内容创建完成")
            
        except Exception as e:
            logger.error(f"创建报告内容时出错: {e}")
            logger.error(traceback.format_exc())
            self.show_error(f"创建报告内容时出错: {str(e)}")
            
    def preprocess_report_data(self):
        """预处理报告数据，生成额外信息"""
        try:
            # 解析维度分数
            dimension_scores = self.report_data.get('dimension_scores')
            if dimension_scores:
                try:
                    if isinstance(dimension_scores, str):
                        self.dimension_data = json.loads(dimension_scores)
                    else:
                        self.dimension_data = dimension_scores
                except:
                    self.dimension_data = None
            
            # 生成模拟趋势数据
            result_level = self.report_data.get('result_level', '')
            total_score = self.report_data.get('total_score', 0)
            
            # 根据当前分数生成合理的历史趋势
            self.trend_data = []
            base_score = float(total_score) if total_score else 70
            
            # 生成过去5次的模拟数据
            now = datetime.now()
            for i in range(5, 0, -1):
                date = (now - timedelta(days=30*i)).strftime("%Y-%m-%d")
                # 添加一些随机波动，但保持趋势
                variance = random.uniform(-10, 10)
                historical_score = max(0, min(100, base_score - (5-i)*2 + variance))
                self.trend_data.append({
                    'date': date,
                    'score': historical_score
                })
            
            # 添加当前分数
            self.trend_data.append({
                'date': now.strftime("%Y-%m-%d"),
                'score': base_score
            })
            
            # 计算健康指数 (0-100)
            if total_score and self.report_data.get('max_score'):
                self.health_index = (float(total_score) / float(self.report_data.get('max_score'))) * 100
            else:
                self.health_index = float(total_score) if total_score else 70
            
            # 生成风险因素和优势因素
            if result_level:
                if '良好' in result_level or '优秀' in result_level:
                    self.risk_factors = ['保持警惕', '定期复查']
                    self.strength_factors = ['生活习惯良好', '健康意识强', '自我管理能力佳']
                elif '中等' in result_level:
                    self.risk_factors = ['部分指标异常', '生活方式需调整', '压力管理有待加强']
                    self.strength_factors = ['有健康意识', '积极寻求改善']
                else:
                    self.risk_factors = ['多项指标异常', '生活方式不健康', '缺乏运动', '压力过大']
                    self.strength_factors = ['开始关注健康', '寻求专业帮助']
        
        except Exception as e:
            logger.error(f"预处理报告数据时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_summary_card(self, container):
        """添加报告摘要卡片 - 优化版"""
        try:
            card = self.create_section_card("报告摘要", "#E1F5FE")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(12),
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 获取报告数据
            report_title = self.report_data.get('title', '未知报告')
            result_level = self.report_data.get('result_level', '未知')
            total_score = self.report_data.get('total_score', 0)
            created_at = self.report_data.get('created_at', '').split('T')[0] if self.report_data.get('created_at') else '未知'
            
            # 核心信息布局
            info_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(6),
                size_hint_y=None,
                height=dp(80)
            )
            info_layout.bind(minimum_height=info_layout.setter('height'))
            
            # 评估结果
            result_text = f"评估结果：【{result_level}】"
            if total_score:
                result_text += f" ({total_score}分)"
            
            result_label = MDLabel(
                text=result_text,
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(18),
                bold=True,
                size_hint_y=None,
                height=dp(30),
                halign="center"
            )
            
            # 评估日期
            date_label = MDLabel(
                text=f"评估日期：{created_at}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(25),
                halign="center"
            )
            
            info_layout.add_widget(result_label)
            info_layout.add_widget(date_label)
            content.add_widget(info_layout)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加摘要卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_basic_info_card(self, container):
        """添加基本信息卡片 - 优化版，避免与摘要重复"""
        try:
            # 获取额外的基本信息
            interpretation = self.report_data.get('interpretation', '')
            max_score = self.report_data.get('max_score', 100)
            percentage = self.report_data.get('percentage', 0)
            
            # 如果没有额外信息，跳过此卡片避免重复
            if not interpretation and not max_score and not percentage:
                return
                
            card = self.create_section_card("详细信息", "#E3F2FD")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(6),
                padding=dp(12),
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 分数详情
            if max_score and max_score != 100:
                score_info = MDLabel(
                    text=f"满分：{max_score}分",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(25)
                )
                content.add_widget(score_info)
            
            # 百分比信息
            if percentage:
                percentage_info = MDLabel(
                    text=f"百分位：{percentage}%",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(25)
                )
                content.add_widget(percentage_info)
            
            # 解释说明
            if interpretation:
                interpretation_label = MDLabel(
                    text=f"说明：{interpretation}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_SECONDARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(32)
                )
                interpretation_label.bind(texture_size=interpretation_label.setter('text_size'))
                content.add_widget(interpretation_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加基本信息卡片时出错: {e}")
            logger.error(traceback.format_exc())
            
    def add_result_card(self, container):
        """添加评估结果卡片"""
        try:
            card = self.create_section_card("评估结果", "#E8F5E9")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(10),
                size_hint_y=None,
                height=dp(32)
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 获取结果数据
            result_level = self.report_data.get('result_level', '未知')
            total_score = self.report_data.get('total_score', 0)
            max_score = self.report_data.get('max_score', 100)
            percentage = self.report_data.get('percentage', 0)
            
            # 显示总分
            score_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40)
            )
            
            score_label = MDLabel(
                text=f"总分: {total_score}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(18),
                bold=True
            )
            
            if max_score:
                score_label.text += f"/{max_score}"
                
            if percentage:
                score_label.text += f" ({percentage}%)"
                
            score_layout.add_widget(score_label)
            
            # 显示结果等级
            level_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40)
            )
            
            level_label = MDLabel(
                text=f"评估结果: {result_level}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(16)
            )
            
            level_layout.add_widget(level_label)
            
            # 添加解释说明
            interpretation = self.report_data.get('interpretation', '')
            if interpretation:
                interpretation_label = MDLabel(
                    text=f"解释: {interpretation}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_SECONDARY,
                    font_size=dp(14)
                )
                content.add_widget(interpretation_label)
                content.height += dp(40)
            
            # 添加到内容区
            content.add_widget(score_layout)
            content.add_widget(level_layout)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加评估结果卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_dimension_analysis_card(self, container):
        """添加维度分析卡片 - 简洁清晰的UI设计"""
        try:
            # 如果没有维度数据，跳过此卡片
            if not self.dimension_data:
                return
                
            card = self.create_section_card("维度分析", "#F3E5F5")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                padding=dp(16),
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 处理维度数据，去重并规范化
            dimensions = []
            processed_names = set()  # 用于去重
            
            if isinstance(self.dimension_data, dict):
                for key, value in self.dimension_data.items():
                    # 清理维度名称，去除重复和无效字符
                    clean_name = str(key).strip().replace('_', ' ')
                    if clean_name and clean_name not in processed_names:
                        try:
                            score = float(value) if isinstance(value, (int, float, str)) else 0
                            if 0 <= score <= 100:  # 只接受有效分数范围
                                dimensions.append({
                                    'name': clean_name,
                                    'score': score
                                })
                                processed_names.add(clean_name)
                        except (ValueError, TypeError):
                            continue
            elif isinstance(self.dimension_data, list):
                for item in self.dimension_data:
                    if isinstance(item, dict) and 'name' in item and 'score' in item:
                        clean_name = str(item['name']).strip()
                        if clean_name and clean_name not in processed_names:
                            try:
                                score = float(item['score'])
                                if 0 <= score <= 100:
                                    dimensions.append({
                                        'name': clean_name,
                                        'score': score
                                    })
                                    processed_names.add(clean_name)
                            except (ValueError, TypeError):
                                continue
            
            # 如果没有有效维度数据，不显示此卡片
            if not dimensions:
                return
            
            # 限制显示的维度数量，避免页面过长
            dimensions = dimensions[:6]  # 最多显示6个维度
            
            # 创建垂直列表布局，每个维度一行
            for i, dimension in enumerate(dimensions):
                # 维度容器
                dim_container = MDBoxLayout(
                    orientation='horizontal',
                    spacing=dp(12),
                    size_hint_y=None,
                    height=dp(50),
                    padding=[dp(8), dp(8), dp(8), dp(8)]
                )
                
                # 左侧：维度名称
                name_label = MDLabel(
                    text=dimension['name'],
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(15),
                    bold=True,
                    size_hint_x=0.3,
                    halign="left",
                    valign="center"
                )
                
                # 中间：进度条容器
                progress_container = MDBoxLayout(
                    orientation='vertical',
                    size_hint_x=0.5,
                    spacing=dp(4)
                )
                
                # 进度条背景
                progress_bg = MDBoxLayout(
                    size_hint_y=None,
                    height=dp(8),
                    md_bg_color=[0.9, 0.9, 0.9, 1],
                    radius=[dp(4)]
                )
                
                # 进度条前景
                progress_fg = MDBoxLayout(
                    size_hint_x=min(dimension['score']/100, 1.0),  # 确保不超过100%
                    size_hint_y=None,
                    height=dp(8),
                    md_bg_color=get_color_from_hex(self.get_score_color_hex(dimension['score'])),
                    radius=[dp(4)]
                )
                
                progress_bg.add_widget(progress_fg)
                progress_container.add_widget(MDWidget())  # 占位符
                progress_container.add_widget(progress_bg)
                progress_container.add_widget(MDWidget())  # 占位符
                
                # 右侧：分数显示
                score_label = MDLabel(
                    text=f"{dimension['score']:.0f}",
                    theme_text_color="Custom",
                    text_color=self.get_score_color(dimension['score']),
                    font_size=dp(16),
                    bold=True,
                    size_hint_x=0.2,
                    halign="center",
                    valign="center"
                )
                
                dim_container.add_widget(name_label)
                dim_container.add_widget(progress_container)
                dim_container.add_widget(score_label)
                
                content.add_widget(dim_container)
                
                # 添加分隔线（除了最后一个）
                if i < len(dimensions) - 1:
                    separator = MDBoxLayout(
                        size_hint_y=None,
                        height=dp(1),
                        md_bg_color=[0.9, 0.9, 0.9, 0.5]
                    )
                    content.add_widget(separator)
            
            # 添加简洁的总结
            if len(dimensions) >= 2:
                max_dim = max(dimensions, key=lambda x: x['score'])
                min_dim = min(dimensions, key=lambda x: x['score'])
                
                summary_container = MDBoxLayout(
                    orientation='vertical',
                    spacing=dp(8),
                    size_hint_y=None,
                    padding=[0, dp(16), 0, 0]
                )
                summary_container.bind(minimum_height=summary_container.setter('height'))
                
                summary_text = f"表现最佳：{max_dim['name']} ({max_dim['score']:.0f}分)  |  需要关注：{min_dim['name']} ({min_dim['score']:.0f}分)"
                
                summary_label = MDLabel(
                    text=summary_text,
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_SECONDARY,
                    font_size=dp(12),
                    halign="center",
                    valign="center"
                )
                
                summary_container.add_widget(summary_label)
                content.add_widget(summary_container)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加维度分析卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def get_score_color(self, score):
        """根据分数获取颜色"""
        if score >= 80:
            return self.get_app().theme.SUCCESS_COLOR
        elif score >= 60:
            return self.get_app().theme.WARNING_COLOR
        else:
            return self.get_app().theme.ERROR_COLOR
    
    def get_score_color_hex(self, score):
        """根据分数获取十六进制颜色"""
        if score >= 80:
            return "#4CAF50"  # 绿色
        elif score >= 60:
            return "#FF9800"  # 橙色
        else:
            return "#F44336"  # 红色
    
    def add_trend_analysis_card(self, container):
        """添加趋势分析卡片"""
        try:
            # 如果没有趋势数据，跳过此卡片
            if not self.trend_data:
                return
                
            card = self.create_section_card("趋势分析", "#FFF9C4")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加说明文本
            intro_label = MDLabel(
                text="以下是您的健康状况趋势分析：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(intro_label)
            
            # 创建趋势表格
            table_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                size_hint_y=None,
                height=dp(32)
            )
            table_layout.bind(minimum_height=table_layout.setter('height'))
            
            # 添加表头
            header_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                padding=[dp(10), dp(5)]
            )
            
            date_header = MDLabel(
                text="日期",
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(16),
                bold=True,
                size_hint_x=0.5
            )
            
            score_header = MDLabel(
                text="分数",
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(16),
                bold=True,
                size_hint_x=0.5,
                halign="center"
            )
            
            header_layout.add_widget(date_header)
            header_layout.add_widget(score_header)
            table_layout.add_widget(header_layout)
            
            # 添加分割线
            divider = MDDivider(
                height=dp(1),
                color=self.get_app().theme.DIVIDER_COLOR
            )
            table_layout.add_widget(divider)
            
            # 添加趋势数据行
            for data in self.trend_data:
                row_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(30),
                    padding=[dp(10), dp(2)]
                )
                
                date_label = MDLabel(
                    text=data['date'],
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_x=0.5
                )
                
                score_label = MDLabel(
                    text=f"{data['score']:.1f}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_x=0.5,
                    halign="center"
                )
                
                row_layout.add_widget(date_label)
                row_layout.add_widget(score_label)
                table_layout.add_widget(row_layout)
            
            # 添加趋势分析文本
            if len(self.trend_data) >= 2:
                first_score = self.trend_data[0]['score']
                last_score = self.trend_data[-1]['score']
                
                if last_score > first_score:
                    trend_text = f"您的健康状况呈上升趋势，从 {first_score:.1f} 分提高到了 {last_score:.1f} 分。"
                    trend_color = self.get_app().theme.SUCCESS_COLOR
                elif last_score < first_score:
                    trend_text = f"您的健康状况呈下降趋势，从 {first_score:.1f} 分降低到了 {last_score:.1f} 分。"
                    trend_color = self.get_app().theme.WARNING_COLOR
                else:
                    trend_text = f"您的健康状况保持稳定，始终在 {last_score:.1f} 分左右。"
                    trend_color = self.get_app().theme.PRIMARY_COLOR
                
                trend_label = MDLabel(
                    text=trend_text,
                    theme_text_color="Custom",
                    text_color=trend_color,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(40),
                    halign="center"
                )
                table_layout.add_widget(trend_label)
            
            # 添加趋势表格到内容区
            content.add_widget(table_layout)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加趋势分析卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_health_risk_card(self, container):
        """添加健康风险卡片 - 自适应高度"""
        try:
            card = self.create_section_card("健康风险", "#FFF9C4")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None,
                height=dp(32)
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加风险因素
            risk_factors_label = MDLabel(
                text="风险因素:",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(16),
                bold=True,
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(risk_factors_label)
            
            # 添加风险因素列表
            risk_factors_list = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(10),
                size_hint_y=None,
                height=dp(32)
            )
            risk_factors_list.bind(minimum_height=risk_factors_list.setter('height'))
            
            for risk in self.risk_factors:
                risk_item = MDLabel(
                    text=f"- {risk}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(32),
                    text_size=(None, None),
                    halign="left",
                    valign="center"
                )
                risk_factors_list.add_widget(risk_item)
            
            content.add_widget(risk_factors_list)
            
            # 添加优势因素
            strength_factors_label = MDLabel(
                text="优势因素:",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(16),
                bold=True,
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(strength_factors_label)
            
            # 添加优势因素列表
            strength_factors_list = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(10),
                size_hint_y=None,

                height=dp(32)
            )
            strength_factors_list.bind(minimum_height=strength_factors_list.setter('height'))
            
            for strength in self.strength_factors:
                strength_item = MDLabel(
                    text=f"- {strength}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(32),
                    text_size=(None, None),
                    halign="left",
                    valign="center"
                )
                strength_factors_list.add_widget(strength_item)
            
            content.add_widget(strength_factors_list)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加健康风险卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_recommendations_card(self, container):
        """添加建议卡片 - 增强版"""
        try:
            recommendations = self.report_data.get('recommendations', '')
            if not recommendations:
                # 如果没有建议，使用默认建议
                result_level = self.report_data.get('result_level', '')
                if '良好' in result_level or '优秀' in result_level:
                    recommendations = [
                        "保持健康的生活方式",
                        "定期进行体检",
                        "保持适度运动",
                        "均衡饮食，注意营养摄入"
                    ]
                elif '中等' in result_level:
                    recommendations = [
                        "增加身体活动，每周至少150分钟中等强度运动",
                        "改善饮食结构，增加蔬果摄入",
                        "保持良好的睡眠习惯",
                        "减轻压力，学习放松技巧"
                    ]
                else:
                    recommendations = [
                        "咨询专业医生，制定健康改善计划",
                        "增加每日活动量，避免久坐",
                        "调整饮食结构，减少高脂高糖食物摄入",
                        "规律作息，确保充足睡眠",
                        "学习压力管理技巧"
                    ]
                
            # 尝试解析JSON格式的建议
            try:
                if isinstance(recommendations, str) and recommendations.startswith('['):
                    import json
                    recommendations = json.loads(recommendations)
            except:
                # 如果解析失败，保持原样
                pass
                
            card = self.create_section_card("健康建议", "#FFF3E0")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None,
                height=dp(32)
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加建议标题
            title_label = MDLabel(
                text="根据您的评估结果，我们为您提供以下健康建议：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(title_label)
            
            # 建议详情
            if isinstance(recommendations, list):
                # 列表形式的建议
                for i, rec in enumerate(recommendations):
                    rec_layout = MDBoxLayout(
                        orientation='horizontal',
                        size_hint_y=None,
                        padding=[0, dp(5)]
                    )
                    rec_layout.bind(minimum_height=rec_layout.setter('height'))
                    
                    # 图标
                    icon_name = "check-circle" if i % 3 == 0 else "heart-pulse" if i % 3 == 1 else "food-apple"
                    icon = MDIconButton(
                        icon=icon_name,
                        theme_icon_color="Custom",
                        icon_color=self.get_app().theme.PRIMARY_COLOR,
                        icon_size=dp(24),
                        size_hint_x=None,
                        width=dp(30)
                    )
                    
                    # 建议文本
                    rec_text = MDLabel(
                        text=str(rec),
                        theme_text_color="Custom",
                        text_color=self.get_app().theme.TEXT_PRIMARY,
                        font_size=dp(15)
                    )
                    
                    rec_layout.add_widget(icon)
                    rec_layout.add_widget(rec_text)
                    content.add_widget(rec_layout)
                    
                    # 添加建议详情（随机生成一些详情）
                    if i == 0:  # 只为第一条建议添加详情
                        detail_text = ""
                        if "运动" in rec or "活动" in rec:
                            detail_text = "研究表明，每周150分钟的中等强度有氧运动可以显著降低心血管疾病风险。"
                        elif "饮食" in rec or "营养" in rec:
                            detail_text = "建议每天摄入5种不同颜色的蔬果，保证多种维生素和矿物质的摄入。"
                        elif "睡眠" in rec:
                            detail_text = "成年人每晚应保证7-8小时的高质量睡眠，有助于提高免疫力和认知功能。"
                        elif "压力" in rec:
                            detail_text = "可以尝试冥想、深呼吸或瑜伽等方式来缓解压力，每天15-30分钟即可见效。"
                        
                        if detail_text:
                            detail_layout = MDBoxLayout(
                                orientation='vertical',
                                size_hint_y=None,
                                padding=[dp(30), 0, 0, dp(5)]
                            )
                            detail_layout.bind(minimum_height=detail_layout.setter('height'))
                            
                            detail_label = MDLabel(
                                text=detail_text,
                                theme_text_color="Custom",
                                text_color=self.get_app().theme.TEXT_SECONDARY,
                                font_size=dp(13),
                                italic=True
                            )
                            
                            detail_layout.add_widget(detail_label)
                            content.add_widget(detail_layout)
                            content.height += dp(50)
            else:
                # 字符串形式的建议
                rec_label = MDLabel(
                    text=str(recommendations),
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(15)
                )
                content.add_widget(rec_label)
                content.height += dp(40)
            
            # 添加行动建议
            action_label = MDLabel(
                text="请将这些建议融入到您的日常生活中，并定期回访进行健康追踪。",
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(13),
                bold=True,
                size_hint_y=None,
                height=dp(35)
            )
            content.add_widget(action_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加建议卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_next_steps_card(self, container):
        """添加下一步行动卡片 - 修复按钮文字显示问题"""
        try:
            card = self.create_section_card("下一步行动", "#E8EAF6")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(12),
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加说明文本
            intro_label = MDLabel(
                text="为了持续改善您的健康状况，建议您采取以下行动：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(25)
            )
            content.add_widget(intro_label)
            
            # 添加行动按钮
            actions = [
                {"text": "预约专家咨询", "icon": "doctor", "color": "#4CAF50"},
                {"text": "查看健康计划", "icon": "calendar-check", "color": "#2196F3"}
            ]
            
            for action in actions:
                action_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(45),
                    padding=[0, dp(3)]
                )
                
                # 修复按钮实现，确保文字正确显示
                action_button = MDButton(
                    style="elevated",
                    size_hint=(1, None),
                    height=dp(40),
                    md_bg_color=get_color_from_hex(action["color"]),
                    on_release=lambda x, a=action["text"]: self.on_action_button(a)
                )
                
                # 直接添加按钮文字，不使用复杂的嵌套布局
                button_text = MDButtonText(
                    text=action["text"],
                    theme_text_color="Custom",
                    text_color=[1, 1, 1, 1],  # 白色文字
                    font_size=dp(15)
                )
                
                action_button.add_widget(button_text)
                action_layout.add_widget(action_button)
                content.add_widget(action_layout)
            
            # 添加提示文本
            tip_label = MDLabel(
                text="提示：定期复查可以帮助您更好地跟踪健康状况变化",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(12),
                italic=True,
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(tip_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加下一步行动卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def on_action_button(self, action_text):
        """处理行动按钮点击事件"""
        try:
            logger.info(f"点击了行动按钮: {action_text}")
            # TODO: 实现相应的行动逻辑
            
            # 显示提示
            from kivymd.uix.snackbar import MDSnackbar
            from kivymd.uix.snackbar import MDSnackbarText
            
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=f"您选择了: {action_text}，此功能正在开发中"
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"处理行动按钮点击时出错: {e}")
            logger.error(traceback.format_exc())
    
    def create_section_card(self, title, bg_color="#FFFFFF"):
        """创建带标题的自适应高度卡片区域 - KivyMD 2.0.1兼容"""
        card = MDCard(
            orientation="vertical",
            size_hint=(1, None),
            height=dp(80),  # 初始最小高度
            padding=dp(12),
            spacing=dp(8),
            elevation=2,
            radius=[dp(12), dp(12), dp(12), dp(12)],
            md_bg_color=get_color_from_hex(bg_color),
            size_hint_y=None
        )
        
        # 标题容器 - 使用固定高度确保一致性
        title_container = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(40),
            padding=[0, dp(4)]
        )
        
        # 标题
        title_label = MDLabel(
            text=title,
            theme_text_color="Custom",
            text_color=self.get_app().theme.PRIMARY_COLOR,
            font_size=dp(18),
            bold=True,
            size_hint_y=None,
            height=dp(32),
            halign="left",
            valign="center"
        )
        
        title_container.add_widget(title_label)
        card.add_widget(title_container)
        
        # 绑定自适应高度更新
        def update_card_height(*args):
            """根据内容自动调整卡片高度"""
            Clock.schedule_once(lambda dt: _update_height(), 0.1)
        
        def _update_height():
            """延迟更新高度，确保所有子组件都已渲染"""
            try:
                total_height = dp(40)  # 标题容器高度
                
                # 计算所有内容子组件的高度
                for child in card.children:
                    if child != title_container and hasattr(child, 'height'):
                        total_height += child.height
                        # 添加间距
                        if hasattr(child, 'spacing') and hasattr(child, 'children') and len(child.children) > 1:
                            total_height += child.spacing * (len(child.children) - 1)
                
                # 添加卡片的padding和spacing
                total_height += card.padding * 2  # 上下padding
                total_height += card.spacing * max(0, len(card.children) - 1)  # 子组件间距
                
                # 设置卡片最终高度，确保不小于最小高度
                final_height = max(dp(80), total_height + dp(16))  # 额外的缓冲空间
                card.height = final_height
                
            except Exception as e:
                logger.error(f"更新卡片高度时出错: {e}")
                card.height = dp(120)  # 设置默认高度
        
        # 绑定高度更新事件
        card.bind(children=update_card_height)
        
        return card
    
    def add_text_content(self, card, label, content):
        """添加文本内容"""
        # 这个方法已不再使用，保留以兼容旧代码
        pass
    
    def add_qa_item(self, card, index, qa_item):
        """添加单个问答项"""
        # 这个方法已不再使用，保留以兼容旧代码
        pass
    
    def show_empty_state(self):
        """显示空状态"""
        # 使用show_error方法代替
        self.show_error("未找到报告数据")
    
    def show_error(self, error_message="未找到报告数据"):
        """显示错误信息"""
        try:
            if not hasattr(self, 'content_container'):
                logger.error("content_container不存在，UI可能未正确初始化")
                return
                
            self.content_container.clear_widgets()
            
            # 创建错误信息容器
            error_container = MDBoxLayout(
                orientation="vertical",
                padding=dp(20),
                spacing=dp(10),
                size_hint_y=None,
                height=dp(200)
            )
            
            # 添加错误图标
            error_icon = MDIconButton(
                icon="alert-circle-outline",
                theme_text_color="Custom",
                text_color=self.get_app().theme.ERROR_COLOR,
                icon_size=dp(64),
                pos_hint={"center_x": 0.5}
            )
            
            # 添加错误文本
            error_label = MDLabel(
                text=f"出错了\n{error_message}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                halign="center",
                font_size=dp(18)
            )
            
            error_container.add_widget(error_icon)
            error_container.add_widget(error_label)
            
            # 添加到主容器
            self.content_container.add_widget(error_container)
        except Exception as e:
            logger.error(f"显示错误信息时出错: {e}")
            logger.error(traceback.format_exc())
    
    def go_back(self):
        """返回上一页"""
        if self.manager:
            self.manager.current = 'survey_screen'
        else:
            self.app.root.current = 'survey_screen'

    def add_qa_card(self, container):
        """添加问答详情卡片"""
        # 这个方法已不再使用，保留以兼容旧代码
        pass