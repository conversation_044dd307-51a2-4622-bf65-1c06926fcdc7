# -*- coding: utf-8 -*-
"""
维度计算服务

此模块提供了用于计算和分析量表和问卷维度分数的功能。
支持基于维度定义自动计算各维度得分，并生成维度分析报告。
"""

from typing import Dict, List, Any, Optional, Tuple
import json
from sqlalchemy.orm import Session

from app.models.assessment import AssessmentTemplate, AssessmentTemplateQuestion, AssessmentResponse
from app.models.questionnaire import QuestionnaireTemplate, QuestionnaireTemplateQuestion, QuestionnaireResponse


class DimensionService:
    """维度计算服务类
    
    用于计算量表和问卷的维度分数，并提供维度分析功能。
    """
    
    def __init__(self, db: Session):
        """初始化维度服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def calculate_assessment_dimensions(self, 
                                      assessment_response: AssessmentResponse,
                                      template: AssessmentTemplate) -> Dict[str, Any]:
        """计算评估量表的维度分数
        
        Args:
            assessment_response: 评估回答对象
            template: 评估模板对象
            
        Returns:
            维度分数字典，格式：{
                "dimension_key": {
                    "name": "维度名称",
                    "score": 分数,
                    "max_score": 满分,
                    "percentage": 百分比,
                    "level": "水平等级"
                }
            }
        """
        if not template.dimensions:
            return {}
        
        # 获取用户答案
        answers = assessment_response.answers if assessment_response.answers else {}
        
        # 获取模板问题
        questions = self.db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == template.id
        ).all()
        
        # 创建问题ID到问题对象的映射
        question_map = {q.question_id: q for q in questions}
        
        dimension_scores = {}
        
        for dimension in template.dimensions:
            dimension_key = dimension.get("key")
            dimension_name = dimension.get("name")
            dimension_max_score = dimension.get("max_score", 0)
            question_ids = dimension.get("question_ids", [])
            
            # 计算该维度的总分
            dimension_score = 0
            valid_questions = 0
            
            for question_id in question_ids:
                if question_id in answers and question_id in question_map:
                    question = question_map[question_id]
                    answer_value = answers[question_id]
                    
                    # 根据问题类型和选项计算分数
                    question_score = self._calculate_question_score(
                        question, answer_value
                    )
                    
                    if question_score is not None:
                        dimension_score += question_score
                        valid_questions += 1
            
            # 计算百分比
            percentage = (dimension_score / dimension_max_score * 100) if dimension_max_score > 0 else 0
            
            # 确定水平等级
            level = self._determine_dimension_level(percentage)
            
            dimension_scores[dimension_key] = {
                "name": dimension_name,
                "score": dimension_score,
                "max_score": dimension_max_score,
                "percentage": round(percentage, 2),
                "level": level,
                "valid_questions": valid_questions
            }
        
        return dimension_scores
    
    def calculate_questionnaire_dimensions(self, 
                                         questionnaire_response: QuestionnaireResponse,
                                         template: QuestionnaireTemplate) -> Dict[str, Any]:
        """计算问卷的维度分数
        
        Args:
            questionnaire_response: 问卷回答对象
            template: 问卷模板对象
            
        Returns:
            维度分数字典
        """
        if not template.dimensions:
            return {}
        
        # 获取用户答案
        answers = questionnaire_response.answers if questionnaire_response.answers else {}
        
        # 获取模板问题
        questions = self.db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == template.id
        ).all()
        
        # 创建问题ID到问题对象的映射
        question_map = {q.question_id: q for q in questions}
        
        dimension_scores = {}
        
        for dimension in template.dimensions:
            dimension_key = dimension.get("key")
            dimension_name = dimension.get("name")
            dimension_max_score = dimension.get("max_score", 0)
            question_ids = dimension.get("question_ids", [])
            
            # 计算该维度的总分
            dimension_score = 0
            valid_questions = 0
            
            for question_id in question_ids:
                if question_id in answers and question_id in question_map:
                    question = question_map[question_id]
                    answer_value = answers[question_id]
                    
                    # 根据问题类型和选项计算分数
                    question_score = self._calculate_question_score(
                        question, answer_value
                    )
                    
                    if question_score is not None:
                        dimension_score += question_score
                        valid_questions += 1
            
            # 计算百分比
            percentage = (dimension_score / dimension_max_score * 100) if dimension_max_score > 0 else 0
            
            # 确定水平等级
            level = self._determine_dimension_level(percentage)
            
            dimension_scores[dimension_key] = {
                "name": dimension_name,
                "score": dimension_score,
                "max_score": dimension_max_score,
                "percentage": round(percentage, 2),
                "level": level,
                "valid_questions": valid_questions
            }
        
        return dimension_scores
    
    def _calculate_question_score(self, question, answer_value) -> Optional[float]:
        """计算单个问题的分数
        
        Args:
            question: 问题对象
            answer_value: 用户答案
            
        Returns:
            问题分数，如果无法计算则返回None
        """
        try:
            # 如果问题有选项，从选项中获取分数
            if hasattr(question, 'options') and question.options:
                options = question.options
                
                # 处理不同的选项格式
                if isinstance(options, list):
                    for option in options:
                        if isinstance(option, dict):
                            # 检查选项值匹配
                            if (option.get("value") == answer_value or 
                                option.get("label") == answer_value):
                                return float(option.get("score", 0))
                elif isinstance(options, dict):
                    # 直接从字典中获取分数
                    if str(answer_value) in options:
                        return float(options[str(answer_value)])
            
            # 如果没有选项，尝试直接转换答案为分数
            if isinstance(answer_value, (int, float)):
                return float(answer_value)
            
            # 尝试解析字符串数字
            if isinstance(answer_value, str) and answer_value.isdigit():
                return float(answer_value)
            
            return 0.0
            
        except (ValueError, TypeError, KeyError):
            return None
    
    def _determine_dimension_level(self, percentage: float) -> str:
        """根据百分比确定维度水平等级
        
        Args:
            percentage: 百分比分数
            
        Returns:
            水平等级字符串
        """
        if percentage >= 80:
            return "优秀"
        elif percentage >= 60:
            return "良好"
        elif percentage >= 40:
            return "一般"
        elif percentage >= 20:
            return "较差"
        else:
            return "很差"
    
    def generate_dimension_report(self, dimension_scores: Dict[str, Any]) -> Dict[str, Any]:
        """生成维度分析报告
        
        Args:
            dimension_scores: 维度分数字典
            
        Returns:
            维度分析报告
        """
        if not dimension_scores:
            return {
                "summary": "暂无维度数据",
                "recommendations": [],
                "strengths": [],
                "weaknesses": []
            }
        
        # 计算总体统计
        total_dimensions = len(dimension_scores)
        avg_percentage = sum(d["percentage"] for d in dimension_scores.values()) / total_dimensions
        
        # 找出优势和劣势维度
        strengths = []
        weaknesses = []
        
        for key, data in dimension_scores.items():
            if data["percentage"] >= 70:
                strengths.append({
                    "dimension": data["name"],
                    "percentage": data["percentage"],
                    "level": data["level"]
                })
            elif data["percentage"] < 40:
                weaknesses.append({
                    "dimension": data["name"],
                    "percentage": data["percentage"],
                    "level": data["level"]
                })
        
        # 生成建议
        recommendations = self._generate_recommendations(dimension_scores)
        
        # 生成总结
        summary = self._generate_summary(avg_percentage, total_dimensions, strengths, weaknesses)
        
        return {
            "summary": summary,
            "average_percentage": round(avg_percentage, 2),
            "total_dimensions": total_dimensions,
            "strengths": strengths,
            "weaknesses": weaknesses,
            "recommendations": recommendations
        }
    
    def _generate_recommendations(self, dimension_scores: Dict[str, Any]) -> List[str]:
        """生成改进建议
        
        Args:
            dimension_scores: 维度分数字典
            
        Returns:
            建议列表
        """
        recommendations = []
        
        for key, data in dimension_scores.items():
            percentage = data["percentage"]
            dimension_name = data["name"]
            
            if percentage < 40:
                recommendations.append(f"建议重点关注{dimension_name}方面的改善")
            elif percentage < 60:
                recommendations.append(f"建议适当加强{dimension_name}方面的训练")
        
        if not recommendations:
            recommendations.append("各维度表现良好，建议保持现有状态")
        
        return recommendations
    
    def _generate_summary(self, avg_percentage: float, total_dimensions: int, 
                         strengths: List[Dict], weaknesses: List[Dict]) -> str:
        """生成总结报告
        
        Args:
            avg_percentage: 平均百分比
            total_dimensions: 总维度数
            strengths: 优势维度列表
            weaknesses: 劣势维度列表
            
        Returns:
            总结文本
        """
        summary_parts = []
        
        # 总体评价
        if avg_percentage >= 70:
            summary_parts.append("总体表现优秀")
        elif avg_percentage >= 50:
            summary_parts.append("总体表现良好")
        else:
            summary_parts.append("总体表现有待提升")
        
        # 优势描述
        if strengths:
            strength_names = [s["dimension"] for s in strengths]
            summary_parts.append(f"在{', '.join(strength_names)}方面表现突出")
        
        # 劣势描述
        if weaknesses:
            weakness_names = [w["dimension"] for w in weaknesses]
            summary_parts.append(f"在{', '.join(weakness_names)}方面需要改进")
        
        return "，".join(summary_parts) + "。"