"""移动端文件上传聚合API"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks
from sqlalchemy.orm import Session
import os
import json
from datetime import datetime

from app.api.deps import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.document import Document
from app.models.health_record import HealthRecord, RecordType
from app.models.enums import DocumentType, DocumentCategory
from app.core.config import settings
from app.utils.performance_monitor import monitor_performance
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/upload", response_model=dict)
@monitor_performance("mobile_upload_document")
async def mobile_upload_document(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    document_type: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    category: Optional[str] = Form(None),  # 预期的健康资料类别
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """移动端文件上传聚合API
    
    文件上传流程：
    1. 文件首先保存到documents表（文档管理）
    2. 返回文档ID，可通过前端"文档管理"查阅
    3. 后台通过OCR等服务进行数字化处理
    4. 根据类别分别保存到健康资料子数据表
    """
    try:
        logger.info(f"[DEBUG] Starting upload for file: {file.filename}")
        # 验证文件
        if not file.filename:
            raise HTTPException(
                status_code=400,
                detail="文件名不能为空"
            )

        # 验证文件大小（限制为10MB）
        contents = await file.read()
        file_size = len(contents)
        if file_size > 10 * 1024 * 1024:  # 10MB
            raise HTTPException(
                status_code=400,
                detail="文件大小不能超过10MB"
            )

        # 验证文件类型
        allowed_types = {
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
            'application/pdf', 'text/plain', 'text/csv',
            'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }

        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file.content_type}"
            )

        # 创建用户专属上传目录
        upload_dir = os.path.join(
            settings.UPLOAD_DIR, 
            current_user.custom_id, 
            "mobile",
            datetime.now().strftime('%Y%m%d')
        )
        print(f"[DEBUG] Upload directory: {upload_dir}")
        try:
            os.makedirs(upload_dir, exist_ok=True)
            print(f"[DEBUG] Directory created successfully")
        except Exception as dir_e:
            print(f"[DEBUG] Directory creation failed: {str(dir_e)}")
            raise

        # 生成安全的文件名
        import uuid
        file_extension = os.path.splitext(file.filename)[1]
        safe_filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = os.path.join(upload_dir, safe_filename)

        # 保存文件
        print(f"[DEBUG] Saving file to: {file_path}")
        try:
            with open(file_path, "wb") as f:
                f.write(contents)
            print(f"[DEBUG] File saved successfully")
        except Exception as save_e:
            print(f"[DEBUG] File save failed: {str(save_e)}")
            raise

        # 根据文件类型确定DocumentType
        def get_document_type(content_type: str, filename: str) -> DocumentType:
            """根据MIME类型和文件名确定文档类型"""
            if content_type.startswith('image/'):
                return DocumentType.IMAGE
            elif content_type == 'application/pdf':
                return DocumentType.PDF
            elif content_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                return DocumentType.WORD
            elif content_type in ['text/plain', 'text/csv']:
                return DocumentType.TEXT
            else:
                return DocumentType.OTHER
        
        # 根据category确定DocumentCategory
        def get_document_category(category: str) -> DocumentCategory:
            """根据预期分类确定文档分类"""
            if not category:
                return DocumentCategory.OTHER
            
            category_mapping = {
                'medical': DocumentCategory.MEDICAL_RECORD,
                'lab': DocumentCategory.LAB_REPORT,
                'imaging': DocumentCategory.IMAGING_REPORT,
                'prescription': DocumentCategory.PRESCRIPTION,
                'discharge': DocumentCategory.DISCHARGE_SUMMARY,
                'referral': DocumentCategory.REFERRAL,
                'consent': DocumentCategory.CONSENT_FORM,
                'insurance': DocumentCategory.INSURANCE,
                'vaccination': DocumentCategory.VACCINATION,
                'allergy': DocumentCategory.ALLERGY,
                'health_certificate': DocumentCategory.HEALTH_CERTIFICATE
            }
            
            return category_mapping.get(category.lower(), DocumentCategory.OTHER)
        
        # 创建文档记录
        document = Document(
            custom_id=current_user.custom_id,
            title=title or file.filename,
            document_type=document_type or "mobile_upload",
            file_type=get_document_type(file.content_type, file.filename),
            document_category=get_document_category(category),
            file_path=os.path.relpath(file_path, settings.BASE_DIR),
            description=description,
            status="active",  # 立即设置为可用状态，确保文档列表能显示
            filename=file.filename,
            file_size=file_size,
            mime_type=file.content_type,
            source="mobile",  # 标记来源为移动端
            file_metadata=json.dumps({
                "original_filename": file.filename,
                "content_type": file.content_type,
                "size": file_size,
                "upload_source": "mobile",
                "expected_category": category,  # 预期分类
                "safe_filename": safe_filename,
                "upload_timestamp": datetime.now().isoformat()
            })
        )

        db.add(document)
        db.commit()
        db.refresh(document)
        
        # 同时创建健康记录，确保移动端查询能找到文档
        # 使用独立的数据库会话，避免影响文档创建
        try:
            # 创建新的数据库会话用于健康记录
            from app.db.base_session import SessionLocal
            hr_db = SessionLocal()
            
            health_record = HealthRecord(
                custom_id=current_user.custom_id,
                record_type=RecordType.DOCUMENT,  # 标记为文档类型
                title=title or file.filename,
                description=description or f"移动端上传的{category or '文档'}",
                content=json.dumps({
                    "document_id": document.id,
                    "filename": document.filename,
                    "file_type": document.file_type.value if document.file_type else "unknown",
                    "document_category": document.document_category.value if document.document_category else "other",
                    "source": "mobile_upload",
                    "expected_category": category
                }),
                metadata=json.dumps({
                    "document_id": document.id,
                    "original_filename": file.filename,
                    "file_size": file_size,
                    "mime_type": file.content_type,
                    "upload_source": "mobile",
                    "processing_status": "pending"
                })
            )
            
            hr_db.add(health_record)
            hr_db.commit()
            hr_db.refresh(health_record)
            hr_db.close()
            
            logger.info(f"[DEBUG] Created health record {health_record.id} for document {document.id}")
            
        except Exception as hr_e:
            # 健康记录创建失败，关闭会话但不影响文档
            try:
                hr_db.rollback()
                hr_db.close()
            except:
                pass
            logger.error(f"[DEBUG] Failed to create health record: {str(hr_e)}")
            import traceback
            logger.error(f"[DEBUG] Health record creation error traceback: {traceback.format_exc()}")
            # 健康记录创建失败不影响文档上传成功
            pass

        # TODO: 添加后台任务进行OCR处理
        # background_tasks.add_task(process_document_ocr, document.id)

        return {
            "status": "success",
            "message": "文件上传成功，正在进行数字化处理",
            "data": {
                "document_id": document.id,
                "title": document.title,
                "filename": document.filename,
                "file_size": document.file_size,
                "status": document.status,
                "upload_time": document.created_at.isoformat() if document.created_at else None,
                "processing_info": {
                    "current_stage": "uploaded",
                    "next_stage": "ocr_processing",
                    "estimated_time": "1-5分钟"
                }
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        # 只有在文档创建阶段失败时才回滚
        # 如果是HealthRecord创建失败，文档已经成功创建，不应该回滚
        try:
            # 检查document是否已经创建
            if 'document' not in locals() or not document.id:
                db.rollback()
        except:
            db.rollback()
        logger.error(f"[DEBUG] Upload failed: {str(e)}")
        import traceback
        logger.error(f"[DEBUG] Upload error traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )

@router.get("/status/{document_id}", response_model=dict)
@monitor_performance("mobile_upload_status")
def get_upload_status(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取文件处理状态"""
    try:
        document = db.query(Document).filter(
            Document.id == document_id,
            Document.custom_id == current_user.custom_id,
            Document.source == "mobile"
        ).first()
        
        if not document:
            raise HTTPException(
                status_code=404,
                detail="文档不存在或无权访问"
            )
        
        # 解析metadata
        metadata = json.loads(document.file_metadata) if document.file_metadata else {}
        
        # 根据状态返回不同信息
        status_info = {
            "pending_processing": {
                "stage": "等待处理",
                "description": "文件已上传，等待OCR处理"
            },
            "processing": {
                "stage": "处理中",
                "description": "正在进行OCR识别和内容分析"
            },
            "processed": {
                "stage": "处理完成",
                "description": "已完成数字化处理，数据已保存到健康资料"
            },
            "failed": {
                "stage": "处理失败",
                "description": "处理过程中出现错误，请重新上传"
            },
            "active": {
                "stage": "可用",
                "description": "文档可在文档管理中查看"
            }
        }
        
        current_status = status_info.get(document.status, {
            "stage": "未知状态",
            "description": "状态信息不可用"
        })
        
        return {
            "status": "success",
            "data": {
                "document_id": document.id,
                "title": document.title,
                "filename": document.filename,
                "upload_time": document.created_at.isoformat() if document.created_at else None,
                "current_status": document.status,
                "status_info": current_status,
                "ocr_processed": document.ocr_processed,
                "ocr_confidence": document.ocr_confidence,
                "metadata": metadata
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取上传状态失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取状态失败: {str(e)}"
        )

# TODO: 后台OCR处理函数
# async def process_document_ocr(document_id: int):
#     """后台OCR处理任务"""
#     pass