"""
基础屏幕模块

这个模块提供了一个基础屏幕类，用于标准化屏幕初始化过程，
确保所有屏幕都遵循相同的初始化流程，并避免重复代码。
"""
from kivy.uix.screenmanager import Screen
from kivy.clock import Clock
from kivy.properties import BooleanProperty
from kivy.lang import Builder

from widgets.logo_manager import get_logo_manager

class BaseScreen(Screen):
    """基础屏幕类

    这个类提供了标准化的屏幕初始化流程，包括：
    1. 延迟初始化UI，确保KV规则已应用
    2. 自动清理重复的Logo
    3. 提供通用的生命周期方法
    """
    is_initialized = BooleanProperty(False)

    def __init__(self, **kwargs):
        """初始化基础屏幕"""
        super(BaseScreen, self).__init__(**kwargs)
        self._init_scheduled = False

        # 在KV规则应用后延迟初始化UI
        Clock.schedule_once(self._delayed_init, 0.1)

    def _delayed_init(self, dt):
        """延迟初始化，确保KV规则已应用"""
        if not self._init_scheduled:
            self._init_scheduled = True
            Clock.schedule_once(self.init_ui, 0.2)

    def init_ui(self, dt=0):
        """初始化UI

        子类应该覆盖这个方法来实现自己的UI初始化逻辑。
        """
        self.is_initialized = True
        return True

    def on_enter(self):
        """进入屏幕时调用

        如果屏幕尚未初始化，会触发初始化过程。
        子类应该调用super().on_enter()以确保正确的初始化流程。
        """
        # 如果尚未初始化，触发初始化过程
        if not self.is_initialized:
            Clock.schedule_once(self.init_ui, 0)

    def on_leave(self):
        """离开屏幕时调用

        子类可以覆盖这个方法来实现自己的清理逻辑。
        """
        pass

    def on_back(self):
        """处理返回按钮点击事件

        默认行为是返回到上一个屏幕。
        子类可以覆盖这个方法来实现自定义的返回逻辑。
        """
        app = self.get_app()
        if app and app.root:
            app.root.current = 'homepage_screen'

    def get_app(self):
        """获取应用实例

        Returns:
            MDApp: 应用实例
        """
        from kivymd.app import MDApp
        return MDApp.get_running_app()

    @staticmethod
    def load_kv_string(kv_string):
        """加载KV语言字符串

        这个方法提供了一个安全的方式来加载KV语言字符串，
        避免重复加载导致的错误。

        Args:
            kv_string: KV语言字符串

        Returns:
            bool: 如果成功加载，返回True；否则返回False
        """
        try:
            Builder.load_string(kv_string)
            return True
        except Exception as e:
            print(f"加载KV字符串失败: {e}")
            return False
