"""基本健康信息屏幕模块

提供用户基本健康信息的录入、编辑和管理功能。
"""

from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, DictProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
from datetime import datetime

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog
from widgets.logo import HealthLogo

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles

# 导入工具类
from utils.health_data_manager import get_health_data_manager
from utils.cloud_api import get_cloud_api

# 设置日志
logger = logging.getLogger(__name__)

# 定义KV语言字符串
KV = '''
<InfoCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(120)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    spacing: dp(8)
    
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)
        
        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Primary"
            size_hint_x: 0.7
            
        MDLabel:
            text: root.value
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.PRIMARY_COLOR
            size_hint_x: 0.3
            halign: "right"
    
    MDDivider:
        height: dp(1)
    
    MDLabel:
        text: root.description
        font_style: "Label"
        theme_text_color: "Secondary"
        text_size: self.width, None
        size_hint_y: None
        height: self.texture_size[1]
        
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(4)
        
        Widget:
            size_hint_x: 1
            
        MDIconButton:
            icon: "pencil"
            icon_size: dp(20)
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR
            on_release: root.on_edit()

<BasicHealthInfoScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(8)
        
        # 顶部栏
        MDBoxLayout:
            id: top_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(8), dp(0), dp(8), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {"center_y": 0.5}
                on_release: root.go_back()
                
            MDLabel:
                text: "基本健康信息"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                size_hint_x: 0.7
                pos_hint: {"center_y": 0.5}
                halign: "center"
                
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {"center_y": 0.5}
                on_release: root.add_info()
        
        # Logo
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(80)
        
        # 主内容区
        MDScrollView:
            id: scroll_view
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(12)
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class InfoCard(MDCard):
    """信息卡片组件"""
    title = StringProperty("")
    value = StringProperty("")
    description = StringProperty("")
    info_data = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
    def on_edit(self):
        """编辑信息"""
        screen = self.get_root_window().children[0].current_screen
        if hasattr(screen, 'edit_info'):
            screen.edit_info(self.info_data)

class BasicHealthInfoScreen(BaseScreen):
    """基本健康信息屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.health_info = []
        Clock.schedule_once(self.init_ui, 0.2)
    
    def init_ui(self, dt=0):
        """初始化UI"""
        self.load_data()
        self.refresh_ui()
    
    def on_enter(self):
        """进入屏幕时调用"""
        super().on_enter()
        self.init_ui()
    
    def go_back(self):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'homepage_screen'
    
    def add_info(self):
        """添加信息"""
        self.show_info_dialog()
    
    def edit_info(self, info_data):
        """编辑信息"""
        self.show_info_dialog(info_data)
    
    def show_info_dialog(self, info_data=None):
        """显示信息对话框"""
        # 创建对话框内容
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(16),
            size_hint_y=None,
            height=dp(300),
            padding=[dp(0), dp(8), dp(0), dp(8)]
        )
        
        # 标题输入
        title_field = MDTextField(
            hint_text="信息标题",
            text=info_data.get('title', '') if info_data else '',
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(title_field)
        
        # 值输入
        value_field = MDTextField(
            hint_text="信息值",
            text=info_data.get('value', '') if info_data else '',
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(value_field)
        
        # 描述输入
        desc_field = MDTextField(
            hint_text="描述信息",
            text=info_data.get('description', '') if info_data else '',
            mode="outlined",
            multiline=True,
            size_hint_y=None,
            height=dp(120)
        )
        content.add_widget(desc_field)
        
        # 创建对话框
        dialog = MDDialog(
            title="编辑信息" if info_data else "添加信息",
            content=content,
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="保存"),
                    style="filled",
                    on_release=lambda x: self.save_info(
                        dialog, info_data, title_field.text, 
                        value_field.text, desc_field.text
                    )
                ),
            ],
        )
        dialog.open()
    
    def save_info(self, dialog, info_data, title, value, description):
        """保存信息"""
        if not title.strip():
            self.show_message("请输入信息标题")
            return
        
        new_info = {
            "id": info_data.get('id') if info_data else str(len(self.health_info) + 1),
            "title": title.strip(),
            "value": value.strip(),
            "description": description.strip(),
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if info_data:
            # 编辑现有信息
            for i, info in enumerate(self.health_info):
                if info.get('id') == info_data.get('id'):
                    self.health_info[i] = new_info
                    break
        else:
            # 添加新信息
            self.health_info.append(new_info)
        
        # 刷新UI
        self.refresh_ui()
        
        # 关闭对话框
        dialog.dismiss()
        
        # 显示成功消息
        self.show_message("保存成功")
    
    def load_data(self):
        """加载数据"""
        try:
            # 在实际应用中，这里应该从API或本地存储加载数据
            # 这里使用模拟数据进行演示
            self.health_info = [
                {
                    "id": "1",
                    "title": "身高",
                    "value": "175cm",
                    "description": "成年身高，基本稳定",
                    "date": "2023-06-15 10:30:00"
                },
                {
                    "id": "2",
                    "title": "体重",
                    "value": "68kg",
                    "description": "标准体重范围内，需要保持",
                    "date": "2023-06-15 10:30:00"
                },
                {
                    "id": "3",
                    "title": "血型",
                    "value": "A型",
                    "description": "RH阳性，常见血型",
                    "date": "2023-06-15 10:30:00"
                },
                {
                    "id": "4",
                    "title": "过敏史",
                    "value": "花粉过敏",
                    "description": "春季花粉过敏，需要注意防护",
                    "date": "2023-06-15 10:30:00"
                },
                {
                    "id": "5",
                    "title": "家族病史",
                    "value": "高血压",
                    "description": "父亲有高血压病史，需要定期监测",
                    "date": "2023-06-15 10:30:00"
                }
            ]
        except Exception as e:
            logger.error(f"加载健康信息失败: {str(e)}")
            self.health_info = []
    
    def refresh_ui(self):
        """刷新UI"""
        try:
            # 清空主布局
            self.ids.main_layout.clear_widgets()
            
            if not self.health_info:
                # 显示空状态
                empty_card = MDCard(
                    MDBoxLayout(
                        MDLabel(
                            text="暂无健康信息记录\n点击右上角+号添加信息",
                            halign="center",
                            theme_text_color="Secondary",
                            font_style="Body",
                            role="medium"
                        ),
                        orientation='vertical',
                        padding=dp(32)
                    ),
                    size_hint_y=None,
                    height=dp(120),
                    md_bg_color=self.app.theme.SURFACE_COLOR,
                    radius=[dp(12)],
                    elevation=1
                )
                self.ids.main_layout.add_widget(empty_card)
                return
            
            # 添加信息卡片
            for info in self.health_info:
                info_card = InfoCard(
                    title=info["title"],
                    value=info["value"],
                    description=info["description"],
                    info_data=info
                )
                self.ids.main_layout.add_widget(info_card)
                
        except Exception as e:
            logger.error(f"刷新UI失败: {str(e)}")
    
    def show_message(self, message):
        """显示消息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()

# 用于测试的应用程序
if __name__ == '__main__':
    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles
        
        def build(self):
            self.user_data = {"username": "测试用户", "user_id": "test123", "gender": "男", "age": 45}
            return BasicHealthInfoScreen()