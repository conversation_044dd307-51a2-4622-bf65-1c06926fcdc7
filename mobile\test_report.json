{"timestamp": "2025-07-02T22:53:14.722248", "duration": 5.344657, "results": {"TestOptimizedTheme": {"tests_run": 3, "failures": 0, "errors": 3, "success_rate": 0.0}, "TestAPIConfig": {"tests_run": 4, "failures": 0, "errors": 2, "success_rate": 50.0}, "TestAPIClient": {"tests_run": 2, "failures": 0, "errors": 1, "success_rate": 50.0}, "TestPerformanceMonitor": {"tests_run": 2, "failures": 0, "errors": 2, "success_rate": 0.0}, "TestIntegration": {"tests_run": 3, "failures": 0, "errors": 1, "success_rate": 66.66666666666666}}, "details": [{"type": "error", "test": "test_color_palette_creation (test_optimized_components.TestOptimizedTheme.test_color_palette_creation)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 39, in setUp\n    from theme_optimized import OptimizedColorPalette, OptimizedTheme\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 135, in <module>\n    class OptimizedTypography:\n    ...<32 lines>...\n        WEIGHT_BOLD = '700'\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 139, in OptimizedTypography\n    DISPLAY_LARGE = sp(57)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 168, in sp\n    return dpi2px(value, 'sp')\n  File \"kivy\\\\_metrics.pyx\", line 55, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 59, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 35, in kivy._metrics.dispatch_pixel_scale\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\context.py\", line 36, in __getattribute__\n    return getattr(object.__getattribute__(self, '_obj'), name)\n  File \"kivy\\\\properties.pyx\", line 531, in kivy.properties.Property.__get__\n  File \"kivy\\\\properties.pyx\", line 1656, in kivy.properties.AliasProperty.get\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 206, in get_dpi\n    EventLoop.ensure_window()\n    ~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\base.py\", line 139, in ensure_window\n    sys.exit(1)\n    ~~~~~~~~^^^\nSystemExit: 1\n", "suite": "TestOptimizedTheme"}, {"type": "error", "test": "test_module_colors (test_optimized_components.TestOptimizedTheme.test_module_colors)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 39, in setUp\n    from theme_optimized import OptimizedColorPalette, OptimizedTheme\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 135, in <module>\n    class OptimizedTypography:\n    ...<32 lines>...\n        WEIGHT_BOLD = '700'\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 139, in OptimizedTypography\n    DISPLAY_LARGE = sp(57)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 168, in sp\n    return dpi2px(value, 'sp')\n  File \"kivy\\\\_metrics.pyx\", line 55, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 59, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 35, in kivy._metrics.dispatch_pixel_scale\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\context.py\", line 36, in __getattribute__\n    return getattr(object.__getattribute__(self, '_obj'), name)\n  File \"kivy\\\\properties.pyx\", line 531, in kivy.properties.Property.__get__\n  File \"kivy\\\\properties.pyx\", line 1656, in kivy.properties.AliasProperty.get\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 206, in get_dpi\n    EventLoop.ensure_window()\n    ~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\base.py\", line 139, in ensure_window\n    sys.exit(1)\n    ~~~~~~~~^^^\nSystemExit: 1\n", "suite": "TestOptimizedTheme"}, {"type": "error", "test": "test_theme_creation (test_optimized_components.TestOptimizedTheme.test_theme_creation)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 39, in setUp\n    from theme_optimized import OptimizedColorPalette, OptimizedTheme\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 135, in <module>\n    class OptimizedTypography:\n    ...<32 lines>...\n        WEIGHT_BOLD = '700'\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 139, in OptimizedTypography\n    DISPLAY_LARGE = sp(57)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 168, in sp\n    return dpi2px(value, 'sp')\n  File \"kivy\\\\_metrics.pyx\", line 55, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 59, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 35, in kivy._metrics.dispatch_pixel_scale\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\context.py\", line 36, in __getattribute__\n    return getattr(object.__getattribute__(self, '_obj'), name)\n  File \"kivy\\\\properties.pyx\", line 531, in kivy.properties.Property.__get__\n  File \"kivy\\\\properties.pyx\", line 1656, in kivy.properties.AliasProperty.get\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 206, in get_dpi\n    EventLoop.ensure_window()\n    ~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\base.py\", line 139, in ensure_window\n    sys.exit(1)\n    ~~~~~~~~^^^\nSystemExit: 1\n", "suite": "TestOptimizedTheme"}, {"type": "error", "test": "test_optimized_api_config (test_optimized_components.TestAPIConfig.test_optimized_api_config)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 135, in test_optimized_api_config\n    loaded_config = api_config.load_config(\"test_config.json\")\n                    ^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'OptimizedAPIConfig' object has no attribute 'load_config'\n", "suite": "TestAPIConfig"}, {"type": "error", "test": "test_url_construction (test_optimized_components.TestAPIConfig.test_url_construction)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 153, in test_url_construction\n    full_url = api_config.get_full_url(config, self.APIEndpoint.USER_LOGIN)\nTypeError: OptimizedAPIConfig.get_full_url() takes 2 positional arguments but 3 were given\n", "suite": "TestAPIConfig"}, {"type": "error", "test": "test_request_headers (test_optimized_components.TestAPIClient.test_request_headers)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py\", line 1424, in patched\n    return func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 181, in test_request_headers\n    headers = client.get_request_headers()\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'OptimizedAPIClient' object has no attribute 'get_request_headers'\n", "suite": "TestAPIClient"}, {"type": "error", "test": "test_metric_recording (test_optimized_components.TestPerformanceMonitor.test_metric_recording)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py\", line 1424, in patched\n    return func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 212, in test_metric_recording\n    monitor = self.PerformanceMonitor()\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\utils\\performance_monitor.py\", line 76, in __init__\n    self._start_background_monitoring()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'PerformanceMonitor' object has no attribute '_start_background_monitoring'\n", "suite": "TestPerformanceMonitor"}, {"type": "error", "test": "test_performance_monitor_creation (test_optimized_components.TestPerformanceMonitor.test_performance_monitor_creation)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py\", line 1424, in patched\n    return func(*newargs, **newkeywargs)\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 203, in test_performance_monitor_creation\n    monitor = self.PerformanceMonitor()\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\utils\\performance_monitor.py\", line 76, in __init__\n    self._start_background_monitoring()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'PerformanceMonitor' object has no attribute '_start_background_monitoring'\n", "suite": "TestPerformanceMonitor"}, {"type": "error", "test": "test_component_integration (test_optimized_components.TestIntegration.test_component_integration)", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\tests\\test_optimized_components.py\", line 236, in test_component_integration\n    from theme_optimized import OptimizedTheme\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 135, in <module>\n    class OptimizedTypography:\n    ...<32 lines>...\n        WEIGHT_BOLD = '700'\n  File \"C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\theme_optimized.py\", line 139, in OptimizedTypography\n    DISPLAY_LARGE = sp(57)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 168, in sp\n    return dpi2px(value, 'sp')\n  File \"kivy\\\\_metrics.pyx\", line 55, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 59, in kivy._metrics.dpi2px\n  File \"kivy\\\\_metrics.pyx\", line 35, in kivy._metrics.dispatch_pixel_scale\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\context.py\", line 36, in __getattribute__\n    return getattr(object.__getattribute__(self, '_obj'), name)\n  File \"kivy\\\\properties.pyx\", line 531, in kivy.properties.Property.__get__\n  File \"kivy\\\\properties.pyx\", line 1656, in kivy.properties.AliasProperty.get\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\metrics.py\", line 206, in get_dpi\n    EventLoop.ensure_window()\n    ~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\kivy\\base.py\", line 139, in ensure_window\n    sys.exit(1)\n    ~~~~~~~~^^^\nSystemExit: 1\n", "suite": "TestIntegration"}]}