# 安装指南

## 系统要求

- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python版本**: 3.8+
- **内存**: 最少 4GB RAM
- **存储空间**: 最少 1GB 可用空间

## 安装步骤

### 1. 安装Python依赖

```bash
# 安装基础依赖
pip install kivy kivymd requests

# 安装可选依赖（用于性能监控）
pip install psutil
```

### 2. 克隆项目

```bash
git clone <repository_url>
cd health-management-mobile
```

### 3. 配置环境

```bash
# 复制配置文件模板
cp api/api_config.json.template api/api_config.json

# 编辑配置文件
# 修改 api_config.json 中的服务器地址等配置
```

### 4. 运行应用

```bash
# 运行优化版本
python main_optimized.py

# 或运行原版本
python main.py
```

## 配置说明

### API配置 (api/api_config.json)

```json
{
  "base_url": "http://localhost:8000",
  "backup_url": "http://127.0.0.1:8000",
  "timeout": 30,
  "max_retries": 3,
  "retry_delay": 1.0,
  "api_version": "v1"
}
```

**配置项说明**:

- `base_url`: 主服务器地址
- `backup_url`: 备用服务器地址
- `timeout`: 请求超时时间（秒）
- `max_retries`: 最大重试次数
- `retry_delay`: 重试延迟时间（秒）
- `api_version`: API版本

### 主题配置

应用支持自定义主题配置，可以通过修改 `theme_optimized.py` 来调整：

- 颜色方案
- 字体样式
- 间距设置
- 深色模式

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查Python版本是否符合要求
   - 确认所有依赖已正确安装
   - 查看错误日志

2. **API连接失败**
   - 检查网络连接
   - 验证API配置文件
   - 确认服务器状态

3. **界面显示异常**
   - 检查屏幕分辨率设置
   - 尝试重启应用
   - 清除应用缓存

### 日志查看

应用日志保存在以下位置：

- **Windows**: `%APPDATA%/HealthApp/logs/`
- **macOS**: `~/Library/Application Support/HealthApp/logs/`
- **Linux**: `~/.local/share/HealthApp/logs/`
