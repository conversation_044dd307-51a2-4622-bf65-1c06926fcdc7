#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的移动端API功能
验证与公网服务器的连接和API端点
"""

import sys
import os
import logging

# 添加mobile目录到Python路径
mobile_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mobile')
sys.path.insert(0, mobile_dir)

from utils.cloud_api import get_cloud_api
from utils.app_config import API_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_server_connection():
    """测试服务器连接"""
    print("\n=== 测试服务器连接 ===")
    print(f"主要服务器: {API_CONFIG['BASE_URL']}")
    print(f"备用服务器: {API_CONFIG['BACKUP_URL']}")
    print(f"回退服务器: {API_CONFIG['FALLBACK_URL']}")
    
    # 获取API实例
    api = get_cloud_api()
    
    # 测试服务器健康状态
    health_result = api.check_server_health()
    print(f"服务器健康状态: {health_result}")
    
    return health_result.get('online', False)

def test_login():
    """测试登录功能"""
    print("\n=== 测试登录功能 ===")
    
    # 获取API实例
    api = get_cloud_api()
    
    # 测试登录
    login_result = api.authenticate(username="admin", password="admin123")
    
    if login_result and login_result.get('access_token'):
        print("✓ 登录成功")
        print(f"Token类型: {login_result.get('token_type')}")
        print(f"用户信息: {login_result.get('user', {})}")
        return True
    else:
        print("✗ 登录失败")
        print(f"错误信息: {login_result}")
        return False

def test_assessment_templates():
    """测试获取评估量表模板"""
    print("\n=== 测试评估量表模板 ===")
    
    # 获取API实例
    api = get_cloud_api()
    
    # 先登录
    login_result = api.authenticate(username="admin", password="admin123")
    if not login_result or not login_result.get('access_token'):
        print("✗ 需要先登录")
        return False
    
    # 获取评估量表模板
    templates_result = api.get_mobile_assessment_templates()
    
    if templates_result and templates_result.get('status') == 'success':
        print("✓ 成功获取评估量表模板")
        data = templates_result.get('data', [])
        print(f"模板数量: {len(data)}")
        if data:
            print(f"第一个模板: {data[0].get('name', 'N/A')}")
        return True
    else:
        print("✗ 获取评估量表模板失败")
        print(f"错误信息: {templates_result}")
        return False

def test_questionnaire_templates():
    """测试获取问卷模板"""
    print("\n=== 测试问卷模板 ===")
    
    # 获取API实例
    api = get_cloud_api()
    
    # 先登录
    login_result = api.authenticate(username="admin", password="admin123")
    if not login_result or not login_result.get('access_token'):
        print("✗ 需要先登录")
        return False
    
    # 获取问卷模板
    templates_result = api.get_mobile_questionnaire_templates()
    
    if templates_result and templates_result.get('status') == 'success':
        print("✓ 成功获取问卷模板")
        data = templates_result.get('data', [])
        print(f"模板数量: {len(data)}")
        if data:
            print(f"第一个模板: {data[0].get('name', 'N/A')}")
        return True
    else:
        print("✗ 获取问卷模板失败")
        print(f"错误信息: {templates_result}")
        return False

def test_user_assessments():
    """测试获取用户评估量表"""
    print("\n=== 测试用户评估量表 ===")
    
    # 获取API实例
    api = get_cloud_api()
    
    # 先登录
    login_result = api.authenticate(username="admin", password="admin123")
    if not login_result or not login_result.get('access_token'):
        print("✗ 需要先登录")
        return False
    
    # 获取用户评估量表
    assessments_result = api.get_mobile_assessments()
    
    if assessments_result and assessments_result.get('status') == 'success':
        print("✓ 成功获取用户评估量表")
        data = assessments_result.get('data', [])
        print(f"评估量表数量: {len(data)}")
        if data:
            print(f"第一个评估量表: {data[0].get('name', 'N/A')}")
        return True
    else:
        print("✗ 获取用户评估量表失败")
        print(f"错误信息: {assessments_result}")
        return False

def test_user_questionnaires():
    """测试获取用户问卷"""
    print("\n=== 测试用户问卷 ===")
    
    # 获取API实例
    api = get_cloud_api()
    
    # 先登录
    login_result = api.authenticate(username="admin", password="admin123")
    if not login_result or not login_result.get('access_token'):
        print("✗ 需要先登录")
        return False
    
    # 获取用户问卷
    questionnaires_result = api.get_mobile_questionnaires()
    
    if questionnaires_result and questionnaires_result.get('status') == 'success':
        print("✓ 成功获取用户问卷")
        data = questionnaires_result.get('data', [])
        print(f"问卷数量: {len(data)}")
        if data:
            print(f"第一个问卷: {data[0].get('name', 'N/A')}")
        return True
    else:
        print("✗ 获取用户问卷失败")
        print(f"错误信息: {questionnaires_result}")
        return False

def main():
    """主测试函数"""
    print("开始测试更新后的移动端API...")
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试服务器连接
    test_results.append(("服务器连接", test_server_connection()))
    
    # 2. 测试登录
    test_results.append(("登录功能", test_login()))
    
    # 3. 测试评估量表模板
    test_results.append(("评估量表模板", test_assessment_templates()))
    
    # 4. 测试问卷模板
    test_results.append(("问卷模板", test_questionnaire_templates()))
    
    # 5. 测试用户评估量表
    test_results.append(("用户评估量表", test_user_assessments()))
    
    # 6. 测试用户问卷
    test_results.append(("用户问卷", test_user_questionnaires()))
    
    # 输出测试结果汇总
    print("\n=== 测试结果汇总 ===")
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！移动端API已成功配置为使用公网服务器。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)