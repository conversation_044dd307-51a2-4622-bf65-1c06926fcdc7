#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一API响应处理模块
提供标准化的响应格式、错误处理和状态码管理
"""

import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Type
from fastapi import HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel, Field
import logging

# 配置日志
logger = logging.getLogger(__name__)

class ResponseStatus(str, Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

class ErrorType(str, Enum):
    """错误类型枚举"""
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    NOT_FOUND_ERROR = "not_found_error"
    CONFLICT_ERROR = "conflict_error"
    BUSINESS_ERROR = "business_error"
    SYSTEM_ERROR = "system_error"
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    EXTERNAL_API_ERROR = "external_api_error"

class ErrorDetail(BaseModel):
    """错误详情模型"""
    type: ErrorType = Field(..., description="错误类型")
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    field: Optional[str] = Field(None, description="相关字段")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total: int = Field(..., description="总记录数")
    total_pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")

class MetaInfo(BaseModel):
    """元信息模型"""
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    request_id: Optional[str] = Field(None, description="请求ID")
    version: str = Field(default="1.0.0", description="API版本")
    execution_time: Optional[float] = Field(None, description="执行时间（毫秒）")
    pagination: Optional[PaginationInfo] = Field(None, description="分页信息")

class ApiResponse(BaseModel):
    """标准API响应模型"""
    status: ResponseStatus = Field(..., description="响应状态")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    errors: Optional[List[ErrorDetail]] = Field(None, description="错误列表")
    meta: MetaInfo = Field(default_factory=MetaInfo, description="元信息")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ResponseHandler:
    """响应处理器"""
    
    # 错误代码映射
    ERROR_CODES = {
        # 验证错误 (4000-4099)
        ErrorType.VALIDATION_ERROR: {
            "required_field": "4001",
            "invalid_format": "4002",
            "invalid_value": "4003",
            "invalid_length": "4004",
            "invalid_type": "4005",
        },
        
        # 认证错误 (4100-4199)
        ErrorType.AUTHENTICATION_ERROR: {
            "invalid_credentials": "4101",
            "token_expired": "4102",
            "token_invalid": "4103",
            "token_missing": "4104",
            "account_locked": "4105",
            "account_disabled": "4106",
        },
        
        # 授权错误 (4200-4299)
        ErrorType.AUTHORIZATION_ERROR: {
            "insufficient_permissions": "4201",
            "access_denied": "4202",
            "role_required": "4203",
            "resource_forbidden": "4204",
        },
        
        # 资源错误 (4300-4399)
        ErrorType.NOT_FOUND_ERROR: {
            "resource_not_found": "4301",
            "user_not_found": "4302",
            "record_not_found": "4303",
            "endpoint_not_found": "4304",
        },
        
        # 冲突错误 (4400-4499)
        ErrorType.CONFLICT_ERROR: {
            "resource_exists": "4401",
            "duplicate_entry": "4402",
            "version_conflict": "4403",
            "state_conflict": "4404",
        },
        
        # 业务错误 (5000-5099)
        ErrorType.BUSINESS_ERROR: {
            "business_rule_violation": "5001",
            "operation_not_allowed": "5002",
            "quota_exceeded": "5003",
            "service_unavailable": "5004",
        },
        
        # 系统错误 (5100-5199)
        ErrorType.SYSTEM_ERROR: {
            "internal_error": "5101",
            "configuration_error": "5102",
            "dependency_error": "5103",
            "timeout_error": "5104",
        },
        
        # 数据库错误 (5200-5299)
        ErrorType.DATABASE_ERROR: {
            "connection_error": "5201",
            "query_error": "5202",
            "transaction_error": "5203",
            "constraint_violation": "5204",
        },
        
        # 外部API错误 (5300-5399)
        ErrorType.EXTERNAL_API_ERROR: {
            "api_unavailable": "5301",
            "api_timeout": "5302",
            "api_rate_limit": "5303",
            "api_invalid_response": "5304",
        },
    }
    
    # 错误消息映射
    ERROR_MESSAGES = {
        "4001": "必填字段不能为空",
        "4002": "字段格式不正确",
        "4003": "字段值无效",
        "4004": "字段长度不符合要求",
        "4005": "字段类型不正确",
        
        "4101": "用户名或密码错误",
        "4102": "登录令牌已过期",
        "4103": "登录令牌无效",
        "4104": "缺少登录令牌",
        "4105": "账户已被锁定",
        "4106": "账户已被禁用",
        
        "4201": "权限不足",
        "4202": "访问被拒绝",
        "4203": "需要特定角色权限",
        "4204": "禁止访问该资源",
        
        "4301": "请求的资源不存在",
        "4302": "用户不存在",
        "4303": "记录不存在",
        "4304": "接口不存在",
        
        "4401": "资源已存在",
        "4402": "数据重复",
        "4403": "版本冲突",
        "4404": "状态冲突",
        
        "5001": "违反业务规则",
        "5002": "操作不被允许",
        "5003": "超出配额限制",
        "5004": "服务暂不可用",
        
        "5101": "系统内部错误",
        "5102": "配置错误",
        "5103": "依赖服务错误",
        "5104": "请求超时",
        
        "5201": "数据库连接错误",
        "5202": "数据库查询错误",
        "5203": "数据库事务错误",
        "5204": "数据库约束违反",
        
        "5301": "外部API不可用",
        "5302": "外部API超时",
        "5303": "外部API限流",
        "5304": "外部API响应无效",
    }
    
    @classmethod
    def success(
        cls,
        data: Any = None,
        message: str = "操作成功",
        meta: Optional[Dict[str, Any]] = None,
        status_code: int = status.HTTP_200_OK
    ) -> JSONResponse:
        """成功响应"""
        meta_info = MetaInfo(**(meta or {}))
        
        response = ApiResponse(
            status=ResponseStatus.SUCCESS,
            message=message,
            data=data,
            meta=meta_info
        )
        
        return JSONResponse(
            status_code=status_code,
            content=jsonable_encoder(response, exclude_none=True)
        )
    
    @classmethod
    def error(
        cls,
        error_type: ErrorType,
        error_key: str,
        message: Optional[str] = None,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: Optional[int] = None
    ) -> JSONResponse:
        """错误响应"""
        # 获取错误代码
        error_code = cls.ERROR_CODES.get(error_type, {}).get(error_key, "9999")
        
        # 获取错误消息
        if message is None:
            message = cls.ERROR_MESSAGES.get(error_code, "未知错误")
        
        # 确定状态码
        if status_code is None:
            status_code = cls._get_status_code_by_error_type(error_type)
        
        # 创建错误详情
        error_detail = ErrorDetail(
            type=error_type,
            code=error_code,
            message=message,
            field=field,
            details=details
        )
        
        response = ApiResponse(
            status=ResponseStatus.ERROR,
            message=message,
            errors=[error_detail],
            meta=MetaInfo()
        )
        
        # 记录错误日志
        logger.error(f"API错误: {error_type} - {error_code} - {message}")
        
        return JSONResponse(
            status_code=status_code,
            content=jsonable_encoder(response, exclude_none=True)
        )
    
    @classmethod
    def validation_error(
        cls,
        errors: List[Dict[str, Any]],
        message: str = "数据验证失败"
    ) -> JSONResponse:
        """验证错误响应"""
        error_details = []
        
        for error in errors:
            error_detail = ErrorDetail(
                type=ErrorType.VALIDATION_ERROR,
                code=cls.ERROR_CODES[ErrorType.VALIDATION_ERROR].get("invalid_format", "4002"),
                message=error.get("msg", "验证失败"),
                field=error.get("loc", [""])[-1] if error.get("loc") else None,
                details=error
            )
            error_details.append(error_detail)
        
        response = ApiResponse(
            status=ResponseStatus.ERROR,
            message=message,
            errors=error_details,
            meta=MetaInfo()
        )
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=jsonable_encoder(response, exclude_none=True)
        )
    
    @classmethod
    def paginated_success(
        cls,
        data: List[Any],
        page: int,
        page_size: int,
        total: int,
        message: str = "查询成功"
    ) -> JSONResponse:
        """分页成功响应"""
        total_pages = (total + page_size - 1) // page_size
        
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        meta_info = MetaInfo(pagination=pagination)
        
        response = ApiResponse(
            status=ResponseStatus.SUCCESS,
            message=message,
            data=data,
            meta=meta_info
        )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=jsonable_encoder(response, exclude_none=True)
        )
    
    @classmethod
    def created(
        cls,
        data: Any = None,
        message: str = "创建成功"
    ) -> JSONResponse:
        """创建成功响应"""
        return cls.success(
            data=data,
            message=message,
            status_code=status.HTTP_201_CREATED
        )
    
    @classmethod
    def updated(
        cls,
        data: Any = None,
        message: str = "更新成功"
    ) -> JSONResponse:
        """更新成功响应"""
        return cls.success(data=data, message=message)
    
    @classmethod
    def deleted(
        cls,
        message: str = "删除成功"
    ) -> JSONResponse:
        """删除成功响应"""
        return cls.success(message=message, status_code=status.HTTP_204_NO_CONTENT)
    
    @classmethod
    def not_found(
        cls,
        resource: str = "资源",
        message: Optional[str] = None
    ) -> JSONResponse:
        """资源不存在响应"""
        if message is None:
            message = f"{resource}不存在"
        
        return cls.error(
            error_type=ErrorType.NOT_FOUND_ERROR,
            error_key="resource_not_found",
            message=message,
            status_code=status.HTTP_404_NOT_FOUND
        )
    
    @classmethod
    def unauthorized(
        cls,
        message: str = "未授权访问"
    ) -> JSONResponse:
        """未授权响应"""
        return cls.error(
            error_type=ErrorType.AUTHENTICATION_ERROR,
            error_key="token_missing",
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED
        )
    
    @classmethod
    def forbidden(
        cls,
        message: str = "禁止访问"
    ) -> JSONResponse:
        """禁止访问响应"""
        return cls.error(
            error_type=ErrorType.AUTHORIZATION_ERROR,
            error_key="access_denied",
            message=message,
            status_code=status.HTTP_403_FORBIDDEN
        )
    
    @classmethod
    def conflict(
        cls,
        message: str = "资源冲突"
    ) -> JSONResponse:
        """冲突响应"""
        return cls.error(
            error_type=ErrorType.CONFLICT_ERROR,
            error_key="resource_exists",
            message=message,
            status_code=status.HTTP_409_CONFLICT
        )
    
    @classmethod
    def internal_error(
        cls,
        message: str = "系统内部错误",
        details: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """内部错误响应"""
        return cls.error(
            error_type=ErrorType.SYSTEM_ERROR,
            error_key="internal_error",
            message=message,
            details=details,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    @classmethod
    def _get_status_code_by_error_type(cls, error_type: ErrorType) -> int:
        """根据错误类型获取HTTP状态码"""
        mapping = {
            ErrorType.VALIDATION_ERROR: status.HTTP_422_UNPROCESSABLE_ENTITY,
            ErrorType.AUTHENTICATION_ERROR: status.HTTP_401_UNAUTHORIZED,
            ErrorType.AUTHORIZATION_ERROR: status.HTTP_403_FORBIDDEN,
            ErrorType.NOT_FOUND_ERROR: status.HTTP_404_NOT_FOUND,
            ErrorType.CONFLICT_ERROR: status.HTTP_409_CONFLICT,
            ErrorType.BUSINESS_ERROR: status.HTTP_400_BAD_REQUEST,
            ErrorType.SYSTEM_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
            ErrorType.NETWORK_ERROR: status.HTTP_503_SERVICE_UNAVAILABLE,
            ErrorType.DATABASE_ERROR: status.HTTP_500_INTERNAL_SERVER_ERROR,
            ErrorType.EXTERNAL_API_ERROR: status.HTTP_502_BAD_GATEWAY,
        }
        return mapping.get(error_type, status.HTTP_500_INTERNAL_SERVER_ERROR)

# 异常处理装饰器
def handle_exceptions(func):
    """异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except HTTPException as e:
            # FastAPI HTTP异常
            return ResponseHandler.error(
                error_type=ErrorType.SYSTEM_ERROR,
                error_key="internal_error",
                message=e.detail,
                status_code=e.status_code
            )
        except ValueError as e:
            # 值错误
            return ResponseHandler.error(
                error_type=ErrorType.VALIDATION_ERROR,
                error_key="invalid_value",
                message=str(e)
            )
        except Exception as e:
            # 其他异常
            logger.error(f"未处理的异常: {str(e)}\n{traceback.format_exc()}")
            return ResponseHandler.internal_error(
                message="系统发生未知错误",
                details={"error": str(e)} if not isinstance(e, Exception) else None
            )
    
    return wrapper

# 全局响应处理器实例
response_handler = ResponseHandler()

# 便捷函数
def success_response(*args, **kwargs):
    """成功响应便捷函数"""
    return response_handler.success(*args, **kwargs)

def error_response(*args, **kwargs):
    """错误响应便捷函数"""
    return response_handler.error(*args, **kwargs)

def paginated_response(*args, **kwargs):
    """分页响应便捷函数"""
    return response_handler.paginated_success(*args, **kwargs)

def created_response(*args, **kwargs):
    """创建响应便捷函数"""
    return response_handler.created(*args, **kwargs)

def updated_response(*args, **kwargs):
    """更新响应便捷函数"""
    return response_handler.updated(*args, **kwargs)

def deleted_response(*args, **kwargs):
    """删除响应便捷函数"""
    return response_handler.deleted(*args, **kwargs)

def not_found_response(*args, **kwargs):
    """未找到响应便捷函数"""
    return response_handler.not_found(*args, **kwargs)