#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备份恢复工具
用于从备份目录恢复项目到原始状态
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('restore.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class RestoreTool:
    """恢复工具类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_root = self.project_root / 'backup'
        self.restored_files = []
        self.errors = []
    
    def list_available_backups(self):
        """列出可用的备份"""
        if not self.backup_root.exists():
            logger.error("备份目录不存在")
            return []
        
        backups = []
        for backup_dir in self.backup_root.iterdir():
            if backup_dir.is_dir() and backup_dir.name.startswith('202'):
                backups.append(backup_dir)
        
        # 按时间排序
        backups.sort(key=lambda x: x.name)
        return backups
    
    def show_backup_info(self, backup_dir):
        """显示备份信息"""
        logger.info(f"\n备份目录: {backup_dir.name}")
        logger.info(f"备份路径: {backup_dir}")
        
        # 统计备份内容
        file_count = 0
        dir_count = 0
        
        for item in backup_dir.rglob('*'):
            if item.is_file():
                file_count += 1
            elif item.is_dir():
                dir_count += 1
        
        logger.info(f"包含文件: {file_count} 个")
        logger.info(f"包含目录: {dir_count} 个")
        
        # 显示主要文件
        main_files = ['main.py', 'theme.py', 'migration_report.md']
        logger.info("\n主要文件:")
        for file_name in main_files:
            file_path = backup_dir / file_name
            if file_path.exists():
                logger.info(f"  ✓ {file_name}")
            else:
                logger.info(f"  ✗ {file_name} (不存在)")
    
    def restore_from_backup(self, backup_dir, confirm=True):
        """从指定备份恢复文件"""
        if not backup_dir.exists():
            logger.error(f"备份目录不存在: {backup_dir}")
            return False
        
        if confirm:
            response = input(f"\n确认从备份 {backup_dir.name} 恢复文件？(y/N): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                logger.info("恢复已取消")
                return False
        
        logger.info(f"开始从备份恢复: {backup_dir.name}")
        
        try:
            # 遍历备份目录中的所有文件和目录
            for item in backup_dir.iterdir():
                if item.name == 'migration_report.md':
                    continue  # 跳过迁移报告
                
                target_path = self.project_root / item.name
                
                if item.is_file():
                    # 恢复文件
                    if target_path.exists():
                        # 备份当前文件
                        backup_current = target_path.with_suffix(target_path.suffix + '.bak')
                        shutil.copy2(target_path, backup_current)
                        logger.info(f"当前文件已备份: {backup_current}")
                    
                    shutil.copy2(item, target_path)
                    self.restored_files.append(str(target_path))
                    logger.info(f"✓ 恢复文件: {item.name}")
                
                elif item.is_dir():
                    # 恢复目录
                    if target_path.exists():
                        # 备份当前目录
                        backup_current = target_path.with_name(target_path.name + '_bak')
                        if backup_current.exists():
                            shutil.rmtree(backup_current)
                        shutil.copytree(target_path, backup_current)
                        logger.info(f"当前目录已备份: {backup_current}")
                        
                        # 删除当前目录
                        shutil.rmtree(target_path)
                    
                    shutil.copytree(item, target_path)
                    self.restored_files.append(str(target_path))
                    logger.info(f"✓ 恢复目录: {item.name}")
            
            logger.info(f"\n恢复完成！共恢复 {len(self.restored_files)} 个文件/目录")
            return True
            
        except Exception as e:
            logger.error(f"恢复过程中出错: {e}")
            self.errors.append(str(e))
            return False
    
    def clean_optimized_files(self):
        """清理优化版本文件"""
        logger.info("清理优化版本文件...")
        
        optimized_files = [
            'main_optimized.py',
            'theme.py',
            'api/api_config_optimized.py',
            'api/api_client_optimized.py',
            'check_migration.py'
        ]
        
        cleaned_count = 0
        
        for file_path in optimized_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    full_path.unlink()
                    cleaned_count += 1
                    logger.info(f"✓ 删除优化文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除文件失败 {file_path}: {e}")
        
        logger.info(f"清理完成，共删除 {cleaned_count} 个优化文件")
    
    def generate_restore_report(self):
        """生成恢复报告"""
        report_content = f"""# 恢复报告

**恢复时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**项目目录**: {self.project_root}

## 恢复摘要

- **恢复文件数**: {len(self.restored_files)}
- **错误数**: {len(self.errors)}

## 恢复的文件列表

"""
        
        for file_path in self.restored_files:
            report_content += f"- {file_path}\n"
        
        if self.errors:
            report_content += "\n## 错误信息\n\n"
            for error in self.errors:
                report_content += f"- {error}\n"
        
        report_content += "\n## 后续步骤\n\n"
        report_content += "1. 检查恢复的文件是否正常\n"
        report_content += "2. 运行 `python main.py` 测试应用\n"
        report_content += "3. 如有问题，检查备份文件\n"
        report_content += "\n✅ 项目已恢复到原始状态！\n"
        
        # 保存报告
        report_path = self.project_root / 'restore_report.md'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"恢复报告已保存: {report_path}")
        return report_path


def main():
    """主函数"""
    print("健康管理应用 - 备份恢复工具")
    print("=" * 40)
    print("此工具将帮助您从备份恢复项目到原始状态")
    print()
    
    restore_tool = RestoreTool()
    
    # 列出可用备份
    backups = restore_tool.list_available_backups()
    
    if not backups:
        print("❌ 没有找到可用的备份")
        return 1
    
    print(f"找到 {len(backups)} 个备份:")
    for i, backup in enumerate(backups, 1):
        print(f"{i}. {backup.name}")
    
    # 选择备份
    while True:
        try:
            choice = input("\n请选择要恢复的备份 (输入序号): ").strip()
            if not choice:
                print("恢复已取消")
                return 0
            
            backup_index = int(choice) - 1
            if 0 <= backup_index < len(backups):
                selected_backup = backups[backup_index]
                break
            else:
                print("无效的选择，请重新输入")
        except ValueError:
            print("请输入有效的数字")
    
    # 显示备份信息
    restore_tool.show_backup_info(selected_backup)
    
    # 执行恢复
    if restore_tool.restore_from_backup(selected_backup):
        # 清理优化文件
        restore_tool.clean_optimized_files()
        
        # 生成报告
        restore_tool.generate_restore_report()
        
        print("\n🎉 恢复成功完成！")
        print("\n下一步:")
        print("1. 运行 'python main.py' 测试应用")
        print("2. 检查功能是否正常")
        return 0
    else:
        print("\n❌ 恢复过程中遇到问题")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)