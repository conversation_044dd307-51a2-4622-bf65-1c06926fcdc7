# -*- coding: utf-8 -*-
"""
测试管理API路由
提供自动化测试执行、覆盖率报告、测试历史分析等功能

版本: 1.0
作者: Health Management System
创建时间: 2024-12-30
"""

import os
import json
import asyncio
import subprocess
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from ...core.db_connection import get_db
from ...core.error_handler import ErrorHandler
from ...core.logging_utils import get_logger
from ...core.response_handler import ResponseHandler
from ...core.auth import get_current_user
from ...models import User

logger = get_logger(__name__)
router = APIRouter(prefix="/test-management", tags=["测试管理"])

# 初始化服务
error_handler = ErrorHandler()
response_handler = ResponseHandler()

# ==================== 请求/响应模型 ====================

class TestSuiteRequest(BaseModel):
    """测试套件请求模型"""
    name: str = Field(..., description="测试套件名称")
    description: str = Field(..., description="测试套件描述")
    type: str = Field(..., description="测试类型")
    test_files: List[str] = Field(default=[], description="测试文件列表")
    config: Dict[str, Any] = Field(default={}, description="测试配置")

class TestExecutionRequest(BaseModel):
    """测试执行请求模型"""
    suite_ids: List[str] = Field(default=[], description="测试套件ID列表")
    test_types: List[str] = Field(default=[], description="测试类型列表")
    parallel: bool = Field(default=False, description="是否并行执行")
    coverage: bool = Field(default=True, description="是否生成覆盖率报告")
    timeout: int = Field(default=300, description="超时时间（秒）")

class TestResult(BaseModel):
    """测试结果模型"""
    id: str
    name: str
    suite_name: str
    type: str
    status: str
    duration: float
    message: str
    error_details: Optional[str] = None
    coverage: Optional[float] = None
    timestamp: datetime

class TestSuite(BaseModel):
    """测试套件模型"""
    id: str
    name: str
    description: str
    type: str
    status: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    duration: float
    coverage: float
    last_run: Optional[datetime] = None
    test_cases: List[TestResult] = []

class CoverageReport(BaseModel):
    """覆盖率报告模型"""
    overall_coverage: float
    line_coverage: float
    branch_coverage: float
    function_coverage: float
    file_coverage: Dict[str, float]
    uncovered_lines: Dict[str, List[int]]
    timestamp: datetime

# ==================== 测试管理核心类 ====================

class TestManager:
    """测试管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.parent.parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.test_results_dir = self.project_root / "test_results"
        self.test_results_dir.mkdir(exist_ok=True)
        
        # 测试套件配置
        self.test_suites = {
            "unit_backend": {
                "name": "后端单元测试",
                "description": "测试后端核心功能和业务逻辑",
                "type": "unit",
                "command": "pytest tests/unit -v --cov=app --cov-report=json",
                "cwd": self.backend_dir
            },
            "integration_backend": {
                "name": "后端集成测试",
                "description": "测试后端API接口和数据库集成",
                "type": "integration",
                "command": "pytest tests/integration -v --cov=app --cov-report=json",
                "cwd": self.backend_dir
            },
            "api_tests": {
                "name": "API测试",
                "description": "测试所有API端点的功能和性能",
                "type": "api",
                "command": "python test_api.py",
                "cwd": self.project_root
            },
            "data_export": {
                "name": "数据导出测试",
                "description": "测试数据导入导出功能",
                "type": "data",
                "command": "python test_data_export.py",
                "cwd": self.backend_dir
            },
            "database_integrity": {
                "name": "数据库完整性测试",
                "description": "检查数据库表结构和数据完整性",
                "type": "database",
                "command": "python check_tables.py",
                "cwd": self.backend_dir
            },
            "frontend_unit": {
                "name": "前端单元测试",
                "description": "测试前端组件和工具函数",
                "type": "unit",
                "command": "npm run test:unit",
                "cwd": self.frontend_dir
            },
            "frontend_e2e": {
                "name": "前端E2E测试",
                "description": "端到端功能测试",
                "type": "e2e",
                "command": "npm run test:e2e",
                "cwd": self.frontend_dir
            }
        }
    
    async def execute_test_suite(self, suite_id: str, background_tasks: BackgroundTasks) -> Dict[str, Any]:
        """执行测试套件"""
        if suite_id not in self.test_suites:
            raise HTTPException(status_code=404, detail=f"测试套件 {suite_id} 不存在")
        
        suite_config = self.test_suites[suite_id]
        
        # 创建测试结果文件
        result_file = self.test_results_dir / f"{suite_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 后台执行测试
        background_tasks.add_task(self._run_test_suite, suite_id, suite_config, result_file)
        
        return {
            "message": f"测试套件 {suite_config['name']} 开始执行",
            "suite_id": suite_id,
            "result_file": str(result_file)
        }
    
    async def _run_test_suite(self, suite_id: str, suite_config: Dict[str, Any], result_file: Path):
        """运行测试套件（后台任务）"""
        start_time = datetime.now()
        
        try:
            # 执行测试命令
            process = await asyncio.create_subprocess_shell(
                suite_config["command"],
                cwd=suite_config["cwd"],
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 解析测试结果
            result = {
                "suite_id": suite_id,
                "name": suite_config["name"],
                "type": suite_config["type"],
                "status": "passed" if process.returncode == 0 else "failed",
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration": duration,
                "return_code": process.returncode,
                "stdout": stdout.decode() if stdout else "",
                "stderr": stderr.decode() if stderr else "",
                "coverage": await self._extract_coverage(suite_config["cwd"])
            }
            
            # 保存结果
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"测试套件 {suite_id} 执行完成，状态: {result['status']}")
            
        except Exception as e:
            logger.error(f"执行测试套件 {suite_id} 失败: {str(e)}")
            
            # 保存错误结果
            error_result = {
                "suite_id": suite_id,
                "name": suite_config["name"],
                "type": suite_config["type"],
                "status": "error",
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": (datetime.now() - start_time).total_seconds(),
                "error": str(e)
            }
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(error_result, f, ensure_ascii=False, indent=2)
    
    async def _extract_coverage(self, cwd: Path) -> Optional[float]:
        """提取覆盖率信息"""
        try:
            coverage_file = cwd / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file, 'r') as f:
                    coverage_data = json.load(f)
                    return coverage_data.get("totals", {}).get("percent_covered", 0.0)
        except Exception as e:
            logger.warning(f"提取覆盖率失败: {str(e)}")
        return None
    
    def get_test_results(self, suite_id: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取测试结果"""
        results = []
        
        for result_file in sorted(self.test_results_dir.glob("*.json"), reverse=True):
            if len(results) >= limit:
                break
                
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                    
                if suite_id is None or result.get("suite_id") == suite_id:
                    results.append(result)
                    
            except Exception as e:
                logger.warning(f"读取测试结果文件 {result_file} 失败: {str(e)}")
        
        return results
    
    def get_coverage_report(self) -> Dict[str, Any]:
        """获取覆盖率报告"""
        try:
            # 合并前后端覆盖率报告
            backend_coverage = self._get_backend_coverage()
            frontend_coverage = self._get_frontend_coverage()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "overall_coverage": (backend_coverage.get("percent_covered", 0) + 
                                   frontend_coverage.get("percent_covered", 0)) / 2,
                "backend": backend_coverage,
                "frontend": frontend_coverage
            }
        except Exception as e:
            logger.error(f"获取覆盖率报告失败: {str(e)}")
            return {"error": str(e)}
    
    def _get_backend_coverage(self) -> Dict[str, Any]:
        """获取后端覆盖率"""
        try:
            coverage_file = self.backend_dir / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"获取后端覆盖率失败: {str(e)}")
        return {"percent_covered": 0}
    
    def _get_frontend_coverage(self) -> Dict[str, Any]:
        """获取前端覆盖率"""
        try:
            coverage_file = self.frontend_dir / "coverage" / "coverage-summary.json"
            if coverage_file.exists():
                with open(coverage_file, 'r') as f:
                    data = json.load(f)
                    total = data.get("total", {})
                    return {
                        "percent_covered": total.get("lines", {}).get("pct", 0),
                        "lines": total.get("lines", {}),
                        "functions": total.get("functions", {}),
                        "branches": total.get("branches", {}),
                        "statements": total.get("statements", {})
                    }
        except Exception as e:
            logger.warning(f"获取前端覆盖率失败: {str(e)}")
        return {"percent_covered": 0}

# 全局测试管理器实例
test_manager = TestManager()

# ==================== API端点 ====================

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "ok",
        "service": "测试管理API",
        "timestamp": datetime.now().isoformat(),
        "test_suites": len(test_manager.test_suites)
    }

@router.get("/suites")
async def get_test_suites(current_user: User = Depends(get_current_user)):
    """获取所有测试套件"""
    try:
        suites = []
        for suite_id, config in test_manager.test_suites.items():
            # 获取最近的测试结果
            recent_results = test_manager.get_test_results(suite_id, limit=1)
            recent_result = recent_results[0] if recent_results else None
            
            suite_info = {
                "id": suite_id,
                "name": config["name"],
                "description": config["description"],
                "type": config["type"],
                "status": recent_result.get("status", "pending") if recent_result else "pending",
                "last_run": recent_result.get("end_time") if recent_result else None,
                "duration": recent_result.get("duration", 0) if recent_result else 0,
                "coverage": recent_result.get("coverage", 0) if recent_result else 0
            }
            suites.append(suite_info)
        
        return response_handler.success(suites)
    except Exception as e:
        logger.error(f"获取测试套件失败: {str(e)}")
        return response_handler.error("获取测试套件失败")

@router.post("/suites/{suite_id}/run")
async def run_test_suite(
    suite_id: str,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """运行指定测试套件"""
    try:
        result = await test_manager.execute_test_suite(suite_id, background_tasks)
        return response_handler.success(result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"运行测试套件失败: {str(e)}")
        return response_handler.error("运行测试套件失败")

@router.post("/run-all")
async def run_all_tests(
    background_tasks: BackgroundTasks,
    test_types: List[str] = Query(default=[]),
    current_user: User = Depends(get_current_user)
):
    """运行所有测试或指定类型的测试"""
    try:
        results = []
        for suite_id, config in test_manager.test_suites.items():
            if not test_types or config["type"] in test_types:
                result = await test_manager.execute_test_suite(suite_id, background_tasks)
                results.append(result)
        
        return response_handler.success({
            "message": f"开始执行 {len(results)} 个测试套件",
            "suites": results
        })
    except Exception as e:
        logger.error(f"运行所有测试失败: {str(e)}")
        return response_handler.error("运行所有测试失败")

@router.get("/results")
async def get_test_results(
    suite_id: Optional[str] = Query(None),
    limit: int = Query(default=100, le=1000),
    current_user: User = Depends(get_current_user)
):
    """获取测试结果"""
    try:
        results = test_manager.get_test_results(suite_id, limit)
        return response_handler.success(results)
    except Exception as e:
        logger.error(f"获取测试结果失败: {str(e)}")
        return response_handler.error("获取测试结果失败")

@router.get("/coverage")
async def get_coverage_report(current_user: User = Depends(get_current_user)):
    """获取覆盖率报告"""
    try:
        report = test_manager.get_coverage_report()
        return response_handler.success(report)
    except Exception as e:
        logger.error(f"获取覆盖率报告失败: {str(e)}")
        return response_handler.error("获取覆盖率报告失败")

@router.get("/statistics")
async def get_test_statistics(
    days: int = Query(default=30, ge=1, le=365),
    current_user: User = Depends(get_current_user)
):
    """获取测试统计信息"""
    try:
        # 获取指定天数内的测试结果
        cutoff_date = datetime.now() - timedelta(days=days)
        all_results = test_manager.get_test_results(limit=1000)
        
        # 过滤时间范围内的结果
        filtered_results = [
            result for result in all_results
            if datetime.fromisoformat(result.get("start_time", "")) >= cutoff_date
        ]
        
        # 计算统计信息
        total_tests = len(filtered_results)
        passed_tests = len([r for r in filtered_results if r.get("status") == "passed"])
        failed_tests = len([r for r in filtered_results if r.get("status") == "failed"])
        error_tests = len([r for r in filtered_results if r.get("status") == "error"])
        
        # 按类型统计
        type_stats = {}
        for result in filtered_results:
            test_type = result.get("type", "unknown")
            if test_type not in type_stats:
                type_stats[test_type] = {"total": 0, "passed": 0, "failed": 0, "error": 0}
            
            type_stats[test_type]["total"] += 1
            status = result.get("status", "error")
            if status in type_stats[test_type]:
                type_stats[test_type][status] += 1
        
        # 计算平均执行时间
        durations = [r.get("duration", 0) for r in filtered_results if r.get("duration")]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        statistics = {
            "period_days": days,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "average_duration": avg_duration,
            "type_statistics": type_stats,
            "recent_results": filtered_results[:10]  # 最近10个结果
        }
        
        return response_handler.success(statistics)
    except Exception as e:
        logger.error(f"获取测试统计信息失败: {str(e)}")
        return response_handler.error("获取测试统计信息失败")

@router.delete("/results")
async def clear_test_results(
    older_than_days: int = Query(default=30),
    current_user: User = Depends(get_current_user)
):
    """清理测试结果"""
    try:
        cutoff_date = datetime.now() - timedelta(days=older_than_days)
        deleted_count = 0
        
        for result_file in test_manager.test_results_dir.glob("*.json"):
            try:
                # 检查文件修改时间
                file_time = datetime.fromtimestamp(result_file.stat().st_mtime)
                if file_time < cutoff_date:
                    result_file.unlink()
                    deleted_count += 1
            except Exception as e:
                logger.warning(f"删除文件 {result_file} 失败: {str(e)}")
        
        return response_handler.success({
            "message": f"已删除 {deleted_count} 个测试结果文件",
            "deleted_count": deleted_count
        })
    except Exception as e:
        logger.error(f"清理测试结果失败: {str(e)}")
        return response_handler.error("清理测试结果失败")

@router.get("/export/coverage")
async def export_coverage_report(
    format: str = Query(default="html", regex="^(html|json|xml)$"),
    current_user: User = Depends(get_current_user)
):
    """导出覆盖率报告"""
    try:
        report = test_manager.get_coverage_report()
        
        if format == "json":
            return response_handler.success(report)
        elif format == "html":
            # 生成HTML报告
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>测试覆盖率报告</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
                    .coverage-item {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
                    .high {{ background: #d4edda; }}
                    .medium {{ background: #fff3cd; }}
                    .low {{ background: #f8d7da; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>测试覆盖率报告</h1>
                    <p>生成时间: {report.get('timestamp', '')}</p>
                    <p>总体覆盖率: {report.get('overall_coverage', 0):.2f}%</p>
                </div>
                
                <h2>后端覆盖率</h2>
                <div class="coverage-item">
                    <p>覆盖率: {report.get('backend', {}).get('percent_covered', 0):.2f}%</p>
                </div>
                
                <h2>前端覆盖率</h2>
                <div class="coverage-item">
                    <p>覆盖率: {report.get('frontend', {}).get('percent_covered', 0):.2f}%</p>
                </div>
            </body>
            </html>
            """
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8')
            temp_file.write(html_content)
            temp_file.close()
            
            return FileResponse(
                temp_file.name,
                media_type='text/html',
                filename=f"coverage_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            )
        
        return response_handler.error("不支持的导出格式")
    except Exception as e:
        logger.error(f"导出覆盖率报告失败: {str(e)}")
        return response_handler.error("导出覆盖率报告失败")