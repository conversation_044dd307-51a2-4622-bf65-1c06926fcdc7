2025-08-02 18:16:58,570 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-02 18:16:58,576 - auth_service - INFO - 统一认证服务初始化完成
2025-08-02 18:16:58,745 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-02 18:16:58,747 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-02 18:16:59,388 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-08-02 18:17:00,540 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-08-02 18:17:00,542 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-08-02 18:17:00,545 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-08-02 18:17:00,594 - health_monitor - INFO - 健康监控器初始化完成
2025-08-02 18:17:00,604 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-08-02 18:17:00,607 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-08-02 18:17:00,609 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-08-02 18:17:00,615 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-08-02 18:17:00,617 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-08-02 18:17:00,619 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-02 18:17:00,628 - alert_manager - INFO - 已初始化默认告警规则
2025-08-02 18:17:00,628 - alert_manager - INFO - 已初始化默认通知渠道
2025-08-02 18:17:00,629 - alert_manager - INFO - 告警管理器初始化完成
2025-08-02 18:17:01,849 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-08-02 18:17:01,854 - db_service - INFO - 数据库服务初始化完成
2025-08-02 18:17:01,867 - notification_service - INFO - 通知服务初始化完成
2025-08-02 18:17:01,869 - main - INFO - 错误处理模块导入成功
2025-08-02 18:17:01,940 - main - INFO - 监控模块导入成功
2025-08-02 18:17:01,947 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-08-02 18:17:06,128 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-08-02 18:17:06,192 - main - INFO - 应用启动中...
2025-08-02 18:17:06,196 - error_handling - INFO - 错误处理已设置
2025-08-02 18:17:06,197 - main - INFO - 错误处理系统初始化完成
2025-08-02 18:17:06,199 - monitoring - INFO - 添加指标端点成功: /metrics
2025-08-02 18:17:06,201 - monitoring - INFO - 添加健康检查端点成功: /health
2025-08-02 18:17:06,202 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-08-02 18:17:06,204 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-08-02 18:17:06,216 - monitoring - INFO - 启动资源监控线程成功
2025-08-02 18:17:06,217 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-08-02 18:17:06,219 - monitoring - INFO - 监控系统初始化完成
2025-08-02 18:17:06,220 - main - INFO - 监控系统初始化完成
2025-08-02 18:17:06,227 - app.db.init_db - INFO - 所有模型导入成功
2025-08-02 18:17:06,230 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-02 18:17:06,243 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:06,246 - app.db.init_db - INFO - 所有模型导入成功
2025-08-02 18:17:06,248 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-02 18:17:06,249 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-08-02 18:17:06,250 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-08-02 18:17:06,253 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 18:17:06,261 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-08-02 18:17:06,264 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,275 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-08-02 18:17:06,279 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,281 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-08-02 18:17:06,284 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,290 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-08-02 18:17:06,292 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,297 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-08-02 18:17:06,299 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,304 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-08-02 18:17:06,307 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,310 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-08-02 18:17:06,313 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,315 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-08-02 18:17:06,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-08-02 18:17:06,321 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,325 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-08-02 18:17:06,329 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,332 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-08-02 18:17:06,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,341 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-08-02 18:17:06,343 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,349 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-08-02 18:17:06,354 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,357 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-08-02 18:17:06,359 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,366 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-08-02 18:17:06,369 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,371 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-08-02 18:17:06,373 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,376 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-08-02 18:17:06,382 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,386 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-08-02 18:17:06,388 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-08-02 18:17:06,392 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,398 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-08-02 18:17:06,401 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,403 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-08-02 18:17:06,405 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,408 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-08-02 18:17:06,411 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,417 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-08-02 18:17:06,419 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,421 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-08-02 18:17:06,423 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,427 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-08-02 18:17:06,432 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,434 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-08-02 18:17:06,436 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,438 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-08-02 18:17:06,440 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,447 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-08-02 18:17:06,449 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,452 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-08-02 18:17:06,454 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,456 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-08-02 18:17:06,458 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,465 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-08-02 18:17:06,467 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,470 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-08-02 18:17:06,472 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,475 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-08-02 18:17:06,481 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,483 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-08-02 18:17:06,486 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,488 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-08-02 18:17:06,496 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,499 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-08-02 18:17:06,501 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,504 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-08-02 18:17:06,509 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-08-02 18:17:06,517 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,519 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-08-02 18:17:06,522 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,530 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-08-02 18:17:06,532 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,534 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-08-02 18:17:06,537 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,543 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-08-02 18:17:06,546 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,548 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-08-02 18:17:06,551 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,556 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 18:17:06,559 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-08-02 18:17:06,565 - app.db.init_db - INFO - 模型关系初始化完成
2025-08-02 18:17:06,567 - app.db.init_db - INFO - 模型关系设置完成
2025-08-02 18:17:06,568 - main - INFO - 数据库初始化完成（强制重建）
2025-08-02 18:17:06,571 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:06,576 - main - INFO - 数据库连接正常
2025-08-02 18:17:06,578 - main - INFO - 开始初始化模板数据
2025-08-02 18:17:06,580 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:07,108 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-08-02 18:17:07,188 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-08-02 18:17:07,268 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-08-02 18:17:07,335 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-08-02 18:17:07,338 - main - INFO - 模板数据初始化完成
2025-08-02 18:17:07,341 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-08-02 18:17:07,342 - main - INFO - 应用启动完成
2025-08-02 18:17:21,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-08-02 18:17:26,289 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:17:26,512 - main - INFO - 请求没有认证头部
2025-08-02 18:17:26,653 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:17:26,754 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:17:29,285 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:17:29,302 - main - INFO - 请求没有认证头部
2025-08-02 18:17:29,305 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:17:29,338 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:17:29,352 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:17:29,359 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:17:29,364 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:17:29,367 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:29,369 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:17:32,381 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:17:32,395 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:17:36,719 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 70.8%
2025-08-02 18:17:51,833 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 79.2%
2025-08-02 18:18:01,567 - health_monitor - DEBUG - 系统指标 - CPU: 14.0%, 内存: 61.0%, 磁盘: 94.7%
2025-08-02 18:18:06,940 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 16.7%
2025-08-02 18:18:22,051 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 70.8%
2025-08-02 18:18:37,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 45.8%
2025-08-02 18:18:52,263 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 45.8%
2025-08-02 18:19:02,595 - health_monitor - DEBUG - 系统指标 - CPU: 36.9%, 内存: 58.2%, 磁盘: 94.7%
2025-08-02 18:19:07,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 42.9%
2025-08-02 18:19:22,652 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 100.0%
2025-08-02 18:19:37,788 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 75.0%
2025-08-02 18:19:52,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 38.5%
2025-08-02 18:20:03,910 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 59.4%, 磁盘: 94.7%
2025-08-02 18:20:08,002 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 57.1%
2025-08-02 18:20:23,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 100.0%
2025-08-02 18:20:38,225 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 38.5%
2025-08-02 18:20:53,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 20.8%
2025-08-02 18:21:04,946 - health_monitor - DEBUG - 系统指标 - CPU: 38.3%, 内存: 58.8%, 磁盘: 94.7%
2025-08-02 18:21:08,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 25.0%
2025-08-02 18:21:23,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 48.3%
2025-08-02 18:21:38,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 25.0%
2025-08-02 18:21:53,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 0.0%
2025-08-02 18:22:05,968 - health_monitor - DEBUG - 系统指标 - CPU: 27.7%, 内存: 58.4%, 磁盘: 94.7%
2025-08-02 18:22:08,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 32.0%
2025-08-02 18:22:23,968 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 17.9%
2025-08-02 18:22:39,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 15.4%
2025-08-02 18:22:54,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 66.7%
2025-08-02 18:23:00,637 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-08-02 18:23:06,990 - health_monitor - DEBUG - 系统指标 - CPU: 43.3%, 内存: 58.8%, 磁盘: 94.7%
2025-08-02 18:23:09,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 71.4%
2025-08-02 18:23:24,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 66.7%
2025-08-02 18:23:32,982 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:23:32,997 - main - INFO - 请求没有认证头部
2025-08-02 18:23:32,998 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:23:33,000 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:23:35,061 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:23:35,063 - main - INFO - 请求没有认证头部
2025-08-02 18:23:35,066 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:23:35,068 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:23:35,069 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:23:35,071 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:23:35,072 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:23:35,074 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:23:35,080 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:23:36,578 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:23:36,584 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:23:39,585 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 96.8%
2025-08-02 18:23:54,696 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 71.4%
2025-08-02 18:24:00,643 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.7, 阈值: 90
2025-08-02 18:24:08,014 - health_monitor - DEBUG - 系统指标 - CPU: 74.9%, 内存: 61.6%, 磁盘: 94.7%
2025-08-02 18:24:09,809 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 69.0%
2025-08-02 18:24:24,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 20.7%
2025-08-02 18:24:40,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 100.0%
2025-08-02 18:24:55,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 75.0%
2025-08-02 18:24:58,570 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:24:58,572 - main - INFO - 请求没有认证头部
2025-08-02 18:24:58,575 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:24:58,578 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:25:00,645 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:25:00,647 - main - INFO - 请求没有认证头部
2025-08-02 18:25:00,650 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:25:00,653 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:25:00,654 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:25:00,656 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:25:00,657 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:25:00,659 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:25:00,660 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:25:01,594 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:25:01,601 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:25:09,042 - health_monitor - DEBUG - 系统指标 - CPU: 47.9%, 内存: 63.3%, 磁盘: 94.7%
2025-08-02 18:25:10,323 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 10.7%
2025-08-02 18:25:25,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 19.2%
2025-08-02 18:25:40,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 96.6%
2025-08-02 18:25:56,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 40.0%, CPU使用率 100.0%
2025-08-02 18:26:11,034 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 45.9%, 磁盘: 94.7%
2025-08-02 18:26:12,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.0%, CPU使用率 100.0%
2025-08-02 18:26:30,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 100.0%
2025-08-02 18:26:47,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 100.0%
2025-08-02 18:27:03,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 100.0%
2025-08-02 18:27:13,613 - health_monitor - DEBUG - 系统指标 - CPU: 66.9%, 内存: 60.9%, 磁盘: 94.7%
2025-08-02 18:27:18,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 39.3%
2025-08-02 18:27:33,668 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 14.3%
2025-08-02 18:27:48,773 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 74.1%
2025-08-02 18:28:03,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 70.8%
2025-08-02 18:28:14,645 - health_monitor - DEBUG - 系统指标 - CPU: 44.6%, 内存: 66.0%, 磁盘: 94.7%
2025-08-02 18:28:18,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 55.2%
2025-08-02 18:28:34,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 32.1%
2025-08-02 18:28:49,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 33.3%
2025-08-02 18:29:04,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 41.7%
2025-08-02 18:29:15,671 - health_monitor - DEBUG - 系统指标 - CPU: 31.9%, 内存: 69.5%, 磁盘: 94.7%
2025-08-02 18:29:19,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 89.3%
2025-08-02 18:29:34,506 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 29.6%
2025-08-02 18:29:49,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 66.7%
2025-08-02 18:30:04,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 0.0%
2025-08-02 18:30:16,694 - health_monitor - DEBUG - 系统指标 - CPU: 31.5%, 内存: 66.9%, 磁盘: 94.7%
2025-08-02 18:30:19,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 38.5%
2025-08-02 18:30:34,930 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 32.1%
2025-08-02 18:30:50,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 29.2%
2025-08-02 18:31:05,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 42.9%
2025-08-02 18:31:18,158 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 38.9%, 磁盘: 94.7%
2025-08-02 18:31:20,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 40.2%, CPU使用率 100.0%
2025-08-02 18:31:36,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 100.0%
2025-08-02 18:31:52,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.3%, CPU使用率 100.0%
2025-08-02 18:32:07,660 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.7%, CPU使用率 100.0%
2025-08-02 18:32:19,344 - health_monitor - DEBUG - 系统指标 - CPU: 67.2%, 内存: 50.3%, 磁盘: 94.7%
2025-08-02 18:32:22,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 12.5%
2025-08-02 18:32:37,872 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 62.1%
2025-08-02 18:32:52,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 29.6%
2025-08-02 18:33:08,081 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 16.7%
2025-08-02 18:33:20,378 - health_monitor - DEBUG - 系统指标 - CPU: 35.5%, 内存: 48.1%, 磁盘: 94.7%
2025-08-02 18:33:23,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 35.7%
2025-08-02 18:33:38,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 32.1%
2025-08-02 18:33:53,395 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 8.3%
2025-08-02 18:34:08,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 46.4%
2025-08-02 18:34:21,400 - health_monitor - DEBUG - 系统指标 - CPU: 28.4%, 内存: 47.8%, 磁盘: 94.7%
2025-08-02 18:34:23,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 48.3%
2025-08-02 18:34:38,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 15.4%
2025-08-02 18:34:53,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 45.8%
2025-08-02 18:35:08,920 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 55.2%
2025-08-02 18:35:22,426 - health_monitor - DEBUG - 系统指标 - CPU: 57.8%, 内存: 48.1%, 磁盘: 94.7%
2025-08-02 18:35:24,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 46.4%
2025-08-02 18:35:39,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 0.0%
2025-08-02 18:35:54,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 69.2%
2025-08-02 18:36:09,340 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 55.2%
2025-08-02 18:36:23,449 - health_monitor - DEBUG - 系统指标 - CPU: 33.2%, 内存: 47.5%, 磁盘: 94.7%
2025-08-02 18:36:24,445 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 3.8%
2025-08-02 18:36:39,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 79.2%
2025-08-02 18:36:54,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 40.0%
2025-08-02 18:37:09,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 35.7%
2025-08-02 18:37:24,477 - health_monitor - DEBUG - 系统指标 - CPU: 34.5%, 内存: 48.0%, 磁盘: 94.7%
2025-08-02 18:37:24,868 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 51.7%
2025-08-02 18:37:39,973 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 58.3%
2025-08-02 18:37:55,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 39.3%
2025-08-02 18:38:10,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 13.8%
2025-08-02 18:38:25,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 70.8%
2025-08-02 18:38:25,499 - health_monitor - DEBUG - 系统指标 - CPU: 55.2%, 内存: 48.3%, 磁盘: 94.7%
2025-08-02 18:38:40,392 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.4%, CPU使用率 41.7%
2025-08-02 18:38:55,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 7.1%
2025-08-02 18:39:10,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 53.6%
2025-08-02 18:39:25,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 46.2%
2025-08-02 18:39:26,521 - health_monitor - DEBUG - 系统指标 - CPU: 41.5%, 内存: 48.0%, 磁盘: 94.7%
2025-08-02 18:39:40,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 8.3%
2025-08-02 18:39:55,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 60.7%
2025-08-02 18:40:11,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 53.6%
2025-08-02 18:40:26,130 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 29.2%
2025-08-02 18:40:27,546 - health_monitor - DEBUG - 系统指标 - CPU: 38.9%, 内存: 48.1%, 磁盘: 94.7%
2025-08-02 18:40:41,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 20.8%
2025-08-02 18:40:56,345 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 66.7%
2025-08-02 18:41:11,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 33.3%
2025-08-02 18:41:26,563 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 16.7%
2025-08-02 18:41:28,615 - health_monitor - DEBUG - 系统指标 - CPU: 37.0%, 内存: 48.9%, 磁盘: 94.7%
2025-08-02 18:41:41,668 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 28.6%
2025-08-02 18:41:56,775 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 35.7%
2025-08-02 18:42:10,116 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:42:10,117 - main - INFO - 请求没有认证头部
2025-08-02 18:42:10,118 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:42:10,119 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:42:11,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.1%, CPU使用率 42.9%
2025-08-02 18:42:12,174 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:42:12,175 - main - INFO - 请求没有认证头部
2025-08-02 18:42:12,175 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:42:12,177 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:42:12,177 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:42:12,180 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:42:12,181 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:42:12,182 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:42:12,182 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:42:13,223 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:42:13,225 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:42:26,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 37.5%
2025-08-02 18:42:29,641 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 51.9%, 磁盘: 94.7%
2025-08-02 18:42:42,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 37.5%
2025-08-02 18:42:57,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 21.4%
2025-08-02 18:43:12,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.7%, CPU使用率 28.6%
2025-08-02 18:43:27,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 45.8%
2025-08-02 18:43:30,663 - health_monitor - DEBUG - 系统指标 - CPU: 51.8%, 内存: 48.7%, 磁盘: 94.7%
2025-08-02 18:43:42,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 67.9%
2025-08-02 18:43:57,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.6%, CPU使用率 39.3%
2025-08-02 18:44:12,788 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 97.1%
2025-08-02 18:44:27,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 25.0%
2025-08-02 18:44:31,686 - health_monitor - DEBUG - 系统指标 - CPU: 44.0%, 内存: 48.7%, 磁盘: 94.7%
2025-08-02 18:44:43,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 45.8%
2025-08-02 18:44:58,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 79.3%
2025-08-02 18:45:13,213 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 17.9%
2025-08-02 18:45:28,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 33.3%
2025-08-02 18:45:32,707 - health_monitor - DEBUG - 系统指标 - CPU: 31.9%, 内存: 49.0%, 磁盘: 94.7%
2025-08-02 18:45:43,422 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 66.7%
2025-08-02 18:45:58,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 42.9%
2025-08-02 18:46:13,635 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 17.9%
2025-08-02 18:46:28,740 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 76.0%
2025-08-02 18:46:33,731 - health_monitor - DEBUG - 系统指标 - CPU: 42.9%, 内存: 49.0%, 磁盘: 94.7%
2025-08-02 18:46:43,852 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.3%, CPU使用率 35.7%
2025-08-02 18:46:58,958 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.6%, CPU使用率 0.0%
2025-08-02 18:47:14,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 54.2%
2025-08-02 18:47:29,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 36.7%
2025-08-02 18:47:34,756 - health_monitor - DEBUG - 系统指标 - CPU: 30.5%, 内存: 49.4%, 磁盘: 94.7%
2025-08-02 18:47:44,274 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 10.7%
2025-08-02 18:47:59,412 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.4%, CPU使用率 83.3%
2025-08-02 18:48:14,518 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 66.7%
2025-08-02 18:48:29,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.4%, CPU使用率 17.9%
2025-08-02 18:48:33,165 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:48:33,166 - main - INFO - 请求没有认证头部
2025-08-02 18:48:33,167 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:48:33,168 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:48:35,199 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:48:35,199 - main - INFO - 请求没有认证头部
2025-08-02 18:48:35,200 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:48:35,202 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:48:35,202 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:48:35,203 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:48:35,204 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:48:35,205 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:48:35,206 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:48:35,820 - health_monitor - DEBUG - 系统指标 - CPU: 29.9%, 内存: 50.5%, 磁盘: 94.7%
2025-08-02 18:48:36,035 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:48:36,037 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:48:44,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 73.3%
2025-08-02 18:48:59,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 45.8%
