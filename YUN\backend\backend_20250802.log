2025-08-02 18:16:58,570 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-02 18:16:58,576 - auth_service - INFO - 统一认证服务初始化完成
2025-08-02 18:16:58,745 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-02 18:16:58,747 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-02 18:16:59,388 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-08-02 18:17:00,540 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-08-02 18:17:00,542 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-08-02 18:17:00,545 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-08-02 18:17:00,594 - health_monitor - INFO - 健康监控器初始化完成
2025-08-02 18:17:00,604 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-08-02 18:17:00,607 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-08-02 18:17:00,609 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-08-02 18:17:00,615 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-08-02 18:17:00,617 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-08-02 18:17:00,619 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-02 18:17:00,628 - alert_manager - INFO - 已初始化默认告警规则
2025-08-02 18:17:00,628 - alert_manager - INFO - 已初始化默认通知渠道
2025-08-02 18:17:00,629 - alert_manager - INFO - 告警管理器初始化完成
2025-08-02 18:17:01,849 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-08-02 18:17:01,854 - db_service - INFO - 数据库服务初始化完成
2025-08-02 18:17:01,867 - notification_service - INFO - 通知服务初始化完成
2025-08-02 18:17:01,869 - main - INFO - 错误处理模块导入成功
2025-08-02 18:17:01,940 - main - INFO - 监控模块导入成功
2025-08-02 18:17:01,947 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-08-02 18:17:06,128 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-08-02 18:17:06,192 - main - INFO - 应用启动中...
2025-08-02 18:17:06,196 - error_handling - INFO - 错误处理已设置
2025-08-02 18:17:06,197 - main - INFO - 错误处理系统初始化完成
2025-08-02 18:17:06,199 - monitoring - INFO - 添加指标端点成功: /metrics
2025-08-02 18:17:06,201 - monitoring - INFO - 添加健康检查端点成功: /health
2025-08-02 18:17:06,202 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-08-02 18:17:06,204 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-08-02 18:17:06,216 - monitoring - INFO - 启动资源监控线程成功
2025-08-02 18:17:06,217 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-08-02 18:17:06,219 - monitoring - INFO - 监控系统初始化完成
2025-08-02 18:17:06,220 - main - INFO - 监控系统初始化完成
2025-08-02 18:17:06,227 - app.db.init_db - INFO - 所有模型导入成功
2025-08-02 18:17:06,230 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-02 18:17:06,243 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:06,246 - app.db.init_db - INFO - 所有模型导入成功
2025-08-02 18:17:06,248 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-02 18:17:06,249 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-08-02 18:17:06,250 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-08-02 18:17:06,253 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 18:17:06,261 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-08-02 18:17:06,264 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,275 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-08-02 18:17:06,279 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,281 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-08-02 18:17:06,284 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,290 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-08-02 18:17:06,292 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,297 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-08-02 18:17:06,299 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,304 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-08-02 18:17:06,307 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,310 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-08-02 18:17:06,313 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,315 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-08-02 18:17:06,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.1%, CPU使用率 100.0%
2025-08-02 18:17:06,321 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,325 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-08-02 18:17:06,329 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,332 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-08-02 18:17:06,338 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,341 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-08-02 18:17:06,343 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,349 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-08-02 18:17:06,354 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,357 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-08-02 18:17:06,359 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,366 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-08-02 18:17:06,369 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,371 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-08-02 18:17:06,373 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,376 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-08-02 18:17:06,382 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,386 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-08-02 18:17:06,388 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,390 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-08-02 18:17:06,392 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,398 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-08-02 18:17:06,401 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,403 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-08-02 18:17:06,405 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,408 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-08-02 18:17:06,411 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,417 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-08-02 18:17:06,419 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,421 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-08-02 18:17:06,423 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,427 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-08-02 18:17:06,432 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,434 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-08-02 18:17:06,436 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,438 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-08-02 18:17:06,440 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,447 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-08-02 18:17:06,449 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,452 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-08-02 18:17:06,454 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,456 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-08-02 18:17:06,458 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,465 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-08-02 18:17:06,467 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,470 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-08-02 18:17:06,472 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,475 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-08-02 18:17:06,481 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,483 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-08-02 18:17:06,486 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,488 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-08-02 18:17:06,496 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,499 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-08-02 18:17:06,501 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,504 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-08-02 18:17:06,509 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,515 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-08-02 18:17:06,517 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,519 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-08-02 18:17:06,522 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,530 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-08-02 18:17:06,532 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,534 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-08-02 18:17:06,537 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,543 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-08-02 18:17:06,546 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,548 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-08-02 18:17:06,551 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 18:17:06,556 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 18:17:06,559 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-08-02 18:17:06,565 - app.db.init_db - INFO - 模型关系初始化完成
2025-08-02 18:17:06,567 - app.db.init_db - INFO - 模型关系设置完成
2025-08-02 18:17:06,568 - main - INFO - 数据库初始化完成（强制重建）
2025-08-02 18:17:06,571 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:06,576 - main - INFO - 数据库连接正常
2025-08-02 18:17:06,578 - main - INFO - 开始初始化模板数据
2025-08-02 18:17:06,580 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:07,108 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-08-02 18:17:07,188 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-08-02 18:17:07,268 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-08-02 18:17:07,335 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-08-02 18:17:07,338 - main - INFO - 模板数据初始化完成
2025-08-02 18:17:07,341 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-08-02 18:17:07,342 - main - INFO - 应用启动完成
2025-08-02 18:17:21,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.3%, CPU使用率 100.0%
2025-08-02 18:17:26,289 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:17:26,512 - main - INFO - 请求没有认证头部
2025-08-02 18:17:26,653 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:17:26,754 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:17:29,285 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:17:29,302 - main - INFO - 请求没有认证头部
2025-08-02 18:17:29,305 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:17:29,338 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:17:29,352 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:17:29,359 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:17:29,364 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:17:29,367 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:17:29,369 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:17:32,381 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:17:32,395 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:17:36,719 - monitoring - DEBUG - 资源指标更新: 内存使用率 62.1%, CPU使用率 70.8%
2025-08-02 18:17:51,833 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 79.2%
2025-08-02 18:18:01,567 - health_monitor - DEBUG - 系统指标 - CPU: 14.0%, 内存: 61.0%, 磁盘: 94.7%
2025-08-02 18:18:06,940 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 16.7%
2025-08-02 18:18:22,051 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.4%, CPU使用率 70.8%
2025-08-02 18:18:37,157 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.0%, CPU使用率 45.8%
2025-08-02 18:18:52,263 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 45.8%
2025-08-02 18:19:02,595 - health_monitor - DEBUG - 系统指标 - CPU: 36.9%, 内存: 58.2%, 磁盘: 94.7%
2025-08-02 18:19:07,369 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 42.9%
2025-08-02 18:19:22,652 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.9%, CPU使用率 100.0%
2025-08-02 18:19:37,788 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 75.0%
2025-08-02 18:19:52,894 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 38.5%
2025-08-02 18:20:03,910 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 59.4%, 磁盘: 94.7%
2025-08-02 18:20:08,002 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 57.1%
2025-08-02 18:20:23,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 100.0%
2025-08-02 18:20:38,225 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.7%, CPU使用率 38.5%
2025-08-02 18:20:53,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 20.8%
2025-08-02 18:21:04,946 - health_monitor - DEBUG - 系统指标 - CPU: 38.3%, 内存: 58.8%, 磁盘: 94.7%
2025-08-02 18:21:08,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.3%, CPU使用率 25.0%
2025-08-02 18:21:23,543 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 48.3%
2025-08-02 18:21:38,649 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 25.0%
2025-08-02 18:21:53,756 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.5%, CPU使用率 0.0%
2025-08-02 18:22:05,968 - health_monitor - DEBUG - 系统指标 - CPU: 27.7%, 内存: 58.4%, 磁盘: 94.7%
2025-08-02 18:22:08,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 32.0%
2025-08-02 18:22:23,968 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 17.9%
2025-08-02 18:22:39,073 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.4%, CPU使用率 15.4%
2025-08-02 18:22:54,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 66.7%
2025-08-02 18:23:00,637 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-08-02 18:23:06,990 - health_monitor - DEBUG - 系统指标 - CPU: 43.3%, 内存: 58.8%, 磁盘: 94.7%
2025-08-02 18:23:09,294 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.8%, CPU使用率 71.4%
2025-08-02 18:23:24,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 60.6%, CPU使用率 66.7%
2025-08-02 18:23:32,982 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:23:32,997 - main - INFO - 请求没有认证头部
2025-08-02 18:23:32,998 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:23:33,000 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:23:35,061 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:23:35,063 - main - INFO - 请求没有认证头部
2025-08-02 18:23:35,066 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:23:35,068 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:23:35,069 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:23:35,071 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:23:35,072 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:23:35,074 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:23:35,080 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:23:36,578 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:23:36,584 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:23:39,585 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.9%, CPU使用率 96.8%
2025-08-02 18:23:54,696 - monitoring - DEBUG - 资源指标更新: 内存使用率 64.6%, CPU使用率 71.4%
2025-08-02 18:24:00,643 - alert_manager - WARNING - 触发告警: disk_usage, 当前值: 94.7, 阈值: 90
2025-08-02 18:24:08,014 - health_monitor - DEBUG - 系统指标 - CPU: 74.9%, 内存: 61.6%, 磁盘: 94.7%
2025-08-02 18:24:09,809 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.5%, CPU使用率 69.0%
2025-08-02 18:24:24,917 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.1%, CPU使用率 20.7%
2025-08-02 18:24:40,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.0%, CPU使用率 100.0%
2025-08-02 18:24:55,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.6%, CPU使用率 75.0%
2025-08-02 18:24:58,570 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:24:58,572 - main - INFO - 请求没有认证头部
2025-08-02 18:24:58,575 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:24:58,578 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:25:00,645 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:25:00,647 - main - INFO - 请求没有认证头部
2025-08-02 18:25:00,650 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:25:00,653 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:25:00,654 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:25:00,656 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:25:00,657 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:25:00,659 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:25:00,660 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:25:01,594 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:25:01,601 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:25:09,042 - health_monitor - DEBUG - 系统指标 - CPU: 47.9%, 内存: 63.3%, 磁盘: 94.7%
2025-08-02 18:25:10,323 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.3%, CPU使用率 10.7%
2025-08-02 18:25:25,429 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.4%, CPU使用率 19.2%
2025-08-02 18:25:40,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 96.6%
2025-08-02 18:25:56,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 40.0%, CPU使用率 100.0%
2025-08-02 18:26:11,034 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 45.9%, 磁盘: 94.7%
2025-08-02 18:26:12,404 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.0%, CPU使用率 100.0%
2025-08-02 18:26:30,782 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.9%, CPU使用率 100.0%
2025-08-02 18:26:47,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 58.2%, CPU使用率 100.0%
2025-08-02 18:27:03,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.1%, CPU使用率 100.0%
2025-08-02 18:27:13,613 - health_monitor - DEBUG - 系统指标 - CPU: 66.9%, 内存: 60.9%, 磁盘: 94.7%
2025-08-02 18:27:18,562 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.9%, CPU使用率 39.3%
2025-08-02 18:27:33,668 - monitoring - DEBUG - 资源指标更新: 内存使用率 59.5%, CPU使用率 14.3%
2025-08-02 18:27:48,773 - monitoring - DEBUG - 资源指标更新: 内存使用率 61.0%, CPU使用率 74.1%
2025-08-02 18:28:03,879 - monitoring - DEBUG - 资源指标更新: 内存使用率 63.9%, CPU使用率 70.8%
2025-08-02 18:28:14,645 - health_monitor - DEBUG - 系统指标 - CPU: 44.6%, 内存: 66.0%, 磁盘: 94.7%
2025-08-02 18:28:18,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.6%, CPU使用率 55.2%
2025-08-02 18:28:34,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 32.1%
2025-08-02 18:28:49,193 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 33.3%
2025-08-02 18:29:04,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 41.7%
2025-08-02 18:29:15,671 - health_monitor - DEBUG - 系统指标 - CPU: 31.9%, 内存: 69.5%, 磁盘: 94.7%
2025-08-02 18:29:19,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 89.3%
2025-08-02 18:29:34,506 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 29.6%
2025-08-02 18:29:49,611 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 66.7%
2025-08-02 18:30:04,715 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 0.0%
2025-08-02 18:30:16,694 - health_monitor - DEBUG - 系统指标 - CPU: 31.5%, 内存: 66.9%, 磁盘: 94.7%
2025-08-02 18:30:19,826 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.9%, CPU使用率 38.5%
2025-08-02 18:30:34,930 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.0%, CPU使用率 32.1%
2025-08-02 18:30:50,035 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.7%, CPU使用率 29.2%
2025-08-02 18:31:05,139 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 42.9%
2025-08-02 18:31:18,158 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 38.9%, 磁盘: 94.7%
2025-08-02 18:31:20,777 - monitoring - DEBUG - 资源指标更新: 内存使用率 40.2%, CPU使用率 100.0%
2025-08-02 18:31:36,292 - monitoring - DEBUG - 资源指标更新: 内存使用率 46.2%, CPU使用率 100.0%
2025-08-02 18:31:52,279 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.3%, CPU使用率 100.0%
2025-08-02 18:32:07,660 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.7%, CPU使用率 100.0%
2025-08-02 18:32:19,344 - health_monitor - DEBUG - 系统指标 - CPU: 67.2%, 内存: 50.3%, 磁盘: 94.7%
2025-08-02 18:32:22,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 12.5%
2025-08-02 18:32:37,872 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 62.1%
2025-08-02 18:32:52,977 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 29.6%
2025-08-02 18:33:08,081 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 16.7%
2025-08-02 18:33:20,378 - health_monitor - DEBUG - 系统指标 - CPU: 35.5%, 内存: 48.1%, 磁盘: 94.7%
2025-08-02 18:33:23,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 35.7%
2025-08-02 18:33:38,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 32.1%
2025-08-02 18:33:53,395 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 8.3%
2025-08-02 18:34:08,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 46.4%
2025-08-02 18:34:21,400 - health_monitor - DEBUG - 系统指标 - CPU: 28.4%, 内存: 47.8%, 磁盘: 94.7%
2025-08-02 18:34:23,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 48.3%
2025-08-02 18:34:38,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 15.4%
2025-08-02 18:34:53,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 45.8%
2025-08-02 18:35:08,920 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 55.2%
2025-08-02 18:35:22,426 - health_monitor - DEBUG - 系统指标 - CPU: 57.8%, 内存: 48.1%, 磁盘: 94.7%
2025-08-02 18:35:24,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 46.4%
2025-08-02 18:35:39,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.8%, CPU使用率 0.0%
2025-08-02 18:35:54,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 69.2%
2025-08-02 18:36:09,340 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 55.2%
2025-08-02 18:36:23,449 - health_monitor - DEBUG - 系统指标 - CPU: 33.2%, 内存: 47.5%, 磁盘: 94.7%
2025-08-02 18:36:24,445 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.6%, CPU使用率 3.8%
2025-08-02 18:36:39,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 79.2%
2025-08-02 18:36:54,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 40.0%
2025-08-02 18:37:09,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.7%, CPU使用率 35.7%
2025-08-02 18:37:24,477 - health_monitor - DEBUG - 系统指标 - CPU: 34.5%, 内存: 48.0%, 磁盘: 94.7%
2025-08-02 18:37:24,868 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.0%, CPU使用率 51.7%
2025-08-02 18:37:39,973 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 58.3%
2025-08-02 18:37:55,077 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 39.3%
2025-08-02 18:38:10,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 13.8%
2025-08-02 18:38:25,287 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.3%, CPU使用率 70.8%
2025-08-02 18:38:25,499 - health_monitor - DEBUG - 系统指标 - CPU: 55.2%, 内存: 48.3%, 磁盘: 94.7%
2025-08-02 18:38:40,392 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.4%, CPU使用率 41.7%
2025-08-02 18:38:55,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 7.1%
2025-08-02 18:39:10,604 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 53.6%
2025-08-02 18:39:25,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 46.2%
2025-08-02 18:39:26,521 - health_monitor - DEBUG - 系统指标 - CPU: 41.5%, 内存: 48.0%, 磁盘: 94.7%
2025-08-02 18:39:40,814 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 8.3%
2025-08-02 18:39:55,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 60.7%
2025-08-02 18:40:11,025 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.2%, CPU使用率 53.6%
2025-08-02 18:40:26,130 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.1%, CPU使用率 29.2%
2025-08-02 18:40:27,546 - health_monitor - DEBUG - 系统指标 - CPU: 38.9%, 内存: 48.1%, 磁盘: 94.7%
2025-08-02 18:40:41,235 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 20.8%
2025-08-02 18:40:56,345 - monitoring - DEBUG - 资源指标更新: 内存使用率 47.9%, CPU使用率 66.7%
2025-08-02 18:41:11,458 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 33.3%
2025-08-02 18:41:26,563 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 16.7%
2025-08-02 18:41:28,615 - health_monitor - DEBUG - 系统指标 - CPU: 37.0%, 内存: 48.9%, 磁盘: 94.7%
2025-08-02 18:41:41,668 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 28.6%
2025-08-02 18:41:56,775 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.5%, CPU使用率 35.7%
2025-08-02 18:42:10,116 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:42:10,117 - main - INFO - 请求没有认证头部
2025-08-02 18:42:10,118 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:42:10,119 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:42:11,882 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.1%, CPU使用率 42.9%
2025-08-02 18:42:12,174 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:42:12,175 - main - INFO - 请求没有认证头部
2025-08-02 18:42:12,175 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:42:12,177 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:42:12,177 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:42:12,180 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:42:12,181 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:42:12,182 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:42:12,182 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:42:13,223 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:42:13,225 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:42:26,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 37.5%
2025-08-02 18:42:29,641 - health_monitor - DEBUG - 系统指标 - CPU: 19.5%, 内存: 51.9%, 磁盘: 94.7%
2025-08-02 18:42:42,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 37.5%
2025-08-02 18:42:57,200 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 21.4%
2025-08-02 18:43:12,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.7%, CPU使用率 28.6%
2025-08-02 18:43:27,408 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 45.8%
2025-08-02 18:43:30,663 - health_monitor - DEBUG - 系统指标 - CPU: 51.8%, 内存: 48.7%, 磁盘: 94.7%
2025-08-02 18:43:42,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 67.9%
2025-08-02 18:43:57,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.6%, CPU使用率 39.3%
2025-08-02 18:44:12,788 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 97.1%
2025-08-02 18:44:27,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 25.0%
2025-08-02 18:44:31,686 - health_monitor - DEBUG - 系统指标 - CPU: 44.0%, 内存: 48.7%, 磁盘: 94.7%
2025-08-02 18:44:43,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 45.8%
2025-08-02 18:44:58,109 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 79.3%
2025-08-02 18:45:13,213 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 17.9%
2025-08-02 18:45:28,317 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 33.3%
2025-08-02 18:45:32,707 - health_monitor - DEBUG - 系统指标 - CPU: 31.9%, 内存: 49.0%, 磁盘: 94.7%
2025-08-02 18:45:43,422 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 66.7%
2025-08-02 18:45:58,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 42.9%
2025-08-02 18:46:13,635 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 17.9%
2025-08-02 18:46:28,740 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 76.0%
2025-08-02 18:46:33,731 - health_monitor - DEBUG - 系统指标 - CPU: 42.9%, 内存: 49.0%, 磁盘: 94.7%
2025-08-02 18:46:43,852 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.3%, CPU使用率 35.7%
2025-08-02 18:46:58,958 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.6%, CPU使用率 0.0%
2025-08-02 18:47:14,064 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 54.2%
2025-08-02 18:47:29,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 36.7%
2025-08-02 18:47:34,756 - health_monitor - DEBUG - 系统指标 - CPU: 30.5%, 内存: 49.4%, 磁盘: 94.7%
2025-08-02 18:47:44,274 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 10.7%
2025-08-02 18:47:59,412 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.4%, CPU使用率 83.3%
2025-08-02 18:48:14,518 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 66.7%
2025-08-02 18:48:29,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.4%, CPU使用率 17.9%
2025-08-02 18:48:33,165 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:48:33,166 - main - INFO - 请求没有认证头部
2025-08-02 18:48:33,167 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:48:33,168 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:48:35,199 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:48:35,199 - main - INFO - 请求没有认证头部
2025-08-02 18:48:35,200 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:48:35,202 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:48:35,202 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:48:35,203 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:48:35,204 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:48:35,205 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:48:35,206 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:48:35,820 - health_monitor - DEBUG - 系统指标 - CPU: 29.9%, 内存: 50.5%, 磁盘: 94.7%
2025-08-02 18:48:36,035 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:48:36,037 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:48:44,728 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.1%, CPU使用率 73.3%
2025-08-02 18:48:59,834 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 45.8%
2025-08-02 18:49:14,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 37.5%
2025-08-02 18:49:30,043 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 31.0%
2025-08-02 18:49:36,917 - health_monitor - DEBUG - 系统指标 - CPU: 31.5%, 内存: 49.2%, 磁盘: 94.7%
2025-08-02 18:49:45,148 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 53.6%
2025-08-02 18:50:00,253 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.3%, CPU使用率 16.7%
2025-08-02 18:50:15,358 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.5%, CPU使用率 10.7%
2025-08-02 18:50:30,485 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 90.9%
2025-08-02 18:50:37,939 - health_monitor - DEBUG - 系统指标 - CPU: 42.6%, 内存: 49.3%, 磁盘: 94.7%
2025-08-02 18:50:45,591 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 28.6%
2025-08-02 18:51:00,698 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.4%, CPU使用率 42.3%
2025-08-02 18:51:15,802 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 37.5%
2025-08-02 18:51:30,908 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 33.3%
2025-08-02 18:51:38,961 - health_monitor - DEBUG - 系统指标 - CPU: 40.5%, 内存: 48.8%, 磁盘: 94.7%
2025-08-02 18:51:46,012 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.6%, CPU使用率 10.7%
2025-08-02 18:52:01,238 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 71.0%
2025-08-02 18:52:16,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 35.5%
2025-08-02 18:52:31,447 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.9%, CPU使用率 51.7%
2025-08-02 18:52:39,990 - health_monitor - DEBUG - 系统指标 - CPU: 37.2%, 内存: 49.1%, 磁盘: 94.7%
2025-08-02 18:52:46,553 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 48.1%
2025-08-02 18:53:01,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 0.0%
2025-08-02 18:53:16,765 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 44.8%
2025-08-02 18:53:31,870 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 64.3%
2025-08-02 18:53:41,011 - health_monitor - DEBUG - 系统指标 - CPU: 31.8%, 内存: 49.0%, 磁盘: 94.7%
2025-08-02 18:53:46,975 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.3%, CPU使用率 7.4%
2025-08-02 18:53:59,820 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:53:59,821 - main - INFO - 请求没有认证头部
2025-08-02 18:53:59,822 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:53:59,823 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:54:01,864 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:54:01,865 - main - INFO - 请求没有认证头部
2025-08-02 18:54:01,866 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:54:01,867 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:54:01,871 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:54:01,872 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:54:01,874 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:54:01,875 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:54:01,875 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:54:02,121 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.6%, CPU使用率 73.0%
2025-08-02 18:54:05,349 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:54:05,408 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:54:17,319 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.3%, CPU使用率 44.8%
2025-08-02 18:54:32,424 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.5%, CPU使用率 25.0%
2025-08-02 18:54:42,034 - health_monitor - DEBUG - 系统指标 - CPU: 26.4%, 内存: 52.5%, 磁盘: 94.7%
2025-08-02 18:54:47,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.5%, CPU使用率 0.0%
2025-08-02 18:55:02,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.3%, CPU使用率 28.6%
2025-08-02 18:55:17,737 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 12.5%
2025-08-02 18:55:32,841 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.3%, CPU使用率 32.1%
2025-08-02 18:55:43,058 - health_monitor - DEBUG - 系统指标 - CPU: 35.8%, 内存: 49.2%, 磁盘: 94.7%
2025-08-02 18:55:47,962 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 25.0%
2025-08-02 18:56:03,066 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.3%, CPU使用率 0.0%
2025-08-02 18:56:18,171 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 39.3%
2025-08-02 18:56:33,275 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 34.5%
2025-08-02 18:56:44,079 - health_monitor - DEBUG - 系统指标 - CPU: 27.9%, 内存: 49.2%, 磁盘: 94.7%
2025-08-02 18:56:48,381 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 4.2%
2025-08-02 18:57:03,486 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.7%, CPU使用率 41.7%
2025-08-02 18:57:18,590 - monitoring - DEBUG - 资源指标更新: 内存使用率 48.8%, CPU使用率 50.0%
2025-08-02 18:57:33,694 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.0%, CPU使用率 23.1%
2025-08-02 18:57:45,101 - health_monitor - DEBUG - 系统指标 - CPU: 36.7%, 内存: 49.0%, 磁盘: 94.7%
2025-08-02 18:57:48,799 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.1%, CPU使用率 33.3%
2025-08-02 18:58:03,904 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.5%, CPU使用率 64.3%
2025-08-02 18:58:19,009 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 19.2%
2025-08-02 18:58:34,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.5%, CPU使用率 4.2%
2025-08-02 18:58:46,123 - health_monitor - DEBUG - 系统指标 - CPU: 37.4%, 内存: 49.4%, 磁盘: 94.7%
2025-08-02 18:58:49,221 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.2%, CPU使用率 79.2%
2025-08-02 18:59:04,326 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.5%, CPU使用率 21.4%
2025-08-02 18:59:19,432 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.4%, CPU使用率 14.8%
2025-08-02 18:59:34,536 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.8%, CPU使用率 60.0%
2025-08-02 18:59:47,146 - health_monitor - DEBUG - 系统指标 - CPU: 32.6%, 内存: 50.6%, 磁盘: 94.7%
2025-08-02 18:59:47,509 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 18:59:47,510 - main - INFO - 请求没有认证头部
2025-08-02 18:59:47,511 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:59:47,512 - main - INFO - --- 请求结束: 200 ---

2025-08-02 18:59:49,552 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 18:59:49,553 - main - INFO - 请求没有认证头部
2025-08-02 18:59:49,554 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 18:59:49,556 - app.core.db_connection - DEBUG - 当前线程ID: 4656
2025-08-02 18:59:49,557 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 18:59:49,558 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 18:59:49,559 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 18:59:49,560 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 18:59:49,561 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 18:59:49,561 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 18:59:49,562 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 18:59:49,563 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 18:59:49,564 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 18:59:49,565 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 18:59:49,662 - monitoring - DEBUG - 资源指标更新: 内存使用率 50.2%, CPU使用率 39.3%
2025-08-02 18:59:50,438 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 18:59:50,442 - main - INFO - --- 请求结束: 200 ---

2025-08-02 19:00:04,842 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.2%, CPU使用率 34.6%
2025-08-02 19:00:19,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 46.4%
2025-08-02 19:00:35,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.0%, CPU使用率 0.0%
2025-08-02 19:00:48,173 - health_monitor - DEBUG - 系统指标 - CPU: 49.2%, 内存: 52.6%, 磁盘: 94.7%
2025-08-02 19:00:50,159 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.8%, CPU使用率 29.2%
2025-08-02 19:01:05,265 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.6%, CPU使用率 50.0%
2025-08-02 19:01:20,370 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.6%, CPU使用率 7.1%
2025-08-02 19:01:35,478 - monitoring - DEBUG - 资源指标更新: 内存使用率 49.8%, CPU使用率 64.3%
2025-08-02 19:01:49,995 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 52.2%, 磁盘: 94.7%
2025-08-02 19:01:51,218 - monitoring - DEBUG - 资源指标更新: 内存使用率 52.4%, CPU使用率 100.0%
2025-08-02 19:02:07,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 51.9%, CPU使用率 100.0%
2025-08-02 19:02:22,774 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.0%, CPU使用率 23.1%
2025-08-02 19:02:37,878 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.7%, CPU使用率 50.0%
2025-08-02 19:02:51,228 - health_monitor - DEBUG - 系统指标 - CPU: 30.5%, 内存: 33.7%, 磁盘: 94.7%
2025-08-02 19:02:52,982 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.7%, CPU使用率 64.0%
2025-08-02 19:03:08,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.4%, CPU使用率 36.7%
2025-08-02 19:03:23,190 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.9%, CPU使用率 62.5%
2025-08-02 19:03:38,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.5%, CPU使用率 72.4%
2025-08-02 19:03:52,250 - health_monitor - DEBUG - 系统指标 - CPU: 72.0%, 内存: 35.4%, 磁盘: 94.7%
2025-08-02 19:03:53,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.4%, CPU使用率 75.0%
2025-08-02 19:04:08,504 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.3%, CPU使用率 79.2%
2025-08-02 19:04:23,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.2%, CPU使用率 87.5%
2025-08-02 19:04:38,718 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.2%, CPU使用率 53.6%
2025-08-02 19:04:53,271 - health_monitor - DEBUG - 系统指标 - CPU: 52.3%, 内存: 35.2%, 磁盘: 94.7%
2025-08-02 19:04:53,821 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.2%, CPU使用率 53.6%
2025-08-02 19:05:08,925 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.7%, CPU使用率 66.7%
2025-08-02 19:05:24,030 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.9%, CPU使用率 62.5%
2025-08-02 19:05:39,135 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.0%, CPU使用率 0.0%
2025-08-02 19:05:54,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.9%, CPU使用率 33.3%
2025-08-02 19:05:54,293 - health_monitor - DEBUG - 系统指标 - CPU: 41.1%, 内存: 33.9%, 磁盘: 94.7%
2025-08-02 19:06:09,343 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.7%, CPU使用率 44.0%
2025-08-02 19:06:24,448 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.8%, CPU使用率 26.9%
2025-08-02 19:06:39,551 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.8%, CPU使用率 16.7%
2025-08-02 19:06:54,655 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.8%, CPU使用率 68.0%
2025-08-02 19:06:55,316 - health_monitor - DEBUG - 系统指标 - CPU: 36.8%, 内存: 33.8%, 磁盘: 94.6%
2025-08-02 19:07:09,760 - monitoring - DEBUG - 资源指标更新: 内存使用率 33.8%, CPU使用率 37.9%
2025-08-02 19:07:24,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.0%, CPU使用率 38.5%
2025-08-02 19:07:39,971 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.0%, CPU使用率 29.2%
2025-08-02 19:07:55,075 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.2%, CPU使用率 58.3%
2025-08-02 19:07:56,345 - health_monitor - DEBUG - 系统指标 - CPU: 35.9%, 内存: 34.2%, 磁盘: 94.5%
2025-08-02 19:08:10,188 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.2%, CPU使用率 81.5%
2025-08-02 19:08:25,295 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.3%, CPU使用率 67.9%
2025-08-02 19:08:40,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 46.4%
2025-08-02 19:08:55,504 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.6%, CPU使用率 62.5%
2025-08-02 19:08:57,374 - health_monitor - DEBUG - 系统指标 - CPU: 42.0%, 内存: 34.6%, 磁盘: 94.5%
2025-08-02 19:09:10,609 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 25.0%
2025-08-02 19:09:25,714 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 46.4%
2025-08-02 19:09:40,817 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.3%, CPU使用率 54.2%
2025-08-02 19:09:55,922 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.6%, CPU使用率 58.3%
2025-08-02 19:09:58,401 - health_monitor - DEBUG - 系统指标 - CPU: 61.2%, 内存: 35.5%, 磁盘: 94.4%
2025-08-02 19:10:11,029 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.9%, CPU使用率 89.3%
2025-08-02 19:10:26,136 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.2%, CPU使用率 100.0%
2025-08-02 19:10:41,240 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.3%, CPU使用率 37.5%
2025-08-02 19:10:56,344 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.1%, CPU使用率 29.2%
2025-08-02 19:10:59,422 - health_monitor - DEBUG - 系统指标 - CPU: 47.5%, 内存: 36.1%, 磁盘: 94.4%
2025-08-02 19:11:11,573 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.2%, CPU使用率 100.0%
2025-08-02 19:11:26,731 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.1%, CPU使用率 65.5%
2025-08-02 19:11:41,835 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.2%, CPU使用率 22.2%
2025-08-02 19:11:56,939 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.1%, CPU使用率 41.7%
2025-08-02 19:12:00,446 - health_monitor - DEBUG - 系统指标 - CPU: 49.2%, 内存: 36.2%, 磁盘: 94.3%
2025-08-02 19:12:12,045 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.3%, CPU使用率 64.3%
2025-08-02 19:12:27,150 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.3%, CPU使用率 30.8%
2025-08-02 19:12:42,265 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.2%, CPU使用率 96.4%
2025-08-02 19:12:57,372 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.4%, CPU使用率 42.9%
2025-08-02 19:13:01,469 - health_monitor - DEBUG - 系统指标 - CPU: 45.4%, 内存: 36.3%, 磁盘: 93.8%
2025-08-02 19:13:12,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.5%, CPU使用率 64.3%
2025-08-02 19:13:27,582 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.5%, CPU使用率 0.0%
2025-08-02 19:13:42,686 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.4%, CPU使用率 35.7%
2025-08-02 19:13:57,791 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.7%, CPU使用率 64.3%
2025-08-02 19:14:02,492 - health_monitor - DEBUG - 系统指标 - CPU: 42.8%, 内存: 36.7%, 磁盘: 92.5%
2025-08-02 19:14:12,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.7%, CPU使用率 54.2%
2025-08-02 19:14:27,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.6%, CPU使用率 25.0%
2025-08-02 19:14:43,103 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.4%, CPU使用率 35.7%
2025-08-02 19:14:58,225 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.2%, CPU使用率 71.4%
2025-08-02 19:15:03,513 - health_monitor - DEBUG - 系统指标 - CPU: 27.9%, 内存: 35.1%, 磁盘: 92.5%
2025-08-02 19:15:13,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.1%, CPU使用率 8.3%
2025-08-02 19:15:28,435 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.1%, CPU使用率 53.6%
2025-08-02 19:15:43,542 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.2%, CPU使用率 17.9%
2025-08-02 19:15:58,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 23.1%
2025-08-02 19:16:04,532 - health_monitor - DEBUG - 系统指标 - CPU: 27.9%, 内存: 34.9%, 磁盘: 92.5%
2025-08-02 19:16:14,177 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.9%, CPU使用率 100.0%
2025-08-02 19:16:29,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 41.4%
2025-08-02 19:16:44,394 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 37.5%
2025-08-02 19:16:59,498 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 14.3%
2025-08-02 19:17:05,570 - health_monitor - DEBUG - 系统指标 - CPU: 51.4%, 内存: 35.0%, 磁盘: 92.5%
2025-08-02 19:17:14,641 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 84.6%
2025-08-02 19:17:29,745 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 39.3%
2025-08-02 19:17:44,850 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.9%, CPU使用率 21.4%
2025-08-02 19:17:59,954 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.9%, CPU使用率 37.5%
2025-08-02 19:18:06,592 - health_monitor - DEBUG - 系统指标 - CPU: 48.6%, 内存: 34.8%, 磁盘: 92.5%
2025-08-02 19:18:15,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.9%, CPU使用率 75.0%
2025-08-02 19:18:30,163 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 15.4%
2025-08-02 19:18:45,268 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.1%, CPU使用率 29.2%
2025-08-02 19:19:00,372 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.1%, CPU使用率 39.3%
2025-08-02 19:19:07,614 - health_monitor - DEBUG - 系统指标 - CPU: 42.6%, 内存: 35.1%, 磁盘: 92.5%
2025-08-02 19:19:15,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 39.3%
2025-08-02 19:19:30,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.3%, CPU使用率 12.5%
2025-08-02 19:19:45,685 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.3%, CPU使用率 32.1%
2025-08-02 19:20:00,789 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.3%, CPU使用率 32.1%
2025-08-02 19:20:08,636 - health_monitor - DEBUG - 系统指标 - CPU: 34.5%, 内存: 35.3%, 磁盘: 92.5%
2025-08-02 19:20:15,895 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.3%, CPU使用率 45.8%
2025-08-02 19:20:31,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.4%, CPU使用率 25.0%
2025-08-02 19:20:46,104 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.3%, CPU使用率 33.3%
2025-08-02 19:21:01,209 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.1%, CPU使用率 8.3%
2025-08-02 19:21:09,659 - health_monitor - DEBUG - 系统指标 - CPU: 61.3%, 内存: 34.4%, 磁盘: 92.5%
2025-08-02 19:21:16,340 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.3%, CPU使用率 79.3%
2025-08-02 19:21:31,446 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 89.7%
2025-08-02 19:21:46,551 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 20.8%
2025-08-02 19:22:01,654 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 42.9%
2025-08-02 19:22:10,682 - health_monitor - DEBUG - 系统指标 - CPU: 50.8%, 内存: 34.7%, 磁盘: 92.3%
2025-08-02 19:22:16,759 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 35.7%
2025-08-02 19:22:31,863 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.6%, CPU使用率 45.8%
2025-08-02 19:22:46,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 7.1%
2025-08-02 19:23:02,071 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.6%, CPU使用率 46.7%
2025-08-02 19:23:11,715 - health_monitor - DEBUG - 系统指标 - CPU: 46.7%, 内存: 34.6%, 磁盘: 92.3%
2025-08-02 19:23:17,348 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 81.0%
2025-08-02 19:23:32,465 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.6%, CPU使用率 35.7%
2025-08-02 19:23:47,571 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 14.8%
2025-08-02 19:24:02,676 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.7%, CPU使用率 29.2%
2025-08-02 19:24:12,736 - health_monitor - DEBUG - 系统指标 - CPU: 26.5%, 内存: 34.6%, 磁盘: 92.3%
2025-08-02 19:24:17,780 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 58.3%
2025-08-02 19:24:32,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.4%, CPU使用率 7.7%
2025-08-02 19:24:47,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 12.0%
2025-08-02 19:25:03,095 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.5%, CPU使用率 50.0%
2025-08-02 19:25:13,761 - health_monitor - DEBUG - 系统指标 - CPU: 55.0%, 内存: 34.8%, 磁盘: 92.3%
2025-08-02 19:25:18,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.0%, CPU使用率 39.3%
2025-08-02 19:25:33,315 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.2%, CPU使用率 41.7%
2025-08-02 19:25:48,419 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.4%, CPU使用率 100.0%
2025-08-02 19:26:03,523 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.5%, CPU使用率 59.3%
2025-08-02 19:26:14,785 - health_monitor - DEBUG - 系统指标 - CPU: 58.3%, 内存: 35.7%, 磁盘: 91.5%
2025-08-02 19:26:18,628 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.7%, CPU使用率 50.0%
2025-08-02 19:26:33,733 - monitoring - DEBUG - 资源指标更新: 内存使用率 35.9%, CPU使用率 50.0%
2025-08-02 19:26:48,839 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.1%, CPU使用率 60.7%
2025-08-02 19:27:03,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 36.4%, CPU使用率 48.1%
2025-08-02 19:27:15,812 - health_monitor - DEBUG - 系统指标 - CPU: 60.1%, 内存: 36.3%, 磁盘: 91.2%
2025-08-02 19:27:19,049 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.9%, CPU使用率 34.6%
2025-08-02 19:27:34,154 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.8%, CPU使用率 41.4%
2025-08-02 19:27:49,258 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.9%, CPU使用率 40.7%
2025-08-02 19:28:04,362 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.8%, CPU使用率 8.3%
2025-08-02 19:28:16,834 - health_monitor - DEBUG - 系统指标 - CPU: 29.5%, 内存: 34.7%, 磁盘: 91.2%
2025-08-02 19:28:19,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 34.6%, CPU使用率 35.7%
2025-08-02 23:01:59,454 - app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-02 23:01:59,462 - auth_service - INFO - 统一认证服务初始化完成
2025-08-02 23:01:59,778 - backend.app.core.db_connection - INFO - 数据库引擎创建成功: sqlite:///C:\Users\<USER>\Desktop\health-Trea\YUN\backend\app.db
2025-08-02 23:01:59,825 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-02 23:02:00,641 - BackendMockDataManager - INFO - 后端模拟数据模式已启用
2025-08-02 23:02:02,243 - root - INFO - 成功导入psutil模块，路径: C:\Users\<USER>\Desktop\health-Trea\myenv\Lib\site-packages\psutil\__init__.py
2025-08-02 23:02:02,246 - fallback_manager - DEBUG - 当前Python路径: ['C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\myenv\\Lib\\site-packages\\Pythonwin', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN\\backend', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN', 'C:\\Users\\<USER>\\Desktop\\health-Trea\\YUN']
2025-08-02 23:02:02,251 - fallback_manager - INFO - 依赖 psutil (psutil) 可用
2025-08-02 23:02:02,448 - health_monitor - INFO - 健康监控器初始化完成
2025-08-02 23:02:02,464 - app.core.system_monitor - INFO - 已加载 288 个历史数据点
2025-08-02 23:02:02,475 - app.core.alert_detector - INFO - 已加载 6 个告警规则
2025-08-02 23:02:02,477 - app.core.alert_detector - INFO - 已加载 0 个当前告警
2025-08-02 23:02:02,497 - app.core.alert_detector - INFO - 已加载 370 个历史告警
2025-08-02 23:02:02,515 - app.core.alert_detector - INFO - 已加载 1 个通知渠道
2025-08-02 23:02:02,529 - query_cache - INFO - 查询缓存初始化完成 - 启用: True, 使用Redis: False
2025-08-02 23:02:02,546 - alert_manager - INFO - 已初始化默认告警规则
2025-08-02 23:02:02,550 - alert_manager - INFO - 已初始化默认通知渠道
2025-08-02 23:02:02,555 - alert_manager - INFO - 告警管理器初始化完成
2025-08-02 23:02:04,192 - db_service - INFO - 数据库引擎和会话工厂创建成功
2025-08-02 23:02:04,195 - db_service - INFO - 数据库服务初始化完成
2025-08-02 23:02:04,211 - notification_service - INFO - 通知服务初始化完成
2025-08-02 23:02:04,212 - main - INFO - 错误处理模块导入成功
2025-08-02 23:02:04,290 - main - INFO - 监控模块导入成功
2025-08-02 23:02:04,293 - main - INFO - 不再使用迁移脚本，使用标准化的数据库模型
2025-08-02 23:02:10,342 - app.services.ocr_service - WARNING - OpenCV未安装，图像预处理功能将不可用
2025-08-02 23:02:10,424 - main - INFO - 应用启动中...
2025-08-02 23:02:10,426 - error_handling - INFO - 错误处理已设置
2025-08-02 23:02:10,427 - main - INFO - 错误处理系统初始化完成
2025-08-02 23:02:10,429 - monitoring - INFO - 添加指标端点成功: /metrics
2025-08-02 23:02:10,431 - monitoring - INFO - 添加健康检查端点成功: /health
2025-08-02 23:02:10,435 - monitoring - INFO - 添加详细健康检查端点成功: /api/health/detailed
2025-08-02 23:02:10,440 - monitoring - INFO - 系统信息初始化完成: Markey, Windows-10-10.0.19045-SP0, Python 3.13.2, CPU核心数: 4
2025-08-02 23:02:10,447 - monitoring - INFO - 启动资源监控线程成功
2025-08-02 23:02:10,449 - monitoring - INFO - 监控系统初始化成功（不使用中间件）
2025-08-02 23:02:10,452 - monitoring - INFO - 监控系统初始化完成
2025-08-02 23:02:10,456 - main - INFO - 监控系统初始化完成
2025-08-02 23:02:10,461 - app.db.init_db - INFO - 所有模型导入成功
2025-08-02 23:02:10,489 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-02 23:02:10,507 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:02:10,508 - app.db.init_db - INFO - 所有模型导入成功
2025-08-02 23:02:10,518 - app.db.init_db - INFO - 使用sha256_crypt进行密码哈希和验证
2025-08-02 23:02:10,521 - app.db.init_db - INFO - 正在运行数据库迁移...
2025-08-02 23:02:10,524 - app.db.init_db - INFO - 正在检查并更新数据库表结构...
2025-08-02 23:02:10,537 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-02 23:02:10,541 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alerts")
2025-08-02 23:02:10,543 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,548 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.9%, CPU使用率 100.0%
2025-08-02 23:02:10,555 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_rules")
2025-08-02 23:02:10,559 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,567 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("alert_channels")
2025-08-02 23:02:10,570 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,572 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-08-02 23:02:10,577 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,581 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_records")
2025-08-02 23:02:10,586 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,588 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_overviews")
2025-08-02 23:02:10,591 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,597 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medical_records")
2025-08-02 23:02:10,601 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,605 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("inpatient_records")
2025-08-02 23:02:10,610 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,614 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("surgery_records")
2025-08-02 23:02:10,619 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,621 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_reports")
2025-08-02 23:02:10,626 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,630 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("lab_report_items")
2025-08-02 23:02:10,635 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,638 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_reports")
2025-08-02 23:02:10,643 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,647 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("examination_reports")
2025-08-02 23:02:10,656 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,661 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("documents")
2025-08-02 23:02:10,665 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,672 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("other_records")
2025-08-02 23:02:10,676 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,679 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("operation_logs")
2025-08-02 23:02:10,690 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,693 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("follow_up_records")
2025-08-02 23:02:10,695 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,698 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("health_diaries")
2025-08-02 23:02:10,707 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,710 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("registration_records")
2025-08-02 23:02:10,713 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,720 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("prescription_records")
2025-08-02 23:02:10,724 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,726 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("laboratory_records")
2025-08-02 23:02:10,729 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,737 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("imaging_records")
2025-08-02 23:02:10,795 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,826 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medications")
2025-08-02 23:02:10,830 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,837 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("medication_usages")
2025-08-02 23:02:10,842 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,846 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("role_applications")
2025-08-02 23:02:10,851 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,860 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessments")
2025-08-02 23:02:10,863 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,871 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_items")
2025-08-02 23:02:10,874 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,877 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_responses")
2025-08-02 23:02:10,879 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,884 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_templates")
2025-08-02 23:02:10,888 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,891 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_template_questions")
2025-08-02 23:02:10,893 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,896 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaires")
2025-08-02 23:02:10,906 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,910 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_items")
2025-08-02 23:02:10,912 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,918 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_responses")
2025-08-02 23:02:10,921 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,923 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_answers")
2025-08-02 23:02:10,925 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,928 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_templates")
2025-08-02 23:02:10,930 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,938 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_template_questions")
2025-08-02 23:02:10,941 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,944 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_results")
2025-08-02 23:02:10,952 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,954 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_results")
2025-08-02 23:02:10,956 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,959 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("report_templates")
2025-08-02 23:02:10,961 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,970 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("assessment_distributions")
2025-08-02 23:02:10,972 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,975 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("questionnaire_distributions")
2025-08-02 23:02:10,985 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,987 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("service_stats")
2025-08-02 23:02:10,989 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-08-02 23:02:10,994 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-02 23:02:10,998 - app.db.init_db - INFO - 数据库表结构检查和更新完成
2025-08-02 23:02:11,005 - app.db.init_db - INFO - 模型关系初始化完成
2025-08-02 23:02:11,007 - app.db.init_db - INFO - 模型关系设置完成
2025-08-02 23:02:11,012 - main - INFO - 数据库初始化完成（强制重建）
2025-08-02 23:02:11,018 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:02:11,020 - main - INFO - 数据库连接正常
2025-08-02 23:02:11,021 - main - INFO - 开始初始化模板数据
2025-08-02 23:02:11,023 - db_service - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:02:11,707 - app.db.init_templates - INFO - 开始初始化评估量表模板...
2025-08-02 23:02:11,811 - app.db.init_templates - INFO - 成功初始化 5 个评估量表模板
2025-08-02 23:02:11,891 - app.db.init_templates - INFO - 开始初始化调查问卷模板...
2025-08-02 23:02:11,970 - app.db.init_templates - INFO - 成功初始化 5 个调查问卷模板
2025-08-02 23:02:11,972 - main - INFO - 模板数据初始化完成
2025-08-02 23:02:11,976 - main - INFO - 数据库表结构已经标准化，不需要迁移
2025-08-02 23:02:11,977 - main - INFO - 应用启动完成
2025-08-02 23:02:19,877 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:02:19,884 - main - INFO - 请求没有认证头部
2025-08-02 23:02:19,885 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:02:19,888 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:02:22,219 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:02:22,230 - main - INFO - 请求没有认证头部
2025-08-02 23:02:22,258 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:02:22,390 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:02:22,393 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:02:22,403 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 23:02:22,404 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:02:22,406 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:02:22,409 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:02:24,531 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:02:24,759 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:02:26,134 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.3%, CPU使用率 100.0%
2025-08-02 23:02:41,400 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 100.0%
2025-08-02 23:02:56,532 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 75.9%
2025-08-02 23:03:03,392 - health_monitor - DEBUG - 系统指标 - CPU: 97.0%, 内存: 69.9%, 磁盘: 83.7%
2025-08-02 23:03:11,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 54.2%
2025-08-02 23:03:27,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 100.0%
2025-08-02 23:03:42,856 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 70.8%
2025-08-02 23:03:57,963 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 37.5%
2025-08-02 23:04:04,438 - health_monitor - DEBUG - 系统指标 - CPU: 37.0%, 内存: 69.3%, 磁盘: 83.7%
2025-08-02 23:04:13,069 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 13.8%
2025-08-02 23:04:28,175 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 26.9%
2025-08-02 23:04:43,281 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 66.7%
2025-08-02 23:04:58,388 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 20.8%
2025-08-02 23:05:05,470 - health_monitor - DEBUG - 系统指标 - CPU: 50.8%, 内存: 68.9%, 磁盘: 83.7%
2025-08-02 23:05:13,495 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 20.0%
2025-08-02 23:05:28,600 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 27.6%
2025-08-02 23:05:43,706 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 23.1%
2025-08-02 23:05:58,812 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.6%, CPU使用率 20.8%
2025-08-02 23:06:06,497 - health_monitor - DEBUG - 系统指标 - CPU: 36.7%, 内存: 68.8%, 磁盘: 83.7%
2025-08-02 23:06:13,919 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 58.3%
2025-08-02 23:06:29,026 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 76.9%
2025-08-02 23:06:44,133 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 22.2%
2025-08-02 23:06:59,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 10.7%
2025-08-02 23:07:07,535 - health_monitor - DEBUG - 系统指标 - CPU: 86.6%, 内存: 69.4%, 磁盘: 83.7%
2025-08-02 23:07:14,680 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 100.0%
2025-08-02 23:07:30,015 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 100.0%
2025-08-02 23:07:45,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 48.1%
2025-08-02 23:08:00,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.2%, CPU使用率 64.3%
2025-08-02 23:08:02,697 - alert_manager - WARNING - 触发告警: disk_free_space, 当前值: 0.0, 阈值: 1024
2025-08-02 23:08:05,144 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:08:05,163 - main - INFO - 请求没有认证头部
2025-08-02 23:08:05,164 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:08:05,168 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:08:07,456 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:08:07,461 - main - INFO - 请求没有认证头部
2025-08-02 23:08:07,464 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:08:07,467 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:08:07,470 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:08:07,472 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 23:08:07,477 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:08:07,479 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:08:07,480 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:08:08,536 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:08:08,543 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:08:08,601 - health_monitor - DEBUG - 系统指标 - CPU: 60.3%, 内存: 67.9%, 磁盘: 83.7%
2025-08-02 23:08:15,359 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 46.2%
2025-08-02 23:08:30,530 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 78.4%
2025-08-02 23:08:45,729 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 87.5%
2025-08-02 23:09:00,956 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 97.4%
2025-08-02 23:09:09,646 - health_monitor - DEBUG - 系统指标 - CPU: 32.8%, 内存: 70.4%, 磁盘: 83.7%
2025-08-02 23:09:16,072 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.8%, CPU使用率 0.0%
2025-08-02 23:09:31,182 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 87.5%
2025-08-02 23:09:46,289 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 33.3%
2025-08-02 23:10:01,395 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 23.1%
2025-08-02 23:10:10,669 - health_monitor - DEBUG - 系统指标 - CPU: 47.7%, 内存: 65.8%, 磁盘: 83.7%
2025-08-02 23:10:16,511 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 96.0%
2025-08-02 23:10:31,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 41.4%
2025-08-02 23:10:46,722 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.6%, CPU使用率 28.0%
2025-08-02 23:11:01,837 - monitoring - DEBUG - 资源指标更新: 内存使用率 65.9%, CPU使用率 100.0%
2025-08-02 23:11:11,693 - health_monitor - DEBUG - 系统指标 - CPU: 67.9%, 内存: 66.0%, 磁盘: 83.7%
2025-08-02 23:11:16,981 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 89.3%
2025-08-02 23:11:32,089 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.6%, CPU使用率 44.0%
2025-08-02 23:11:47,197 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 83.3%
2025-08-02 23:12:02,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.0%, CPU使用率 59.3%
2025-08-02 23:12:12,720 - health_monitor - DEBUG - 系统指标 - CPU: 52.5%, 内存: 66.3%, 磁盘: 83.7%
2025-08-02 23:12:17,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.7%, CPU使用率 56.5%
2025-08-02 23:12:32,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.3%, CPU使用率 41.7%
2025-08-02 23:12:47,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 66.5%, CPU使用率 79.2%
2025-08-02 23:13:03,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 67.5%, CPU使用率 97.6%
2025-08-02 23:13:14,459 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 68.0%, 磁盘: 83.7%
2025-08-02 23:13:18,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 100.0%
2025-08-02 23:13:34,039 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 55.6%
2025-08-02 23:13:49,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-08-02 23:13:58,878 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:13:58,886 - main - INFO - 请求没有认证头部
2025-08-02 23:13:58,889 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:13:58,892 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:14:00,908 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:14:00,910 - main - INFO - 请求没有认证头部
2025-08-02 23:14:00,913 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:14:00,917 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:14:00,918 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:14:00,924 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 23:14:00,929 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:14:00,931 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:14:00,933 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:14:04,085 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:14:04,113 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:14:04,670 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 100.0%
2025-08-02 23:14:16,534 - health_monitor - DEBUG - 系统指标 - CPU: 97.1%, 内存: 69.5%, 磁盘: 83.7%
2025-08-02 23:14:19,934 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 100.0%
2025-08-02 23:14:35,412 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.7%, CPU使用率 100.0%
2025-08-02 23:14:50,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 96.4%
2025-08-02 23:15:05,638 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 100.0%
2025-08-02 23:15:18,103 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 69.4%, 磁盘: 83.7%
2025-08-02 23:15:20,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 100.0%
2025-08-02 23:15:35,948 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.0%, CPU使用率 83.3%
2025-08-02 23:15:51,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 50.0%
2025-08-02 23:16:06,232 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 94.1%
2025-08-02 23:16:19,157 - health_monitor - DEBUG - 系统指标 - CPU: 48.3%, 内存: 68.1%, 磁盘: 83.7%
2025-08-02 23:16:21,342 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.1%, CPU使用率 50.0%
2025-08-02 23:16:36,449 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 54.2%
2025-08-02 23:16:51,558 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 50.0%
2025-08-02 23:17:06,663 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 48.0%
2025-08-02 23:17:20,202 - health_monitor - DEBUG - 系统指标 - CPU: 95.0%, 内存: 71.4%, 磁盘: 83.7%
2025-08-02 23:17:21,771 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 66.7%
2025-08-02 23:17:36,876 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 53.8%
2025-08-02 23:17:51,983 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 50.0%
2025-08-02 23:18:07,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 48.1%
2025-08-02 23:18:21,282 - health_monitor - DEBUG - 系统指标 - CPU: 75.8%, 内存: 70.5%, 磁盘: 83.7%
2025-08-02 23:18:22,231 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 96.9%
2025-08-02 23:18:37,356 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 50.0%
2025-08-02 23:18:52,462 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 46.2%
2025-08-02 23:19:07,567 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 53.6%
2025-08-02 23:19:22,348 - health_monitor - DEBUG - 系统指标 - CPU: 64.3%, 内存: 70.5%, 磁盘: 83.7%
2025-08-02 23:19:22,673 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 18.5%
2025-08-02 23:19:37,779 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 8.3%
2025-08-02 23:19:52,884 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 41.7%
2025-08-02 23:20:07,988 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 38.5%
2025-08-02 23:20:23,093 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 29.2%
2025-08-02 23:20:23,376 - health_monitor - DEBUG - 系统指标 - CPU: 35.3%, 内存: 69.0%, 磁盘: 83.7%
2025-08-02 23:20:38,198 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.2%, CPU使用率 4.2%
2025-08-02 23:20:53,304 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 53.6%
2025-08-02 23:21:08,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.7%, CPU使用率 15.4%
2025-08-02 23:21:23,551 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 75.0%
2025-08-02 23:21:24,470 - health_monitor - DEBUG - 系统指标 - CPU: 49.4%, 内存: 69.2%, 磁盘: 83.7%
2025-08-02 23:21:38,694 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 100.0%
2025-08-02 23:21:53,803 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 65.5%
2025-08-02 23:22:08,912 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 93.1%
2025-08-02 23:22:24,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 93.1%
2025-08-02 23:22:25,558 - health_monitor - DEBUG - 系统指标 - CPU: 66.7%, 内存: 69.5%, 磁盘: 83.7%
2025-08-02 23:22:39,165 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 42.9%
2025-08-02 23:22:54,272 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 66.7%
2025-08-02 23:23:09,378 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 42.9%
2025-08-02 23:23:24,528 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.5%, CPU使用率 76.5%
2025-08-02 23:23:26,589 - health_monitor - DEBUG - 系统指标 - CPU: 73.0%, 内存: 69.5%, 磁盘: 83.7%
2025-08-02 23:23:39,635 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 83.3%
2025-08-02 23:23:54,742 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 59.3%
2025-08-02 23:24:09,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 46.4%
2025-08-02 23:24:24,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 76.9%
2025-08-02 23:24:27,619 - health_monitor - DEBUG - 系统指标 - CPU: 56.0%, 内存: 69.6%, 磁盘: 83.7%
2025-08-02 23:24:40,086 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 53.6%
2025-08-02 23:24:55,194 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 39.3%
2025-08-02 23:25:10,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 93.1%
2025-08-02 23:25:25,409 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 62.1%
2025-08-02 23:25:28,649 - health_monitor - DEBUG - 系统指标 - CPU: 69.8%, 内存: 70.2%, 磁盘: 83.7%
2025-08-02 23:25:40,521 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 82.1%
2025-08-02 23:25:55,626 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 69.0%
2025-08-02 23:26:10,735 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.8%, CPU使用率 42.9%
2025-08-02 23:26:25,862 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.9%, CPU使用率 46.4%
2025-08-02 23:26:29,683 - health_monitor - DEBUG - 系统指标 - CPU: 82.0%, 内存: 69.8%, 磁盘: 83.7%
2025-08-02 23:26:40,967 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.0%, CPU使用率 79.2%
2025-08-02 23:26:56,078 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.1%, CPU使用率 76.0%
2025-08-02 23:27:11,212 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.3%, CPU使用率 100.0%
2025-08-02 23:27:26,320 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 20.8%
2025-08-02 23:27:30,709 - health_monitor - DEBUG - 系统指标 - CPU: 62.3%, 内存: 69.0%, 磁盘: 83.7%
2025-08-02 23:27:41,426 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 6.9%
2025-08-02 23:27:56,531 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 20.8%
2025-08-02 23:28:11,636 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.5%, CPU使用率 41.7%
2025-08-02 23:28:26,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.2%, CPU使用率 39.3%
2025-08-02 23:28:31,738 - health_monitor - DEBUG - 系统指标 - CPU: 51.8%, 内存: 68.3%, 磁盘: 83.7%
2025-08-02 23:28:41,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.4%, CPU使用率 28.6%
2025-08-02 23:28:56,953 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 68.0%
2025-08-02 23:29:12,059 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 52.0%
2025-08-02 23:29:27,164 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 26.9%
2025-08-02 23:29:32,796 - health_monitor - DEBUG - 系统指标 - CPU: 55.7%, 内存: 69.2%, 磁盘: 83.7%
2025-08-02 23:29:42,302 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.3%, CPU使用率 90.3%
2025-08-02 23:29:57,421 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.0%, CPU使用率 45.8%
2025-08-02 23:30:12,526 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.1%, CPU使用率 20.8%
2025-08-02 23:30:27,633 - monitoring - DEBUG - 资源指标更新: 内存使用率 68.9%, CPU使用率 85.7%
2025-08-02 23:30:33,843 - health_monitor - DEBUG - 系统指标 - CPU: 65.9%, 内存: 69.6%, 磁盘: 83.7%
2025-08-02 23:30:42,741 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.4%, CPU使用率 71.4%
2025-08-02 23:30:57,847 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 40.7%
2025-08-02 23:31:12,952 - monitoring - DEBUG - 资源指标更新: 内存使用率 69.6%, CPU使用率 29.2%
2025-08-02 23:31:28,057 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 20.8%
2025-08-02 23:31:34,869 - health_monitor - DEBUG - 系统指标 - CPU: 47.9%, 内存: 69.8%, 磁盘: 83.7%
2025-08-02 23:31:43,161 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.2%, CPU使用率 57.7%
2025-08-02 23:31:58,305 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 100.0%
2025-08-02 23:32:10,374 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:32:10,375 - main - INFO - 请求没有认证头部
2025-08-02 23:32:10,376 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:32:10,378 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:32:12,401 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:32:12,402 - main - INFO - 请求没有认证头部
2025-08-02 23:32:12,402 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:32:12,404 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:32:12,405 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:32:12,406 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 23:32:12,407 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:32:12,408 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:32:12,408 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:32:13,360 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:32:13,363 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:32:13,418 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 45.8%
2025-08-02 23:32:28,524 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 58.3%
2025-08-02 23:32:35,891 - health_monitor - DEBUG - 系统指标 - CPU: 38.5%, 内存: 72.5%, 磁盘: 83.7%
2025-08-02 23:32:43,630 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.4%, CPU使用率 28.6%
2025-08-02 23:32:58,735 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 74.1%
2025-08-02 23:33:13,841 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.3%, CPU使用率 16.7%
2025-08-02 23:33:28,945 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 0.0%
2025-08-02 23:33:36,932 - health_monitor - DEBUG - 系统指标 - CPU: 16.2%, 内存: 72.2%, 磁盘: 83.7%
2025-08-02 23:33:44,052 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 50.0%
2025-08-02 23:33:59,158 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 0.0%
2025-08-02 23:34:14,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 4.2%
2025-08-02 23:34:29,370 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 45.8%
2025-08-02 23:34:37,965 - health_monitor - DEBUG - 系统指标 - CPU: 58.8%, 内存: 72.9%, 磁盘: 83.7%
2025-08-02 23:34:44,475 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 57.1%
2025-08-02 23:34:59,581 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 35.7%
2025-08-02 23:35:14,688 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 50.0%
2025-08-02 23:35:29,793 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 62.5%
2025-08-02 23:35:39,011 - health_monitor - DEBUG - 系统指标 - CPU: 56.3%, 内存: 73.9%, 磁盘: 83.7%
2025-08-02 23:35:44,900 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 66.7%
2025-08-02 23:36:00,007 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 42.9%
2025-08-02 23:36:15,118 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 56.0%
2025-08-02 23:36:30,226 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 57.1%
2025-08-02 23:36:40,043 - health_monitor - DEBUG - 系统指标 - CPU: 47.1%, 内存: 73.3%, 磁盘: 83.7%
2025-08-02 23:36:45,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 33.3%
2025-08-02 23:37:00,443 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 80.0%
2025-08-02 23:37:15,549 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 57.1%
2025-08-02 23:37:30,657 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 40.7%
2025-08-02 23:37:41,074 - health_monitor - DEBUG - 系统指标 - CPU: 69.6%, 内存: 73.9%, 磁盘: 83.7%
2025-08-02 23:37:45,766 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 54.2%
2025-08-02 23:38:00,874 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 50.0%
2025-08-02 23:38:15,979 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 14.8%
2025-08-02 23:38:31,085 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.2%, CPU使用率 85.7%
2025-08-02 23:38:42,100 - health_monitor - DEBUG - 系统指标 - CPU: 43.2%, 内存: 71.2%, 磁盘: 83.7%
2025-08-02 23:38:46,641 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 100.0%
2025-08-02 23:39:01,770 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 15.4%
2025-08-02 23:39:16,875 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 16.7%
2025-08-02 23:39:31,980 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 41.7%
2025-08-02 23:39:43,157 - health_monitor - DEBUG - 系统指标 - CPU: 36.9%, 内存: 70.7%, 磁盘: 83.7%
2025-08-02 23:39:47,087 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 37.5%
2025-08-02 23:40:02,191 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.5%, CPU使用率 7.1%
2025-08-02 23:40:17,297 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 4.2%
2025-08-02 23:40:32,403 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 83.3%
2025-08-02 23:40:44,180 - health_monitor - DEBUG - 系统指标 - CPU: 33.7%, 内存: 70.8%, 磁盘: 83.7%
2025-08-02 23:40:47,508 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.9%, CPU使用率 25.0%
2025-08-02 23:41:02,614 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 37.9%
2025-08-02 23:41:17,721 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 21.4%
2025-08-02 23:41:32,831 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 75.0%
2025-08-02 23:41:45,285 - health_monitor - DEBUG - 系统指标 - CPU: 51.3%, 内存: 71.6%, 磁盘: 83.7%
2025-08-02 23:41:48,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.6%, CPU使用率 100.0%
2025-08-02 23:42:03,306 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 85.4%
2025-08-02 23:42:12,531 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:42:12,533 - main - INFO - 请求没有认证头部
2025-08-02 23:42:12,533 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:42:12,535 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:42:14,573 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:42:14,574 - main - INFO - 请求没有认证头部
2025-08-02 23:42:14,574 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:42:14,576 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:42:14,577 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:42:14,578 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 23:42:14,579 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:42:14,580 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:42:14,581 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:42:15,335 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:42:15,337 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:42:18,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 25.0%
2025-08-02 23:42:33,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 19.2%
2025-08-02 23:42:46,313 - health_monitor - DEBUG - 系统指标 - CPU: 42.7%, 内存: 70.6%, 磁盘: 83.7%
2025-08-02 23:42:48,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 58.3%
2025-08-02 23:43:03,748 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 79.2%
2025-08-02 23:43:18,860 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 41.7%
2025-08-02 23:43:33,964 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.7%, CPU使用率 0.0%
2025-08-02 23:43:46,898 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:43:46,899 - main - INFO - 请求没有认证头部
2025-08-02 23:43:46,900 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:43:46,902 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:43:47,337 - health_monitor - DEBUG - 系统指标 - CPU: 21.6%, 内存: 72.0%, 磁盘: 83.7%
2025-08-02 23:43:48,939 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:43:48,940 - main - INFO - 请求没有认证头部
2025-08-02 23:43:48,941 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:43:48,942 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:43:48,943 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:43:48,945 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-02 23:43:48,946 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:43:48,947 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:43:48,948 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:43:48,951 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 23:43:48,953 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 23:43:48,954 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 23:43:48,955 - app.core.db_connection - DEBUG - 数据库连接已检入
2025-08-02 23:43:49,097 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.1%, CPU使用率 25.0%
2025-08-02 23:43:51,872 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:43:51,875 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:44:04,278 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 45.8%
2025-08-02 23:44:19,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 37.5%
2025-08-02 23:44:34,494 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.3%, CPU使用率 30.8%
2025-08-02 23:44:48,364 - health_monitor - DEBUG - 系统指标 - CPU: 46.9%, 内存: 71.2%, 磁盘: 83.7%
2025-08-02 23:44:49,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 17.9%
2025-08-02 23:45:04,703 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.0%, CPU使用率 45.8%
2025-08-02 23:45:19,808 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.2%, CPU使用率 8.3%
2025-08-02 23:45:35,046 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 100.0%
2025-08-02 23:45:49,390 - health_monitor - DEBUG - 系统指标 - CPU: 37.5%, 内存: 71.0%, 磁盘: 83.7%
2025-08-02 23:45:50,151 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.1%, CPU使用率 21.4%
2025-08-02 23:46:05,256 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 58.6%
2025-08-02 23:46:20,362 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 28.0%
2025-08-02 23:46:35,467 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 29.2%
2025-08-02 23:46:50,412 - health_monitor - DEBUG - 系统指标 - CPU: 32.2%, 内存: 70.6%, 磁盘: 83.7%
2025-08-02 23:46:50,572 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.6%, CPU使用率 50.0%
2025-08-02 23:47:05,677 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.4%, CPU使用率 34.5%
2025-08-02 23:47:20,781 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.7%, CPU使用率 12.5%
2025-08-02 23:47:35,886 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 33.3%
2025-08-02 23:47:50,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 92.3%
2025-08-02 23:47:51,535 - health_monitor - DEBUG - 系统指标 - CPU: 92.5%, 内存: 73.1%, 磁盘: 83.7%
2025-08-02 23:48:06,099 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 53.6%
2025-08-02 23:48:21,407 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 100.0%
2025-08-02 23:48:37,236 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 100.0%
2025-08-02 23:48:53,015 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 98.3%
2025-08-02 23:48:53,684 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 74.3%, 磁盘: 83.8%
2025-08-02 23:49:09,168 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 98.2%
2025-08-02 23:49:13,820 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:49:13,845 - main - INFO - 请求没有认证头部
2025-08-02 23:49:13,863 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:49:13,867 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:49:16,070 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:49:16,072 - main - INFO - 请求没有认证头部
2025-08-02 23:49:16,073 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:49:16,075 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:49:16,078 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:49:16,079 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:49:16,080 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:49:16,081 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:49:22,172 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:49:22,181 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:49:24,589 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 100.0%
2025-08-02 23:49:40,415 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.6%, CPU使用率 99.2%
2025-08-02 23:49:55,332 - health_monitor - DEBUG - 系统指标 - CPU: 97.7%, 内存: 76.6%, 磁盘: 83.8%
2025-08-02 23:49:55,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.7%, CPU使用率 58.3%
2025-08-02 23:50:10,698 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 83.3%
2025-08-02 23:50:25,855 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 74.1%
2025-08-02 23:50:40,960 - monitoring - DEBUG - 资源指标更新: 内存使用率 70.8%, CPU使用率 48.1%
2025-08-02 23:50:56,102 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 100.0%
2025-08-02 23:50:56,363 - health_monitor - DEBUG - 系统指标 - CPU: 63.2%, 内存: 72.9%, 磁盘: 83.8%
2025-08-02 23:51:11,239 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 86.7%
2025-08-02 23:51:26,380 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 71.9%
2025-08-02 23:51:41,896 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 100.0%
2025-08-02 23:51:54,398 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:51:54,401 - main - INFO - 请求没有认证头部
2025-08-02 23:51:54,402 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:51:54,404 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:51:56,666 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:51:56,672 - main - INFO - 请求没有认证头部
2025-08-02 23:51:56,674 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:51:56,676 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:51:56,678 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:51:56,679 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:51:56,680 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:51:56,682 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:51:57,084 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 56.2%
2025-08-02 23:51:57,447 - health_monitor - DEBUG - 系统指标 - CPU: 77.0%, 内存: 74.2%, 磁盘: 83.8%
2025-08-02 23:52:02,427 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:52:02,431 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:52:12,248 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.8%, CPU使用率 95.8%
2025-08-02 23:52:27,540 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.0%, CPU使用率 100.0%
2025-08-02 23:52:42,887 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.1%, CPU使用率 100.0%
2025-08-02 23:52:58,545 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 98.1%
2025-08-02 23:52:58,724 - health_monitor - DEBUG - 系统指标 - CPU: 99.0%, 内存: 74.5%, 磁盘: 83.8%
2025-08-02 23:52:59,673 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:52:59,709 - main - INFO - 请求没有认证头部
2025-08-02 23:52:59,711 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:52:59,714 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:53:01,964 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:53:01,965 - main - INFO - 请求没有认证头部
2025-08-02 23:53:01,966 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:53:01,969 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:53:01,970 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:53:01,971 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:53:01,972 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:53:01,973 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:53:08,743 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:53:08,838 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:53:13,665 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.3%, CPU使用率 85.7%
2025-08-02 23:53:28,772 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 85.7%
2025-08-02 23:53:43,883 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 55.2%
2025-08-02 23:53:58,990 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 42.9%
2025-08-02 23:53:59,756 - health_monitor - DEBUG - 系统指标 - CPU: 32.8%, 内存: 71.8%, 磁盘: 83.8%
2025-08-02 23:54:14,096 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.9%, CPU使用率 65.4%
2025-08-02 23:54:29,202 - monitoring - DEBUG - 资源指标更新: 内存使用率 71.8%, CPU使用率 33.3%
2025-08-02 23:54:44,309 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 52.0%
2025-08-02 23:54:59,482 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 97.1%
2025-08-02 23:55:00,893 - health_monitor - DEBUG - 系统指标 - CPU: 81.7%, 内存: 74.9%, 磁盘: 83.8%
2025-08-02 23:55:14,616 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 96.0%
2025-08-02 23:55:30,156 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.0%, CPU使用率 100.0%
2025-08-02 23:55:45,364 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 100.0%
2025-08-02 23:56:00,480 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 87.5%
2025-08-02 23:56:02,447 - health_monitor - DEBUG - 系统指标 - CPU: 99.4%, 内存: 74.4%, 磁盘: 83.8%
2025-08-02 23:56:15,598 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 93.1%
2025-08-02 23:56:18,285 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-02 23:56:18,335 - main - INFO - 请求没有认证头部
2025-08-02 23:56:18,522 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:56:18,527 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:56:20,885 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-02 23:56:20,891 - main - INFO - 请求没有认证头部
2025-08-02 23:56:20,893 - main - INFO - 没有认证头部，设置用户为None
2025-08-02 23:56:20,908 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-02 23:56:20,916 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-02 23:56:20,918 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-02 23:56:20,927 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-02 23:56:20,927 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-02 23:56:29,424 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-02 23:56:29,599 - main - INFO - --- 请求结束: 200 ---

2025-08-02 23:56:31,531 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-08-02 23:56:46,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 100.0%
2025-08-02 23:57:02,374 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-08-02 23:57:03,753 - health_monitor - DEBUG - 系统指标 - CPU: 98.0%, 内存: 77.5%, 磁盘: 83.8%
2025-08-02 23:57:17,482 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.7%, CPU使用率 54.2%
2025-08-02 23:57:32,587 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 16.0%
2025-08-02 23:57:47,701 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 62.5%
2025-08-02 23:58:02,819 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 64.3%
2025-08-02 23:58:04,804 - health_monitor - DEBUG - 系统指标 - CPU: 35.0%, 内存: 72.9%, 磁盘: 83.8%
2025-08-02 23:58:18,108 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 86.1%
2025-08-02 23:58:33,213 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 16.7%
2025-08-02 23:58:48,318 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 35.7%
2025-08-02 23:59:03,422 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 0.0%
2025-08-02 23:59:05,827 - health_monitor - DEBUG - 系统指标 - CPU: 14.1%, 内存: 73.1%, 磁盘: 83.8%
2025-08-02 23:59:18,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 4.2%
2025-08-02 23:59:33,632 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 28.6%
2025-08-02 23:59:48,737 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 3.6%
2025-08-03 00:00:03,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 33.3%
2025-08-03 00:00:06,849 - health_monitor - DEBUG - 系统指标 - CPU: 20.9%, 内存: 73.1%, 磁盘: 83.8%
2025-08-03 00:00:18,949 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 12.5%
2025-08-03 00:00:34,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 15.4%
2025-08-03 00:00:49,160 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 0.0%
2025-08-03 00:01:04,264 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 8.3%
2025-08-03 00:01:07,911 - health_monitor - DEBUG - 系统指标 - CPU: 71.5%, 内存: 73.9%, 磁盘: 83.8%
2025-08-03 00:01:19,371 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 29.2%
2025-08-03 00:01:34,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 33.3%
2025-08-03 00:01:49,588 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 70.8%
2025-08-03 00:02:04,693 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 21.4%
2025-08-03 00:02:08,933 - health_monitor - DEBUG - 系统指标 - CPU: 44.0%, 内存: 74.6%, 磁盘: 83.8%
2025-08-03 00:02:19,800 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 23.1%
2025-08-03 00:02:34,905 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 16.0%
2025-08-03 00:02:50,011 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 4.2%
2025-08-03 00:03:05,116 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 7.1%
2025-08-03 00:03:09,955 - health_monitor - DEBUG - 系统指标 - CPU: 18.3%, 内存: 73.6%, 磁盘: 83.8%
2025-08-03 00:03:20,225 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 25.0%
2025-08-03 00:03:35,331 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 25.0%
2025-08-03 00:03:50,437 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 25.0%
2025-08-03 00:04:05,541 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 19.2%
2025-08-03 00:04:10,980 - health_monitor - DEBUG - 系统指标 - CPU: 23.7%, 内存: 73.3%, 磁盘: 83.8%
2025-08-03 00:04:20,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 27.6%
2025-08-03 00:04:35,753 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 25.0%
2025-08-03 00:04:50,857 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 0.0%
2025-08-03 00:05:05,962 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 0.0%
2025-08-03 00:05:12,002 - health_monitor - DEBUG - 系统指标 - CPU: 24.3%, 内存: 73.1%, 磁盘: 83.8%
2025-08-03 00:05:21,069 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 28.6%
2025-08-03 00:05:36,175 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.3%, CPU使用率 3.7%
2025-08-03 00:05:51,280 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 4.2%
2025-08-03 00:06:06,386 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 12.5%
2025-08-03 00:06:13,023 - health_monitor - DEBUG - 系统指标 - CPU: 28.9%, 内存: 73.4%, 磁盘: 83.8%
2025-08-03 00:06:21,491 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 17.9%
2025-08-03 00:06:36,599 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 53.6%
2025-08-03 00:06:51,704 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 19.2%
2025-08-03 00:07:06,809 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 16.7%
2025-08-03 00:07:14,046 - health_monitor - DEBUG - 系统指标 - CPU: 15.1%, 内存: 73.2%, 磁盘: 83.8%
2025-08-03 00:07:21,913 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 18.5%
2025-08-03 00:07:37,020 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 46.4%
2025-08-03 00:07:52,126 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 22.2%
2025-08-03 00:08:07,231 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 4.2%
2025-08-03 00:08:15,067 - health_monitor - DEBUG - 系统指标 - CPU: 13.6%, 内存: 73.3%, 磁盘: 83.8%
2025-08-03 00:08:22,336 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 17.9%
2025-08-03 00:08:37,442 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.2%, CPU使用率 35.7%
2025-08-03 00:08:52,591 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 93.8%
2025-08-03 00:09:07,709 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 46.4%
2025-08-03 00:09:16,094 - health_monitor - DEBUG - 系统指标 - CPU: 34.0%, 内存: 73.5%, 磁盘: 83.8%
2025-08-03 00:09:22,813 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 59.3%
2025-08-03 00:09:37,920 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 50.0%
2025-08-03 00:09:53,027 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 41.7%
2025-08-03 00:10:08,131 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 39.3%
2025-08-03 00:10:17,116 - health_monitor - DEBUG - 系统指标 - CPU: 21.1%, 内存: 73.4%, 磁盘: 83.8%
2025-08-03 00:10:23,273 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.3%, CPU使用率 85.7%
2025-08-03 00:10:38,511 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-08-03 00:10:53,617 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.5%, CPU使用率 67.9%
2025-08-03 00:11:09,202 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 100.0%
2025-08-03 00:11:18,723 - health_monitor - DEBUG - 系统指标 - CPU: 99.7%, 内存: 74.4%, 磁盘: 83.8%
2025-08-03 00:11:24,480 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-08-03 00:11:39,642 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 97.1%
2025-08-03 00:11:54,806 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.8%, CPU使用率 90.6%
2025-08-03 00:12:09,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 70.8%
2025-08-03 00:12:19,835 - health_monitor - DEBUG - 系统指标 - CPU: 86.6%, 内存: 73.2%, 磁盘: 83.8%
2025-08-03 00:12:25,114 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 100.0%
2025-08-03 00:12:40,390 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 100.0%
2025-08-03 00:12:55,711 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 100.0%
2025-08-03 00:13:10,965 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 100.0%
2025-08-03 00:13:21,441 - health_monitor - DEBUG - 系统指标 - CPU: 87.0%, 内存: 75.9%, 磁盘: 83.8%
2025-08-03 00:13:26,097 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 84.4%
2025-08-03 00:13:41,854 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 100.0%
2025-08-03 00:13:57,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-08-03 00:14:14,209 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 100.0%
2025-08-03 00:14:22,649 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 75.4%, 磁盘: 83.8%
2025-08-03 00:14:29,746 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 100.0%
2025-08-03 00:14:44,984 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 88.0%
2025-08-03 00:15:00,100 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.2%, CPU使用率 86.7%
2025-08-03 00:15:15,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 100.0%
2025-08-03 00:15:24,061 - health_monitor - DEBUG - 系统指标 - CPU: 96.2%, 内存: 75.0%, 磁盘: 83.8%
2025-08-03 00:15:30,829 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.8%, CPU使用率 100.0%
2025-08-03 00:15:46,411 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.5%, CPU使用率 100.0%
2025-08-03 00:16:01,642 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 95.6%
2025-08-03 00:16:16,893 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.8%, CPU使用率 95.0%
2025-08-03 00:16:25,424 - health_monitor - DEBUG - 系统指标 - CPU: 99.3%, 内存: 77.6%, 磁盘: 83.8%
2025-08-03 00:16:32,037 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.0%, CPU使用率 100.0%
2025-08-03 00:16:47,195 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.3%, CPU使用率 92.9%
2025-08-03 00:17:02,587 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.6%, CPU使用率 100.0%
2025-08-03 00:17:17,718 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.8%, CPU使用率 88.0%
2025-08-03 00:17:26,810 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 76.8%, 磁盘: 83.8%
2025-08-03 00:17:32,823 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.7%, CPU使用率 83.3%
2025-08-03 00:17:47,941 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.1%, CPU使用率 100.0%
2025-08-03 00:18:03,053 - monitoring - DEBUG - 资源指标更新: 内存使用率 78.1%, CPU使用率 89.7%
2025-08-03 00:18:18,180 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.8%, CPU使用率 96.0%
2025-08-03 00:18:28,040 - health_monitor - DEBUG - 系统指标 - CPU: 94.6%, 内存: 77.6%, 磁盘: 83.8%
2025-08-03 00:18:33,417 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.8%, CPU使用率 97.7%
2025-08-03 00:18:48,938 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.6%, CPU使用率 100.0%
2025-08-03 00:19:04,271 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.2%, CPU使用率 100.0%
2025-08-03 00:19:19,517 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.4%, CPU使用率 100.0%
2025-08-03 00:19:29,142 - health_monitor - DEBUG - 系统指标 - CPU: 95.0%, 内存: 78.2%, 磁盘: 83.8%
2025-08-03 00:19:34,727 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.1%, CPU使用率 100.0%
2025-08-03 00:19:49,909 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.1%, CPU使用率 96.6%
2025-08-03 00:20:03,473 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 95.0, 阈值: 90
2025-08-03 00:20:05,402 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.7%, CPU使用率 100.0%
2025-08-03 00:20:20,576 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.6%, CPU使用率 100.0%
2025-08-03 00:20:30,351 - health_monitor - DEBUG - 系统指标 - CPU: 96.7%, 内存: 76.2%, 磁盘: 83.8%
2025-08-03 00:20:35,932 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.7%, CPU使用率 100.0%
2025-08-03 00:20:52,354 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.0%, CPU使用率 100.0%
2025-08-03 00:21:08,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-03 00:21:23,527 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.6%, CPU使用率 97.7%
2025-08-03 00:21:31,648 - health_monitor - DEBUG - 系统指标 - CPU: 86.3%, 内存: 74.5%, 磁盘: 83.8%
2025-08-03 00:21:38,700 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 100.0%
2025-08-03 00:21:53,961 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 97.9%
2025-08-03 00:22:09,594 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 98.4%
2025-08-03 00:22:24,999 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 100.0%
2025-08-03 00:22:32,949 - health_monitor - DEBUG - 系统指标 - CPU: 90.3%, 内存: 75.2%, 磁盘: 83.8%
2025-08-03 00:22:40,387 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.0%, CPU使用率 100.0%
2025-08-03 00:22:55,603 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 100.0%
2025-08-03 00:23:10,812 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.5%, CPU使用率 100.0%
2025-08-03 00:23:26,000 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-03 00:23:34,207 - health_monitor - DEBUG - 系统指标 - CPU: 83.7%, 内存: 73.7%, 磁盘: 83.8%
2025-08-03 00:23:41,199 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-08-03 00:23:56,356 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.8%, CPU使用率 100.0%
2025-08-03 00:24:11,864 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 100.0%
2025-08-03 00:24:27,205 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 97.1%
2025-08-03 00:24:35,318 - health_monitor - DEBUG - 系统指标 - CPU: 89.8%, 内存: 74.9%, 磁盘: 83.8%
2025-08-03 00:24:42,476 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 68.6%
2025-08-03 00:24:57,601 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 100.0%
2025-08-03 00:25:12,765 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 100.0%
2025-08-03 00:25:27,887 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.0%, CPU使用率 75.0%
2025-08-03 00:25:36,643 - health_monitor - DEBUG - 系统指标 - CPU: 95.3%, 内存: 73.4%, 磁盘: 83.8%
2025-08-03 00:25:43,645 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-08-03 00:25:59,214 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 100.0%
2025-08-03 00:26:14,379 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.3%, CPU使用率 40.7%
2025-08-03 00:26:29,485 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 60.0%
2025-08-03 00:26:37,703 - health_monitor - DEBUG - 系统指标 - CPU: 86.1%, 内存: 74.7%, 磁盘: 83.8%
2025-08-03 00:26:44,590 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 76.0%
2025-08-03 00:26:59,970 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 100.0%
2025-08-03 00:27:15,322 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 78.8%
2025-08-03 00:27:30,622 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-08-03 00:27:38,927 - health_monitor - DEBUG - 系统指标 - CPU: 91.1%, 内存: 74.5%, 磁盘: 83.8%
2025-08-03 00:27:45,762 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.3%, CPU使用率 96.0%
2025-08-03 00:28:01,129 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 97.9%
2025-08-03 00:28:16,259 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 78.1%
2025-08-03 00:28:31,552 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 100.0%
2025-08-03 00:28:40,189 - health_monitor - DEBUG - 系统指标 - CPU: 91.7%, 内存: 75.6%, 磁盘: 83.8%
2025-08-03 00:28:46,752 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.3%, CPU使用率 85.7%
2025-08-03 00:29:01,885 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 91.7%
2025-08-03 00:29:17,055 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 100.0%
2025-08-03 00:29:32,332 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.9%, CPU使用率 100.0%
2025-08-03 00:29:41,609 - health_monitor - DEBUG - 系统指标 - CPU: 98.9%, 内存: 73.6%, 磁盘: 83.8%
2025-08-03 00:29:47,481 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 89.3%
2025-08-03 00:30:02,592 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 79.2%
2025-08-03 00:30:17,817 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.2%, CPU使用率 100.0%
2025-08-03 00:30:33,092 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-08-03 00:30:42,910 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 74.0%, 磁盘: 83.8%
2025-08-03 00:30:48,224 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 100.0%
2025-08-03 00:31:03,769 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.8%, CPU使用率 100.0%
2025-08-03 00:31:19,123 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 95.8%
2025-08-03 00:31:34,303 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 100.0%
2025-08-03 00:31:43,955 - health_monitor - DEBUG - 系统指标 - CPU: 95.4%, 内存: 73.2%, 磁盘: 83.8%
2025-08-03 00:31:49,413 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 89.3%
2025-08-03 00:32:04,522 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 67.9%
2025-08-03 00:32:19,629 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 71.4%
2025-08-03 00:32:34,738 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.0%, CPU使用率 71.4%
2025-08-03 00:32:45,147 - health_monitor - DEBUG - 系统指标 - CPU: 96.0%, 内存: 75.0%, 磁盘: 83.8%
2025-08-03 00:32:49,903 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 100.0%
2025-08-03 00:33:03,886 - alert_manager - WARNING - 触发告警: cpu_usage, 当前值: 96.0, 阈值: 90
2025-08-03 00:33:05,054 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 87.5%
2025-08-03 00:33:20,170 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.6%, CPU使用率 96.3%
2025-08-03 00:33:35,299 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.5%, CPU使用率 93.5%
2025-08-03 00:33:46,195 - health_monitor - DEBUG - 系统指标 - CPU: 91.9%, 内存: 74.2%, 磁盘: 83.8%
2025-08-03 00:33:50,582 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.7%, CPU使用率 98.3%
2025-08-03 00:34:05,867 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 57.1%
2025-08-03 00:34:21,499 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 100.0%
2025-08-03 00:34:36,648 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 94.4%
2025-08-03 00:34:47,330 - health_monitor - DEBUG - 系统指标 - CPU: 86.3%, 内存: 75.2%, 磁盘: 83.8%
2025-08-03 00:34:51,761 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.0%, CPU使用率 92.0%
2025-08-03 00:35:07,104 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 90.0%
2025-08-03 00:35:22,273 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 95.2%
2025-08-03 00:35:37,394 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.1%, CPU使用率 92.9%
2025-08-03 00:35:48,452 - health_monitor - DEBUG - 系统指标 - CPU: 97.8%, 内存: 73.4%, 磁盘: 83.8%
2025-08-03 00:35:52,599 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.9%, CPU使用率 100.0%
2025-08-03 00:36:07,712 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 40.0%
2025-08-03 00:36:22,860 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.9%, CPU使用率 93.8%
2025-08-03 00:36:37,972 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.6%, CPU使用率 71.4%
2025-08-03 00:36:49,647 - health_monitor - DEBUG - 系统指标 - CPU: 92.5%, 内存: 75.8%, 磁盘: 83.8%
2025-08-03 00:36:53,141 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 100.0%
2025-08-03 00:37:08,258 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.1%, CPU使用率 64.3%
2025-08-03 00:37:23,365 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.4%, CPU使用率 46.4%
2025-08-03 00:37:38,615 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.8%, CPU使用率 100.0%
2025-08-03 00:37:50,767 - health_monitor - DEBUG - 系统指标 - CPU: 93.4%, 内存: 73.1%, 磁盘: 83.8%
2025-08-03 00:37:53,870 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.4%, CPU使用率 100.0%
2025-08-03 00:38:08,994 - monitoring - DEBUG - 资源指标更新: 内存使用率 74.5%, CPU使用率 76.0%
2025-08-03 00:38:24,101 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.9%, CPU使用率 75.0%
2025-08-03 00:38:39,438 - monitoring - DEBUG - 资源指标更新: 内存使用率 73.8%, CPU使用率 100.0%
2025-08-03 00:38:52,063 - health_monitor - DEBUG - 系统指标 - CPU: 100.0%, 内存: 76.4%, 磁盘: 83.8%
2025-08-03 00:38:54,296 - main - INFO - 
--- 请求开始: GET /api/health ---
2025-08-03 00:38:54,307 - main - INFO - 请求没有认证头部
2025-08-03 00:38:54,313 - main - INFO - 没有认证头部，设置用户为None
2025-08-03 00:38:54,315 - main - INFO - --- 请求结束: 200 ---

2025-08-03 00:38:54,843 - monitoring - DEBUG - 资源指标更新: 内存使用率 77.5%, CPU使用率 100.0%
2025-08-03 00:38:56,459 - main - INFO - 
--- 请求开始: POST /api/auth/register/login ---
2025-08-03 00:38:56,460 - main - INFO - 请求没有认证头部
2025-08-03 00:38:56,461 - main - INFO - 没有认证头部，设置用户为None
2025-08-03 00:38:56,463 - app.core.db_connection - DEBUG - 当前线程ID: 16336
2025-08-03 00:38:56,464 - app.core.db_connection - INFO - 尝试使用db_connection获取会话
2025-08-03 00:38:56,465 - app.core.db_connection - INFO - 数据库连接已创建
2025-08-03 00:38:56,467 - app.core.db_connection - DEBUG - 数据库连接已检出
2025-08-03 00:38:56,468 - app.core.db_connection - INFO - 数据库连接成功 (尝试 1/3)
2025-08-03 00:38:56,469 - app.core.db_connection - INFO - 使用db_connection获取会话成功
2025-08-03 00:38:59,214 - app.core.security - INFO - 使用sha256_crypt验证密码成功
2025-08-03 00:38:59,217 - main - INFO - --- 请求结束: 200 ---

2025-08-03 00:39:09,975 - monitoring - DEBUG - 资源指标更新: 内存使用率 76.4%, CPU使用率 57.1%
2025-08-03 00:39:25,081 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.1%, CPU使用率 21.4%
2025-08-03 00:39:40,186 - monitoring - DEBUG - 资源指标更新: 内存使用率 75.2%, CPU使用率 37.5%
2025-08-03 00:39:53,087 - health_monitor - DEBUG - 系统指标 - CPU: 47.9%, 内存: 71.9%, 磁盘: 83.8%
2025-08-03 00:39:55,474 - monitoring - DEBUG - 资源指标更新: 内存使用率 72.0%, CPU使用率 97.7%
