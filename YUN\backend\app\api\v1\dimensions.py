# -*- coding: utf-8 -*-
"""
维度分析API端点

此模块提供了维度分析相关的API端点，包括：
- 获取量表/问卷的维度定义
- 计算维度分数
- 生成维度分析报告
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.base_session import get_db
from app.models.assessment import AssessmentTemplate, AssessmentResponse
from app.models.questionnaire import QuestionnaireTemplate, QuestionnaireResponse
from app.clinical_scales.services.dimension_service import DimensionService
from app.core.auth import get_current_user
from app.models.user import User

router = APIRouter()


@router.get("/assessment/{template_id}/dimensions")
async def get_assessment_dimensions(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取评估量表的维度定义
    
    Args:
        template_id: 量表模板ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        维度定义信息
    """
    # 获取量表模板
    template = db.query(AssessmentTemplate).filter(
        AssessmentTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="量表模板不存在"
        )
    
    return {
        "template_id": template.id,
        "template_name": template.name,
        "dimensions": template.dimensions or [],
        "has_dimensions": bool(template.dimensions)
    }


@router.get("/questionnaire/{template_id}/dimensions")
async def get_questionnaire_dimensions(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取问卷的维度定义
    
    Args:
        template_id: 问卷模板ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        维度定义信息
    """
    # 获取问卷模板
    template = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.id == template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问卷模板不存在"
        )
    
    return {
        "template_id": template.id,
        "template_name": template.name,
        "dimensions": template.dimensions or [],
        "has_dimensions": bool(template.dimensions)
    }


@router.get("/assessment/response/{response_id}/dimension-scores")
async def get_assessment_dimension_scores(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取评估回答的维度分数
    
    Args:
        response_id: 回答ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        维度分数信息
    """
    # 获取评估回答
    response = db.query(AssessmentResponse).filter(
        AssessmentResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评估回答不存在"
        )
    
    # 检查权限（只能查看自己的回答）
    if response.custom_id != current_user.custom_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此评估回答"
        )
    
    # 获取模板
    template = db.query(AssessmentTemplate).filter(
        AssessmentTemplate.id == response.assessment.template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评估模板不存在"
        )
    
    # 计算维度分数
    dimension_service = DimensionService(db)
    dimension_scores = dimension_service.calculate_assessment_dimensions(response, template)
    
    # 生成分析报告
    report = dimension_service.generate_dimension_report(dimension_scores)
    
    return {
        "response_id": response.id,
        "assessment_id": response.assessment_id,
        "template_name": template.name,
        "dimension_scores": dimension_scores,
        "analysis_report": report,
        "calculated_at": response.updated_at.isoformat() if response.updated_at else None
    }


@router.get("/questionnaire/response/{response_id}/dimension-scores")
async def get_questionnaire_dimension_scores(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取问卷回答的维度分数
    
    Args:
        response_id: 回答ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        维度分数信息
    """
    # 获取问卷回答
    response = db.query(QuestionnaireResponse).filter(
        QuestionnaireResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问卷回答不存在"
        )
    
    # 检查权限（只能查看自己的回答）
    if response.custom_id != current_user.custom_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此问卷回答"
        )
    
    # 获取模板
    template = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.id == response.questionnaire.template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问卷模板不存在"
        )
    
    # 计算维度分数
    dimension_service = DimensionService(db)
    dimension_scores = dimension_service.calculate_questionnaire_dimensions(response, template)
    
    # 生成分析报告
    report = dimension_service.generate_dimension_report(dimension_scores)
    
    return {
        "response_id": response.id,
        "questionnaire_id": response.questionnaire_id,
        "template_name": template.name,
        "dimension_scores": dimension_scores,
        "analysis_report": report,
        "calculated_at": response.updated_at.isoformat() if response.updated_at else None
    }


@router.post("/assessment/response/{response_id}/recalculate-dimensions")
async def recalculate_assessment_dimensions(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """重新计算评估回答的维度分数并保存
    
    Args:
        response_id: 回答ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        更新后的维度分数信息
    """
    # 获取评估回答
    response = db.query(AssessmentResponse).filter(
        AssessmentResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评估回答不存在"
        )
    
    # 检查权限
    if response.custom_id != current_user.custom_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权修改此评估回答"
        )
    
    # 获取模板
    template = db.query(AssessmentTemplate).filter(
        AssessmentTemplate.id == response.assessment.template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评估模板不存在"
        )
    
    # 计算维度分数
    dimension_service = DimensionService(db)
    dimension_scores = dimension_service.calculate_assessment_dimensions(response, template)
    
    # 保存维度分数到数据库
    response.dimension_scores = dimension_scores
    db.commit()
    
    # 生成分析报告
    report = dimension_service.generate_dimension_report(dimension_scores)
    
    return {
        "response_id": response.id,
        "message": "维度分数已重新计算并保存",
        "dimension_scores": dimension_scores,
        "analysis_report": report
    }


@router.post("/questionnaire/response/{response_id}/recalculate-dimensions")
async def recalculate_questionnaire_dimensions(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """重新计算问卷回答的维度分数并保存
    
    Args:
        response_id: 回答ID
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        更新后的维度分数信息
    """
    # 获取问卷回答
    response = db.query(QuestionnaireResponse).filter(
        QuestionnaireResponse.id == response_id
    ).first()
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问卷回答不存在"
        )
    
    # 检查权限
    if response.custom_id != current_user.custom_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权修改此问卷回答"
        )
    
    # 获取模板
    template = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.id == response.questionnaire.template_id
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="问卷模板不存在"
        )
    
    # 计算维度分数
    dimension_service = DimensionService(db)
    dimension_scores = dimension_service.calculate_questionnaire_dimensions(response, template)
    
    # 保存维度分数到数据库
    response.dimension_scores = dimension_scores
    db.commit()
    
    # 生成分析报告
    report = dimension_service.generate_dimension_report(dimension_scores)
    
    return {
        "response_id": response.id,
        "message": "维度分数已重新计算并保存",
        "dimension_scores": dimension_scores,
        "analysis_report": report
    }