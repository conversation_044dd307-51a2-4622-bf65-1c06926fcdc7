#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移动端API获取的数据是否包含description和instructions字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.cloud_api import CloudAPI
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mobile_api_fields():
    """测试移动端API字段"""
    print("=== 测试移动端API字段 ===")
    
    # 创建API客户端
    api = CloudAPI()
    
    # 检查是否已有token
    if api.token:
        print(f"使用已有token: {api.token[:20]}...")
    else:
        print("未找到有效token，请先在移动端登录")
        return
    
    # 测试获取评估量表
    print("\n=== 测试获取评估量表 ===")
    result = api.get_mobile_assessments()
    
    if not result:
        print("API调用失败，无返回数据")
        return
        
    print(f"API返回状态: {result.get('status')}")
    
    if result.get('status') != 'success':
        print(f"API调用失败: {result.get('message')}")
        return
    
    data = result.get('data', [])
    if isinstance(data, dict):
        data = data.get('assessments', [])
    
    print(f"获取到 {len(data)} 个评估量表")
    
    # 检查每个评估量表的字段
    for i, assessment in enumerate(data):
        print(f"\n--- 评估量表 {i+1} ---")
        print(f"ID: {assessment.get('id')}")
        print(f"名称: {assessment.get('name')}")
        print(f"标题: {assessment.get('title')}")
        print(f"状态: {assessment.get('status')}")
        
        # 检查直接字段
        description = assessment.get('description')
        instructions = assessment.get('instructions')
        
        print(f"直接description字段: {'✅ 有值' if description else '❌ 无值或空'}")
        if description:
            print(f"  内容: {description[:100]}...")
            
        print(f"直接instructions字段: {'✅ 有值' if instructions else '❌ 无值或空'}")
        if instructions:
            print(f"  内容: {instructions[:100]}...")
        
        # 检查template子对象
        template = assessment.get('template')
        if template:
            print(f"template对象: ✅ 存在")
            template_desc = template.get('description')
            template_inst = template.get('instructions')
            
            print(f"template.description字段: {'✅ 有值' if template_desc else '❌ 无值或空'}")
            if template_desc:
                print(f"  内容: {template_desc[:100]}...")
                
            print(f"template.instructions字段: {'✅ 有值' if template_inst else '❌ 无值或空'}")
            if template_inst:
                print(f"  内容: {template_inst[:100]}...")
        else:
            print(f"template对象: ❌ 不存在")
        
        # 只显示前3个评估量表的详细信息
        if i >= 2:
            break
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_mobile_api_fields()