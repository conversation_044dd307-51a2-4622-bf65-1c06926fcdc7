#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移除未使用变量脚本

根据分析报告移除medication_management_screen.py中未使用的变量：
- DB_VERSION
- MAX_CACHE_SIZE
"""

import os
import re
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('remove_unused_variables.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def remove_unused_variables(file_path: str):
    """移除未使用的变量"""
    logger.info("🔧 开始移除未使用的变量...")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        modifications = []
        
        # 移除DB_VERSION变量定义
        db_version_pattern = r'^DB_VERSION\s*=\s*\d+\s*$'
        if re.search(db_version_pattern, content, re.MULTILINE):
            content = re.sub(db_version_pattern, '', content, flags=re.MULTILINE)
            modifications.append("移除未使用的DB_VERSION变量")
            logger.info("✅ 移除DB_VERSION变量")
        
        # 移除MAX_CACHE_SIZE变量定义
        max_cache_pattern = r'^MAX_CACHE_SIZE\s*=\s*\d+\s*$'
        if re.search(max_cache_pattern, content, re.MULTILINE):
            content = re.sub(max_cache_pattern, '', content, flags=re.MULTILINE)
            modifications.append("移除未使用的MAX_CACHE_SIZE变量")
            logger.info("✅ 移除MAX_CACHE_SIZE变量")
        
        # 清理多余的空行（连续的空行合并为单个空行）
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 如果有修改，写入文件
        if content != original_content:
            # 创建备份
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{file_path}_backup_unused_vars_{timestamp}"
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            logger.info(f"📁 备份文件已创建: {backup_path}")
            
            # 写入修改后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"✅ 文件修改完成: {file_path}")
            for mod in modifications:
                logger.info(f"  - {mod}")
            
            return True, modifications, backup_path
        else:
            logger.info("ℹ️ 未发现需要移除的变量")
            return False, [], None
            
    except Exception as e:
        logger.error(f"处理文件失败: {e}")
        return False, [], None

def main():
    """主函数"""
    logger.info("🚀 开始移除未使用变量...")
    
    file_path = "c:\\Users\\<USER>\\Desktop\\health-Trea\\mobile\\screens\\medication_management_screen.py"
    
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return
    
    success, modifications, backup_path = remove_unused_variables(file_path)
    
    # 输出结果
    print("\n" + "="*60)
    if success:
        print("🎉 变量清理完成!")
        print(f"📁 备份文件: {backup_path}")
        print("🔧 应用的修改:")
        for mod in modifications:
            print(f"  - {mod}")
    else:
        print("ℹ️ 无需修改")
    print("="*60)

if __name__ == "__main__":
    main()