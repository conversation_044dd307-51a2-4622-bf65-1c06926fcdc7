"""健康日记屏幕模块

提供健康日记的记录、查看、编辑和统计功能。
"""

import os
import json
from datetime import datetime

from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp
from kivy.properties import StringProperty, DictProperty
from kivy.uix.boxlayout import BoxLayout
from kivy.clock import Clock
from kivy.lang import Builder

from kivymd.app import MDApp
from kivymd.uix.button import MDButton, MDButtonText, MDFabButton
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.label import MDLabel
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.textfield import MDTextField
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.divider import MDDivider

from .base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import AppTheme, AppMetrics, FontStyles
from utils.health_diary_api import get_health_diary_api

# 设置日志
logger = KivyLogger

# KV语言定义界面
KV = '''
<DiaryEntryCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(160)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    spacing: dp(8)
    
    MDBoxLayout:
        size_hint_y: None
        height: dp(24)
        spacing: dp(8)
        
        MDLabel:
            text: root.date_text
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY
            font_style: "Body"
            role: "medium"
            size_hint_x: 0.5
            halign: "left"
        
        MDLabel:
            text: root.time_text
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            font_style: "Body"
            role: "small"
            size_hint_x: 0.3
            halign: "left"
        
        MDLabel:
            text: root.mood_text
            theme_text_color: "Custom"
            text_color: app.theme.PRIMARY_COLOR
            font_style: "Headline"
            role: "small"
            size_hint_x: 0.1
            halign: "right"
        
        MDBoxLayout:
            size_hint_x: 0.1
            spacing: dp(4)
            MDIconButton:
                icon: "pencil"
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_COLOR
                on_release: root.edit_entry()
            MDIconButton:
                icon: "delete"
                theme_icon_color: "Custom"
                icon_color: app.theme.ERROR_COLOR
                on_release: root.delete_entry()
    
    MDDivider:
        height: dp(1)
        md_bg_color: app.theme.DIVIDER_COLOR
    
    MDLabel:
        text: root.content_text
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        font_style: "Body"
        role: "medium"

<HealthDiaryScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    MDBoxLayout:
        orientation: 'vertical'
        size_hint_y: 1
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            MDLabel:
                text: "健康日记"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            MDIconButton:
                icon: "calendar"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.show_calendar()
        # 滚动内容区
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            MDBoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                spacing: dp(16)
                padding: [dp(0), dp(0), dp(0), dp(80)]
                # 顶部logo
                HealthLogo:
                    id: top_logo
                    size_hint_y: None
                    height: dp(120)
                    logo_size: dp(80), dp(80)
                    title_font_size: dp(18)
                    subtitle_font_size: dp(14)
                # 统计卡片
                MDCard:
                    id: statistics_card
                    size_hint_y: None
                    height: dp(100)
                    md_bg_color: app.theme.PRIMARY_COLOR
                    radius: [dp(12)]
                    elevation: 2
                    padding: [dp(16), dp(16), dp(16), dp(16)]
                    MDBoxLayout:
                        orientation: "horizontal"
                        spacing: dp(16)
                        # 本月记录
                        MDBoxLayout:
                            orientation: "vertical"
                            size_hint_x: 0.33
                            MDLabel:
                                text: "本月记录"
                                theme_text_color: "Custom"
                                text_color: 1, 1, 1, 1
                                font_style: "Body"
                                role: "small"
                                halign: "center"
                            MDLabel:
                                id: month_count_label
                                text: "0 条"
                                theme_text_color: "Custom"
                                text_color: 1, 1, 1, 1
                                font_style: "Headline"
                                role: "small"
                                halign: "center"
                        # 连续记录
                        MDBoxLayout:
                            orientation: "vertical"
                            size_hint_x: 0.33
                            MDLabel:
                                text: "连续记录"
                                theme_text_color: "Custom"
                                text_color: 1, 1, 1, 1
                                font_style: "Body"
                                role: "small"
                                halign: "center"
                            MDLabel:
                                id: streak_count_label
                                text: "0 天"
                                theme_text_color: "Custom"
                                text_color: 1, 1, 1, 1
                                font_style: "Headline"
                                role: "small"
                                halign: "center"
                        # 平均心情
                        MDBoxLayout:
                            orientation: "vertical"
                            size_hint_x: 0.33
                            MDLabel:
                                text: "平均心情"
                                theme_text_color: "Custom"
                                text_color: 1, 1, 1, 1
                                font_style: "Body"
                                role: "small"
                                halign: "center"
                            MDLabel:
                                id: avg_mood_label
                                text: "😐"
                                theme_text_color: "Custom"
                                text_color: 1, 1, 1, 1
                                font_style: "Headline"
                                role: "large"
                                halign: "center"
                # 日记列表
                MDBoxLayout:
                    id: diary_list
                    orientation: "vertical"
                    spacing: dp(16)
                    size_hint_y: None
                    height: self.minimum_height
        # 悬浮添加按钮
        MDFabButton:
            icon: "plus"
            md_bg_color: app.theme.ACCENT_COLOR
            pos_hint: {"right": 0.98, "y": 0.02}
            on_release: root.add_entry()
'''

# 修改DiaryEntryCard类，使用Factory.MDCard而不是直接继承MDCard
from kivy.factory import Factory

# 修改DiaryEntryCard类，确保它不依赖于__getattr__方法
class DiaryEntryCard(Factory.MDCard):
    """日记条目卡片"""
    date_text = StringProperty("")
    time_text = StringProperty("")
    content_text = StringProperty("")
    mood_text = StringProperty("😐")
    entry_data = DictProperty({})
    
    # 显式定义可能会通过__getattr__访问的属性
    size_hint_y = None
    height = dp(160)
    padding = dp(16)
    spacing = dp(8)
    orientation = "vertical"
    
    def __init__(self, **kwargs):
        # 使用super()初始化
        super(DiaryEntryCard, self).__init__(**kwargs)
        self.app = MDApp.get_running_app()
        
        # 显式设置属性，避免通过__getattr__访问
        self.md_bg_color = self.app.theme.CARD_BACKGROUND
        self.radius = [dp(12)]
        self.elevation = 2
    
    def edit_entry(self):
        """编辑条目"""
        screen = self.parent.parent.parent.parent
        if hasattr(screen, 'edit_entry'):
            screen.edit_entry(self.entry_data)
    
    def delete_entry(self):
        """删除条目"""
        screen = self.parent.parent.parent.parent
        if hasattr(screen, 'delete_entry'):
            screen.delete_entry(self.entry_data)

# 只在模块顶部加载一次KV
Builder.load_string(KV)

class HealthDiaryScreen(BaseScreen):
    """健康日记屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.diary_api = get_health_diary_api()
        self.diary_entries = []
        Clock.schedule_once(self.init_ui, 0.2)
    
    def init_ui(self, dt=0):
        try:
            self.load_diary_entries()
            self.update_statistics()
            self.refresh_diary_list()
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 初始化失败: {e}")
            self.show_error(f"初始化失败: {str(e)}")
    
    def on_enter(self):
        # 只在首次进入时初始化UI，避免重复刷新
        if not getattr(self, '_ui_initialized', False):
            self.init_ui()
            self._ui_initialized = True
    
    def go_back(self):
        """返回主页"""
        app = MDApp.get_running_app()
        if app and app.root:
            app.root.current = 'homepage_screen'
    
    def show_calendar(self):
        """显示日历"""
        # TODO: 实现日历功能
        self.show_info("日历功能即将上线")
    
    def add_entry(self):
        """添加日记条目"""
        self.show_diary_entry_dialog()
    
    def show_diary_entry_dialog(self, entry_data=None):
        """显示日记条目对话框"""
        is_edit = entry_data is not None
        title = "编辑健康日记" if is_edit else "添加健康日记"
        # 创建内容布局
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(16),
            size_hint_y=None,
            height=dp(350)
        )
        # 症状输入
        symptoms_field = MDTextField(
            hint_text="今日症状（如有）",
            multiline=True,
            max_height=dp(100)
        )
        if is_edit and 'symptoms' in entry_data:
            symptoms_field.text = entry_data['symptoms']
        content.add_widget(symptoms_field)
        # 用药输入
        medication_field = MDTextField(
            hint_text="今日用药（如有）",
            multiline=True,
            max_height=dp(100)
        )
        if is_edit and 'medication' in entry_data:
            medication_field.text = entry_data['medication']
        content.add_widget(medication_field)
        # 心情选择
        mood_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            spacing=dp(8)
        )
        moods = ["😢", "😞", "😐", "😊", "😄"]
        mood_buttons = []
        selected_mood = [2]  # 默认选择中性
        if is_edit and 'mood' in entry_data:
            try:
                selected_mood[0] = moods.index(entry_data['mood'])
            except ValueError:
                selected_mood[0] = 2
        def on_mood_select(index):
            selected_mood[0] = index
            for i, btn in enumerate(mood_buttons):
                btn.style = "filled" if i == index else "outlined"
        for i, mood in enumerate(moods):
            btn = MDButton(
                MDButtonText(text=mood),
                style="filled" if i == selected_mood[0] else "outlined",
                size_hint_x=None,
                width=dp(50)
            )
            btn.bind(on_release=lambda x, idx=i: on_mood_select(idx))
            mood_buttons.append(btn)
            mood_layout.add_widget(btn)
        content.add_widget(mood_layout)
        # 备注输入
        notes_field = MDTextField(
            hint_text="其他备注（可选）",
            multiline=True,
            max_height=dp(100)
        )
        if is_edit and 'notes' in entry_data:
            notes_field.text = entry_data['notes']
        content.add_widget(notes_field)
        # 创建对话框（KivyMD 2.x新API）
        dialog = MDDialog()
        dialog.add_widget(MDLabel(text=title, font_style="Title", size_hint_y=None, height=dp(40)))
        dialog.add_widget(content)
        button_container = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48), padding=[dp(16), 0, dp(16), dp(8)])
        cancel_btn = MDButton(MDButtonText(text="取消"), style="text", on_release=lambda x: dialog.dismiss())
        save_btn = MDButton(MDButtonText(text="保存"), style="filled", on_release=lambda x: self.save_diary_entry(
            dialog, symptoms_field, medication_field, moods[selected_mood[0]], notes_field, entry_data
        ))
        button_container.add_widget(cancel_btn)
        button_container.add_widget(save_btn)
        dialog.add_widget(button_container)
        dialog.open()
    
    def save_diary_entry(self, dialog, symptoms_field, medication_field, mood, notes_field, entry_data=None):
        """保存日记条目"""
        try:
            # 收集数据
            now = datetime.now()
            
            # 准备数据
            entry = {
                'date': entry_data['date'] if entry_data else now.strftime('%Y-%m-%d'),
                'time': entry_data['time'] if entry_data else now.strftime('%H:%M'),
                'symptoms': symptoms_field.text.strip(),
                'medication': medication_field.text.strip(),
                'mood': mood,
                'notes': notes_field.text.strip()
            }
            
            # 使用API保存数据
            if entry_data:
                # 编辑现有条目
                success = self.diary_api.update_diary_entry(entry_data['id'], entry)
                action = "更新"
            else:
                # 添加新条目
                entry_id = self.diary_api.create_diary_entry(entry)
                success = entry_id is not None
                action = "添加"
            
            # 关闭对话框
            dialog.dismiss()
            
            if success:
                # 重新加载数据
                self.load_diary_entries()
                self.update_statistics()
                self.refresh_diary_list()
                
                # 显示成功消息
                self.show_info(f"日记条目{action}成功")
            else:
                self.show_info(f"日记条目{action}失败，请重试")
            
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 保存日记条目失败: {e}")
            self.show_error(f"保存失败: {str(e)}")
    
    def edit_entry(self, entry_data):
        """编辑日记条目"""
        self.show_diary_entry_dialog(entry_data)
    
    def delete_entry(self, entry_data):
        """删除日记条目"""
        dialog = MDDialog(
            title="确认删除",
            text="您确定要删除这条日记吗？",
            buttons=[
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="删除"),
                    style="filled",
                    theme_bg_color="Custom",
                    md_bg_color=self.app.theme.ERROR_COLOR,
                    on_release=lambda x: self.confirm_delete_entry(dialog, entry_data)
                ),
            ],
        )
        dialog.open()
    
    def confirm_delete_entry(self, dialog, entry_data):
        """确认删除日记条目"""
        try:
            # 使用API删除数据
            success = self.diary_api.delete_diary_entry(entry_data['id'])
            
            # 关闭对话框
            dialog.dismiss()
            
            if success:
                # 重新加载数据
                self.load_diary_entries()
                self.update_statistics()
                self.refresh_diary_list()
                
                # 显示成功消息
                self.show_info("日记条目删除成功")
            else:
                self.show_info("删除失败，请重试")
            
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 删除日记条目失败: {e}")
            self.show_error(f"删除失败: {str(e)}")
    
    def load_diary_entries(self):
        """加载日记条目"""
        try:
            # 使用API获取数据
            self.diary_entries = self.diary_api.get_diary_entries(limit=100)
            
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 加载日记条目失败: {e}")
            self.diary_entries = []
            self.show_error(f"加载日记条目失败: {str(e)}")
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            # 获取统计信息
            stats = self.diary_api.get_diary_statistics()
            
            # 更新UI - 先检查ids是否存在
            if hasattr(self, 'ids'):
                if hasattr(self.ids, 'month_count_label') and self.ids.month_count_label:
                    self.ids.month_count_label.text = f"{stats['month_entries']} 条"
                if hasattr(self.ids, 'streak_count_label') and self.ids.streak_count_label:
                    self.ids.streak_count_label.text = f"{stats['streak_days']} 天"
                if hasattr(self.ids, 'avg_mood_label') and self.ids.avg_mood_label:
                    self.ids.avg_mood_label.text = stats['average_mood']
            else:
                KivyLogger.warning("HealthDiaryScreen: ids不存在，无法更新统计信息")
            
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 更新统计信息失败: {e}")
            # 设置默认值
            if hasattr(self, 'ids'):
                if hasattr(self.ids, 'month_count_label') and self.ids.month_count_label:
                    self.ids.month_count_label.text = "0 条"
                if hasattr(self.ids, 'streak_count_label') and self.ids.streak_count_label:
                    self.ids.streak_count_label.text = "0 天"
                if hasattr(self.ids, 'avg_mood_label') and self.ids.avg_mood_label:
                    self.ids.avg_mood_label.text = "😐"
                
    def refresh_diary_list(self):
        """刷新日记列表"""
        try:
            # 检查diary_list是否存在
            if not hasattr(self, 'ids') or not hasattr(self.ids, 'diary_list') or not self.ids.diary_list:
                KivyLogger.warning("HealthDiaryScreen: diary_list不存在，无法刷新日记列表")
                return
                
            self.ids.diary_list.clear_widgets()
            
            if not self.diary_entries:
                # 显示空状态
                # 创建布局
                box_layout = MDBoxLayout(
                    orientation='vertical',
                    padding=dp(32)
                )
                
                # 创建标签
                label = MDLabel(
                    text="还没有日记记录\n点击右下角的 + 按钮开始记录吧！",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium"
                )
                
                # 添加标签到布局
                box_layout.add_widget(label)
                
                # 创建卡片并添加布局 - 直接使用MDCard而不是Factory.MDCard
                from kivymd.uix.card import MDCard
                empty_card = MDCard()
                empty_card.size_hint_y = None
                empty_card.height = dp(120)
                empty_card.md_bg_color = self.app.theme.CARD_BACKGROUND
                empty_card.radius = [dp(12)]
                empty_card.elevation = 2
                empty_card.add_widget(box_layout)
                
                self.ids.diary_list.add_widget(empty_card)
                return
            
            # 添加日记条目
            for entry in self.diary_entries:
                # 构建内容文本
                content_parts = []
                if entry.get('symptoms'):
                    content_parts.append(f"症状：{entry['symptoms']}")
                if entry.get('medication'):
                    content_parts.append(f"用药：{entry['medication']}")
                if entry.get('notes'):
                    content_parts.append(f"备注：{entry['notes']}")
                
                content_text = "\n".join(content_parts) if content_parts else "无详细记录"
                
                # 创建日记条目卡片 - 使用DiaryEntryCard类
                entry_card = DiaryEntryCard()
                entry_card.date_text = entry['date']
                entry_card.time_text = entry['time']
                entry_card.content_text = content_text
                entry_card.mood_text = entry.get('mood', '😐')
                entry_card.entry_data = entry
                
                self.ids.diary_list.add_widget(entry_card)
                
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 刷新日记列表失败: {e}")
            self.show_error(f"刷新日记列表失败: {str(e)}")
    
    def show_info(self, message):
        """显示信息提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 显示信息失败: {e}")
    
    def show_error(self, message):
        """显示错误提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_error'):
                app.show_error(message)
            else:
                self.show_info(f"错误: {message}")
        except Exception as e:
            KivyLogger.error(f"HealthDiaryScreen: 显示错误失败: {e}")

# 添加异常处理装饰器
def exception_handler(func):
    """装饰器：捕获并处理函数执行过程中的异常"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            Logger.error(f"{func.__name__}失败: {str(e)}")
            # 返回一个安全的默认值，避免应用崩溃
            if func.__name__ == 'update_statistics':
                return None
            elif func.__name__ == 'refresh_diary_list':
                return None
            elif func.__name__ == 'get_diary_statistics':
                return {
                    'total_entries': 0,
                    'month_entries': 0,
                    'streak_days': 0,
                    'average_mood': '😐',
                    'last_entry_date': None
                }
            return None
    return wrapper

# 应用异常处理装饰器到关键方法
def apply_exception_handlers(cls):
    """为类的方法应用异常处理装饰器"""
    methods_to_wrap = ['update_statistics', 'refresh_diary_list']
    for method_name in methods_to_wrap:
        if hasattr(cls, method_name):
            original_method = getattr(cls, method_name)
            setattr(cls, method_name, exception_handler(original_method))
    return cls

# 在类定义后应用装饰器
apply_exception_handlers(HealthDiaryScreen)