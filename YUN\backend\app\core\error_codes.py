class ErrorCodes:
    SUCCESS = 0
    TOKEN_EXPIRED = 4001
    PERMISSION_DENIED = 4003
    NOT_FOUND = 4004
    VALIDATION_ERROR = 4005
    SERVER_ERROR = 5000
    # 可继续扩展

ERROR_MESSAGES = {
    ErrorCodes.SUCCESS: "success",
    ErrorCodes.TOKEN_EXPIRED: "Token expired",
    ErrorCodes.PERMISSION_DENIED: "Permission denied",
    ErrorCodes.NOT_FOUND: "Resource not found",
    ErrorCodes.VALIDATION_ERROR: "Validation error",
    ErrorCodes.SERVER_ERROR: "Internal server error",
} 