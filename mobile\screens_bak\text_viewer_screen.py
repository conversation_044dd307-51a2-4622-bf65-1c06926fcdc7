# -*- coding: utf-8 -*-
import logging
import os
import threading
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from kivy.uix.scrollview import ScrollView

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton
from kivymd.uix.label import MDLabel

from screens.base_screen import BaseScreen
from kivy.factory import Factory

# 获取日志记录器
logger = logging.getLogger(__name__)

class TextViewerScreen(BaseScreen):
    """文本查看器屏幕"""
    title = StringProperty("文本查看器")
    text_source = StringProperty("")
    text_content = StringProperty("")
    is_loading = BooleanProperty(False)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        Clock.schedule_once(self.init_ui)
    
    def init_ui(self, dt=None):
        """初始化UI"""
        try:
            # 获取文本源
            self.text_source = getattr(self.app, 'text_to_view', "")
            
            # 获取标题
            text_title = getattr(self.app, 'text_title', "")
            if text_title:
                self.title = text_title
            
            # 加载文本
            if self.text_source:
                self.load_text()
            else:
                self.show_error("未找到文本源")
        except Exception as e:
            logger.error(f"初始化文本查看器时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"初始化文本查看器时出错: {str(e)}")
    
    def load_text(self):
        """加载文本"""
        try:
            self.is_loading = True
            
            # 清空文本容器
            text_container = self.ids.text_container
            text_container.clear_widgets()
            
            # 创建文本加载提示
            loading_label = MDLabel(
                text="正在加载文本...",
                halign="center",
                valign="center",
                theme_text_color="Secondary"
            )
            text_container.add_widget(loading_label)
            
            # 在后台线程中加载文本
            threading.Thread(target=self._load_text_thread, args=(loading_label,)).start()
        except Exception as e:
            logger.error(f"加载文本时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"加载文本时出错: {str(e)}")
            self.is_loading = False
    
    def _load_text_thread(self, loading_label):
        """后台线程加载文本"""
        try:
            # 判断文本源类型
            if self.text_source.startswith(('http://', 'https://')):
                # 从URL加载
                import requests
                response = requests.get(self.text_source, timeout=30)
                response.raise_for_status()
                text = response.text
            elif os.path.exists(self.text_source):
                # 从文件加载
                with open(self.text_source, 'r', encoding='utf-8') as f:
                    text = f.read()
            else:
                # 直接使用文本源
                text = self.text_source
            
            # 在主线程中更新UI
            Clock.schedule_once(lambda dt: self._update_text_ui(text, loading_label))
        except Exception as e:
            logger.error(f"加载文本线程出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            Clock.schedule_once(lambda dt: self._on_text_error(str(e), loading_label))
    
    def _update_text_ui(self, text, loading_label):
        """更新文本UI"""
        try:
            # 移除加载提示 - 先移除特定的loading_label
            if loading_label and loading_label in self.ids.text_container.children:
                self.ids.text_container.remove_widget(loading_label)
            
            # 清空容器，确保移除所有可能残留的加载提示
            self.ids.text_container.clear_widgets()
            
            # 更新文本内容
            self.text_content = text
            
            # 创建滚动视图
            scroll_view = ScrollView(
                do_scroll_x=False,
                do_scroll_y=True
            )
            
            # 创建文本标签
            text_label = MDLabel(
                text=self.text_content,
                theme_text_color="Primary",
                size_hint_y=None,
                padding=[dp(16), dp(16)],
                text_size=(None, None),
                halign="left",
                valign="top"
            )
            # 设置文本标签的宽度和高度
            text_label.bind(texture_size=lambda instance, size: setattr(instance, 'height', size[1]))
            text_label.bind(width=lambda instance, width: setattr(instance, 'text_size', (width - dp(32), None)))
            
            # 添加标签到滚动视图
            scroll_view.add_widget(text_label)
            
            # 添加滚动视图到容器
            self.ids.text_container.add_widget(scroll_view)
            
            self.is_loading = False
            logger.info(f"文本加载完成，内容长度: {len(text)} 字符")
        except Exception as e:
            logger.error(f"更新文本UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"更新文本UI时出错: {str(e)}")
            self.is_loading = False
    
    def _on_text_error(self, error_msg, loading_label):
        """文本加载失败处理"""
        try:
            # 移除加载提示 - 先移除特定的loading_label
            if loading_label and loading_label in self.ids.text_container.children:
                self.ids.text_container.remove_widget(loading_label)
            
            # 清空容器，确保移除所有可能残留的加载提示
            self.ids.text_container.clear_widgets()
            
            # 显示错误信息
            error_label = MDLabel(
                text=f"文本加载失败: {error_msg}",
                halign="center",
                valign="center",
                theme_text_color="Error"
            )
            self.ids.text_container.add_widget(error_label)
            
            self.is_loading = False
        except Exception as e:
            logger.error(f"文本加载失败处理时出错: {e}")
    
    def go_back(self, *args):
        """返回上一页"""
        app = MDApp.get_running_app()
        app.root.transition.direction = 'right'
        app.root.current = 'health_document_screen'
    
    def show_error(self, message):
        """显示错误信息"""
        logger.error(message)
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            
            MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=self.app.theme.ERROR_COLOR,
            ).open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")

# 注册屏幕
Factory.register('TextViewerScreen', cls=TextViewerScreen)

# 定义KV字符串
KV = '''
<TextViewerScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: root.title
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
                font_size: dp(20)
                bold: True
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.load_text()
        
        # 文本容器
        MDBoxLayout:
            id: text_container
            orientation: 'vertical'
            padding: dp(16)
'''

# 加载KV字符串
from kivy.lang import Builder
Builder.load_string(KV)
print("TextViewerScreen KV loaded")
