from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
import os
import json
import logging
from datetime import datetime  # 添加datetime模块导入

# 获取日志记录器
logger = logging.getLogger(__name__)

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import (
    MDTextField,
    MDTextFieldLeadingIcon,
    MDTextFieldHintText,
    MDTextFieldHelperText,
    MDTextFieldTrailingIcon,
    MDTextFieldMaxLengthText
)
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dropdownitem.dropdownitem import MDDropDownItem, MDDropDownItemText
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.scrollview import ScrollView
from kivymd.uix.card import MDCard

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入云端API
from utils.cloud_api import get_cloud_api

# 导入安全配置
from utils.security_config import validate_password_strength, login_limiter
import time

# 不再导入Logo组件，使用main.py中导入的版本
# from widgets.logo import HealthLogo  # 只导入HealthLogo组件，不导入add_logo_to_layout

# 定义KV语言字符串
KV = '''
<LoginTab>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    spacing: dp(20)
    padding: [0, dp(10), 0, 0]

    MDTextField:
        id: username
        mode: "outlined"
        size_hint_y: None
        height: dp(56)  # 增加高度，减少拥挤感
        font_size: dp(16)  # 增加字体大小

        MDTextFieldLeadingIcon:
            icon: "account"
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR

        MDTextFieldHintText:
            text: "请输入用户名/手机号"

    MDTextField:
        id: password
        mode: "outlined"
        size_hint_y: None
        height: dp(56)  # 增加高度，减少拥挤感
        font_size: dp(16)  # 增加字体大小
        password: True

        MDTextFieldLeadingIcon:
            icon: "lock"
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR

        MDTextFieldHintText:
            text: "请输入密码"

        MDTextFieldTrailingIcon:
            id: password_icon
            icon: "eye-off"
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR

    MDBoxLayout:
        size_hint_y: None
        height: dp(30)
        spacing: dp(10)
        padding: [dp(4), 0, dp(4), 0]

        # 左侧显示"记住密码"
        MDBoxLayout:
            adaptive_width: True
            spacing: dp(8)

            MDCheckbox:
                id: remember_password
                size_hint: None, None
                size: dp(24), dp(24)
                on_active: app.root.get_screen('login_screen').remember_password = self.active

            MDLabel:
                text: "记住密码"
                adaptive_size: True
                font_style: "Body"
                role: "small"
                pos_hint: {"center_y": 0.5}

        # 右侧显示"忘记密码"
        Widget:
            size_hint_x: 1

        MDButton:
            style: "text"
            pos_hint: {"center_y": 0.5}
            on_release: app.root.get_screen('login_screen').on_forgot_password()
            padding: [0, 0, 0, 0]

            MDButtonText:
                text: "忘记密码?"
                theme_text_color: "Primary"
                font_style: "Body"
                role: "small"

<PhoneTab>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(40)
    spacing: dp(20)
    padding: [0, dp(10), 0, 0]

    MDTextField:
        id: phone
        mode: "outlined"
        size_hint_y: None
        height: dp(56)  # 增加高度，减少拥挤感
        font_size: dp(16)  # 增加字体大小
        input_filter: "int"

        MDTextFieldLeadingIcon:
            icon: "phone"
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR

        MDTextFieldHintText:
            text: "请输入手机号"

        MDTextFieldMaxLengthText:
            max_text_length: 11

    MDBoxLayout:
        size_hint_y: None
        height: dp(56)  # 增加高度，减少拥挤感
        spacing: dp(8)  # 增加间距，减少拥挤感

        MDTextField:
            id: verification_code
            mode: "outlined"
            size_hint_x: 0.7  # 增加输入框比例，给按钮更少空间
            size_hint_y: None
            height: dp(56)  # 增加高度
            font_size: dp(16)  # 增加字体大小
            input_filter: "int"

            MDTextFieldLeadingIcon:
                icon: "numeric"
                theme_icon_color: "Custom"
                icon_color: app.theme.PRIMARY_COLOR

            MDTextFieldHintText:
                text: "请输入验证码"

            MDTextFieldMaxLengthText:
                max_text_length: 6

        MDButton:
            style: "filled"
            size_hint_x: 0.3  # 缩短按钮比例
            height: dp(56)  # 增加高度
            padding: [dp(2), 0, dp(2), 0]  # 减少内边距
            on_release: app.root.get_screen('login_screen').on_request_verification_code()

            MDButtonText:
                text: "获取验证码"
                font_size: dp(13)  # 稍微减小字体以适应更窄的按钮
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT

    # 添加空白区块以保持与账号登录高度一致
    MDBoxLayout:
        size_hint_y: None
        height: dp(30)

<LoginScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    ScrollView:
        id: scroll_view
        do_scroll_x: False
        do_scroll_y: True

        MDBoxLayout:
            id: main_layout
            orientation: 'vertical'
            size_hint_y: None
            height: self.minimum_height
            padding: [dp(10), dp(5), dp(10), dp(5)]
            spacing: dp(2)

            # Logo区域 - 使用统一的Logo组件
            MDBoxLayout:
                id: logo_container
                orientation: 'vertical'
                size_hint_y: None
                height: dp(200)  # Logo区域高度
                padding: [0, dp(10), 0, dp(5)]
                size_hint_x: 0.95
                pos_hint: {"center_x": 0.5}

                # 使用统一的HealthLogo组件
                HealthLogo:
                    id: health_logo
            MDCard:
                id: login_card
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: dp(24)
                spacing: dp(5)
                elevation: 2
                radius: [dp(16)]
                shadow_softness: 8
                shadow_offset: (0, 1)

                MDBoxLayout:
                    id: identity_layout
                    size_hint_y: None
                    height: dp(48)

                    MDLabel:
                        text: "请选择身份："
                        halign: "left"
                        size_hint_y: None
                        height: dp(40)
                        font_size: app.font_styles.BODY_MEDIUM['font_size']  # 明确引用具体样式
                        font_name: app.font_styles.BODY_MEDIUM['font_name']
                        theme_text_color: "Primary"
                        padding: [0, 0, 0, 0]

                    MDDropDownItem:
                        id: identity_selector
                        size_hint_y: None
                        height: dp(48)
                        on_release: root.show_identity_menu(self)

                        MDDropDownItemText:
                            id: identity_text
                            text: "请选择登录身份"
                            font_size: app.font_styles.BODY_MEDIUM['font_size']  # 明确引用具体样式
                            font_name: app.font_styles.BODY_MEDIUM['font_name']

                MDBoxLayout:
                    id: tabs_layout
                    size_hint_y: None
                    height: dp(40)
                    padding: [dp(10), 0, dp(10), 0]

                    MDButton:
                        id: account_tab_btn
                        style: "text"
                        size_hint_x: 0.5
                        md_bg_color: [0, 0, 0, 0]
                        on_release:
                            root.current_tab = "account"
                            root.switch_tab("account")

                        MDButtonText:
                            text: "账号登录"
                            theme_text_color: "Primary" if root.current_tab == "account" else "Secondary"
                            bold: root.current_tab == "account"

                    MDButton:
                        id: phone_tab_btn
                        style: "text"
                        size_hint_x: 0.5
                        md_bg_color: [0, 0, 0, 0]
                        on_release:
                            root.current_tab = "phone"
                            root.switch_tab("phone")

                        MDButtonText:
                            text: "手机号登录"
                            theme_text_color: "Primary" if root.current_tab == "phone" else "Secondary"
                            bold: root.current_tab == "phone"

                MDBoxLayout:
                    id: tab_indicator
                    size_hint_y: None
                    height: dp(4)

                    MDBoxLayout:
                        id: account_indicator
                        size_hint_x: 0.5

                    MDBoxLayout:
                        id: phone_indicator
                        size_hint_x: 0.5

                MDBoxLayout:
                    id: tab_content
                    size_hint_y: None
                    height: self.minimum_height

                MDButton:
                    id: login_button
                    style: "filled"
                    md_bg_color: app.theme.ACCENT_COLOR
                    size_hint_x: 0.8  # 设置按钮宽度为卡片的80%
                    pos_hint: {"center_x": 0.5}  # 居中显示
                    height: dp(56)
                    elevation: 2
                    shadow_softness: 2
                    on_release: root.on_login()

                    MDButtonText:
                        text: "登 录"
                        font_size: app.font_styles.BUTTON_LARGE['font_size']
                        bold: True
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT

            # 底部辅助操作区域
            MDBoxLayout:
                id: alternative_login_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                spacing: dp(5)

                MDLabel:
                    text: "其他登录方式"
                    halign: "center"
                    theme_text_color: "Secondary"
                    font_style: "Body"
                    role: "small"
                    size_hint_y: None
                    height: dp(40)
                    padding: [0, 0, 0, 0]

                MDBoxLayout:
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(30)
                    padding: [0, 0, 0, dp(20)]

                    Widget:
                        size_hint_x: 0.2

                    MDIconButton:
                        icon: "wechat"
                        icon_size: dp(32)
                        on_release: root.on_wechat_login()

                    MDIconButton:
                        icon: "fingerprint"
                        icon_size: dp(32)
                        on_release: root.on_fingerprint_login()

                    MDIconButton:
                        icon: "face-recognition"
                        icon_size: dp(32)
                        on_release: root.on_face_login()

                    Widget:
                        size_hint_x: 0.2

                MDBoxLayout:
                    size_hint_y: None
                    height: dp(48)

                    Widget:
                        size_hint_x: 0.2

                    MDBoxLayout:
                        size_hint_y: None
                        height: self.minimum_height

                        MDLabel:
                            text: "新用户注册"
                            font_style: "Body"
                            role: "medium"
                            halign: "center"
                            size_hint_y: None
                            height: dp(40)
                            padding: [0, 0, 0, 0]

                        MDButton:
                            style: "text"
                            on_release: root.on_register()

                            MDButtonText:
                                text: "立即注册"
                                theme_text_color: "Primary"

                    Widget:
                        size_hint_x: 0.2

                MDLabel:
                    text: "登录即代表同意《用户协议》和《隐私政策》"
                    halign: "center"
                    theme_text_color: "Secondary"
                    font_style: "Body"
                    role: "small"
                    size_hint_y: None
                    height: dp(40)
                    padding: [0, 0, 0, 0]
'''

# 注册KV语言
Builder.load_string(KV)

class LoginTab(MDBoxLayout):
    """账号登录选项卡"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 延迟初始化，确保组件已经完全加载
        Clock.schedule_once(self._post_init, 0.2)

    def _post_init(self, dt):
        """延迟初始化，确保组件已完全加载"""
        # 设置密码图标的点击事件
        self._set_password_icon_callback()

        # 确保输入框大小正确
        if hasattr(self.ids, 'username'):
            self.ids.username.size_hint_y = None
            self.ids.username.height = dp(48)

        if hasattr(self.ids, 'password'):
            self.ids.password.size_hint_y = None
            self.ids.password.height = dp(48)

    def _set_password_icon_callback(self, dt=None):
        """设置密码图标的点击事件 - KivyMD 2.0.1兼容版本"""
        if not hasattr(self.ids, 'password'):
            Clock.schedule_once(self._set_password_icon_callback, 0.5)
            return

        password_field = self.ids.password
        
        try:
            # 方法1：直接通过ID访问（最可靠）
            if hasattr(self.ids, 'password_icon'):
                icon = self.ids.password_icon
                # 清除之前的绑定
                icon.unbind(on_release=self._on_password_icon_release)
                # 绑定新的事件
                icon.bind(on_release=self._on_password_icon_release)
                logger.info("密码图标事件绑定成功（方法1）")
                return
                
            # 方法2：遍历查找图标组件
            for child in password_field.children:
                if (hasattr(child, 'icon') and 
                    hasattr(child, 'on_release') and 
                    'eye' in str(getattr(child, 'icon', ''))):
                    # 清除之前的绑定
                    child.unbind(on_release=self._on_password_icon_release)
                    # 绑定新的事件
                    child.bind(on_release=self._on_password_icon_release)
                    logger.info("密码图标事件绑定成功（方法2）")
                    return
                    
        except Exception as e:
            logger.error(f"密码图标事件绑定失败: {e}")
            # 延迟重试
            Clock.schedule_once(self._set_password_icon_callback, 1.0)

    def _on_password_icon_release(self, instance):
        """密码图标点击事件处理器"""
        self.toggle_password_visibility()
        
    def _set_password_trailing_icon(self):
        """设置密码框的尾部图标"""
        try:
            # 获取密码输入框
            password_field = self.ids.password
            
            # 尝试获取trailing_icon
            trailing_icon = None
            
            # 查找trailing_icon
            for child in password_field.children:
                if hasattr(child, 'icon') and child.icon in ["eye", "eye-off"]:
                    trailing_icon = child
                    break
            
            if not trailing_icon:
                print("未找到密码图标，尝试延迟绑定")
                # 如果没找到，延迟再试一次
                Clock.schedule_once(lambda dt: self._set_password_trailing_icon(), 0.5)
                return
            
            # 定义图标点击事件处理函数
            def on_icon_press(*args):
                print("密码图标被点击")
                self.toggle_password_visibility()
                
            # 尝试多种事件绑定方式
            try:
                # 首先尝试on_release，这是最常用的
                trailing_icon.bind(on_release=on_icon_press)
                print("使用on_release绑定成功")
            except:
                try:
                    trailing_icon.bind(on_press=on_icon_press)
                    print("使用on_press绑定成功")
                except:
                    try:
                        # 最后尝试touch事件
                        def on_touch_wrapper(instance, touch):
                            if instance.collide_point(*touch.pos):
                                if touch.grab_current is None:
                                    touch.grab(instance)
                                    on_icon_press()
                                    return True
                            return False
                        trailing_icon.bind(on_touch_down=on_touch_wrapper)
                        print("使用on_touch_down绑定成功")
                    except Exception as e:
                        print(f"所有绑定方式都失败: {e}")
            
            # 设置图标样式
            trailing_icon.theme_icon_color = "Custom"
            trailing_icon.icon_color = AppTheme.PRIMARY_COLOR
            trailing_icon.icon = "eye-off"  # 确保初始为隐藏状态
            print("密码图标事件已绑定")
            
        except Exception as e:
            print(f"设置密码尾部图标时出错: {e}")
            import traceback
            traceback.print_exc()

    def _on_password_icon_touch(self, instance, touch):
        """处理密码图标的触摸事件"""
        if instance.collide_point(*touch.pos):
            if touch.is_double_tap:
                return False
            self.toggle_password_visibility()
            return True
        return False

    def toggle_password_visibility(self):
        """切换密码可见性"""
        try:
            # 获取密码输入框
            password_field = self.ids.password

            # 获取trailing icon - 使用更准确的查找方法
            trailing_icon = None

            # 方法1：通过类型名查找
            for child in password_field.children:
                if type(child).__name__ == 'MDTextFieldTrailingIcon':
                    trailing_icon = child
                    break
                elif hasattr(child, 'icon') and child.icon in ["eye", "eye-off"]:
                    trailing_icon = child
                    break

            # 方法2：如果还没找到，尝试通过id查找
            if not trailing_icon and hasattr(self.ids, 'password_icon'):
                trailing_icon = self.ids.password_icon

            if trailing_icon:
                # 切换图标和密码可见性
                current_icon = getattr(trailing_icon, 'icon', 'eye-off')
                if current_icon == "eye-off":
                    trailing_icon.icon = "eye"
                    password_field.password = False
                    print("密码已显示")
                else:
                    trailing_icon.icon = "eye-off"
                    password_field.password = True
                    print("密码已隐藏")

                print(f"密码可见性已切换: {'隐藏' if password_field.password else '显示'}")
            else:
                print("未找到密码图标，无法切换可见性")

        except Exception as e:
            print(f"切换密码可见性时出错: {e}")
            import traceback
            traceback.print_exc()

class PhoneTab(MDBoxLayout):
    """手机号登录选项卡"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 延迟初始化，确保组件已经完全加载
        Clock.schedule_once(self._post_init, 0.2)

    def _post_init(self, dt):
        """延迟初始化，确保组件已完全加载"""
        # 确保输入框大小正确
        if hasattr(self.ids, 'phone'):
            self.ids.phone.size_hint_y = None
            self.ids.phone.height = dp(48)

        if hasattr(self.ids, 'verification_code'):
            self.ids.verification_code.size_hint_y = None
            self.ids.verification_code.height = dp(48)

        # 确保按钮样式正确
        for child in self.children:
            if isinstance(child, MDBoxLayout) and hasattr(child, 'children'):
                for btn in child.children:
                    if isinstance(btn, MDButton):
                        btn.height = dp(48)
                        btn.md_bg_color = AppTheme.PRIMARY_COLOR
                        # 确保按钮文本颜色正确
                        for text_widget in btn.children:
                            if isinstance(text_widget, MDButtonText):
                                text_widget.text_color = AppTheme.TEXT_LIGHT

class LoginScreen(BaseScreen):
    """登录屏幕"""
    current_tab = StringProperty("account")
    identity = StringProperty("")  # 内部存储的身份值
    remember_password = BooleanProperty(False)
    identity_menu = None  # 定义为实例变量

    def __init__(self, **kwargs):
        self.credentials_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../user_data.json")
        super(LoginScreen, self).__init__(**kwargs)

    def init_ui(self, dt=0):
        """初始化UI组件"""
        # 设置初始选中的tab
        self.current_tab = 'account'

        # 确保身份选择下拉菜单显示默认提示语
        if hasattr(self, 'ids') and 'identity_text' in self.ids:
            self.ids.identity_text.text = "请选择身份"

        # 直接切换到账号登录界面
        self.switch_tab('account')

        # 更新指示器
        self.update_tab_indicator()

        # 调整布局
        self.adjust_layout()

        # 加载已保存的凭据
        Clock.schedule_once(self.load_saved_credentials, 0.5)

    def adjust_layout(self):
        """调整界面布局"""
        # 设置登录卡片宽度为固定比例
        self.ids.login_card.size_hint_x = 0.9
        self.ids.login_card.pos_hint = {"center_x": 0.5}

        # 调整logo和品牌区域
        self.ids.logo_container.size_hint_x = 0.9
        self.ids.logo_container.pos_hint = {"center_x": 0.5}

        # 设置统一的间距
        self.ids.main_layout.spacing = dp(15)
        self.ids.main_layout.padding = [dp(15), dp(15), dp(15), dp(15)]

        # 确保登录按钮样式正确
        self.ids.login_button.size_hint_x = 0.8
        self.ids.login_button.pos_hint = {"center_x": 0.5}
        self.ids.login_button.height = dp(50)

        # 确保滚动视图能够适应内容
        self.ids.scroll_view.size_hint = (1, 1)
        self.ids.scroll_view.do_scroll_y = True

    def switch_tab(self, tab_name):
        """切换标签页

        Args:
            tab_name: 标签页名称，'account' 或 'phone'
        """
        # 更新当前标签
        self.current_tab = tab_name

        # 根据标签页显示不同内容
        content_area = self.ids.tab_content
        content_area.clear_widgets()

        if tab_name == 'account':
            content_area.add_widget(LoginTab())
        else:
            content_area.add_widget(PhoneTab())

        # 更新指示器
        self.update_tab_indicator()

    def update_tab_indicator(self):
        """更新选项卡指示器"""
        # 更新按钮文本颜色
        for child in self.ids.account_tab_btn.children:
            if isinstance(child, MDButtonText):
                child.theme_text_color = "Primary" if self.current_tab == "account" else "Secondary"
                child.bold = self.current_tab == "account"

        for child in self.ids.phone_tab_btn.children:
            if isinstance(child, MDButtonText):
                child.theme_text_color = "Primary" if self.current_tab == "phone" else "Secondary"
                child.bold = self.current_tab == "phone"

        # 更新指示器颜色
        app = MDApp.get_running_app()
        primary_color = [0, 0, 0, 1]  # 默认黑色

        # 安全地获取主题颜色
        if app is not None and hasattr(app, 'theme'):
            primary_color = app.theme.PRIMARY_COLOR

        # 设置指示器颜色
        self.ids.account_indicator.md_bg_color = primary_color if self.current_tab == "account" else [0, 0, 0, 0]
        self.ids.phone_indicator.md_bg_color = primary_color if self.current_tab == "phone" else [0, 0, 0, 0]

        # 增加选中标签的高亮效果 - 使用更淡的背景色
        light_primary = [primary_color[0], primary_color[1], primary_color[2], 0.15]
        self.ids.account_tab_btn.md_bg_color = light_primary if self.current_tab == "account" else [0, 0, 0, 0]
        self.ids.phone_tab_btn.md_bg_color = light_primary if self.current_tab == "phone" else [0, 0, 0, 0]

    def show_identity_menu(self, caller):
        """显示身份选择菜单

        Args:
            caller: 调用此方法的组件，通常是MDDropDownItem
        """
        # 每次都重新创建菜单，确保响应正常
        menu_items = [
            {
                "text": "个人用户",
                "on_release": lambda x="个人用户": self.set_identity(x),
            },
            {
                "text": "单位管理员",
                "on_release": lambda x="单位管理员": self.set_identity(x),
            },
            {
                "text": "健康顾问",
                "on_release": lambda x="健康顾问": self.set_identity(x),
            },
            {
                "text": "超级管理员",
                "on_release": lambda x="超级管理员": self.set_identity(x),
            }
        ]

        # 重新创建菜单以确保响应正常
        self.identity_menu = MDDropdownMenu(
            caller=caller,
            items=menu_items,
            width=dp(200),
            position="auto",  # 自动定位
            max_height=dp(200),  # 限制最大高度
        )
        self.identity_menu.open()

    def set_identity(self, identity):
        """设置选择的身份

        Args:
            identity: 选择的身份
        """
        # 如果传入的是空字符串或"personal"，则设置为"请选择身份"
        if not identity or identity.lower() == "personal":
            display_text = "请选择登录身份"
            self.identity = ""
        else:
            display_text = identity
            self.identity = identity

        # 更新显示文本
        self.ids.identity_text.text = display_text

        # 更新视觉反馈 - 强制触发UI更新
        self._update_identity_visual_feedback()

        # 安全地调用dismiss方法
        if self.identity_menu is not None and hasattr(self.identity_menu, 'dismiss'):
            self.identity_menu.dismiss()

        # 打印选择结果，便于调试
        print(f"身份选择: {identity} -> 显示: {display_text}")

    def _update_identity_visual_feedback(self):
        """更新身份选择器的视觉反馈"""
        try:
            # 根据是否选择了身份来更新文字颜色和样式
            has_identity = self.identity and self.identity != "请选择登录身份"

            if hasattr(self.ids, 'identity_text'):
                # 更新文字颜色 - 使用KivyMD 2.0.1的方式
                if has_identity:
                    self.ids.identity_text.theme_text_color = "Primary"
                    # 可以添加其他视觉效果，比如字体加粗
                    self.ids.identity_text.bold = True
                else:
                    self.ids.identity_text.theme_text_color = "Secondary"
                    self.ids.identity_text.bold = False

        except Exception as e:
            print(f"更新身份选择器视觉反馈时出错: {e}")

    def on_login(self):
        """处理登录事件

        验证用户凭据，成功后切换到对应用户并保存登录状态。
        根据不同的登录方式（账号密码或手机验证码）进行不同的验证和处理。
        登录成功后会保存用户信息并导航到主页。
        """
        # 获取当前标签页中的输入字段
        if self.current_tab == "account":
            # 账号密码登录
            username = self.ids.tab_content.children[0].ids.username.text.strip()
            password = self.ids.tab_content.children[0].ids.password.text.strip()

            if not username:
                self.show_error("请输入用户名")
                return

            if not password:
                self.show_error("请输入密码")
                return

            # 验证用户凭据
            auth_result = self.verify_user_credentials(username, password)

            if auth_result:
                # 登录成功，保存凭据（如果需要记住密码）
                if self.remember_password:
                    self.save_credentials(username, password)

                # 获取用户管理器
                from utils.user_manager import get_user_manager
                user_manager = get_user_manager()

                # 确保auth_result中包含custom_id，而不是使用数据库主键id
                user_id = auth_result.get("user_id")
                custom_id = auth_result.get("custom_id")

                # 如果没有custom_id但有user_id，记录警告
                if not custom_id and user_id:
                    print(f"警告: 后端返回的数据中没有custom_id，使用的是数据库主键id: {user_id}")
                    print("不再生成临时ID，这可能导致某些功能无法正常工作")

                # 优先使用custom_id作为用户ID
                user_id_to_use = custom_id if custom_id else user_id
                print(f"用户切换使用ID: {user_id_to_use}")

                # 切换到登录用户，并强制保存状态
                user = user_manager.switch_user(user_id_to_use, force_save=True)

                if not user:
                    # 如果本地没有对应的用户账户，使用云端返回的信息创建一个新账户
                    print(f"本地未找到用户 {user_id_to_use}，使用云端信息创建新账户")
                    user_info = auth_result.get('user_info', {})
                    user = user_manager.add_account(
                        username=username,
                        password=password,
                        full_name=user_info.get('full_name', username),
                        role=user_info.get('role', '个人用户'),
                        user_id=user_id,
                        custom_id=custom_id
                    )
                    
                    if not user:
                        self.show_error("创建用户账户失败")
                        return
                    
                    print(f"成功创建新用户账户: {username}, custom_id: {custom_id}")

                # 获取完整的用户数据
                from utils.storage import UserStorage
                user_data = UserStorage.get_user_data()

                # 确保user_data包含custom_id
                if custom_id:
                    user_data['custom_id'] = custom_id
                    print(f"添加custom_id到user_data: {custom_id}")
                elif user and hasattr(user, 'custom_id') and user.custom_id:
                    user_data['custom_id'] = user.custom_id
                    print(f"添加custom_id到user_data: {user.custom_id}")

                # 确保user_data包含full_name（标准字段名）
                if user and hasattr(user, 'full_name') and user.full_name:
                    user_data['full_name'] = user.full_name
                    print(f"添加full_name到user_data: {user.full_name}")

                # 设置应用中的用户数据
                app = MDApp.get_running_app()
                if app and hasattr(app, 'set_user_data'):
                    app.set_user_data(user_data)
                    print(f"设置应用中的用户数据: {user_data}")

                # 显示登录成功消息
                full_name = auth_result.get('full_name') or username
                self.show_success(f"欢迎回来，{full_name}")

                # 确保cloud_api实例有正确的认证信息
                try:
                    from utils.cloud_api import get_cloud_api
                    cloud_api = get_cloud_api()
                    if cloud_api:
                        # 设置token和custom_id
                        if 'access_token' in auth_result:
                            cloud_api.token = auth_result['access_token']
                            print(f"设置cloud_api的token: {auth_result['access_token'][:10]}...")

                            # 同时保存token到UserStorage
                            try:
                                from utils.storage import UserStorage
                                UserStorage.save_token(auth_result['access_token'])
                                print("已保存token到UserStorage")
                            except Exception as storage_e:
                                print(f"保存token到UserStorage失败: {str(storage_e)}")

                        if 'custom_id' in auth_result:
                            cloud_api.custom_id = auth_result['custom_id']
                            print(f"设置cloud_api的custom_id: {auth_result['custom_id']}")
                        # 保存认证信息
                        cloud_api.save_auth_info()
                        print("已保存cloud_api认证信息")
                except Exception as e:
                    print(f"设置cloud_api认证信息时出错: {str(e)}")

                # 导航到主页
                Clock.schedule_once(lambda dt: self.navigate_to_homepage(), 1)
            else:
                # 登录失败
                self.show_error("用户名或密码错误")
        else:
            # 手机号验证码登录
            phone = self.ids.tab_content.children[0].ids.phone.text.strip()
            code = self.ids.tab_content.children[0].ids.verification_code.text.strip()

            if not phone:
                self.show_error("请输入手机号")
                return

            if not code:
                self.show_error("请输入验证码")
                return

            # 模拟验证码验证成功
            # 在实际应用中，这里应该调用API验证验证码
            if True:  # 假设验证码验证成功
                # 查找与手机号匹配的用户
                from utils.user_manager import get_user_manager
                user_manager = get_user_manager()

                # 查找所有用户中手机号匹配的用户
                found_user = None
                for account in user_manager.get_all_accounts():
                    if account.phone == phone:
                        found_user = account
                        break

                if found_user:
                    # 找到了匹配的用户，切换到该用户
                    # 优先使用custom_id作为用户ID
                    user_id_to_use = found_user.custom_id if hasattr(found_user, 'custom_id') and found_user.custom_id else found_user.user_id
                    print(f"用户切换使用ID: {user_id_to_use}")
                    user = user_manager.switch_user(user_id_to_use, force_save=True)

                    if not user:
                        self.show_error("用户切换失败")
                        return

                    # 获取完整的用户数据
                    from utils.storage import UserStorage
                    user_data = UserStorage.get_user_data()

                    # 确保user_data包含custom_id
                    if user and hasattr(user, 'custom_id') and user.custom_id:
                        user_data['custom_id'] = user.custom_id
                        print(f"添加custom_id到user_data: {user.custom_id}")

                    # 确保user_data包含full_name（标准字段名）
                    if user and hasattr(user, 'full_name') and user.full_name:
                        user_data['full_name'] = user.full_name
                        print(f"添加full_name到user_data: {user.full_name}")

                    # 设置应用中的用户数据
                    app = MDApp.get_running_app()
                    if app and hasattr(app, 'set_user_data'):
                        app.set_user_data(user_data)
                        print(f"设置应用中的用户数据: {user_data}")

                    # 显示登录成功消息
                    full_name = found_user.full_name or found_user.username
                    self.show_success(f"欢迎回来，{full_name}")

                    # 导航到主页
                    Clock.schedule_once(lambda dt: self.navigate_to_homepage(), 1)
                else:
                    # 没有找到匹配的用户
                    self.show_error("未找到绑定该手机号的账户")

    def navigate_to_homepage(self):
        """导航到主页（支持屏幕懒加载）"""
        try:
            # 获取应用的屏幕管理器
            screen_manager = self.manager

            # 获取当前用户身份
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                # 未登录，强制跳转回登录页并提示
                if self.manager:
                    self.manager.current = 'login_screen'
                self.show_error("请先登录")
                return

            # 登录成功后，处理上传队列
            self._process_upload_queue_after_login()

            # 根据角色决定跳转的页面
            role = current_user.role

            # 映射角色到对应的屏幕
            screen_map = {
                "个人用户": "homepage_screen",
                "健康顾问": "consultant_screen",
                "单位管理员": "unit_screen",
                "超级管理员": "supermanager_screen"
            }

            # 获取目标屏幕
            target_screen = screen_map.get(role, 'homepage_screen')

            # 使用懒加载导航
            self._load_and_navigate(target_screen)

        except Exception as e:
            self.show_error(f"导航到主页时出错: {str(e)}")
            print(f"导航到主页时出错: {str(e)}")

            # 出错时导航到登录页
            if self.manager:
                self.manager.current = 'login_screen'

    def _load_and_navigate(self, screen_name):
        """懒加载屏幕并导航"""
        try:
            from utils.screen_loader import load_screen_if_needed

            # 尝试懒加载屏幕
            if load_screen_if_needed(screen_name):
                # 加载成功，导航到目标屏幕
                if self.manager and self.manager.has_screen(screen_name):
                    self.manager.current = screen_name
                    print(f"导航至 {screen_name}")
                else:
                    print(f"屏幕 {screen_name} 加载失败，导航到登录屏幕")
                    if self.manager:
                        self.manager.current = 'login_screen'
            else:
                print(f"无法加载屏幕 {screen_name}，保持在登录屏幕")
                self.show_error(f"加载 {screen_name} 失败")

        except Exception as e:
            print(f"懒加载导航时出错: {e}")
            # 回退到直接导航
            if self.manager and self.manager.has_screen(screen_name):
                self.manager.current = screen_name
            elif self.manager:
                self.manager.current = 'login_screen'

    def _process_upload_queue_after_login(self):
        """登录成功后处理上传队列"""
        try:
            # 获取当前用户
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                print("用户未登录，无法处理上传队列")
                return

            # 检查current_user是否有custom_id属性
            if not hasattr(current_user, 'custom_id') or not current_user.custom_id:
                print("用户没有custom_id，无法处理上传队列")
                return

            # 获取API客户端
            from utils.api_factory import get_api_client
            api_client = get_api_client()

            # 设置custom_id
            if hasattr(api_client, 'cloud_api'):
                api_client.cloud_api.custom_id = current_user.custom_id
                # 同时设置token，确保认证信息完整
                if hasattr(api_client.cloud_api, 'token') and api_client.cloud_api.token:
                    print(f"cloud_api已有token: {api_client.cloud_api.token[:10]}...")
                else:
                    # 尝试从用户管理器获取token
                    from utils.cloud_api import get_cloud_api
                    cloud_api = get_cloud_api()
                    if cloud_api and hasattr(cloud_api, 'token') and cloud_api.token:
                        api_client.cloud_api.token = cloud_api.token
                        print(f"从cloud_api获取token: {cloud_api.token[:10]}...")

                print(f"设置cloud_api的custom_id: {current_user.custom_id}")

                # 保存认证信息
                api_client.cloud_api.save_auth_info()
                print("已保存cloud_api认证信息")

                # 处理上传队列
                try:
                    success, fail = api_client.cloud_api.process_upload_queue(5)  # 最多处理5个文件
                    print(f"处理上传队列完成: {success} 个成功, {fail} 个失败")
                except Exception as e:
                    print(f"处理上传队列时出错: {str(e)}")
            else:
                print("API客户端没有cloud_api属性，无法处理上传队列")

        except Exception as e:
            print(f"登录后处理上传队列时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_forgot_password(self):
        """忘记密码点击事件"""
        self.show_info("忘记密码功能开发中...")

    def on_request_verification_code(self):
        """请求验证码点击事件"""
        # 获取手机号
        phone = self.ids.tab_content.children[0].ids.phone.text

        # 验证手机号是否填写
        if not phone or len(phone) != 11:
            self.show_error("请输入正确的手机号")
            return

        # 这里应该添加实际的验证码发送逻辑
        # 为了演示，我们假设发送成功
        self.show_success(f"验证码已发送至 {phone}")

    def on_wechat_login(self):
        """微信登录点击事件"""
        self.show_info("微信登录功能开发中...")

    def on_fingerprint_login(self):
        """指纹登录点击事件"""
        self.show_info("指纹登录功能开发中...")

    def on_face_login(self):
        """人脸登录点击事件"""
        self.show_info("人脸登录功能开发中...")

    def on_register(self):
        """注册点击事件"""
        try:
            # 获取应用的屏幕管理器
            screen_manager = self.manager

            # 切换到注册屏幕
            screen_manager.current = 'register'

        except Exception as e:
            self.show_error(f"导航到注册页面时出错: {str(e)}")
            print(f"导航到注册页面时出错: {str(e)}")

    def show_error(self, message):
        """显示错误消息

        Args:
            message: 错误信息
        """
        snackbar = MDSnackbar(
            MDSnackbarText(
                text=message,
            ),
        )
        snackbar.open()

    def show_success(self, message):
        """显示成功消息

        Args:
            message: 成功信息
        """
        snackbar = MDSnackbar(
            MDSnackbarText(
                text=message,
            ),
        )
        snackbar.open()

    def show_info(self, message):
        """显示提示消息

        Args:
            message: 提示信息
        """
        snackbar = MDSnackbar(
            MDSnackbarText(
                text=message,
            ),
        )
        snackbar.open()

    def load_saved_credentials(self, dt=None):
        """加载保存的凭据

        Args:
            dt: Clock调度触发的时间增量（可选）
        """
        try:
            # 获取用户数据管理对象
            from utils.storage import UserStorage

            # 获取保存的用户数据
            user_data = UserStorage.get_user_data()

            # 检查是否有保存的用户凭据
            if (user_data and
                "username" in user_data and
                "password" in user_data and
                "remember_password" in user_data and
                user_data.get("remember_password", False)):
                
                # 检查是否是访客账号，如果是则不自动填充
                username = user_data.get("username", "")
                if username and (username.lower() == "guest" or username.lower() == "访客"):
                    print("检测到访客账号，不自动填充")
                    # 如果没有保存的凭据，设置默认的身份选择提示语
                    if hasattr(self, 'ids') and 'identity_text' in self.ids:
                        self.ids.identity_text.text = "请选择身份"
                    return False

                # 获取角色信息，优先使用role，如果不存在则使用identity，默认为"个人用户"
                role = user_data.get("role", user_data.get("identity", "个人用户"))

                # 确保 tab_content 和其子组件存在
                if (hasattr(self, 'ids') and hasattr(self.ids, 'tab_content') and
                    self.ids.tab_content.children and
                    hasattr(self.ids.tab_content.children[0], 'ids')):

                    tab_ids = self.ids.tab_content.children[0].ids
                    # 如果保存有用户名和密码，并且设置了记住密码，则自动填充
                    if hasattr(tab_ids, 'username'):
                        tab_ids.username.text = user_data["username"]

                    if hasattr(tab_ids, 'password'):
                        tab_ids.password.text = user_data["password"]

                    if hasattr(tab_ids, 'remember_password'):
                        tab_ids.remember_password.active = True
                        self.remember_password = True

                    # 设置角色
                    self.set_identity(role)

                    return True
            else:
                # 如果没有保存的凭据，设置默认的身份选择提示语
                if hasattr(self, 'ids') and 'identity_text' in self.ids:
                    self.ids.identity_text.text = "请选择身份"
            return False
        except Exception as e:
            print(f"加载保存的凭据时出错: {e}")
            # 避免影响用户体验，不显示错误信息
            return False

    def save_credentials(self, username, password):
        """保存用户凭据

        Args:
            username: 用户名
            password: 密码
        """
        try:
            # 如果选择了记住密码，则保存凭据
            if self.remember_password:
                # 获取用户数据
                from utils.user_manager import get_user_manager
                user_manager = get_user_manager()
                current_user = user_manager.get_current_user()

                # 准备保存的数据
                credentials = {
                    "username": username,
                    "password": password,
                    "role": current_user.role if current_user else self.identity,  # 使用标准字段名
                    "full_name": current_user.full_name if current_user else "",  # 使用标准字段名
                    "custom_id": current_user.custom_id if hasattr(current_user, 'custom_id') and current_user else "",  # 使用标准字段名
                    "remember_password": True
                }

                # 确保目录存在
                os.makedirs(os.path.dirname(self.credentials_file), exist_ok=True)

                # 保存凭据
                with open(self.credentials_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(credentials, f, ensure_ascii=False)
            else:
                # 如果没有选择记住密码，则删除保存的凭据
                if os.path.exists(self.credentials_file):
                    os.remove(self.credentials_file)
        except Exception as e:
            print(f"保存用户凭据时出错: {e}")
            # 避免影响用户体验，不显示错误信息

    def verify_user_credentials(self, username, password):
        """验证用户凭据

        Args:
            username: 用户名
            password: 密码

        Returns:
            dict: 认证结果，成功返回用户信息，失败返回None
        """
        # 如果用户名为空，直接返回失败
        if not username:
            self.show_snackbar("用户名不能为空", "error")
            return None

        # 如果密码为空，直接返回失败
        if not password:
            self.show_snackbar("密码不能为空", "error")
            return None

        # 检查是否是访客登录
        if username.lower() == "guest" or username.lower() == "访客":
            # 禁用访客登录，返回失败
            self.show_snackbar("访客登录已禁用，请使用正式账户", "error")
            return None

        # 检查登录尝试限制
        if login_limiter.is_locked(username):
            remaining_time = login_limiter.lockout_duration - (time.time() - login_limiter.attempts[username]['last_attempt'])
            self.show_snackbar(f"账户已被锁定，请在{int(remaining_time/60)}分钟后重试", "error")
            return None

        # 尝试使用云端API进行认证
        try:
            # 获取CloudAPI实例
            from utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()

            # 尝试云端认证
            auth_result = cloud_api.authenticate(username, password)

            # 检查认证结果
            if auth_result and auth_result.get("status") == "success":
                # 登录成功，清除失败记录
                login_limiter.record_attempt(username, success=True)
                logger.info(f"云端认证成功: {auth_result}")
                return auth_result
            else:
                # 登录失败，记录尝试
                login_limiter.record_attempt(username, success=False)
                remaining = login_limiter.get_remaining_attempts(username)
                if remaining > 0:
                    self.show_snackbar(f"用户名或密码错误，还有{remaining}次尝试机会", "error")
                else:
                    self.show_snackbar("登录失败次数过多，账户已被锁定5分钟", "error")
                logger.warning(f"云端认证失败: {auth_result}")
                return None

        except Exception as e:
            # 登录异常，也记录尝试
            login_limiter.record_attempt(username, success=False)
            self.show_snackbar(f"登录失败: {str(e)}", "error")
            logger.error(f"云端认证异常: {e}")
            return None

    def show_snackbar(self, message, message_type="info"):
        """显示Snackbar消息
        
        Args:
            message (str): 要显示的消息
            message_type (str): 消息类型，可选值: "info", "error", "success", "warning"
        """
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text=message), duration=3)
            snackbar.open()
        except Exception as e:
            logger.error(f"显示Snackbar失败: {e}")
            # 如果Snackbar失败，使用toast作为备选
            try:
                from utils.toast import toast
                toast(message)
            except Exception as toast_e:
                logger.error(f"显示Toast也失败: {toast_e}")
                print(f"消息: {message}")