#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：为模板表添加template_key字段

该脚本为assessment_templates和questionnaire_templates表添加template_key字段，
并根据现有的标准模板数据填充template_key值。

运行方式：
python add_template_key_to_templates.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from app.db.session import get_db
from app.clinical_scales.assessment import ALL_ASSESSMENT_TEMPLATES as STANDARD_ASSESSMENT_TEMPLATES
from app.clinical_scales.questionnaire import ALL_QUESTIONNAIRE_TEMPLATES as STANDARD_QUESTIONNAIRE_TEMPLATES
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def column_exists(db, table_name, column_name):
    """检查列是否存在"""
    try:
        result = db.execute(text(f"PRAGMA table_info({table_name})"))
        columns = [row[1] for row in result.fetchall()]
        return column_name in columns
    except Exception:
        return False

def add_template_key_columns():
    """为模板表添加template_key字段"""
    db = next(get_db())
    
    try:
        # 检查assessment_templates表是否已有template_key字段
        if not column_exists(db, 'assessment_templates', 'template_key'):
            logger.info("为assessment_templates表添加template_key字段...")
            db.execute(text("""
                ALTER TABLE assessment_templates
                ADD COLUMN template_key VARCHAR
            """))
            
            # 创建索引
            db.execute(text("""
                CREATE INDEX idx_assessment_templates_template_key 
                ON assessment_templates(template_key)
            """))
            logger.info("assessment_templates表template_key字段添加成功")
        
        # 检查questionnaire_templates表是否已有template_key字段
        if not column_exists(db, 'questionnaire_templates', 'template_key'):
            logger.info("为questionnaire_templates表添加template_key字段...")
            db.execute(text("""
                ALTER TABLE questionnaire_templates
                ADD COLUMN template_key VARCHAR
            """))
            
            # 创建索引
            db.execute(text("""
                CREATE INDEX idx_questionnaire_templates_template_key 
                ON questionnaire_templates(template_key)
            """))
            logger.info("questionnaire_templates表template_key字段添加成功")
        
        db.commit()
        logger.info("成功添加template_key字段")
        
    except Exception as e:
        logger.error(f"添加template_key字段失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def update_template_keys():
    """根据标准模板更新template_key值"""
    db = next(get_db())
    
    try:
        # 更新评估量表模板的template_key
        logger.info("更新评估量表模板的template_key...")
        for template in STANDARD_ASSESSMENT_TEMPLATES.values():
            template_key = template.get('template_key')
            template_name = template.get('name')
            
            if template_key and template_name:
                db.execute(text("""
                    UPDATE assessment_templates 
                    SET template_key = :template_key 
                    WHERE name = :name AND template_key IS NULL
                """), {
                    'template_key': template_key,
                    'name': template_name
                })
        
        # 更新问卷模板的template_key
        logger.info("更新问卷模板的template_key...")
        for template in STANDARD_QUESTIONNAIRE_TEMPLATES.values():
            template_key = template.get('template_key')
            template_name = template.get('name')
            
            if template_key and template_name:
                db.execute(text("""
                    UPDATE questionnaire_templates 
                    SET template_key = :template_key 
                    WHERE name = :name AND template_key IS NULL
                """), {
                    'template_key': template_key,
                    'name': template_name
                })
        
        db.commit()
        logger.info("成功更新template_key值")
        
    except Exception as e:
        logger.error(f"更新template_key值失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("开始数据库迁移：添加template_key字段")
    
    try:
        # 1. 添加字段
        add_template_key_columns()
        
        # 2. 更新数据
        update_template_keys()
        
        logger.info("数据库迁移完成")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()