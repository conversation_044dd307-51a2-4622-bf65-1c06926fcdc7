# 健康管理移动应用 API 文档

> 生成时间: 2025-07-02 22:57:26
> 版本: 优化版本 v1.0

## 目录

- [API 端点](#api-端点)
- [组件文档](#组件文档)
- [错误代码](#错误代码)
- [认证说明](#认证说明)

## API 端点

## 组件文档

### theme_optimized.py

优化的主题配置
基于Material Design 3和现代健康应用设计原则

**类**:

#### OptimizedColorPalette

优化的颜色调色板

#### OptimizedTextColors

优化的文本颜色

#### HealthModuleColors

健康模块专用颜色

#### OptimizedTypography

优化的字体排版

#### OptimizedSpacing

优化的间距系统

#### OptimizedElevation

优化的阴影高度

#### OptimizedBorderRadius

优化的圆角半径

#### OptimizedComponentSizes

优化的组件尺寸

#### OptimizedTheme

优化的主题类

**方法**:
- `__init__()`
- `_apply_dark_mode()`
- `get_module_color()`
- `get_status_color()`
- `get_text_style()`

#### AppTheme

应用主题兼容性类

**方法**:
- `__init__()`

#### AppMetrics

应用度量兼容性类

**方法**:
- `__init__()`

#### FontStyles

字体样式兼容性类

**方法**:
- `__init__()`

#### MDApp

#### ThemableBehavior

---

### api\api_config_optimized.py

优化的API配置管理模块
统一处理API端口、格式规范和通信协议

**类**:

#### APIEndpoint

API端点枚举

#### APIConfig

API配置数据类

#### OptimizedAPIConfig

优化的API配置管理器

**方法**:
- `__init__()`
- `_load_config()`
- `_validate_config()`
- `get_full_url()`
- `get_backup_url()`
- `get_headers()`
- `format_request_data()`
- `validate_response()`
- `get_error_message()`
- `is_success_response()`
- `get_response_data()`
- `timeout()`
- `max_retries()`
- `retry_delay()`

#### APIResponseFormatter

API响应格式化器

**方法**:
- `success()`
- `error()`
- `paginated()`

#### APIErrorCodes

API错误代码常量

---

### api\api_client_optimized.py

优化的API客户端
统一处理与后端的通信，提供标准化的API调用接口

**类**:

#### OptimizedAPIClient

优化的API客户端

**方法**:
- `__init__()`
- `_start_background_tasks()`
- `_health_check_loop()`
- `_queue_processor_loop()`
- `authenticate()`
- `logout()`
- `get_user_info()`
- `get_health_overview()`
- `get_health_records()`
- `get_medical_records()`
- `create_health_record()`
- `update_health_record()`
- `delete_health_record()`
- `upload_file()`
- `request_ocr()`
- `get_ocr_result()`
- `health_check()`
- `_make_authenticated_request()`
- `_make_request()`
- `_try_request()`
- `_handle_response()`
- `_process_upload_queue()`
- `add_to_upload_queue()`

---

### screens\homepage_screen_optimized.py

**类**:

#### OptimizedModuleCard

优化的模块卡片组件

**方法**:
- `__init__()`
- `on_card_click()`

#### SectionHeader

区域标题组件

#### NavButton

导航按钮组件

**方法**:
- `__init__()`
- `on_button_click()`

#### OptimizedHomepageScreen

优化的首页界面

**方法**:
- `__init__()`
- `on_enter()`
- `initialize_ui()`
- `load_user_data()`
- `set_welcome_message()`
- `setup_quick_actions()`
- `setup_health_modules()`
- `setup_medical_services()`
- `setup_navigation()`
- `navigate_to_health_overview()`
- `navigate_to_add_record()`
- `navigate_to_medication()`
- `navigate_to_basic_info()`
- `navigate_to_medical_records()`
- `navigate_to_assessment()`
- `navigate_to_health_diary()`
- `navigate_to_voice_triage()`
- `navigate_to_companion_service()`
- `navigate_to_health_data()`
- `navigate_to_medical_service()`
- `navigate_to_profile()`
- `navigate_to_login()`
- `show_message()`

---

### utils\performance_monitor.py

性能监控工具
提供应用性能监控和分析功能

**类**:

#### PerformanceMetric

性能指标数据类

#### UserAction

用户行为数据类

#### PerformanceMonitor

性能监控器

**方法**:
- `__init__()`
- `record_metric()`
- `get_metrics()`
- `get_average()`
- `clear_metrics()`
- `get_uptime()`

#### StartupTimer

启动时间计时器

**方法**:
- `__init__()`
- `start_phase()`
- `end_phase()`
- `get_total_startup_time()`
- `get_phase_summary()`

**函数**:

#### timing_decorator(metric_name)

性能计时装饰器

Args:
    metric_name: 指标名称

#### get_performance_monitor()

获取全局性能监控器实例

#### get_startup_timer()

获取全局启动计时器实例

#### log_performance_summary()

记录性能摘要

#### schedule_performance_logging()

调度定期性能日志记录

---

## 错误代码

| 代码 | 说明 |
|------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证说明

本API使用Bearer Token认证方式。在请求头中添加：

```
Authorization: Bearer <your_token>
```

获取Token请使用登录接口。Token有效期为24小时。

## 联系信息

如有问题，请联系开发团队。
