from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.widget import Widget
import os
import json
import sys
from datetime import datetime
import threading
import tempfile
import traceback
from kivy.logger import Logger
from kivy.uix.progressbar import ProgressBar

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.list import MD<PERSON>ist, MDListItem
from kivy.factory import Factory

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入Logo组件
from widgets.logo import HealthLogo, add_logo_to_layout

# 导入API客户端
from api.api_client import APIClient

# 定义KV语言字符串
KV = '''
<HealthDataModuleCard>:
    elevation: 3
    radius: [dp(12)]
    md_bg_color: app.theme.SURFACE_COLOR if not root.bg_color else root.bg_color
    size_hint_y: None
    height: dp(120)
    ripple_behavior: True
    on_release: root.on_click()
    
    MDBoxLayout:
        orientation: 'vertical'
        padding: [dp(16), dp(12), dp(16), dp(12)]
        spacing: dp(8)
        
        MDBoxLayout:
            orientation: 'horizontal'
            spacing: dp(12)
            size_hint_y: None
            height: dp(40)
            
            MDIcon:
                icon: root.icon
                theme_icon_color: "Custom"
                icon_color: root.icon_color if root.icon_color else app.theme.PRIMARY_COLOR
                size_hint: None, None
                size: dp(32), dp(32)
                pos_hint: {'center_y': 0.5}
            
            MDLabel:
                text: root.title
                font_style: "Label"
                theme_text_color: "Custom"
                text_color: app.theme.ON_SURFACE
                bold: True
                halign: "left"
                valign: "center"
        
        MDLabel:
            text: root.description
            font_style: "Label"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY
            halign: "left"
            text_size: self.width, None
            size_hint_y: None
            height: self.texture_size[1]

<HealthDataManagementScreen>:
    name: 'health_data_management_screen'
    
    # 背景画布
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: 0
        
        # 顶部应用栏 - 移到最上方与子页面保持一致
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "健康资料管理"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "refresh"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.refresh_data()
        
        # Logo区域 - 移到应用栏下方
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(60)
            pos_hint: {"center_x": 0.5}
        
        # 主要内容区域
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            
            MDBoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(20), dp(10), dp(20), dp(20)]
                spacing: dp(16)
                

                
                # 功能模块标题
                MDLabel:
                    text: "健康资料管理功能"
                    font_style: "Body"
                    role: "medium"  # 从large改为medium
                    theme_text_color: "Custom"
                    text_color: app.theme.PRIMARY_DARK
                    bold: True
                    halign: "left"
                    size_hint_y: None
                    height: self.texture_size[1]
                
                # 第一行：基础功能模块
                MDGridLayout:
                    id: basic_modules_grid
                    cols: 2
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    size_hint_y: None
                    height: self.minimum_height
                
                # 分隔线
                MDBoxLayout:
                    size_hint_y: None
                    height: dp(16)
                
                # 第二行：资料管理模块
                MDGridLayout:
                    id: data_modules_grid
                    cols: 2
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    size_hint_y: None
                    height: self.minimum_height
                
                # 分隔线
                MDBoxLayout:
                    size_hint_y: None
                    height: dp(16)
                
                # 第三行：记录管理模块
                MDGridLayout:
                    id: record_modules_grid
                    cols: 2
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    size_hint_y: None
                    height: self.minimum_height
                
                # 分隔线
                MDBoxLayout:
                    size_hint_y: None
                    height: dp(16)
                
                # 第四行：日记和日志模块
                MDGridLayout:
                    id: diary_modules_grid
                    cols: 2
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(16)
                    size_hint_y: None
                    height: self.minimum_height
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class HealthDataModuleCard(Factory.MDCard):
    """健康资料管理模块卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("功能")
    description = StringProperty("")
    bg_color = ListProperty(None)
    icon_color = ListProperty(None)
    action = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def on_click(self):
        """点击事件处理"""
        if self.action and callable(self.action):
            self.action()

class HealthDataManagementScreen(BaseScreen):
    """健康资料管理屏幕"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        # 延迟初始化UI，确保主题已加载
        Clock.schedule_once(self.init_ui)
    
    def init_ui(self, dt=0):
        """初始化UI"""
        # 加载健康资料管理模块
        self.load_health_data_modules()
    
    def load_health_data_modules(self):
        """加载健康资料管理模块"""
        # 基础功能模块
        basic_modules_data = [
            {
                'title': '健康状态总览',
                'icon': 'chart-line',
                'description': '查看自动总结的健康信息',
                'color': self.app.theme.HEALTH_DATA_COLOR,
                'action': self.navigate_to_health_overview
            },
            {
                'title': '基本健康信息',
                'icon': 'clipboard-text',
                'description': '录入和修改基本健康信息',
                'color': self.app.theme.INFO_COLOR,
                'action': self.navigate_to_basic_health_info
            }
        ]
        
        # 资料管理模块
        data_modules_data = [
            {
                'title': '健康资料传阅',
                'icon': 'file-upload',
                'description': '上传和管理健康资料',
                'color': self.app.theme.SUCCESS_COLOR,
                'action': self.navigate_to_health_file_upload
            },
            {
                'title': '调查问卷/评估量表',
                'icon': 'clipboard-list',
                'description': '填写和查看问卷量表',
                'color': self.app.theme.WARNING_COLOR,
                'action': self.navigate_to_questionnaire
            }
        ]
        
        # 记录管理模块
        record_modules_data = [
            {
                'title': '用药记录',
                'icon': 'pill',
                'description': '管理用药信息和记录',
                'color': self.app.theme.HEALTH_PURPLE,
                'action': self.navigate_to_medication_record
            },
            {
                'title': '历年体检报告',
                'icon': 'file-document',
                'description': '查看历年体检报告',
                'color': self.app.theme.HEALTH_BLUE,
                'action': self.navigate_to_physical_exam_reports
            }
        ]
        
        # 日记和日志模块
        diary_modules_data = [
            {
                'title': '健康日记',
                'icon': 'book-open-variant',
                'description': '记录日常健康状况',
                'color': self.app.theme.HEALTH_GREEN,
                'action': self.navigate_to_health_diary
            },
            {
                'title': '其它记录',
                'icon': 'note-text',
                'description': '其他健康相关记录',
                'color': self.app.theme.TEXT_SECONDARY,
                'action': self.navigate_to_other_records
            }
        ]
        
        # 加载基础功能模块
        basic_grid = self.ids.basic_modules_grid
        basic_grid.clear_widgets()
        for module in basic_modules_data:
            card = HealthDataModuleCard(
                title=module['title'],
                icon=module['icon'],
                description=module['description'],
                icon_color=module['color'],
                action=module['action']
            )
            basic_grid.add_widget(card)
        
        # 加载资料管理模块
        data_grid = self.ids.data_modules_grid
        data_grid.clear_widgets()
        for module in data_modules_data:
            card = HealthDataModuleCard(
                title=module['title'],
                icon=module['icon'],
                description=module['description'],
                icon_color=module['color'],
                action=module['action']
            )
            data_grid.add_widget(card)
        
        # 加载记录管理模块
        record_grid = self.ids.record_modules_grid
        record_grid.clear_widgets()
        for module in record_modules_data:
            card = HealthDataModuleCard(
                title=module['title'],
                icon=module['icon'],
                description=module['description'],
                icon_color=module['color'],
                action=module['action']
            )
            record_grid.add_widget(card)
        
        # 加载日记和日志模块
        diary_grid = self.ids.diary_modules_grid
        diary_grid.clear_widgets()
        for module in diary_modules_data:
            card = HealthDataModuleCard(
                title=module['title'],
                icon=module['icon'],
                description=module['description'],
                icon_color=module['color'],
                action=module['action']
            )
            diary_grid.add_widget(card)
    
    def go_back(self):
        """返回上一级页面"""
        if self.manager:
            self.manager.current = 'homepage_screen'
    
    def refresh_data(self):
        """刷新数据"""
        # 显示刷新提示
        snackbar = MDSnackbar(MDSnackbarText(text="正在刷新数据..."))
        snackbar.open()
        
        # 重新加载模块
        self.load_health_data_modules()
        
        # 延迟显示完成提示
        Clock.schedule_once(lambda dt: self.show_refresh_complete(), 1)
    
    def show_refresh_complete(self):
        """显示刷新完成提示"""
        snackbar = MDSnackbar(MDSnackbarText(text="数据刷新完成"))
        snackbar.open()
    
    # 导航方法
    def navigate_to_health_overview(self):
        """导航到健康状态总览"""
        try:
            self.manager.current = 'health_overview_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_basic_health_info(self):
        """导航到基本健康信息"""
        try:
            self.manager.current = 'basic_health_info_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_health_file_upload(self):
        """导航到健康资料传阅"""
        try:
            self.manager.current = 'health_document_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_questionnaire(self):
        """导航到调查问卷/评估量表"""
        try:
            self.manager.current = 'survey_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_medication_record(self):
        """导航到用药记录"""
        try:
            self.manager.current = 'medication_management_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_physical_exam_reports(self):
        """导航到历年体检报告"""
        try:
            self.manager.current = 'physical_exam_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_health_diary(self):
        """导航到健康日记"""
        try:
            self.manager.current = 'health_diary_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def navigate_to_other_records(self):
        """导航到其它记录"""
        try:
            self.manager.current = 'other_records_screen'
        except Exception as e:
            snackbar = MDSnackbar(MDSnackbarText(text=f"导航失败: {str(e)}"))
            snackbar.open()
    
    def show_feature_not_available(self, feature_name):
        """显示功能不可用提示"""
        snackbar = MDSnackbar(MDSnackbarText(text=f"{feature_name}功能暂不可用"))
        snackbar.open()
    
    def show_feature_coming_soon(self, feature_name):
        """显示功能即将推出提示"""
        snackbar = MDSnackbar(MDSnackbarText(text=f"{feature_name}功能即将推出，敬请期待！"))
        snackbar.open()