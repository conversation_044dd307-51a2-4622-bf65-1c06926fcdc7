#!/usr/bin/env python3
"""
问卷实例生成器

此模块提供了从问卷模板创建问卷实例的功能，确保正确设置template_id。
"""

import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.questionnaire import (
    Questionnaire, 
    QuestionnaireItem, 
    QuestionnaireTemplate, 
    QuestionnaireTemplateQuestion
)
from app.models.distribution import QuestionnaireDistribution
from app.models.user import User


class QuestionnaireGenerator:
    """问卷生成器类
    
    用于从问卷模板创建问卷，确保正确设置template_id。
    注意：原QuestionnaireInstance功能已整合到Questionnaire表中。
    """
    
    def __init__(self, db: Session):
        """初始化问卷生成器
        
        Args:
            db: 数据库会话
        """
        self.db = db
    
    def create_questionnaire_from_template(self, 
                                         template_id: int,
                                         custom_id: str,
                                         status: str = "pending",
                                         created_by: Optional[int] = None) -> Questionnaire:
        """从模板创建问卷实例
        
        Args:
            template_id: 问卷模板ID
            custom_id: 用户或机构ID
            status: 问卷状态
            created_by: 创建者ID
            
        Returns:
            创建的问卷实例对象
        """
        # 获取模板
        template = self.db.query(QuestionnaireTemplate).filter(
            QuestionnaireTemplate.id == template_id
        ).first()
        
        if not template:
            raise ValueError(f"问卷模板ID {template_id} 不存在")
        
        # 创建问卷实例
        questionnaire = Questionnaire(
            custom_id=custom_id,
            questionnaire_type=template.questionnaire_type,
            name=template.name,
            version=template.version,
            notes=template.description,
            status=status,
            template_id=template_id,  # 关键：设置template_id
            created_at=datetime.now()
        )
        
        self.db.add(questionnaire)
        self.db.flush()  # 获取ID
        
        # 获取模板问题
        template_questions = self.db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == template_id
        ).order_by(QuestionnaireTemplateQuestion.order).all()
        
        # 创建问卷题目
        for template_question in template_questions:
            item = QuestionnaireItem(
                questionnaire_id=questionnaire.id,
                question_id=template_question.question_id,
                question_text=template_question.question_text,
                answer='',  # 初始为空
                score=None,
                notes='',
                question_type=template_question.question_type,
                options=template_question.options,
                order=template_question.order,
                is_required=template_question.is_required,
                jump_logic=template_question.jump_logic,
                created_at=datetime.now()
            )
            self.db.add(item)
        
        self.db.commit()
        return questionnaire
    
    def distribute_questionnaire_from_template(self,
                                             template_id: int,
                                             user_ids: List[int],
                                             distributor_id: int,
                                             due_date: Optional[datetime] = None,
                                             message: Optional[str] = None) -> List[Dict[str, Any]]:
        """从模板分发问卷给多个用户
        
        Args:
            template_id: 问卷模板ID
            user_ids: 用户ID列表
            distributor_id: 分发者ID
            due_date: 截止日期
            message: 分发消息
            
        Returns:
            分发结果列表
        """
        results = []
        
        # 获取用户信息
        users = self.db.query(User).filter(User.id.in_(user_ids)).all()
        
        for user in users:
            try:
                # 为每个用户创建问卷实例
                questionnaire = self.create_questionnaire_from_template(
                    template_id=template_id,
                    custom_id=user.custom_id,
                    status="pending",
                    created_by=distributor_id
                )
                
                # 创建分发记录
                distribution = QuestionnaireDistribution(
                    questionnaire_id=questionnaire.id,
                    user_id=user.id,
                    distributor_id=distributor_id,
                    status="pending",
                    due_date=due_date,
                    message=message,
                    created_at=datetime.now()
                )
                self.db.add(distribution)
                
                results.append({
                    "user_id": user.id,
                    "questionnaire_id": questionnaire.id,
                    "distribution_id": distribution.id,
                    "status": "success",
                    "message": "问卷分发成功"
                })
                
            except Exception as e:
                results.append({
                    "user_id": user.id,
                    "questionnaire_id": None,
                    "distribution_id": None,
                    "status": "error",
                    "message": f"问卷分发失败: {str(e)}"
                })
        
        self.db.commit()
        return results
    
    def create_questionnaire_from_standard_template(self,
                                                   standard_template_data: Dict[str, Any],
                                                   custom_id: str,
                                                   status: str = "pending",
                                                   created_by: Optional[int] = None) -> Questionnaire:
        """从标准模板数据创建问卷实例
        
        Args:
            standard_template_data: 标准模板数据
            custom_id: 用户或机构ID
            status: 问卷状态
            created_by: 创建者ID
            
        Returns:
            创建的问卷实例对象
        """
        # 首先检查是否已有对应的模板记录
        template_key = standard_template_data.get('template_key')
        template = None
        
        if template_key:
            template = self.db.query(QuestionnaireTemplate).filter(
                QuestionnaireTemplate.name == standard_template_data.get('name')
            ).first()
        
        # 如果没有模板记录，创建一个
        if not template:
            template = QuestionnaireTemplate(
                name=standard_template_data.get('name', '标准问卷'),
                questionnaire_type=standard_template_data.get('questionnaire_type', 'standard'),
                version=standard_template_data.get('version', '1.0'),
                description=standard_template_data.get('description', '标准问卷模板'),
                instructions=standard_template_data.get('instructions', '请根据实际情况填写问卷'),
                is_active=True,
                created_by=created_by,
                created_at=datetime.now()
            )
            self.db.add(template)
            self.db.flush()
            
            # 添加模板问题
            questions = standard_template_data.get('questions', [])
            for i, question_data in enumerate(questions):
                template_question = QuestionnaireTemplateQuestion(
                    template_id=template.id,
                    question_id=question_data.get('question_id', f"q_{i+1}"),
                    question_text=question_data.get('question_text', f"问题{i+1}"),
                    question_type=question_data.get('question_type', 'text'),
                    options=question_data.get('options', []),
                    order=i + 1,
                    is_required=question_data.get('is_required', True),
                    created_at=datetime.now()
                )
                self.db.add(template_question)
        
        # 使用模板创建问卷实例
        return self.create_questionnaire_from_template(
            template_id=template.id,
            custom_id=custom_id,
            status=status,
            created_by=created_by
        )