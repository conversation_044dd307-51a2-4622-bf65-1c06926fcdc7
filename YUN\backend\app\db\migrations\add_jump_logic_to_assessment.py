#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为assessment_template_questions表添加jump_logic字段的迁移脚本
"""

import os
import sqlite3
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_jump_logic_to_assessment_questions():
    """向assessment_template_questions表添加缺少的jump_logic字段"""
    # 获取数据库文件的路径
    DB_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), "app.db")
    logger.info(f"正在连接数据库: {DB_PATH}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # 检查jump_logic列是否存在
        cursor.execute("PRAGMA table_info(assessment_template_questions)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # 添加jump_logic列（如果不存在）
        if "jump_logic" not in column_names:
            logger.info("添加jump_logic列到assessment_template_questions表")
            cursor.execute("ALTER TABLE assessment_template_questions ADD COLUMN jump_logic JSON")
            conn.commit()
            logger.info("jump_logic列添加成功")
        else:
            logger.info("jump_logic列已存在，无需添加")
            
        # 提交更改
        conn.commit()
        logger.info("assessment_template_questions表字段添加完成")
        return True
    except sqlite3.Error as e:
        logger.error(f"数据库操作错误: {str(e)}")
        return False
    finally:
        # 关闭数据库连接
        if conn:
            conn.close()

if __name__ == "__main__":
    add_jump_logic_to_assessment_questions()