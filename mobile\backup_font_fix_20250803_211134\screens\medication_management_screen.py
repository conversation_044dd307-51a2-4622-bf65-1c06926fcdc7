#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用药管理屏幕模块

提供完整的用药记录管理功能，包括：
1. 当前用药管理 - 查看、添加、编辑正在使用的药物
2. 既往用药管理 - 查看历史用药记录和停药原因
3. 用药提醒设置 - 为药物设置定时提醒
4. 数据导出分享 - 支持用药记录的导出和分享
5. 数据持久化 - 本地数据库存储 + 云端同步

技术特性：
- 基于KivyMD 2.0.1框架开发
- 支持本地SQLite数据库存储
- 集成云端API同步
- 实现推送通知提醒
- 支持数据导出和分享
- 优化性能，支持分页加载
- 敏感数据加密存储
"""

import os
import json
import logging
import sqlite3
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# Kivy/KivyMD imports
from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty, NumericProperty
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.factory import Factory
from kivy.utils import platform

# KivyMD imports
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.divider import MDDivider
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldTrailingIcon
from kivymd.uix.selectioncontrol import MDCheckbox, MDSwitch
from kivymd.uix.progressindicator import MDCircularProgressIndicator

# 项目内部imports
from screens.base_screen import BaseScreen
from widgets.logo import HealthLogo
from theme import AppTheme, AppMetrics, FontStyles
from utils.cloud_api import get_cloud_api
from utils.storage import UserStorage
from utils.common_components import BaseFormField, BaseButton

# 尝试导入通知模块
try:
    if platform == 'android':
        from plyer import notification
        NOTIFICATION_AVAILABLE = True
    else:
        NOTIFICATION_AVAILABLE = False
except ImportError:
    NOTIFICATION_AVAILABLE = False
    KivyLogger.warning("[MedicationManagement] 通知模块不可用")

# 配置日志
logger = logging.getLogger(__name__)

# 数据库配置
DB_VERSION = 1
MEDICATIONS_TABLE = "medications"
REMINDERS_TABLE = "medication_reminders"

# 分页配置
PAGE_SIZE = 20
MAX_CACHE_SIZE = 100

class MedicationDatabaseManager:
    """用药数据库管理器"""

    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        if db_path is None:
            # 使用应用数据目录
            app_data_dir = Path.home() / ".health_management" / "data"
            app_data_dir.mkdir(parents=True, exist_ok=True)
            db_path = app_data_dir / "medications.db"

        self.db_path = str(db_path)
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建用药记录表
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {MEDICATIONS_TABLE} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT NOT NULL,
                        name TEXT NOT NULL,
                        dosage TEXT NOT NULL,
                        frequency TEXT NOT NULL,
                        start_date TEXT NOT NULL,
                        end_date TEXT,
                        reason TEXT,
                        notes TEXT,
                        status TEXT DEFAULT 'active',
                        stop_reason TEXT,
                        stop_date TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL,
                        encrypted_data TEXT
                    )
                ''')

                # 创建用药提醒表
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {REMINDERS_TABLE} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        medication_id INTEGER NOT NULL,
                        user_id TEXT NOT NULL,
                        reminder_time TEXT NOT NULL,
                        reminder_type TEXT DEFAULT 'daily',
                        is_active BOOLEAN DEFAULT 1,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (medication_id) REFERENCES {MEDICATIONS_TABLE} (id)
                    )
                ''')

                # 创建索引
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_medications_user_id ON {MEDICATIONS_TABLE} (user_id)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_medications_status ON {MEDICATIONS_TABLE} (status)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_reminders_user_id ON {REMINDERS_TABLE} (user_id)')

                conn.commit()
                KivyLogger.info("[MedicationDB] 数据库初始化完成")

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 数据库初始化失败: {e}")
            raise

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        try:
            # 简单的哈希加密，实际应用中应使用更强的加密算法
            return hashlib.sha256(data.encode()).hexdigest()
        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 数据加密失败: {e}")
            return data

    def save_medication(self, user_id: str, medication_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """保存用药记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now().isoformat()

                # 加密敏感数据
                encrypted_notes = self.encrypt_sensitive_data(medication_data.get('notes', ''))

                cursor.execute(f'''
                    INSERT INTO {MEDICATIONS_TABLE}
                    (user_id, name, dosage, frequency, start_date, end_date, reason, notes,
                     status, created_at, updated_at, encrypted_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    user_id,
                    medication_data['name'],
                    medication_data['dosage'],
                    medication_data['frequency'],
                    medication_data['start_date'],
                    medication_data.get('end_date'),
                    medication_data.get('reason'),
                    medication_data.get('notes'),
                    'active',
                    now,
                    now,
                    encrypted_notes
                ))

                # 获取插入的记录ID
                medication_id = cursor.lastrowid

                # 查询刚插入的完整记录
                cursor.execute(f'''
                    SELECT id, user_id, name, dosage, frequency, start_date, end_date,
                           reason, notes, status, created_at, updated_at
                    FROM {MEDICATIONS_TABLE}
                    WHERE id = ?
                ''', (medication_id,))

                row = cursor.fetchone()
                if row:
                    # 构建返回的药物数据
                    saved_medication = {
                        'id': row[0],
                        'user_id': row[1],
                        'name': row[2],
                        'dosage': row[3],
                        'frequency': row[4],
                        'start_date': row[5],
                        'end_date': row[6],
                        'reason': row[7],
                        'notes': row[8],
                        'status': row[9],
                        'created_at': row[10],
                        'updated_at': row[11]
                    }

                conn.commit()
                KivyLogger.info(f"[MedicationDB] 保存用药记录成功: {medication_data['name']}, ID: {medication_id}")
                return saved_medication

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 保存用药记录失败: {e}")
            return None

    def update_medication_status(self, medication_id: int, status: str, stop_date: str = None, stop_reason: str = None) -> bool:
        """更新药物状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now().isoformat()

                if status == 'stopped':
                    # 停药状态，更新停药日期和停药原因
                    formatted_stop_date = stop_date
                    if stop_date and 'T' in stop_date:
                        # 如果传入的是完整日期时间，只取日期部分
                        formatted_stop_date = stop_date.split('T')[0]
                    elif not stop_date:
                        # 如果没有提供停药日期，使用当前日期
                        formatted_stop_date = datetime.now().strftime("%Y-%m-%d")

                    cursor.execute(f'''
                        UPDATE {MEDICATIONS_TABLE}
                        SET status = ?, stop_date = ?, stop_reason = ?, updated_at = ?
                        WHERE id = ?
                    ''', (status, formatted_stop_date, stop_reason, now, medication_id))
                else:
                    # 其他状态更新
                    cursor.execute(f'''
                        UPDATE {MEDICATIONS_TABLE}
                        SET status = ?, updated_at = ?
                        WHERE id = ?
                    ''', (status, now, medication_id))

                conn.commit()

                if cursor.rowcount > 0:
                    KivyLogger.info(f"[MedicationDB] 药物状态更新成功: ID {medication_id}, 状态: {status}")
                    return True
                else:
                    KivyLogger.warning(f"[MedicationDB] 未找到要更新的药物: ID {medication_id}")
                    return False

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 更新药物状态失败: {e}")
            return False

    def delete_medication(self, medication_id: int) -> bool:
        """删除药物记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute(f'''
                    DELETE FROM {MEDICATIONS_TABLE}
                    WHERE id = ?
                ''', (medication_id,))

                conn.commit()

                if cursor.rowcount > 0:
                    KivyLogger.info(f"[MedicationDB] 药物删除成功: ID {medication_id}")
                    return True
                else:
                    KivyLogger.warning(f"[MedicationDB] 未找到要删除的药物: ID {medication_id}")
                    return False

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 删除药物失败: {e}")
            return False

    def get_medications(self, user_id: str, status: str = 'active',
                       page: int = 1, page_size: int = PAGE_SIZE) -> List[Dict[str, Any]]:
        """获取用药记录（分页）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                offset = (page - 1) * page_size

                cursor.execute(f'''
                    SELECT * FROM {MEDICATIONS_TABLE}
                    WHERE user_id = ? AND status = ?
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                ''', (user_id, status, page_size, offset))

                columns = [description[0] for description in cursor.description]
                medications = []

                for row in cursor.fetchall():
                    medication = dict(zip(columns, row))
                    medications.append(medication)

                KivyLogger.info(f"[MedicationDB] 获取用药记录成功: {len(medications)} 条")
                return medications

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 获取用药记录失败: {e}")
            return []

    def stop_medication(self, medication_id: int, stop_reason: str) -> bool:
        """停用药物"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now()
                stop_date = now.strftime("%Y-%m-%d")  # 只保存日期部分
                updated_at = now.isoformat()  # 更新时间保持完整格式

                cursor.execute(f'''
                    UPDATE {MEDICATIONS_TABLE}
                    SET status = 'stopped', stop_reason = ?, stop_date = ?, updated_at = ?
                    WHERE id = ?
                ''', (stop_reason, stop_date, updated_at, medication_id))

                conn.commit()
                KivyLogger.info(f"[MedicationDB] 停用药物成功: ID {medication_id}, 停药日期: {stop_date}, 停药原因: {stop_reason}")
                return True

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 停用药物失败: {e}")
            return False

    def save_reminder(self, medication_id: int, user_id: str, reminder_data: Dict[str, Any]) -> bool:
        """保存用药提醒"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                now = datetime.now().isoformat()

                cursor.execute(f'''
                    INSERT INTO {REMINDERS_TABLE}
                    (medication_id, user_id, reminder_time, reminder_type, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    medication_id,
                    user_id,
                    reminder_data['reminder_time'],
                    reminder_data.get('reminder_type', 'daily'),
                    True,
                    now
                ))

                conn.commit()
                KivyLogger.info(f"[MedicationDB] 保存提醒设置成功: 药物ID {medication_id}")
                return True

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 保存提醒设置失败: {e}")
            return False

    def export_data(self, user_id: str, export_path: str) -> bool:
        """导出用药数据"""
        try:
            medications = self.get_medications(user_id, status='active', page=1, page_size=1000)
            history_medications = self.get_medications(user_id, status='stopped', page=1, page_size=1000)

            export_data = {
                'export_time': datetime.now().isoformat(),
                'user_id': user_id,
                'current_medications': medications,
                'history_medications': history_medications,
                'total_count': len(medications) + len(history_medications)
            }

            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            KivyLogger.info(f"[MedicationDB] 数据导出成功: {export_path}")
            return True

        except Exception as e:
            KivyLogger.error(f"[MedicationDB] 数据导出失败: {e}")
            return False

class NotificationManager:
    """通知管理器"""

    def __init__(self):
        self.is_available = NOTIFICATION_AVAILABLE

    def schedule_medication_reminder(self, medication_name: str, reminder_time: str) -> bool:
        """安排用药提醒"""
        if not self.is_available:
            KivyLogger.warning("[NotificationManager] 通知功能不可用")
            return False

        try:
            # 这里应该实现实际的通知调度逻辑
            # 由于Kivy的限制，这里只是模拟
            KivyLogger.info(f"[NotificationManager] 已安排提醒: {medication_name} at {reminder_time}")
            return True

        except Exception as e:
            KivyLogger.error(f"[NotificationManager] 安排提醒失败: {e}")
            return False

    def send_immediate_notification(self, title: str, message: str) -> bool:
        """发送即时通知"""
        if not self.is_available:
            return False

        try:
            notification.notify(
                title=title,
                message=message,
                app_name="健康管理",
                timeout=10
            )
            return True

        except Exception as e:
            KivyLogger.error(f"[NotificationManager] 发送通知失败: {e}")
            return False

# 全局实例
_db_manager = None
_notification_manager = None

# 全局工具函数
def safe_str(value, default=""):
    """安全的字符串转换函数"""
    return str(value) if value is not None else default

def get_medication_db_manager() -> MedicationDatabaseManager:
    """获取用药数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = MedicationDatabaseManager()
    return _db_manager

def get_notification_manager() -> NotificationManager:
    """获取通知管理器实例"""
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager()
    return _notification_manager

KV = '''
<MedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme_cls.surfaceColor
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    ripple_behavior: True
    on_release: root.on_card_click()

    # 药物名称和状态
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)

        MDLabel:
            text: root.name
            font_style: "Headline"
            role: "small"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"

        Widget:
            size_hint_x: 1

        MDLabel:
            text: root.status_text
            font_style: "Body"
            role: "small"
            theme_text_color: "Custom"
            text_color: app.theme_cls.primaryColor if root.status_text == "正在使用" else app.theme_cls.onSurfaceVariantColor
            size_hint_x: None
            width: self.texture_size[0]
            halign: "right"
            valign: "center"

    # 药物信息
    MDBoxLayout:
        orientation: 'vertical'
        size_hint_y: None
        height: dp(60)
        spacing: dp(4)

        MDLabel:
            text: f"剂量: {root.dosage}"
            font_style: "Body"
            role: "small"
            theme_text_color: "Secondary"
            size_hint_y: None
            height: dp(20)
            halign: "left"
            valign: "center"

        MDLabel:
            text: f"用法: {root.frequency}"
            font_style: "Body"
            role: "small"
            theme_text_color: "Secondary"
            size_hint_y: None
            height: dp(20)
            halign: "left"
            valign: "center"

        MDLabel:
            text: f"开始时间: {root.start_date}"
            font_style: "Body"
            role: "small"
            theme_text_color: "Secondary"
            size_hint_y: None
            height: dp(20)
            halign: "left"
            valign: "center"

    # 操作按钮区域
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(8)

        MDButton:
            style: "outlined"
            size_hint_x: 0.3
            on_release: root.on_remind()

            MDButtonIcon:
                icon: "bell"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.primaryColor

            MDButtonText:
                text: "提醒"
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme_cls.primaryColor

        MDButton:
            style: "outlined"
            size_hint_x: 0.3
            on_release: root.on_stop()

            MDButtonIcon:
                icon: "stop"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.errorColor

            MDButtonText:
                text: "停药"
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme_cls.errorColor

        MDButton:
            style: "outlined"
            size_hint_x: 0.3
            on_release: root.on_delete()

            MDButtonIcon:
                icon: "delete"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.errorColor

            MDButtonText:
                text: "删除"
                font_style: "Body"
                role: "small"
                theme_text_color: "Custom"
                text_color: app.theme_cls.errorColor

<MedicationManagementScreen>:
    md_bg_color: app.theme_cls.surfaceColor

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)

        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]

            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()

            MDLabel:
                text: "用药管理"
                font_style: "Headline"
                role: "small"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme_cls.onPrimaryColor
                halign: "center"
                valign: "center"

            # 占位符，保持布局平衡
            MDBoxLayout:
                size_hint_x: None
                width: dp(48)

        # Logo区域 - 移到应用栏下方
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(60)
            pos_hint: {"center_x": 0.5}

        # Tab切换区域
        MDCard:
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme_cls.surfaceContainerColor
            radius: [dp(28)]
            elevation: 1
            padding: [dp(4), dp(4), dp(4), dp(4)]
            pos_hint: {"center_x": 0.5}
            size_hint_x: 0.8

            MDBoxLayout:
                orientation: 'horizontal'
                spacing: dp(4)

                MDButton:
                    id: current_tab_btn
                    style: "filled" if root.current_tab == 'current' else "outlined"
                    md_bg_color: app.theme_cls.primaryColor if root.current_tab == 'current' else app.theme_cls.surfaceColor
                    size_hint_x: 0.5
                    radius: [dp(24)]
                    on_release: root.switch_tab('current')

                    MDButtonIcon:
                        icon: "pill"
                        theme_icon_color: "Custom"
                        icon_color: app.theme_cls.onPrimaryColor if root.current_tab == 'current' else app.theme_cls.onSurfaceVariantColor

                    MDButtonText:
                        text: "目前用药"
                        theme_text_color: "Custom"
                        text_color: app.theme_cls.onPrimaryColor if root.current_tab == 'current' else app.theme_cls.onSurfaceVariantColor

                MDButton:
                    id: history_tab_btn
                    style: "filled" if root.current_tab == 'history' else "outlined"
                    md_bg_color: app.theme_cls.primaryColor if root.current_tab == 'history' else app.theme_cls.surfaceColor
                    size_hint_x: 0.5
                    radius: [dp(24)]
                    on_release: root.switch_tab('history')

                    MDButtonIcon:
                        icon: "history"
                        theme_icon_color: "Custom"
                        icon_color: app.theme_cls.onPrimaryColor if root.current_tab == 'history' else app.theme_cls.onSurfaceVariantColor

                    MDButtonText:
                        text: "既往用药"
                        theme_text_color: "Custom"
                        text_color: app.theme_cls.onPrimaryColor if root.current_tab == 'history' else app.theme_cls.onSurfaceVariantColor

        # 内容区域
        MDCard:
            size_hint_y: None
            height: dp(600)
            md_bg_color: app.theme_cls.surfaceColor
            radius: [dp(12)]
            elevation: 1
            padding: [dp(16), dp(16), dp(16), dp(16)]

            MDBoxLayout:
                orientation: 'vertical'
                spacing: dp(12)

                # 目前用药内容
                MDBoxLayout:
                    id: current_content
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(560) if root.current_tab == 'current' else 0
                    opacity: 1 if root.current_tab == 'current' else 0
                    spacing: dp(12)

                    # 操作按钮区域
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(48)
                        spacing: dp(12)
                        padding: [dp(16), dp(8), dp(16), dp(8)]

                        MDButton:
                            style: "outlined"
                            size_hint_x: 0.33
                            on_release: root.show_delete_dialog()

                            MDButtonIcon:
                                icon: "delete"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.errorColor

                            MDButtonText:
                                text: "删除"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.errorColor

                        MDButton:
                            style: "outlined"
                            size_hint_x: 0.33
                            on_release: root.show_stop_dialog()

                            MDButtonIcon:
                                icon: "stop"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.onSurfaceVariantColor

                            MDButtonText:
                                text: "停用"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onSurfaceVariantColor

                        MDButton:
                            style: "outlined"
                            size_hint_x: 0.33
                            on_release: root.show_batch_reminder_dialog()

                            MDButtonIcon:
                                icon: "bell"
                                theme_icon_color: "Custom"
                                icon_color: app.theme_cls.primaryColor

                            MDButtonText:
                                text: "提醒设置"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.primaryColor

                    # 当前用药卡片容器
                    MDScrollView:
                        do_scroll_x: False
                        do_scroll_y: True

                        MDBoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height
                            spacing: dp(12)
                            padding: [dp(8), dp(8), dp(8), dp(8)]

                            # 添加药物卡片（固定显示）
                            MDCard:
                                size_hint_y: None
                                height: self.minimum_height
                                md_bg_color: app.theme_cls.surfaceContainerHighColor
                                radius: [dp(12)]
                                elevation: 3

                                MDBoxLayout:
                                    orientation: 'vertical'
                                    padding: [dp(16), dp(16), dp(16), dp(16)]
                                    spacing: dp(12)
                                    size_hint_y: None
                                    height: self.minimum_height

                                    # 标题区域
                                    MDBoxLayout:
                                        orientation: 'horizontal'
                                        size_hint_y: None
                                        height: dp(32)
                                        spacing: dp(8)

                                        MDIcon:
                                            icon: "pill"
                                            size_hint_x: None
                                            width: dp(24)
                                            theme_icon_color: "Custom"
                                            icon_color: app.theme_cls.primaryColor
                                            icon_size: dp(24)

                                        MDLabel:
                                            text: "添加新药物"
                                            font_style: "Headline"
                                            role: "small"
                                            theme_text_color: "Custom"
                                            text_color: app.theme_cls.primaryColor
                                            valign: "center"

                                    # 第一行：药物名称和剂量
                                    MDBoxLayout:
                                        orientation: 'horizontal'
                                        size_hint_y: None
                                        height: dp(56)
                                        spacing: dp(8)

                                        MDTextField:
                                            id: medication_name_field
                                            mode: "outlined"
                                            size_hint_x: 0.5

                                            MDTextFieldHintText:
                                                text: "药物名称"

                                        MDTextField:
                                            id: dosage_field
                                            mode: "outlined"
                                            size_hint_x: 0.5

                                            MDTextFieldHintText:
                                                text: "剂量"

                                    # 第二行：使用频次和开始日期
                                    MDBoxLayout:
                                        orientation: 'horizontal'
                                        size_hint_y: None
                                        height: dp(56)
                                        spacing: dp(8)

                                        MDTextField:
                                            id: frequency_field
                                            mode: "outlined"
                                            size_hint_x: 0.5
                                            readonly: True
                                            on_focus: if self.focus: root.show_frequency_menu(self)

                                            MDTextFieldHintText:
                                                text: "使用频次"

                                            MDTextFieldTrailingIcon:
                                                icon: "chevron-down"

                                        MDCard:
                                            size_hint_x: 0.5
                                            md_bg_color: 0, 0, 0, 0
                                            elevation: 0
                                            ripple_behavior: True
                                            on_release: root.show_date_picker(root.ids.start_date_field)

                                            MDTextField:
                                                id: start_date_field
                                                mode: "outlined"
                                                readonly: True

                                                MDTextFieldHintText:
                                                    text: "开始日期"

                                                MDTextFieldTrailingIcon:
                                                    icon: "calendar"

                                    # 第三行：用药原因和注意事项
                                    MDBoxLayout:
                                        orientation: 'horizontal'
                                        size_hint_y: None
                                        height: dp(56)
                                        spacing: dp(8)

                                        MDTextField:
                                            id: reason_field
                                            mode: "outlined"
                                            size_hint_x: 0.5
                                            readonly: True
                                            on_focus: if self.focus: root.show_reason_menu(self)

                                            MDTextFieldHintText:
                                                text: "用药原因"

                                            MDTextFieldTrailingIcon:
                                                icon: "chevron-down"

                                        MDTextField:
                                            id: notes_field
                                            mode: "outlined"
                                            size_hint_x: 0.5
                                            readonly: True
                                            on_focus: if self.focus: root.show_notes_menu(self)

                                            MDTextFieldHintText:
                                                text: "注意事项"

                                            MDTextFieldTrailingIcon:
                                                icon: "chevron-down"

                                    # 添加按钮
                                    MDButton:
                                        style: "filled"
                                        md_bg_color: app.theme_cls.primaryColor
                                        size_hint_y: None
                                        height: dp(40)
                                        radius: [dp(20)]
                                        on_release: root.add_medication_to_list()

                                        MDButtonIcon:
                                            icon: "plus"
                                            theme_icon_color: "Custom"
                                            icon_color: app.theme_cls.onPrimaryColor

                                        MDButtonText:
                                            text: "确认添加"
                                            theme_text_color: "Custom"
                                            text_color: app.theme_cls.onPrimaryColor

                            # 当前用药列表容器
                            MDBoxLayout:
                                id: current_medications_container
                                orientation: 'vertical'
                                size_hint_y: None
                                height: self.minimum_height
                                spacing: dp(12)

                # 既往用药内容
                MDBoxLayout:
                    id: history_content
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(560) if root.current_tab == 'history' else 0
                    opacity: 1 if root.current_tab == 'history' else 0
                    spacing: dp(12)

                    # 搜索功能区域
                    MDBoxLayout:
                        orientation: 'horizontal'
                        size_hint_y: None
                        height: dp(56)
                        spacing: dp(12)
                        padding: [dp(4), dp(8), dp(4), dp(8)]

                        MDTextField:
                            id: history_search_field
                            mode: "outlined"
                            size_hint_x: 0.7

                            MDTextFieldHintText:
                                text: "输入药物名称搜索"

                        MDButton:
                            style: "filled"
                            md_bg_color: app.theme_cls.primaryColor
                            size_hint_x: 0.15
                            on_release: root.search_history_medications()

                            MDButtonText:
                                text: "搜索"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.onPrimaryColor

                        MDButton:
                            style: "outlined"
                            size_hint_x: 0.15
                            on_release: root.clear_history_search()

                            MDButtonText:
                                text: "清空"
                                theme_text_color: "Custom"
                                text_color: app.theme_cls.primaryColor

                    # 既往用药列表
                    MDScrollView:
                        do_scroll_x: False
                        do_scroll_y: True

                        MDBoxLayout:
                            id: history_medications_container
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height
                            spacing: dp(12)
                            padding: [dp(8), dp(8), dp(8), dp(8)]

<CurrentMedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme_cls.surfaceColor
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    ripple_behavior: True
    on_release: root.on_card_click()
    
    # 卡片头部 - 选择框、序号和药物名称
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(12)
        
        # 选择框
        MDCheckbox:
            id: selection_checkbox
            size_hint_x: None
            width: dp(32)
            active: root.is_selected
            on_active: root.on_checkbox_active(self.active)
        
        # 序号
        MDLabel:
            text: f"No.{root.row_index + 1}"
            font_style: "Body"
            role: "small"
            bold: True
            theme_text_color: "Custom"
            text_color: app.theme_cls.primaryColor
            size_hint_x: None
            width: dp(60)
            halign: "left"
            valign: "center"
        
        # 药物名称
        MDLabel:
            text: root.name
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"
            text_size: self.width, None
    
    # 药物详细信息网格
    MDGridLayout:
        cols: 2
        size_hint_y: None
        height: self.minimum_height
        spacing: [dp(16), dp(8)]
        
        # 剂量信息
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "scale-balance"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"剂量: {root.dosage}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 使用频次
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "clock-outline"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"频次: {root.frequency}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 起始时间
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "calendar"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"起始: {root.start_date}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
        
        # 用药原因
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)
            
            MDIcon:
                icon: "heart-pulse"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)
            
            MDLabel:
                text: f"原因: {root.reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
    
    # 注意事项（如果有）
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(24) if root.notes else 0
        spacing: dp(8)
        opacity: 1 if root.notes else 0

        MDIcon:
            icon: "information-outline"
            theme_icon_color: "Custom"
            icon_color: app.theme_cls.errorColor
            size_hint_x: None
            width: dp(20)

        MDLabel:
            text: f"注意: {root.notes}" if root.notes else ""
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme_cls.errorColor
            halign: "left"
            valign: "center"
            text_size: self.width, None

    # 提醒设置信息（如果有）
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(24) if root.get_reminder_text() else 0
        spacing: dp(8)
        opacity: 1 if root.get_reminder_text() else 0

        MDIcon:
            icon: "bell-outline"
            theme_icon_color: "Custom"
            icon_color: app.theme_cls.primaryColor
            size_hint_x: None
            width: dp(20)

        MDLabel:
            text: root.get_reminder_text()
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme_cls.primaryColor
            halign: "left"
            valign: "center"
            text_size: self.width, None

# 既往用药卡片组件
<HistoryMedicationCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme_cls.surfaceContainerColor
    radius: [dp(12)]
    elevation: 1
    padding: [dp(16), dp(12), dp(16), dp(12)]
    spacing: dp(8)
    
    # 卡片头部 - 序号和药物名称
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(12)
        
        # 序号标签
        MDLabel:
            text: f"No.{root.row_index + 1}"
            font_style: "Body"
            role: "small"
            bold: True
            theme_text_color: "Custom"
            text_color: app.theme_cls.onSurfaceVariantColor
            size_hint_x: None
            width: dp(60)
            halign: "left"
            valign: "center"
        
        # 药物名称
        MDLabel:
            text: root.name
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            halign: "left"
            valign: "center"
            text_size: self.width, None
    
    # 药物详细信息 - 垂直布局，每项单独一行
    MDBoxLayout:
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(8)

        # 剂量和频次信息 - 水平排列
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(16)

            # 剂量信息
            MDBoxLayout:
                orientation: 'horizontal'
                size_hint_x: 0.5
                size_hint_y: None
                height: dp(24)
                spacing: dp(8)

                MDIcon:
                    icon: "scale-balance"
                    theme_icon_color: "Custom"
                    icon_color: app.theme_cls.onSurfaceVariantColor
                    size_hint_x: None
                    width: dp(20)

                MDLabel:
                    text: f"剂量: {root.dosage}"
                    font_style: "Body"
                    role: "medium"
                    theme_text_color: "Secondary"
                    halign: "left"
                    valign: "center"

            # 使用频次
            MDBoxLayout:
                orientation: 'horizontal'
                size_hint_x: 0.5
                size_hint_y: None
                height: dp(24)
                spacing: dp(8)

                MDIcon:
                    icon: "clock-outline"
                    theme_icon_color: "Custom"
                    icon_color: app.theme_cls.onSurfaceVariantColor
                    size_hint_x: None
                    width: dp(20)

                MDLabel:
                    text: f"频次: {root.frequency}"
                    font_style: "Body"
                    role: "medium"
                    theme_text_color: "Secondary"
                    halign: "left"
                    valign: "center"

        # 用药日期 - 单独一行
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)

            MDIcon:
                icon: "calendar-range"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"用药日期: {root.start_date}" + (f" 至 {root.get_formatted_stop_date()}" if root.get_formatted_stop_date() else "")
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 用药原因 - 单独一行
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)

            MDIcon:
                icon: "heart-pulse"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.onSurfaceVariantColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"用药原因: {root.reason}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Secondary"
                halign: "left"
                valign: "center"
                text_size: self.width, None

        # 停药原因 - 单独一行
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(24)
            spacing: dp(8)

            MDIcon:
                icon: "alert-circle-outline"
                theme_icon_color: "Custom"
                icon_color: app.theme_cls.errorColor
                size_hint_x: None
                width: dp(20)

            MDLabel:
                text: f"停药原因: {root.get_stop_reason_display()}"
                font_style: "Body"
                role: "medium"
                theme_text_color: "Custom"
                text_color: app.theme_cls.errorColor
                halign: "left"
                valign: "center"
                text_size: self.width, None
# 卡片详情对话框
<MedicationDetailDialog>:
    size_hint: 0.9, None
    height: dp(600)
    md_bg_color: app.theme_cls.surfaceColor
    radius: [dp(16)]
    
    MDBoxLayout:
        orientation: 'vertical'
        padding: [dp(24), dp(20), dp(24), dp(20)]
        spacing: dp(16)
        
        # 对话框标题
        MDLabel:
            text: "药物详情"
            font_style: "Headline"
            role: "small"
            bold: True
            theme_text_color: "Primary"
            size_hint_y: None
            height: dp(32)
            halign: "center"
            valign: "center"
        
        # 药物详细信息
        MDScrollView:
            MDBoxLayout:
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                spacing: dp(16)
                
                # 药物名称
                MDBoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: dp(40)
                    spacing: dp(12)
                    
                    MDIcon:
                        icon: "pill"
                        theme_icon_color: "Custom"
                        icon_color: app.theme_cls.primaryColor
                        size_hint_x: None
                        width: dp(24)
                    
                    MDLabel:
                        text: f"药物名称: {root.medication_name}"
                        font_style: "Body"
                        role: "large"
                        bold: True
                        theme_text_color: "Primary"
                        halign: "left"
                        valign: "center"
                
                # 剂量和频次
                MDGridLayout:
                    cols: 2
                    size_hint_y: None
                    height: dp(80)
                    spacing: dp(16)
                    
                    MDBoxLayout:
                        orientation: 'horizontal'
                        spacing: dp(8)
                        
                        MDIcon:
                            icon: "scale-balance"
                            theme_icon_color: "Custom"
                            icon_color: app.theme_cls.onSurfaceVariantColor
                            size_hint_x: None
                            width: dp(20)
                        
                        MDLabel:
                            text: f"剂量: {root.dosage}"
                            font_style: "Body"
                            role: "medium"
                            theme_text_color: "Secondary"
                            halign: "left"
                            valign: "center"
                    
                    MDBoxLayout:
                        orientation: 'horizontal'
                        spacing: dp(8)
                        
                        MDIcon:
                            icon: "clock-outline"
                            theme_icon_color: "Custom"
                            icon_color: app.theme_cls.onSurfaceVariantColor
                            size_hint_x: None
                            width: dp(20)
                        
                        MDLabel:
                            text: f"频次: {root.frequency}"
                            font_style: "Body"
                            role: "medium"
                            theme_text_color: "Secondary"
                            halign: "left"
                            valign: "center"
                
'''

class MedicationCard(MDCard):
    """用药卡片组件"""
    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    status_text = StringProperty("正在使用")
    medication_data = ObjectProperty(None)

    def __init__(self, **kwargs):
        super(MedicationCard, self).__init__(**kwargs)
        self.ripple_behavior = True
        self.ripple_duration_in_slow = 0.1
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)

    def on_card_click(self):
        """点击卡片查看详情"""
        app = MDApp.get_running_app()
        if hasattr(app.root.current_screen, 'show_medication_detail'):
            app.root.current_screen.show_medication_detail(self.medication_data)

    # 旧的单个药物操作方法已被统一架构替代，已删除

    def on_touch_up(self, touch):
        """处理触摸释放事件"""
        if self.collide_point(*touch.pos) and touch.is_mouse_scrolling is False:
            self.on_release()
        return super(MedicationCard, self).on_touch_up(touch)

    def on_release(self):
        """点击卡片时调用"""
        self.on_card_click()


class CurrentMedicationCard(MDCard):
    """当前用药卡片组件"""

    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    reason = StringProperty("")
    notes = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)
    is_selected = BooleanProperty(False)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ripple_behavior = True
        self.ripple_duration_in_slow = 0.1
        self.ripple_color = (0.8, 0.8, 0.8, 0.5)

    def get_reminder_text(self):
        """获取提醒设置文本"""
        try:
            if not self.medication_data:
                return ""

            reminder_settings = self.medication_data.get('reminder_settings', {})
            if not reminder_settings:
                return ""

            reminder_texts = []

            # 服药提醒 - 支持多个时间段
            med_reminder = reminder_settings.get('med_reminder', {})
            if med_reminder.get('enabled'):
                times = med_reminder.get('times', [])
                if times:
                    # 如果有多个时间段，显示所有时间
                    time_strs = []
                    for time_setting in times:
                        time_text = time_setting.get('time', '')
                        advance_text = time_setting.get('advance_minutes', '15')
                        time_strs.append(f"{time_text}(提前{advance_text}分钟)")
                    
                    if len(time_strs) == 1:
                        reminder_texts.append(f"服药: {time_strs[0]}")
                    else:
                        reminder_texts.append(f"服药({len(time_strs)}次): {', '.join(time_strs)}")
                else:
                    # 兼容旧格式
                    time_text = med_reminder.get('time', '')
                    advance_text = med_reminder.get('advance_minutes', '15')
                    if time_text:
                        reminder_texts.append(f"服药: {time_text} (提前{advance_text}分钟)")

            # 复查提醒
            review_reminder = reminder_settings.get('review_reminder', {})
            if review_reminder.get('enabled'):
                date_text = review_reminder.get('date', '')
                advance_text = review_reminder.get('advance_days', '3')
                reminder_texts.append(f"复查: {date_text} (提前{advance_text}天)")

            return " | ".join(reminder_texts) if reminder_texts else ""

        except Exception as e:
            return ""

    def on_checkbox_active(self, active):
        """处理checkbox状态变化"""
        try:
            # 避免循环调用，只在状态真正改变时更新
            if self.is_selected != active:
                self.is_selected = active
                # 通知父屏幕更新选择列表
                current = self.parent
                while current and not isinstance(current, MedicationManagementScreen):
                    current = current.parent

                if current and hasattr(current, 'update_selection'):
                    current.update_selection(self, self.is_selected)
        except Exception as e:
            KivyLogger.error(f"Error in on_checkbox_active: {e}")

    def toggle_selection(self):
        """切换选择状态（程序调用）"""
        try:
            new_state = not self.is_selected
            self.is_selected = new_state
            # 通知父屏幕更新选择列表
            current = self.parent
            while current and not isinstance(current, MedicationManagementScreen):
                current = current.parent

            if current and hasattr(current, 'update_selection'):
                current.update_selection(self, self.is_selected)
        except Exception as e:
            KivyLogger.error(f"Error in toggle_selection: {e}")

    def on_card_click(self):
        """处理卡片点击事件"""
        if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'parent'):
            screen = self.parent.parent.parent
            if hasattr(screen, 'show_medication_detail'):
                screen.show_medication_detail(self.medication_data)


class HistoryMedicationCard(MDCard):
    """既往用药卡片组件"""

    name = StringProperty("")
    dosage = StringProperty("")
    frequency = StringProperty("")
    start_date = StringProperty("")
    stop_date = StringProperty("")
    reason = StringProperty("")
    stop_reason = StringProperty("")
    notes = StringProperty("")
    row_index = NumericProperty(0)
    medication_data = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_formatted_stop_date(self):
        """获取格式化的停药日期"""
        try:
            if not self.stop_date or not self.stop_date.strip():
                return ""

            # 如果是ISO格式的日期时间，只取日期部分
            if 'T' in self.stop_date:
                return self.stop_date.split('T')[0]
            return self.stop_date
        except Exception as e:
            KivyLogger.error(f"[HistoryMedicationCard] 格式化停药日期失败: {e}")
            return self.stop_date

    def get_stop_reason_display(self):
        """获取停药原因显示文本"""
        try:
            if not self.stop_reason or not self.stop_reason.strip():
                return "未记录停药原因"
            return self.stop_reason.strip()
        except Exception as e:
            KivyLogger.error(f"[HistoryMedicationCard] 获取停药原因失败: {e}")
            return "未记录停药原因"


class MedicationManagementScreen(BaseScreen):
    """用药管理屏幕"""
    medications = ListProperty([])
    history_medications = ListProperty([])
    current_medications = ListProperty([])
    current_tab = StringProperty('current')
    dialog = None
    editing_medication = None
    current_page = NumericProperty(1)
    is_loading = BooleanProperty(False)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()

        # 初始化数据管理器
        self.db_manager = get_medication_db_manager()
        self.notification_manager = get_notification_manager()

        # 初始化数据列表
        self.medications = []
        self.history_medications = []
        self.current_medications = []
        self.selected_medications = []

        # 初始化对话框变量
        self.dialog = None
        self.unified_dialog = None
        self.delete_dialog = None
        self.detail_dialog = None

        # 初始化选择状态
        self.selected_reminder_type = "daily"

        KivyLogger.info("[MedicationManagement] 初始化完成")
        Clock.schedule_once(self.init_ui, 0.2)

    def on_enter(self):
        """进入屏幕时调用"""
        try:
            super().on_enter()
            KivyLogger.info("[MedicationManagement] 进入屏幕")
            self.init_ui()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 进入屏幕失败: {e}")

    def init_ui(self, dt=0):
        """初始化UI"""
        try:
            KivyLogger.info("[MedicationManagement] 开始初始化UI")
            self.load_current_medications()
            self.load_history_medications()
            self.set_default_values()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 初始化UI失败: {e}")

    def set_default_values(self):
        """设置默认值"""
        try:
            from datetime import datetime

            # 延迟设置默认值，确保界面已完全加载
            def _set_defaults(dt):
                try:
                    # 设置默认开始日期为今天
                    if hasattr(self, 'ids') and 'start_date_field' in self.ids:
                        start_date_field = self.ids.get('start_date_field')
                        if start_date_field and hasattr(start_date_field, 'text'):
                            if not start_date_field.text.strip():
                                start_date_field.text = datetime.now().strftime("%Y-%m-%d")
                                KivyLogger.info(f"[MedicationManagement] 设置默认开始日期: {start_date_field.text}")
                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 延迟设置默认值失败: {e}")

            Clock.schedule_once(_set_defaults, 0.5)

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置默认值失败: {e}")

    def get_current_user_id(self) -> Optional[str]:
        """获取当前用户ID"""
        try:
            # 首先尝试从app.user_data获取
            if hasattr(self.app, 'user_data') and self.app.user_data:
                custom_id = self.app.user_data.get('custom_id')
                if custom_id:
                    return custom_id

            # 如果没有获取到，尝试从认证管理器获取
            try:
                from utils.auth_manager import get_auth_manager
                auth_manager = get_auth_manager()
                user_info = auth_manager.get_current_user_info()
                if user_info:
                    return user_info.get('custom_id')
            except Exception as auth_error:
                KivyLogger.warning(f"[MedicationManagement] 获取认证信息失败: {auth_error}")

            return None

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 获取用户ID失败: {e}")
            return None

    def load_medications(self):
        """加载用药记录"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                KivyLogger.info("[MedicationManagement] 未找到用户信息，显示登录提示")
                self.show_empty_state("暂无用药记录\n请先登录后查看")
                return

            if not hasattr(self, 'ids') or 'medications_container' not in self.ids:
                return

            try:
                self.ids.medications_container.clear_widgets()
            except ReferenceError:
                return

            # 从数据库加载用药记录
            self.medications = self.db_manager.get_medications(
                user_id=user_id,
                status='active',
                page=self.current_page,
                page_size=PAGE_SIZE
            )

            if not self.medications:
                # 显示空状态
                self.show_empty_state("暂无用药记录\n点击右上角的 + 按钮添加记录")
                return

            for med in self.medications:
                card = MedicationCard(
                    name=med["name"],
                    dosage=med['dosage'],
                    frequency=med['frequency'],
                    start_date=med.get('start_date', ''),
                    status_text="正在使用",
                    medication_data=med
                )
                self.ids.medications_container.add_widget(card)

            KivyLogger.info(f"[MedicationManagement] 成功加载 {len(self.medications)} 条用药记录")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载用药记录时出错: {e}")
            self.show_error(f"加载用药记录失败: {str(e)}")

    def show_empty_state(self, message: str):
        """显示空状态"""
        try:
            if hasattr(self, 'ids') and 'medications_container' in self.ids:
                self.ids.medications_container.clear_widgets()
                empty_label = MDLabel(
                    text=message,
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium"
                )
                self.ids.medications_container.add_widget(empty_label)
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示空状态失败: {e}")

    def load_current_medications(self):
        """加载当前用药记录"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                KivyLogger.info("[MedicationManagement] 未找到用户信息，无法加载当前用药")
                return

            # 从数据库加载当前用药记录
            self.current_medications = self.db_manager.get_medications(
                user_id=user_id,
                status='active',
                page=1,
                page_size=100
            )

            KivyLogger.info(f"[MedicationManagement] 加载到 {len(self.current_medications)} 条当前用药记录")

            # 刷新当前用药显示
            self.refresh_current_medications()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载当前用药失败: {e}")

    def refresh_current_medications(self):
        """刷新当前用药显示"""
        try:
            KivyLogger.info(f"[MedicationManagement] 开始刷新当前用药显示，数量: {len(self.current_medications)}")

            # 清空现有容器
            if hasattr(self, 'ids') and 'current_medications_container' in self.ids:
                self.ids.current_medications_container.clear_widgets()
            else:
                KivyLogger.warning("[MedicationManagement] 未找到current_medications_container")
                return

            # 如果没有当前用药记录，显示空状态
            if not self.current_medications:
                empty_label = MDLabel(
                    text="暂无当前用药记录\n点击上方添加按钮开始记录用药信息",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium",
                    size_hint_y=None,
                    height=dp(100)
                )
                self.ids.current_medications_container.add_widget(empty_label)
                KivyLogger.info("[MedicationManagement] 显示当前用药空状态")
                return

            # 添加当前用药卡片
            for index, med in enumerate(self.current_medications):
                try:
                    # 使用全局safe_str函数

                    # 调试日志：检查提醒设置
                    reminder_settings = med.get('reminder_settings', {})
                    KivyLogger.info(f"[MedicationManagement] 创建当前用药卡片 - 药物: {med.get('name')}, 提醒设置: {reminder_settings}")

                    card = CurrentMedicationCard(
                        name=safe_str(med.get('name'), '未知药物'),
                        dosage=safe_str(med.get('dosage')),
                        frequency=safe_str(med.get('frequency')),
                        start_date=safe_str(med.get('start_date')),
                        reason=safe_str(med.get('reason')),
                        notes=safe_str(med.get('notes')),
                        row_index=index,
                        medication_data=med
                    )

                    # 恢复选择状态（如果之前被选中）
                    if hasattr(self, 'selected_medications'):
                        for selected_card in self.selected_medications:
                            if (hasattr(selected_card, 'medication_data') and
                                selected_card.medication_data.get('id') == med.get('id')):
                                card.is_selected = True
                                # 更新选择列表中的引用
                                selected_index = self.selected_medications.index(selected_card)
                                self.selected_medications[selected_index] = card
                                break

                    self.ids.current_medications_container.add_widget(card)

                    # 调试日志：检查提醒文本
                    try:
                        reminder_text = card.get_reminder_text()
                        KivyLogger.info(f"[MedicationManagement] 添加当前用药卡片: {med['name']}, 提醒文本: '{reminder_text}'")

                        # 强制刷新卡片显示
                        if hasattr(card, 'ids'):
                            card.canvas.ask_update()
                    except Exception as reminder_error:
                        KivyLogger.error(f"[MedicationManagement] 获取提醒文本失败: {reminder_error}")
                except Exception as card_error:
                    KivyLogger.error(f"[MedicationManagement] 创建当前用药卡片失败: {card_error}")
                    # 添加详细的错误信息
                    KivyLogger.error(f"[MedicationManagement] 药物数据: {med}")
                    import traceback
                    KivyLogger.error(f"[MedicationManagement] 错误详情: {traceback.format_exc()}")
                    continue

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新当前用药显示失败: {e}")

    def load_history_medications(self):
        """加载既往用药记录"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                return

            # 从数据库加载既往用药记录
            self.history_medications = self.db_manager.get_medications(
                user_id=user_id,
                status='stopped',
                page=1,
                page_size=PAGE_SIZE
            )

            self.refresh_history_display()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 加载既往用药失败: {e}")

    def refresh_history_medications(self):
        """刷新既往用药记录（不重新从数据库加载）"""
        try:
            KivyLogger.info(f"[MedicationManagement] 刷新既往用药记录，数量: {len(self.history_medications)}")
            self.refresh_history_display()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新既往用药记录失败: {e}")

    def refresh_history_display(self):
        """刷新既往用药显示"""
        try:
            KivyLogger.info(f"[MedicationManagement] 开始刷新既往用药显示，数量: {len(self.history_medications)}")

            if not hasattr(self, 'ids') or 'history_medications_container' not in self.ids:
                KivyLogger.warning("[MedicationManagement] 未找到history_medications_container")
                return

            container = self.ids.history_medications_container
            container.clear_widgets()

            if not self.history_medications:
                empty_label = MDLabel(
                    text="暂无既往用药记录",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="medium",
                    size_hint_y=None,
                    height=dp(100)
                )
                container.add_widget(empty_label)
                KivyLogger.info("[MedicationManagement] 显示空状态")
                return

            # 按停药日期排序（最新的在前）
            def safe_sort_key(med):
                """安全的排序键，处理None值"""
                stop_date = med.get('stop_date')
                if stop_date is None:
                    return ''  # None值排到最后
                return str(stop_date)

            sorted_medications = sorted(
                self.history_medications,
                key=safe_sort_key,
                reverse=True
            )

            for index, med in enumerate(sorted_medications):
                try:
                    # 使用全局safe_str函数

                    # 格式化停药日期
                    raw_stop_date = med.get("stop_date")
                    formatted_stop_date = ""
                    if raw_stop_date:
                        try:
                            # 如果是ISO格式的日期时间，只取日期部分
                            if 'T' in str(raw_stop_date):
                                formatted_stop_date = str(raw_stop_date).split('T')[0]
                            else:
                                formatted_stop_date = safe_str(raw_stop_date)
                        except Exception as e:
                            KivyLogger.error(f"[MedicationManagement] 格式化停药日期失败: {e}")
                            formatted_stop_date = safe_str(raw_stop_date)

                    # 格式化停药原因
                    stop_reason_value = safe_str(med.get("stop_reason"))

                    # 调试日志：检查停药信息
                    KivyLogger.info(f"[MedicationManagement] 创建既往用药卡片 - 药物: {med.get('name')}")
                    KivyLogger.info(f"[MedicationManagement] 原始停药日期: '{raw_stop_date}' -> 格式化: '{formatted_stop_date}'")
                    KivyLogger.info(f"[MedicationManagement] 停药原因: '{stop_reason_value}'")

                    card = HistoryMedicationCard(
                        name=safe_str(med.get("name"), "未知药物"),
                        dosage=safe_str(med.get('dosage')),
                        frequency=safe_str(med.get('frequency')),
                        start_date=safe_str(med.get("start_date")),
                        stop_date=formatted_stop_date,
                        reason=safe_str(med.get("reason")),
                        stop_reason=stop_reason_value,
                        notes=safe_str(med.get("notes")),
                        row_index=index,
                        medication_data=med
                    )

                    # 验证卡片属性
                    KivyLogger.info(f"[MedicationManagement] 卡片属性 - stop_date: '{card.stop_date}', stop_reason: '{card.stop_reason}'")
                    container.add_widget(card)
                    KivyLogger.info(f"[MedicationManagement] 添加既往用药卡片: {med['name']}")
                except Exception as card_error:
                    KivyLogger.error(f"[MedicationManagement] 创建既往用药卡片失败: {card_error}")
                    # 添加详细的错误信息
                    KivyLogger.error(f"[MedicationManagement] 药物数据: {med}")
                    import traceback
                    KivyLogger.error(f"[MedicationManagement] 错误详情: {traceback.format_exc()}")

            KivyLogger.info(f"[MedicationManagement] 既往用药显示刷新完成，共 {len(sorted_medications)} 条记录")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新既往用药显示失败: {e}")

    def go_back(self):
        """返回上一页"""
        try:
            app = MDApp.get_running_app()
            app.root.current = 'health_data_management_screen'
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'

    def switch_tab(self, tab_name):
        """切换Tab"""
        try:
            KivyLogger.info(f"[MedicationManagement] 切换到Tab: {tab_name}")
            self.current_tab = tab_name

            # 更新内容显示
            if hasattr(self, 'ids'):
                current_content = self.ids.get('current_content')
                history_content = self.ids.get('history_content')

                if current_content and history_content:
                    if tab_name == 'current':
                        # 显示目前用药
                        current_content.height = dp(560)
                        current_content.opacity = 1
                        current_content.disabled = False

                        # 隐藏既往用药
                        history_content.height = 0
                        history_content.opacity = 0
                        history_content.disabled = True

                        # 加载数据
                        self.load_medications()
                        KivyLogger.info("[MedicationManagement] 显示目前用药Tab")

                    else:  # history
                        # 隐藏目前用药
                        current_content.height = 0
                        current_content.opacity = 0
                        current_content.disabled = True

                        # 显示既往用药
                        history_content.height = dp(560)
                        history_content.opacity = 1
                        history_content.disabled = False

                        # 加载数据
                        self.load_history_medications()
                        KivyLogger.info("[MedicationManagement] 显示既往用药Tab")

                else:
                    KivyLogger.warning("[MedicationManagement] 未找到Tab内容容器")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 切换Tab失败: {e}")

    def search_history_medications(self):
        """搜索既往用药记录"""
        try:
            if not hasattr(self, 'ids') or 'history_search_field' not in self.ids:
                return

            search_text = self.ids.history_search_field.text.strip().lower()

            if not search_text:
                self.refresh_history_display()
                return

            # 过滤既往用药记录
            filtered_medications = []
            for med in self.history_medications:
                try:
                    # 安全地获取字符串值
                    name = med.get("name", "") or ""
                    stop_reason = med.get("stop_reason", "") or ""

                    # 检查是否匹配搜索条件
                    if (search_text in name.lower() or
                        search_text in stop_reason.lower()):
                        filtered_medications.append(med)
                except Exception as filter_error:
                    KivyLogger.error(f"[MedicationManagement] 过滤药物失败: {filter_error}")
                    continue

            # 临时更新显示
            original_history = self.history_medications
            self.history_medications = filtered_medications
            self.refresh_history_display()
            self.history_medications = original_history

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 搜索既往用药失败: {e}")

    def show_delete_dialog(self):
        """显示批量删除对话框"""
        try:
            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("请先选择要删除的药物")
                return

            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText, MDButtonIcon

            selected_count = len(self.selected_medications)
            medication_names = [card.medication_data.get('name', '未知药物') for card in self.selected_medications if hasattr(card, 'medication_data')]

            # 构建确认消息
            if selected_count == 1:
                message = f"确定要删除药物 '{medication_names[0]}' 吗？"
            else:
                message = f"确定要删除选中的 {selected_count} 个药物吗？\n\n包括：\n" + "\n".join(f"• {name}" for name in medication_names[:5])
                if len(medication_names) > 5:
                    message += f"\n... 等 {selected_count} 个药物"

            self.delete_dialog = MDDialog(
                MDDialogHeadlineText(text="批量删除确认"),
                MDDialogSupportingText(text=message),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonIcon(icon="close"),
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda *x: self.delete_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonIcon(icon="delete"),
                        MDButtonText(text="确认删除"),
                        style="filled",
                        md_bg_color=self.get_app().theme_cls.errorColor,
                        on_release=lambda *x: self._confirm_batch_delete()
                    )
                ),
                size_hint=(0.9, None),
                auto_dismiss=False
            )

            self.delete_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示批量删除对话框失败: {e}")
            self.show_error("显示删除对话框失败")

    def _confirm_batch_delete(self):
        """确认批量删除"""
        try:
            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("没有选中的药物")
                return

            deleted_count = 0
            user_id = self.get_current_user_id()
            medications_to_delete = [card.medication_data for card in self.selected_medications if hasattr(card, 'medication_data')]

            for medication in medications_to_delete:
                try:
                    # 从数据库中删除
                    if user_id and medication.get('id'):
                        try:
                            success = self.db_manager.delete_medication(medication['id'])
                            if success:
                                KivyLogger.info(f"[MedicationManagement] 药物已从数据库删除: {medication['name']}")
                            else:
                                KivyLogger.warning(f"[MedicationManagement] 数据库删除失败: {medication['name']}")
                        except Exception as db_error:
                            KivyLogger.error(f"[MedicationManagement] 数据库删除异常: {db_error}")

                    # 从当前用药列表中删除
                    if medication in self.current_medications:
                        self.current_medications.remove(medication)
                    if medication in self.medications:
                        self.medications.remove(medication)

                    deleted_count += 1

                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 删除单个药物失败: {e}")

            # 刷新界面（不重新从数据库加载）
            self.refresh_current_medications()

            # 清空选择
            if hasattr(self, 'selected_medications'):
                self.selected_medications.clear()

            # 关闭对话框
            if self.delete_dialog:
                self.delete_dialog.dismiss()

            # 显示成功消息
            if deleted_count > 0:
                self.show_success(f"成功删除 {deleted_count} 个药物")
            else:
                self.show_error("没有药物被删除")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 批量删除失败: {e}")
            self.show_error("删除操作失败")

    def show_stop_dialog(self):
        """显示停药对话框（使用新的截图布局设计）"""
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.boxlayout import MDBoxLayout

            # 调试信息
            selected_count = len(getattr(self, 'selected_medications', []))
            KivyLogger.info(f"[MedicationManagement] 停药对话框 - 已选择药物数量: {selected_count}")

            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("请先选择要停用的药物")
                return

            # 创建对话框内容容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(0),
                size_hint_y=None,
                adaptive_height=True
            )

            # 添加停药字段（使用新的截图布局）
            self._add_stop_fields(content)

            # 创建对话框标题
            if len(self.selected_medications) > 1:
                # 多个药物时显示所有药物名称
                medication_names = [med.name for med in self.selected_medications]
                title_text = f"药物 {', '.join(medication_names)} 将被停用"
            else:
                # 单个药物时显示该药物名称
                medication_name = self.selected_medications[0].name
                title_text = f"药物 {medication_name} 将被停用"
            
            # 创建对话框，使用KivyMD 2.0.1dev0规范
            self.unified_dialog = MDDialog(
                MDDialogHeadlineText(
                    text=title_text,
                    halign="center",
                    theme_text_color="Custom",
                    text_color=(1, 0, 0, 1)  # 红色字体
                ),
                size_hint=(0.85, None),
                height=dp(320),
                auto_dismiss=False
            )
            # 添加内容到对话框容器
            self.unified_dialog.ids.container.add_widget(content)

            self.unified_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示停药对话框失败: {e}")
            self.show_error("显示停药对话框失败")

    def show_batch_reminder_dialog(self):
        """显示提醒设置对话框（使用新的截图布局设计）"""
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.boxlayout import MDBoxLayout

            if not hasattr(self, 'selected_medications') or not self.selected_medications:
                self.show_error("请先选择要设置提醒的药物")
                return

            # 创建对话框内容容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(0),
                size_hint_y=None,
                adaptive_height=True
            )

            # 添加提醒设置字段（使用新的截图布局）
            self._add_reminder_fields(content)

            # 获取选中药物名称
            selected_names = [med.name for med in self.selected_medications]
            if len(selected_names) == 1:
                title_text = selected_names[0]
            else:
                title_text = "、".join(selected_names)

            # 创建对话框，使用KivyMD 2.0.1dev0规范
            self.unified_dialog = MDDialog(
                MDDialogHeadlineText(
                    text=title_text,
                    halign="center",
                    theme_text_color="Custom",
                    text_color=(1, 0, 0, 1)  # 红色字体
                ),
                size_hint=(0.85, None),
                height=dp(420),
                auto_dismiss=False
            )
            
            # 添加内容到对话框容器
            self.unified_dialog.ids.container.add_widget(content)

            self.unified_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示提醒设置对话框失败: {e}")
            self.show_error("显示提醒对话框失败")

    def show_delete_confirmation(self, medication):
        """显示删除确认对话框"""
        try:
            self.delete_dialog = MDDialog(
                MDDialogHeadlineText(text="删除用药记录"),
                MDDialogSupportingText(text=f"确定要永久删除 {medication.get('name', '此药品')} 的用药记录吗？\n\n⚠️ 此操作不可撤销！"),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.delete_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="永久删除"),
                        style="filled",
                        md_bg_color=self.app.theme_cls.errorColor,
                        on_release=lambda x: self.confirm_delete_medication(medication)
                    )
                )
            )

            self.delete_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示删除对话框失败: {e}")
            self.show_error("显示删除对话框失败")

    def confirm_delete_medication(self, medication):
        """确认删除用药记录"""
        try:
            if self.delete_dialog:
                self.delete_dialog.dismiss()

            medication_id = medication.get('id')
            if not medication_id:
                self.show_error("药物ID缺失")
                return

            # 删除用药记录
            success = self.db_manager.delete_medication(medication_id)

            if success:
                self.show_info(f"已删除 {medication.get('name', '药物')} 的用药记录")
                self.load_medications()
            else:
                self.show_error("删除用药记录失败")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 删除用药记录失败: {e}")
            self.show_error("删除用药记录失败")

    def _show_time_picker(self, *args):
        """显示时间选择器"""
        try:
            try:
                from utils.time_picker_utils import show_time_picker

                def on_time_selected(selected_time):
                    """时间选择回调"""
                    try:
                        self.med_time_field.text = selected_time.strftime("%H:%M")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置服药时间失败: {e}")

                show_time_picker(on_time_selected)

            except ImportError:
                # 备用方案：使用简单的输入对话框
                self._show_simple_time_picker(self.med_time_field, "选择服药时间")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示时间选择器失败: {e}")

    def _show_review_date_picker(self, *args):
        """显示复查日期选择器"""
        try:
            try:
                from utils.date_picker_utils import show_review_date_picker

                def on_date_selected(selected_date):
                    """日期选择回调"""
                    try:
                        self.review_date_field.text = selected_date.strftime("%Y-%m-%d")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置复查日期失败: {e}")

                show_review_date_picker(on_date_selected)

            except ImportError:
                # 备用方案：使用简单的输入对话框
                from datetime import datetime, timedelta
                default_date = datetime.now() + timedelta(days=30)
                self.review_date_field.text = default_date.strftime("%Y-%m-%d")
                self._show_simple_date_picker(self.review_date_field, "选择复查日期")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示复查日期选择器失败: {e}")

    def _show_simple_time_picker(self, text_field, title="选择时间"):
        """显示简单的时间输入对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText

            time_input = MDTextField(
                mode="outlined",
                text=text_field.text,
                size_hint_y=None,
                height=dp(56)
            )
            time_input.add_widget(MDTextFieldHintText(text="时间 (HH:MM)"))

            def confirm_time(*args):
                text_field.text = time_input.text
                time_dialog.dismiss()

            time_dialog = MDDialog(
                MDDialogHeadlineText(text=title),
                time_input,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="outlined",
                        on_release=lambda x: time_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确认"),
                        style="filled",
                        on_release=confirm_time
                    )
                ),
                size_hint=(0.8, None),
                auto_dismiss=False
            )
            time_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示简单时间选择器失败: {e}")

    def show_medication_detail(self, medication):
        """显示药物详情对话框"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )

            # 药物基本信息
            info_items = [
                ("药物名称", medication.get('name', '')),
                ("剂量", medication.get('dosage', '')),
                ("用法", medication.get('frequency', '')),
                ("开始时间", medication.get('start_date', '')),
                ("结束时间", medication.get('end_date', '')),
                ("备注", medication.get('notes', '无'))
            ]

            for label_text, value_text in info_items:
                item_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(32),
                    spacing=dp(8)
                )

                label = MDLabel(
                    text=f"{label_text}:",
                    theme_text_color="Primary",
                    size_hint_x=0.3,
                    halign="left",
                    valign="center"
                )
                item_layout.add_widget(label)

                value = MDLabel(
                    text=str(value_text),
                    theme_text_color="Secondary",
                    size_hint_x=0.7,
                    halign="left",
                    valign="center"
                )
                item_layout.add_widget(value)

                content.add_widget(item_layout)

            self.detail_dialog = MDDialog(
                MDDialogHeadlineText(text="药物详情"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="filled",
                        on_release=lambda x: self.detail_dialog.dismiss()
                    )
                )
            )

            self.detail_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示详情对话框失败: {e}")
            self.show_error("显示详情对话框失败")

    def export_medication_data(self):
        """导出用药数据"""
        try:
            user_id = self.get_current_user_id()
            if not user_id:
                self.show_error("用户未登录，无法导出数据")
                return

            # 创建导出目录
            export_dir = Path.home() / "Downloads" / "health_management"
            export_dir.mkdir(parents=True, exist_ok=True)

            # 生成导出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_path = export_dir / f"medication_data_{timestamp}.json"

            # 导出数据
            success = self.db_manager.export_data(user_id, str(export_path))

            if success:
                self.show_info(f"数据导出成功\n文件保存至: {export_path}")
            else:
                self.show_error("数据导出失败")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 导出数据失败: {e}")
            self.show_error("导出数据失败")

    def show_info(self, message):
        """显示信息提示"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示信息失败: {e}")

    def show_error(self, message):
        """显示错误提示"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=f"错误: {message}"),
                pos_hint={"center_x": 0.5},
                duration=3,
                md_bg_color=(0.8, 0.2, 0.2, 1),  # 红色背景
            )
            snackbar.open()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示错误失败: {e}")

    def show_success(self, message):
        """显示成功消息"""
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9,
                md_bg_color=self.get_app().theme_cls.primaryColor,
                duration=3
            )
            snackbar.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示成功消息失败: {e}")

    def show_frequency_menu(self, text_field):
        """显示使用频次下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            frequency_options = [
                "每日一次", "每日两次", "每日三次", "每日四次",
                "每周一次", "每周两次", "每周三次",
                "每月一次", "按需服用", "其他"
            ]

            menu_items = []
            for option in frequency_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_frequency(x, text_field)
                })

            self.frequency_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),  # 使用width替代width_mult
                max_height=dp(200),
            )
            self.frequency_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示频次菜单失败: {e}")

    def set_frequency(self, frequency, text_field):
        """设置使用频次"""
        try:
            text_field.text = frequency
            if hasattr(self, 'frequency_menu') and self.frequency_menu:
                self.frequency_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置频次失败: {e}")

    def show_reason_menu(self, text_field):
        """显示用药原因下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            reason_options = [
                "高血压", "糖尿病", "高血脂", "冠心病",
                "心律不齐", "慢性肾病", "甲状腺疾病",
                "关节炎", "骨质疏松", "抑郁症", "其他原因"
            ]

            menu_items = []
            for option in reason_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_reason(x, text_field)
                })

            self.reason_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),  # 使用width替代width_mult
                max_height=dp(200),
            )
            self.reason_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示原因菜单失败: {e}")

    def set_reason(self, reason, text_field):
        """设置用药原因"""
        try:
            text_field.text = reason
            if hasattr(self, 'reason_menu') and self.reason_menu:
                self.reason_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置原因失败: {e}")

    def show_notes_menu(self, text_field):
        """显示注意事项下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            notes_options = [
                "空腹服用", "餐前服用", "餐中服用", "餐后服用",
                "睡前服用", "避免饮酒", "多饮水", "无特殊要求"
            ]

            menu_items = []
            for option in notes_options:
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=option: self.set_notes(x, text_field)
                })

            self.notes_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(240),  # 使用width替代width_mult
                max_height=dp(200),
            )
            self.notes_menu.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示注意事项菜单失败: {e}")

    def set_notes(self, notes, text_field):
        """设置注意事项"""
        try:
            text_field.text = notes
            if hasattr(self, 'notes_menu') and self.notes_menu:
                self.notes_menu.dismiss()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置注意事项失败: {e}")

    def show_date_picker(self, text_field):
        """显示日期选择器"""
        try:
            try:
                from utils.date_picker_utils import show_medication_start_date_picker

                def on_date_selected(selected_date):
                    """日期选择回调"""
                    try:
                        text_field.text = selected_date.strftime("%Y-%m-%d")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置日期失败: {e}")

                show_medication_start_date_picker(on_date_selected)

            except ImportError:
                # 备用方案：使用简单的输入对话框
                self._show_simple_date_picker(text_field, "选择用药开始日期")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示日期选择器失败: {e}")
            # 最后的备用方案：直接设置当前日期
            from datetime import datetime
            text_field.text = datetime.now().strftime("%Y-%m-%d")

    def _show_simple_date_picker(self, text_field, title="选择日期"):
        """显示简单的日期输入对话框"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            from kivymd.uix.boxlayout import MDBoxLayout
            from datetime import datetime

            # 创建输入框
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )

            self.date_input = MDTextField(
                mode="outlined",
                text=datetime.now().strftime("%Y-%m-%d"),
                size_hint_y=None,
                height=dp(56)
            )
            self.date_input.add_widget(MDTextFieldHintText(text="请输入日期 (YYYY-MM-DD)"))
            content.add_widget(self.date_input)

            # 创建对话框
            self.date_dialog = MDDialog(
                MDDialogHeadlineText(text=title),
                MDDialogSupportingText(text="请输入日期，格式：YYYY-MM-DD"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda *x: self.date_dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="确定"),
                        style="filled",
                        on_release=lambda *x: self._confirm_simple_date(text_field)
                    )
                ),
                size_hint=(0.8, None),
                auto_dismiss=False
            )

            self.date_dialog.open()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示简单日期选择器失败: {e}")
            # 最终备用方案
            from datetime import datetime
            text_field.text = datetime.now().strftime("%Y-%m-%d")

    def _confirm_simple_date(self, text_field):
        """确认简单日期输入"""
        try:
            date_str = self.date_input.text.strip()
            # 验证日期格式
            from datetime import datetime
            datetime.strptime(date_str, "%Y-%m-%d")
            text_field.text = date_str

        except ValueError:
            KivyLogger.error(f"[MedicationManagement] 无效的日期格式: {date_str}")
            self.show_error("请输入正确的日期格式 (YYYY-MM-DD)")
            return
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认日期失败: {e}")
        finally:
            if hasattr(self, 'date_dialog') and self.date_dialog:
                self.date_dialog.dismiss()

    def add_medication_to_list(self):
        """添加药物到列表"""
        try:
            # 获取表单数据
            if not hasattr(self, 'ids'):
                self.show_error("界面未初始化")
                return

            # 获取各个字段的值
            name = getattr(self.ids.get('medication_name_field'), 'text', '').strip()
            dosage = getattr(self.ids.get('dosage_field'), 'text', '').strip()
            frequency = getattr(self.ids.get('frequency_field'), 'text', '').strip()
            start_date = getattr(self.ids.get('start_date_field'), 'text', '').strip()
            reason = getattr(self.ids.get('reason_field'), 'text', '').strip()
            notes = getattr(self.ids.get('notes_field'), 'text', '').strip()

            # 验证必填字段
            if not name:
                self.show_error("请输入药物名称")
                return
            if not dosage:
                self.show_error("请输入用药剂量")
                return
            if not frequency:
                self.show_error("请选择使用频次")
                return
            if not start_date:
                self.show_error("请选择开始日期")
                return

            # 检查是否存在重复药物
            for existing_med in self.current_medications:
                if (existing_med.get('name', '').lower() == name.lower() and
                    existing_med.get('dosage', '') == dosage):
                    self.show_error(f"药物 '{name} {dosage}' 已存在，请勿重复添加")
                    return

            # 获取用户ID
            user_id = self.get_current_user_id()
            if not user_id:
                self.show_error("用户未登录，无法保存药物信息")
                return

            # 创建药物数据
            medication_data = {
                'name': name,
                'dosage': dosage,
                'frequency': frequency,
                'start_date': start_date,
                'reason': reason,
                'notes': notes,
                'status': 'active',  # 数据库中使用 'active' 状态
                'user_id': user_id
            }

            # 保存到数据库
            try:
                saved_medication = self.db_manager.save_medication(user_id, medication_data)
                if saved_medication:
                    # 使用数据库返回的数据（包含ID等信息）
                    medication_data = saved_medication
                    KivyLogger.info(f"[MedicationManagement] 药物已保存到数据库: {medication_data.get('id')}")
                else:
                    self.show_error("保存药物到数据库失败")
                    return
            except Exception as db_error:
                KivyLogger.error(f"[MedicationManagement] 数据库保存失败: {db_error}")
                self.show_error("保存药物失败，请重试")
                return

            # 添加到当前用药列表
            self.current_medications.append(medication_data)

            # 刷新界面显示
            self.refresh_current_medications()

            # 清空表单
            self.clear_add_form()

            # 切换到当前用药标签页
            self.switch_tab('current')

            self.show_success("药物添加成功")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加药物失败: {e}")
            self.show_error("添加药物失败")

    def clear_add_form(self):
        """清空添加表单"""
        try:
            if hasattr(self, 'ids'):
                fields = ['medication_name_field', 'dosage_field', 'frequency_field',
                         'start_date_field', 'reason_field', 'notes_field']
                for field_id in fields:
                    field = self.ids.get(field_id)
                    if field and hasattr(field, 'text'):
                        field.text = ''
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 清空表单失败: {e}")

    def clear_history_search(self):
        """清空历史搜索"""
        try:
            if hasattr(self, 'ids') and 'history_search_field' in self.ids:
                self.ids.history_search_field.text = ''
                # 重新加载所有历史记录
                self.load_history_medications()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 清空历史搜索失败: {e}")

    def unified_stop_medication(self, medication_data=None, stop_reason="", stop_date="", is_single=False):
        """统一停药方法"""
        try:
            from datetime import datetime

            # 如果没有提供停药日期，使用当前日期
            if not stop_date:
                stop_date = datetime.now().strftime("%Y-%m-%d")

            # 如果没有提供停药原因，使用默认原因
            if not stop_reason:
                stop_reason = "用户手动停药"

            if is_single and medication_data:
                # 单个药物停药
                medications_to_stop = [medication_data]
            else:
                # 批量停药（从选中的药物列表）
                medications_to_stop = getattr(self, 'selected_medications', [])
                if hasattr(self, 'selected_medications'):
                    medications_to_stop = [card.medication_data for card in self.selected_medications if hasattr(card, 'medication_data')]

            if not medications_to_stop:
                self.show_error("没有要停用的药物")
                return

            stopped_count = 0
            user_id = self.get_current_user_id()

            for medication in medications_to_stop:
                try:
                    # 更新药物状态
                    medication['status'] = 'stopped'  # 数据库中使用 'stopped' 状态
                    medication['stop_date'] = stop_date
                    medication['stop_reason'] = stop_reason
                    medication['end_date'] = stop_date  # 设置结束日期

                    # 保存到数据库
                    if user_id and medication.get('id'):
                        try:
                            success = self.db_manager.update_medication_status(
                                medication['id'],
                                'stopped',
                                stop_date,
                                stop_reason
                            )
                            if success:
                                KivyLogger.info(f"[MedicationManagement] 药物状态已更新到数据库: {medication['name']}")
                            else:
                                KivyLogger.warning(f"[MedicationManagement] 数据库更新失败: {medication['name']}")
                        except Exception as db_error:
                            KivyLogger.error(f"[MedicationManagement] 数据库更新异常: {db_error}")

                    # 如果停药原因是过敏，更新过敏记录
                    if stop_reason and '过敏' in stop_reason:
                        try:
                            self.update_allergy_record(user_id, medication, stop_date)
                        except Exception as allergy_error:
                            KivyLogger.error(f"[MedicationManagement] 更新过敏记录失败: {allergy_error}")

                    # 从当前用药列表中移除
                    if medication in self.medications:
                        self.medications.remove(medication)
                    if medication in self.current_medications:
                        self.current_medications.remove(medication)

                    # 添加到历史用药列表
                    if medication not in self.history_medications:
                        self.history_medications.append(medication)

                    stopped_count += 1

                except Exception as e:
                    KivyLogger.error(f"[MedicationManagement] 停用单个药物失败: {e}")

            # 刷新界面显示（不重新从数据库加载）
            self.refresh_current_medications()
            self.refresh_history_medications()

            # 清空选择
            if hasattr(self, 'selected_medications'):
                self.selected_medications.clear()

            # 显示成功消息
            if stopped_count > 0:
                self.show_success(f"成功停用 {stopped_count} 个药物")
            else:
                self.show_error("没有药物被停用")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 统一停药方法失败: {e}")
            self.show_error("停药操作失败")

    def confirm_single_delete(self, medication_data):
        """确认删除单个药物"""
        try:
            # 从当前用药列表中删除
            if medication_data in self.medications:
                self.medications.remove(medication_data)

            # 刷新界面
            self.load_current_medications()

            self.show_success("药物删除成功")

            if self.dialog:
                self.dialog.dismiss()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认删除单个药物失败: {e}")
            self.show_error("删除药物失败")

    def update_selection(self, medication_card, is_selected):
        """更新药物选择状态"""
        try:
            if is_selected:
                if medication_card not in self.selected_medications:
                    self.selected_medications.append(medication_card)
            else:
                if medication_card in self.selected_medications:
                    self.selected_medications.remove(medication_card)

            KivyLogger.info(f"MedicationManagement: 已选择 {len(self.selected_medications)} 个药物")
        except Exception as e:
            KivyLogger.error(f"MedicationManagement: 更新选择状态失败: {e}")

    # 重复的show_review_date_picker方法已删除，使用_show_review_date_picker

    def update_allergy_record(self, user_id: str, medication: dict, allergy_date: str):
        """更新过敏记录到基本健康信息"""
        try:
            from datetime import datetime

            # 构建过敏记录数据
            allergy_data = {
                'user_id': user_id,
                'allergen_type': 'medication',  # 过敏原类型：药物
                'allergen_name': medication.get('name', '未知药物'),
                'allergen_details': f"药物: {medication.get('name', '未知药物')}, 剂量: {medication.get('dosage', '')}, 用法: {medication.get('frequency', '')}",
                'reaction_type': 'drug_allergy',  # 反应类型：药物过敏
                'reaction_severity': 'unknown',  # 严重程度：未知（可以后续完善）
                'reaction_symptoms': '药物过敏反应',  # 过敏症状
                'occurrence_date': allergy_date,
                'notes': f"因药物过敏停用 {medication.get('name', '未知药物')}",
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            # 检查是否已存在相同的过敏记录
            existing_allergies = self.get_existing_allergies(user_id, medication.get('name', ''))
            if existing_allergies:
                KivyLogger.info(f"[MedicationManagement] 过敏记录已存在: {medication.get('name', '')}")
                return

            # 保存过敏记录到数据库
            success = self.save_allergy_record(allergy_data)
            if success:
                KivyLogger.info(f"[MedicationManagement] 过敏记录已保存: {medication.get('name', '')}")
                self.show_success(f"已将 {medication.get('name', '')} 添加到过敏记录")
            else:
                KivyLogger.error(f"[MedicationManagement] 过敏记录保存失败: {medication.get('name', '')}")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 更新过敏记录异常: {e}")

    def get_existing_allergies(self, user_id: str, medication_name: str) -> list:
        """检查是否已存在相同的过敏记录"""
        try:
            # 这里应该查询过敏记录数据库表
            # 暂时返回空列表，表示没有重复记录
            # TODO: 实现实际的数据库查询
            return []
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 查询过敏记录失败: {e}")
            return []

    def save_allergy_record(self, allergy_data: dict) -> bool:
        """保存过敏记录到数据库"""
        try:
            # 这里应该调用过敏记录数据库管理器
            # 暂时返回True表示保存成功
            # TODO: 实现实际的数据库保存逻辑

            # 模拟数据库保存
            KivyLogger.info(f"[MedicationManagement] 模拟保存过敏记录: {allergy_data}")

            # 实际实现时应该类似这样：
            # from database.allergy_manager import AllergyDatabaseManager
            # allergy_db = AllergyDatabaseManager()
            # return allergy_db.save_allergy_record(allergy_data)

            return True
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 保存过敏记录异常: {e}")
            return False

    def refresh_data(self):
        """刷新数据 - 供UI刷新按钮调用"""
        try:
            KivyLogger.info("[MedicationManagement] 开始刷新数据")

            # 清空选择状态
            if hasattr(self, 'selected_medications'):
                self.selected_medications.clear()

            # 重新加载药物数据
            self.load_medications()

            # 刷新界面显示
            self.refresh_current_medications()
            self.refresh_history_display()

            self.show_success("数据刷新完成")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 刷新数据失败: {e}")
            self.show_error("刷新数据失败")



    def _add_stop_fields(self, content):
        """添加停药相关字段"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            # 根据截图设计的停药对话框布局
            # 主容器 - 圆角背景
            main_container = MDCard(
                md_bg_color=AppTheme.PRIMARY_LIGHT,  # 浅蓝色背景
                radius=[dp(20)],
                elevation=3,
                size_hint_y=None,
                padding=[dp(16), dp(16), dp(16), dp(16)]  # 调整内边距
            )

            main_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),  # 缩减间距
                size_hint_y=None,
                adaptive_height=True
            )

            # 顶部提示信息 - 白色圆角卡片
            info_card = MDCard(
                md_bg_color=AppTheme.CARD_BACKGROUND,
                radius=[dp(12)],
                elevation=1,
                size_hint_y=None,
                height=dp(48),
                padding=[dp(16), dp(12), dp(16), dp(12)]
            )

            # 获取所有选中药物的名称
            selected_medications = [card for card in self.ids.current_medications_list.children if hasattr(card, 'is_selected') and card.is_selected]
            if len(selected_medications) > 1:
                # 多个药物时显示所有药物名称，使用红色字体
                medication_names = [card.name for card in selected_medications]
                medication_text = "、".join(medication_names)
                info_label = MDLabel(
                    text=medication_text,
                    font_style="Body",
                    role="medium",
                    theme_text_color="Custom",
                    text_color=AppTheme.ERROR_COLOR,  # 红色字体
                    halign="center",
                    valign="center"
                )
            else:
                # 单个药物时
                medication_name = selected_medications[0].name if selected_medications else "未知药物"
                info_label = MDLabel(
                    text=medication_name,
                    font_style="Body",
                    role="medium",
                    theme_text_color="Custom",
                    text_color=AppTheme.ERROR_COLOR,  # 红色字体
                    halign="center",
                    valign="center"
                )
            info_card.add_widget(info_label)
            main_layout.add_widget(info_card)

            # 停用日期按钮 - 使用当前日期，可以修改
            self.unified_stop_date_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_y=None,
                height=dp(52),  # 增加按钮高度
                radius=[dp(12)],
                on_release=self._on_unified_stop_date_click
            )

            current_date = datetime.now().strftime("%Y-%m-%d")
            self.unified_stop_date_button_text = MDButtonText(
                text=f"停用日期: {current_date}",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT
            )
            self.unified_stop_date_button.add_widget(self.unified_stop_date_button_text)
            main_layout.add_widget(self.unified_stop_date_button)

            # 停药原因选择按钮 - 绿色
            self.unified_stop_reason_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_y=None,
                height=dp(52),  # 增加按钮高度
                radius=[dp(12)],
                on_release=self._show_unified_stop_reason_menu
            )

            self.unified_stop_reason_button_text = MDButtonText(
                text="请选择停药原因",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_LIGHT
            )
            self.unified_stop_reason_button.add_widget(self.unified_stop_reason_button_text)
            main_layout.add_widget(self.unified_stop_reason_button)

            # 按钮区域 - 居中设置
            button_container = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(52),
                spacing=dp(0)
            )
            
            # 左侧空白
            button_container.add_widget(MDBoxLayout(size_hint_x=0.1))
            
            button_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_x=0.8,  # 按钮区域占80%宽度
                size_hint_y=None,
                height=dp(52),
                spacing=dp(16)
            )

            # 取消按钮
            cancel_button = MDButton(
                style="outlined",
                md_bg_color=AppTheme.CARD_BACKGROUND,
                size_hint_x=0.4,
                radius=[dp(12)],
                on_release=lambda *x: self.unified_dialog.dismiss() if hasattr(self, 'unified_dialog') else None
            )

            cancel_text = MDButtonText(
                text="取消",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY
            )
            cancel_button.add_widget(cancel_text)
            button_layout.add_widget(cancel_button)

            # 确认按钮 - 绿色渐变效果
            confirm_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_x=0.6,
                radius=[dp(12)],
                on_release=lambda *x: self._confirm_unified_action('stop')
            )

            confirm_text = MDButtonText(
                text="确认",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.ERROR_COLOR  # 红色文字
            )
            confirm_button.add_widget(confirm_text)
            button_layout.add_widget(confirm_button)

            button_container.add_widget(button_layout)
            
            # 右侧空白
            button_container.add_widget(MDBoxLayout(size_hint_x=0.1))

            main_layout.add_widget(button_container)

            main_container.add_widget(main_layout)
            # 绑定高度自适应
            main_layout.bind(minimum_height=main_container.setter('height'))
            content.add_widget(main_container)

            # 初始化停药原因选择
            self.unified_selected_stop_reason = ""

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加停药字段失败: {e}")

    def _add_reminder_fields(self, content):
        """添加提醒设置相关字段"""
        try:
            # 主容器 - 减少间距和padding
            main_container = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(400),  # 进一步减少高度
                spacing=dp(8),   # 减少间距
                padding=[dp(12), dp(8), dp(12), dp(8)]  # 减少padding
            )

            main_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(6),   # 减少间距
                size_hint_y=None,
                height=dp(350)   # 减少高度
            )

            # 服药提醒卡片 - 减少高度和padding
            med_reminder_card = MDCard(
                md_bg_color=AppTheme.CARD_BACKGROUND,
                radius=[dp(12)],
                elevation=2,
                size_hint_y=None,
                height=dp(120),  # 减少高度
                padding=[dp(12), dp(8), dp(12), dp(8)]  # 减少padding
            )

            med_reminder_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(6)  # 减少间距
            )

            # 服药提醒标题和复选框
            med_title_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(32),
                spacing=dp(8)
            )

            # 复选框
            self.unified_med_reminder_checkbox = MDCheckbox(
                size_hint_x=None,
                width=dp(32),
                active=True,  # 默认选中
                theme_icon_color="Custom",
                icon_color=AppTheme.PRIMARY_COLOR
            )
            med_title_layout.add_widget(self.unified_med_reminder_checkbox)

            med_title_layout.add_widget(MDLabel(
                text="服药提醒",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_PRIMARY,
                halign="left",
                valign="center"
            ))
            med_reminder_layout.add_widget(med_title_layout)

            # 多个服药时间段设置 - 减少高度和间距
            times_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(4),   # 减少间距
                size_hint_y=None,
                height=dp(70)    # 减少高度
            )

            # 第一个时间段 - 减少高度和间距
            time1_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(32),   # 减少高度
                spacing=dp(6)    # 减少间距
            )

            time1_layout.add_widget(MDLabel(
                text="时间1:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(45),  # 减少宽度
                halign="left",
                valign="center"
            ))

            self.unified_med_time1_field = MDTextField(
                mode="outlined",
                text="08:00",
                size_hint_x=None,
                width=dp(70),  # 减少宽度
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            self.unified_med_time1_field.add_widget(MDTextFieldHintText(text="时:分"))
            time1_layout.add_widget(self.unified_med_time1_field)

            time1_layout.add_widget(MDLabel(
                text="提前:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(35),  # 减少宽度
                halign="center",
                valign="center"
            ))

            self.unified_med_advance1_field = MDTextField(
                mode="outlined",
                text="15",
                size_hint_x=None,
                width=dp(60),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            self.unified_med_advance1_field.add_widget(MDTextFieldHintText(text="分钟"))
            time1_layout.add_widget(self.unified_med_advance1_field)

            times_layout.add_widget(time1_layout)

            # 第二个时间段
            time2_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(36),
                spacing=dp(6)
            )

            time2_layout.add_widget(MDLabel(
                text="时间2:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(40),
                halign="left",
                valign="center"
            ))

            self.unified_med_time2_field = MDTextField(
                mode="outlined",
                text="20:00",
                size_hint_x=None,
                width=dp(70),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            self.unified_med_time2_field.add_widget(MDTextFieldHintText(text="时:分"))
            time2_layout.add_widget(self.unified_med_time2_field)

            time2_layout.add_widget(MDLabel(
                text="提前:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(35),
                halign="center",
                valign="center"
            ))

            self.unified_med_advance2_field = MDTextField(
                mode="outlined",
                text="15",
                size_hint_x=None,
                width=dp(50),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            self.unified_med_advance2_field.add_widget(MDTextFieldHintText(text="分钟"))
            time2_layout.add_widget(self.unified_med_advance2_field)

            times_layout.add_widget(time2_layout)

            # 添加/删除时间段按钮
            time_control_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(8)
            )

            # 添加时间段按钮
            add_time_button = MDButton(
                style="outlined",
                md_bg_color=AppTheme.CARD_BACKGROUND,
                size_hint_x=0.4,
                radius=[dp(8)],
                on_release=lambda *x: self._add_time_slot(times_layout)
            )
            add_time_text = MDButtonText(
                text="+ 添加时间段",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.PRIMARY_COLOR
            )
            add_time_button.add_widget(add_time_text)
            time_control_layout.add_widget(add_time_button)

            # 删除时间段按钮
            remove_time_button = MDButton(
                style="outlined",
                md_bg_color=AppTheme.CARD_BACKGROUND,
                size_hint_x=0.4,
                radius=[dp(8)],
                on_release=lambda *x: self._remove_time_slot(times_layout)
            )
            remove_time_text = MDButtonText(
                text="- 删除时间段",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.ERROR_COLOR
            )
            remove_time_button.add_widget(remove_time_text)
            time_control_layout.add_widget(remove_time_button)

            # 空白填充
            time_control_layout.add_widget(MDBoxLayout(size_hint_x=0.2))

            med_reminder_layout.add_widget(time_control_layout)
            med_reminder_layout.add_widget(times_layout)

            # 存储times_layout引用以便动态添加/删除
            self.times_layout = times_layout
            self.time_slot_count = 2  # 当前时间段数量

            med_reminder_card.add_widget(med_reminder_layout)
            main_layout.add_widget(med_reminder_card)

            # 复查提醒卡片
            review_reminder_card = MDCard(
                md_bg_color=AppTheme.CARD_BACKGROUND,
                radius=[dp(12)],
                elevation=2,
                size_hint_y=None,
                height=dp(100),
                padding=[dp(16), dp(12), dp(16), dp(12)]
            )

            review_reminder_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8)
            )

            # 复查提醒标题和复选框
            review_title_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(32),
                spacing=dp(8)
            )

            # 复选框
            self.unified_review_reminder_checkbox = MDCheckbox(
                size_hint_x=None,
                width=dp(32),
                active=True,  # 默认选中
                theme_icon_color="Custom",
                icon_color=AppTheme.PRIMARY_COLOR
            )
            review_title_layout.add_widget(self.unified_review_reminder_checkbox)

            review_title_layout.add_widget(MDLabel(
                text="复查提醒",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_PRIMARY,
                halign="left",
                valign="center"
            ))
            review_reminder_layout.add_widget(review_title_layout)

            # 复查日期设置
            review_date_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(6)
            )

            # 复查日期标签
            review_date_layout.add_widget(MDLabel(
                text="复查日期:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(60),
                halign="left",
                valign="center"
            ))

            # 日期输入框
            self.unified_review_date_field = MDTextField(
                mode="outlined",
                text="2024-01-01",
                size_hint_x=None,
                width=dp(100),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            self.unified_review_date_field.add_widget(MDTextFieldHintText(text="日期"))
            review_date_layout.add_widget(self.unified_review_date_field)

            # 提前天数标签
            review_date_layout.add_widget(MDLabel(
                text="提前",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(35),
                halign="center",
                valign="center"
            ))

            # 提前天数输入框
            self.unified_review_advance_field = MDTextField(
                mode="outlined",
                text="3",
                size_hint_x=None,
                width=dp(40),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            self.unified_review_advance_field.add_widget(MDTextFieldHintText(text="天"))
            review_date_layout.add_widget(self.unified_review_advance_field)

            # 天提醒标签
            review_date_layout.add_widget(MDLabel(
                text="天提醒",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(45),
                halign="left",
                valign="center"
            ))

            review_reminder_layout.add_widget(review_date_layout)
            review_reminder_card.add_widget(review_reminder_layout)
            main_layout.add_widget(review_reminder_card)

            # 按钮区域
            button_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(52),  # 增加按钮区域高度
                spacing=dp(16)  # 增加按钮间距
            )

            # 左侧空白布局
            button_layout.add_widget(MDBoxLayout(size_hint_x=0.2))

            # 取消按钮
            cancel_button = MDButton(
                style="outlined",
                md_bg_color=AppTheme.CARD_BACKGROUND,
                size_hint_x=0.3,
                radius=[dp(12)],
                on_release=lambda *x: self.unified_dialog.dismiss() if hasattr(self, 'unified_dialog') else None
            )

            cancel_text = MDButtonText(
                text="取消",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY
            )
            cancel_button.add_widget(cancel_text)
            button_layout.add_widget(cancel_button)

            # 保存按钮 - 绿色
            save_button = MDButton(
                style="filled",
                md_bg_color=AppTheme.HEALTH_GREEN,
                size_hint_x=0.3,
                radius=[dp(12)],
                on_release=lambda *x: self._confirm_unified_action('reminder')
            )

            save_text = MDButtonText(
                text="保存",
                font_style="Body",
                role="medium",
                theme_text_color="Custom",
                text_color=AppTheme.ERROR_COLOR  # 红色文字
            )
            save_button.add_widget(save_text)
            button_layout.add_widget(save_button)

            # 右侧空白布局
            button_layout.add_widget(MDBoxLayout(size_hint_x=0.2))

            main_layout.add_widget(button_layout)

            main_container.add_widget(main_layout)
            content.add_widget(main_container)

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加提醒字段失败: {e}")

    def _add_time_slot(self, times_layout):
        """添加新的时间段"""
        try:
            if self.time_slot_count >= 6:  # 最多6个时间段
                self.show_info("最多只能设置6个时间段")
                return

            self.time_slot_count += 1
            time_num = self.time_slot_count

            # 创建新的时间段布局
            new_time_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(36),
                spacing=dp(6)
            )

            # 时间标签
            new_time_layout.add_widget(MDLabel(
                text=f"时间{time_num}:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(40),
                halign="left",
                valign="center"
            ))

            # 时间输入框
            time_field = MDTextField(
                mode="outlined",
                text="08:00",
                size_hint_x=None,
                width=dp(70),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            time_field.add_widget(MDTextFieldHintText(text="时:分"))
            new_time_layout.add_widget(time_field)

            # 提前标签
            new_time_layout.add_widget(MDLabel(
                text="提前:",
                font_style="Body",
                role="small",
                theme_text_color="Custom",
                text_color=AppTheme.TEXT_SECONDARY,
                size_hint_x=None,
                width=dp(35),
                halign="center",
                valign="center"
            ))

            # 提前时间输入框
            advance_field = MDTextField(
                mode="outlined",
                text="15",
                size_hint_x=None,
                width=dp(50),
                md_bg_color=AppTheme.CARD_BACKGROUND
            )
            advance_field.add_widget(MDTextFieldHintText(text="分钟"))
            new_time_layout.add_widget(advance_field)

            # 将新时间段添加到布局中
            times_layout.add_widget(new_time_layout)

            # 存储字段引用
            setattr(self, f'unified_med_time{time_num}_field', time_field)
            setattr(self, f'unified_med_advance{time_num}_field', advance_field)

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 添加时间段失败: {e}")
            self.show_error("添加时间段失败")

    def _remove_time_slot(self, times_layout):
        """删除最后一个时间段"""
        try:
            if self.time_slot_count <= 1:  # 至少保留一个时间段
                self.show_info("至少需要保留一个时间段")
                return

            # 删除最后一个时间段的布局
            if times_layout.children:
                times_layout.remove_widget(times_layout.children[0])

            # 删除字段引用
            time_num = self.time_slot_count
            if hasattr(self, f'unified_med_time{time_num}_field'):
                delattr(self, f'unified_med_time{time_num}_field')
            if hasattr(self, f'unified_med_advance{time_num}_field'):
                delattr(self, f'unified_med_advance{time_num}_field')

            self.time_slot_count -= 1

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 删除时间段失败: {e}")
            self.show_error("删除时间段失败")

    def _confirm_unified_action(self, action_type):
        """确认统一操作"""
        try:
            if action_type == 'stop':
                self._confirm_unified_stop()
            else:
                self._confirm_unified_reminder()
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认统一操作失败: {e}")

    def _confirm_unified_stop(self):
        """确认统一停药操作"""
        try:
            stop_reason = getattr(self, 'unified_selected_stop_reason', '')
            stop_date = getattr(self, 'unified_selected_stop_date', datetime.now().strftime("%Y-%m-%d"))

            if not stop_reason:
                self.show_error("请选择停药原因")
                return

            # 调用统一停药方法
            self.unified_stop_medication(
                medication_data=None,
                stop_reason=stop_reason,
                stop_date=stop_date,
                is_single=False
            )

            # 关闭对话框
            if hasattr(self, 'unified_dialog') and self.unified_dialog:
                self.unified_dialog.dismiss()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认统一停药失败: {e}")

    def _confirm_unified_reminder(self):
        """确认统一提醒设置"""
        try:
            # 获取服药提醒设置
            med_reminder_enabled = getattr(self, 'unified_med_reminder_checkbox', None)
            
            # 获取复查提醒设置
            review_reminder_enabled = getattr(self, 'unified_review_reminder_checkbox', None)
            review_date = getattr(self, 'unified_review_date_field', None)
            review_advance = getattr(self, 'unified_review_advance_field', None)

            reminder_settings = {}
            settings_saved = False

            # 处理服药提醒 - 支持多个时间段
            if med_reminder_enabled and med_reminder_enabled.active:
                med_times = []
                
                # 收集所有时间段的设置
                for i in range(1, self.time_slot_count + 1):
                    time_field = getattr(self, f'unified_med_time{i}_field', None)
                    advance_field = getattr(self, f'unified_med_advance{i}_field', None)
                    
                    if time_field and time_field.text.strip():
                        time_setting = {
                            'time': time_field.text.strip(),
                            'advance_minutes': advance_field.text.strip() if advance_field and advance_field.text.strip() else '15'
                        }
                        med_times.append(time_setting)
                
                if med_times:
                    reminder_settings['med_reminder'] = {
                        'enabled': True,
                        'times': med_times  # 支持多个时间段
                    }
                    settings_saved = True

            # 处理复查提醒
            if (review_reminder_enabled and review_reminder_enabled.active and
                review_date and review_date.text.strip()):
                reminder_settings['review_reminder'] = {
                    'enabled': True,
                    'date': review_date.text.strip(),
                    'advance_days': review_advance.text.strip() if review_advance and review_advance.text.strip() else '3'
                }
                settings_saved = True

            if settings_saved:
                # 为所有选中的药物设置提醒
                for card in self.selected_medications:
                    if hasattr(card, 'medication_data'):
                        medication = card.medication_data
                        medication['reminder_settings'] = reminder_settings

                        # 更新当前用药列表中的对应药物
                        for i, med in enumerate(self.current_medications):
                            if med.get('id') == medication.get('id') or med.get('name') == medication.get('name'):
                                self.current_medications[i] = medication
                                break

                # 刷新界面显示
                self.refresh_current_medications()

                # 显示成功消息
                reminder_types = []
                if reminder_settings.get('med_reminder', {}).get('enabled'):
                    time_count = len(reminder_settings['med_reminder']['times'])
                    reminder_types.append(f"服药提醒({time_count}个时间段)")
                if reminder_settings.get('review_reminder', {}).get('enabled'):
                    reminder_types.append("复查提醒")

                reminder_text = "、".join(reminder_types)
                self.show_success(f"已为 {len(self.selected_medications)} 个药物设置{reminder_text}")
            else:
                self.show_error("请至少启用一种提醒并填写相关信息")

            # 关闭对话框
            if hasattr(self, 'unified_dialog') and self.unified_dialog:
                self.unified_dialog.dismiss()

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 确认统一提醒设置失败: {e}")

    def _on_unified_stop_date_click(self, *args):
        """统一停药日期按钮点击时显示当前日期"""
        try:
            # 显示当前日期作为默认停药日期
            current_date = datetime.now().strftime("%Y-%m-%d")
            if hasattr(self, 'unified_stop_date_button_text'):
                self.unified_stop_date_button_text.text = f"停药日期: {current_date}"
                # 保存选中的停药日期
                self.unified_selected_stop_date = current_date
                KivyLogger.info(f"[MedicationManagement] 统一停药日期已设置: {current_date}")
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 设置统一停药日期失败: {e}")

    def _show_unified_stop_reason_menu(self, *args):
        """显示统一停药原因下拉菜单"""
        try:
            from kivymd.uix.menu import MDDropdownMenu

            stop_reason_options = [
                "治疗完成",
                "药物不良反应",
                "疗效不佳",
                "医生建议停药",
                "患者自主停药",
                "药物过敏",
                "其他原因"
            ]

            menu_items = []
            for reason in stop_reason_options:
                menu_items.append({
                    "text": reason,
                    "on_release": lambda x=reason: self._select_unified_stop_reason(x)
                })

            # 确保菜单有正确的调用者
            if hasattr(self, 'unified_stop_reason_button'):
                self.unified_stop_reason_menu = MDDropdownMenu(
                    caller=self.unified_stop_reason_button,
                    items=menu_items,
                    width=dp(240),  # 使用width替代width_mult
                    max_height=dp(200),
                    position="bottom"
                )
                self.unified_stop_reason_menu.open()
            else:
                KivyLogger.error("[MedicationManagement] 停药原因按钮不存在")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示统一停药原因菜单失败: {e}")

    def _select_unified_stop_reason(self, reason):
        """选择统一停药原因"""
        try:
            self.unified_selected_stop_reason = reason
            if hasattr(self, 'unified_stop_reason_button_text'):
                self.unified_stop_reason_button_text.text = reason
                KivyLogger.info(f"[MedicationManagement] 停药原因按钮文本已更新: {reason}")
            if hasattr(self, 'unified_stop_reason_menu'):
                self.unified_stop_reason_menu.dismiss()
            KivyLogger.info(f"[MedicationManagement] 统一停药原因已选择: {reason}")
        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 选择统一停药原因失败: {e}")

    def _show_unified_time_picker(self, *args):
        """显示统一时间选择器"""
        try:
            try:
                from utils.date_picker_utils import show_time_picker

                def on_time_selected(selected_time):
                    try:
                        if hasattr(self, 'unified_med_time_field'):
                            self.unified_med_time_field.text = selected_time
                        KivyLogger.info(f"[MedicationManagement] 统一服药时间已设置: {selected_time}")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置统一服药时间失败: {e}")

                show_time_picker(on_time_selected)

            except ImportError:
                # 备用方案：使用简单的时间选择器
                self._show_simple_time_picker(self.unified_med_time_field, "选择服药时间")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示统一时间选择器失败: {e}")

    def _show_unified_review_date_picker(self, *args):
        """显示统一复查日期选择器"""
        try:
            try:
                from utils.date_picker_utils import show_review_date_picker

                def on_date_selected(selected_date):
                    try:
                        if hasattr(self, 'unified_review_date_field'):
                            self.unified_review_date_field.text = selected_date.strftime("%Y-%m-%d")
                        KivyLogger.info(f"[MedicationManagement] 统一复查日期已设置: {selected_date}")
                    except Exception as e:
                        KivyLogger.error(f"[MedicationManagement] 设置统一复查日期失败: {e}")

                show_review_date_picker(on_date_selected)

            except ImportError:
                # 备用方案：使用简单的日期选择器
                self._show_simple_date_picker(self.unified_review_date_field, "选择复查日期")

        except Exception as e:
            KivyLogger.error(f"[MedicationManagement] 显示统一复查日期选择器失败: {e}")

# 注册Factory
Factory.register('MedicationManagementScreen', cls=MedicationManagementScreen)
Factory.register('MedicationCard', cls=MedicationCard)
Factory.register('CurrentMedicationCard', cls=CurrentMedicationCard)
Factory.register('HistoryMedicationCard', cls=HistoryMedicationCard)
Builder.load_string(KV)
