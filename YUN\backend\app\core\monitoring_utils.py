#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控工具模块
提供系统性能监控、健康检查、指标收集、告警等功能
"""

import os
import sys
import time
import psutil
import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from dataclasses import dataclass, asdict, field
from enum import Enum
import json
import threading
from collections import defaultdict, deque
from contextlib import contextmanager
import traceback
from pathlib import Path
import socket
import requests
from urllib.parse import urlparse

from .env_config import env_config
from .logging_utils import get_logger

class MetricType(str, Enum):
    """指标类型枚举"""
    COUNTER = "counter"  # 计数器
    GAUGE = "gauge"      # 仪表
    HISTOGRAM = "histogram"  # 直方图
    SUMMARY = "summary"  # 摘要
    TIMER = "timer"      # 计时器

class HealthStatus(str, Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class AlertLevel(str, Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class ComponentType(str, Enum):
    """组件类型枚举"""
    DATABASE = "database"
    CACHE = "cache"
    EXTERNAL_API = "external_api"
    FILE_SYSTEM = "file_system"
    NETWORK = "network"
    SERVICE = "service"
    CUSTOM = "custom"

@dataclass
class Metric:
    """指标数据"""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    description: Optional[str] = None
    unit: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "value": self.value,
            "type": self.metric_type.value,
            "timestamp": self.timestamp.isoformat(),
            "labels": self.labels,
            "description": self.description,
            "unit": self.unit
        }

@dataclass
class HealthCheck:
    """健康检查结果"""
    component: str
    status: HealthStatus
    message: str
    timestamp: datetime
    component_type: ComponentType = ComponentType.CUSTOM
    response_time_ms: Optional[float] = None
    details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "component": self.component,
            "status": self.status.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "component_type": self.component_type.value,
            "response_time_ms": self.response_time_ms,
            "details": self.details or {}
        }

@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    component: Optional[str] = None
    metric_name: Optional[str] = None
    threshold: Optional[float] = None
    current_value: Optional[float] = None
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "level": self.level.value,
            "title": self.title,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "component": self.component,
            "metric_name": self.metric_name,
            "threshold": self.threshold,
            "current_value": self.current_value,
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None
        }

@dataclass
class SystemInfo:
    """系统信息"""
    hostname: str
    platform: str
    python_version: str
    cpu_count: int
    memory_total_gb: float
    disk_total_gb: float
    uptime_seconds: float
    load_average: List[float]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self._metrics: Dict[str, List[Metric]] = defaultdict(list)
        self._counters: Dict[str, float] = defaultdict(float)
        self._gauges: Dict[str, float] = defaultdict(float)
        self._timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.Lock()
        self.logger = get_logger()
    
    def increment_counter(
        self,
        name: str,
        value: float = 1.0,
        labels: Optional[Dict[str, str]] = None
    ):
        """增加计数器"""
        with self._lock:
            key = self._get_metric_key(name, labels)
            self._counters[key] += value
            
            metric = Metric(
                name=name,
                value=self._counters[key],
                metric_type=MetricType.COUNTER,
                timestamp=datetime.now(),
                labels=labels or {}
            )
            
            self._metrics[name].append(metric)
            self._cleanup_old_metrics(name)
    
    def set_gauge(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """设置仪表值"""
        with self._lock:
            key = self._get_metric_key(name, labels)
            self._gauges[key] = value
            
            metric = Metric(
                name=name,
                value=value,
                metric_type=MetricType.GAUGE,
                timestamp=datetime.now(),
                labels=labels or {}
            )
            
            self._metrics[name].append(metric)
            self._cleanup_old_metrics(name)
    
    def record_timer(
        self,
        name: str,
        duration_ms: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """记录计时器"""
        with self._lock:
            key = self._get_metric_key(name, labels)
            self._timers[key].append(duration_ms)
            
            metric = Metric(
                name=name,
                value=duration_ms,
                metric_type=MetricType.TIMER,
                timestamp=datetime.now(),
                labels=labels or {},
                unit="ms"
            )
            
            self._metrics[name].append(metric)
            self._cleanup_old_metrics(name)
    
    def record_histogram(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """记录直方图（兼容聚合API调用）"""
        # 这里简单实现为记录计时器，实际可根据需要扩展
        self.record_timer(name, value, labels)
    
    def get_metrics(self, name: Optional[str] = None) -> List[Metric]:
        """获取指标"""
        with self._lock:
            if name:
                return self._metrics.get(name, [])
            
            all_metrics = []
            for metrics_list in self._metrics.values():
                all_metrics.extend(metrics_list)
            
            return sorted(all_metrics, key=lambda m: m.timestamp, reverse=True)
    
    def get_counter_value(self, name: str, labels: Optional[Dict[str, str]] = None) -> float:
        """获取计数器值"""
        key = self._get_metric_key(name, labels)
        return self._counters.get(key, 0.0)
    
    def get_gauge_value(self, name: str, labels: Optional[Dict[str, str]] = None) -> Optional[float]:
        """获取仪表值"""
        key = self._get_metric_key(name, labels)
        return self._gauges.get(key)
    
    def get_timer_stats(
        self,
        name: str,
        labels: Optional[Dict[str, str]] = None
    ) -> Dict[str, float]:
        """获取计时器统计"""
        key = self._get_metric_key(name, labels)
        timings = list(self._timers.get(key, []))
        
        if not timings:
            return {}
        
        timings.sort()
        count = len(timings)
        
        return {
            "count": count,
            "min": min(timings),
            "max": max(timings),
            "mean": sum(timings) / count,
            "median": timings[count // 2],
            "p95": timings[int(count * 0.95)] if count > 0 else 0,
            "p99": timings[int(count * 0.99)] if count > 0 else 0
        }
    
    def _get_metric_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """获取指标键"""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}[{label_str}]"
    
    def _cleanup_old_metrics(self, name: str, max_age_hours: int = 24):
        """清理旧指标"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        self._metrics[name] = [
            metric for metric in self._metrics[name]
            if metric.timestamp > cutoff_time
        ]
    
    def export_metrics(self, format_type: str = "json") -> str:
        """导出指标"""
        metrics = self.get_metrics()
        
        if format_type == "json":
            return json.dumps([metric.to_dict() for metric in metrics], indent=2)
        elif format_type == "prometheus":
            return self._export_prometheus_format(metrics)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _export_prometheus_format(self, metrics: List[Metric]) -> str:
        """导出Prometheus格式"""
        lines = []
        
        for metric in metrics:
            labels_str = ""
            if metric.labels:
                label_pairs = [f'{k}="{v}"' for k, v in metric.labels.items()]
                labels_str = "{" + ",".join(label_pairs) + "}"
            
            lines.append(f"{metric.name}{labels_str} {metric.value}")
        
        return "\n".join(lines)

class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self._checks: Dict[str, Callable] = {}
        self._results: Dict[str, HealthCheck] = {}
        self.logger = get_logger()
    
    def register_check(
        self,
        name: str,
        check_func: Callable,
        component_type: ComponentType = ComponentType.CUSTOM
    ):
        """注册健康检查"""
        self._checks[name] = (check_func, component_type)
        self.logger.debug(f"Health check registered: {name}")
    
    async def run_check(self, name: str) -> HealthCheck:
        """运行单个健康检查"""
        if name not in self._checks:
            return HealthCheck(
                component=name,
                status=HealthStatus.UNKNOWN,
                message="Health check not found",
                timestamp=datetime.now()
            )
        
        check_func, component_type = self._checks[name]
        start_time = time.time()
        
        try:
            if asyncio.iscoroutinefunction(check_func):
                result = await check_func()
            else:
                result = check_func()
            
            response_time_ms = (time.time() - start_time) * 1000
            
            if isinstance(result, HealthCheck):
                result.response_time_ms = response_time_ms
                health_check = result
            elif isinstance(result, bool):
                health_check = HealthCheck(
                    component=name,
                    status=HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY,
                    message="OK" if result else "Check failed",
                    timestamp=datetime.now(),
                    component_type=component_type,
                    response_time_ms=response_time_ms
                )
            else:
                health_check = HealthCheck(
                    component=name,
                    status=HealthStatus.HEALTHY,
                    message=str(result),
                    timestamp=datetime.now(),
                    component_type=component_type,
                    response_time_ms=response_time_ms
                )
        
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            health_check = HealthCheck(
                component=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Check failed: {str(e)}",
                timestamp=datetime.now(),
                component_type=component_type,
                response_time_ms=response_time_ms,
                details={"error": str(e), "traceback": traceback.format_exc()}
            )
            
            self.logger.error(f"Health check failed for {name}: {e}")
        
        self._results[name] = health_check
        return health_check
    
    async def run_all_checks(self) -> Dict[str, HealthCheck]:
        """运行所有健康检查"""
        tasks = []
        for name in self._checks:
            tasks.append(self.run_check(name))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        check_results = {}
        for i, (name, result) in enumerate(zip(self._checks.keys(), results)):
            if isinstance(result, Exception):
                check_results[name] = HealthCheck(
                    component=name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Check execution failed: {str(result)}",
                    timestamp=datetime.now()
                )
            else:
                check_results[name] = result
        
        return check_results
    
    def get_overall_status(self) -> HealthStatus:
        """获取整体健康状态"""
        if not self._results:
            return HealthStatus.UNKNOWN
        
        statuses = [check.status for check in self._results.values()]
        
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY
        elif HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        overall_status = self.get_overall_status()
        
        return {
            "overall_status": overall_status.value,
            "timestamp": datetime.now().isoformat(),
            "checks": {name: check.to_dict() for name, check in self._results.items()},
            "summary": {
                "total_checks": len(self._results),
                "healthy": sum(1 for c in self._results.values() if c.status == HealthStatus.HEALTHY),
                "degraded": sum(1 for c in self._results.values() if c.status == HealthStatus.DEGRADED),
                "unhealthy": sum(1 for c in self._results.values() if c.status == HealthStatus.UNHEALTHY),
                "unknown": sum(1 for c in self._results.values() if c.status == HealthStatus.UNKNOWN)
            }
        }

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, metrics_collector: Optional[MetricsCollector] = None):
        self.metrics = metrics_collector or MetricsCollector()
        self.logger = get_logger()
        self._monitoring = False
        self._monitor_task = None
    
    def get_system_info(self) -> SystemInfo:
        """获取系统信息"""
        return SystemInfo(
            hostname=socket.gethostname(),
            platform=sys.platform,
            python_version=sys.version,
            cpu_count=psutil.cpu_count(),
            memory_total_gb=psutil.virtual_memory().total / (1024**3),
            disk_total_gb=psutil.disk_usage('/').total / (1024**3),
            uptime_seconds=time.time() - psutil.boot_time(),
            load_average=list(os.getloadavg()) if hasattr(os, 'getloadavg') else [0.0, 0.0, 0.0]
        )
    
    def collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics.set_gauge("system_cpu_usage_percent", cpu_percent)
            
            # 内存使用
            memory = psutil.virtual_memory()
            self.metrics.set_gauge("system_memory_usage_percent", memory.percent)
            self.metrics.set_gauge("system_memory_used_bytes", memory.used)
            self.metrics.set_gauge("system_memory_available_bytes", memory.available)
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            self.metrics.set_gauge("system_disk_usage_percent", (disk.used / disk.total) * 100)
            self.metrics.set_gauge("system_disk_used_bytes", disk.used)
            self.metrics.set_gauge("system_disk_free_bytes", disk.free)
            
            # 网络IO
            net_io = psutil.net_io_counters()
            self.metrics.set_gauge("system_network_bytes_sent", net_io.bytes_sent)
            self.metrics.set_gauge("system_network_bytes_recv", net_io.bytes_recv)
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            if disk_io:
                self.metrics.set_gauge("system_disk_read_bytes", disk_io.read_bytes)
                self.metrics.set_gauge("system_disk_write_bytes", disk_io.write_bytes)
            
            # 进程数
            process_count = len(psutil.pids())
            self.metrics.set_gauge("system_process_count", process_count)
            
            # 负载平均值
            if hasattr(os, 'getloadavg'):
                load_avg = os.getloadavg()
                self.metrics.set_gauge("system_load_1m", load_avg[0])
                self.metrics.set_gauge("system_load_5m", load_avg[1])
                self.metrics.set_gauge("system_load_15m", load_avg[2])
        
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
    
    def collect_process_metrics(self):
        """收集当前进程指标"""
        try:
            process = psutil.Process()
            
            # CPU使用率
            cpu_percent = process.cpu_percent()
            self.metrics.set_gauge("process_cpu_usage_percent", cpu_percent)
            
            # 内存使用
            memory_info = process.memory_info()
            self.metrics.set_gauge("process_memory_rss_bytes", memory_info.rss)
            self.metrics.set_gauge("process_memory_vms_bytes", memory_info.vms)
            
            # 文件描述符
            try:
                fd_count = process.num_fds()
                self.metrics.set_gauge("process_open_fds", fd_count)
            except (AttributeError, psutil.AccessDenied):
                pass
            
            # 线程数
            thread_count = process.num_threads()
            self.metrics.set_gauge("process_threads", thread_count)
            
            # 运行时间
            create_time = process.create_time()
            uptime_seconds = time.time() - create_time
            self.metrics.set_gauge("process_uptime_seconds", uptime_seconds)
        
        except Exception as e:
            self.logger.error(f"Failed to collect process metrics: {e}")
    
    async def start_monitoring(self, interval_seconds: int = 60):
        """开始监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self.logger.info(f"Starting system monitoring with {interval_seconds}s interval")
        
        async def monitor_loop():
            while self._monitoring:
                try:
                    self.collect_system_metrics()
                    self.collect_process_metrics()
                    await asyncio.sleep(interval_seconds)
                except Exception as e:
                    self.logger.error(f"Monitoring loop error: {e}")
                    await asyncio.sleep(interval_seconds)
        
        self._monitor_task = asyncio.create_task(monitor_loop())
    
    def stop_monitoring(self):
        """停止监控"""
        if not self._monitoring:
            return
        
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
        
        self.logger.info("System monitoring stopped")

class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self._alerts: Dict[str, Alert] = {}
        self._rules: List[Dict[str, Any]] = []
        self._handlers: List[Callable] = []
        self.logger = get_logger()
    
    def add_rule(
        self,
        metric_name: str,
        threshold: float,
        operator: str = ">",
        level: AlertLevel = AlertLevel.WARNING,
        message_template: str = "Metric {metric} is {value} (threshold: {threshold})"
    ):
        """添加告警规则"""
        rule = {
            "metric_name": metric_name,
            "threshold": threshold,
            "operator": operator,
            "level": level,
            "message_template": message_template
        }
        self._rules.append(rule)
        self.logger.debug(f"Alert rule added: {metric_name} {operator} {threshold}")
    
    def add_handler(self, handler: Callable[[Alert], None]):
        """添加告警处理器"""
        self._handlers.append(handler)
    
    def check_metrics(self, metrics: List[Metric]):
        """检查指标是否触发告警"""
        for metric in metrics:
            for rule in self._rules:
                if metric.name == rule["metric_name"]:
                    self._check_rule(metric, rule)
    
    def _check_rule(self, metric: Metric, rule: Dict[str, Any]):
        """检查单个规则"""
        threshold = rule["threshold"]
        operator = rule["operator"]
        value = metric.value
        
        triggered = False
        
        if operator == ">" and value > threshold:
            triggered = True
        elif operator == "<" and value < threshold:
            triggered = True
        elif operator == ">=" and value >= threshold:
            triggered = True
        elif operator == "<=" and value <= threshold:
            triggered = True
        elif operator == "==" and value == threshold:
            triggered = True
        elif operator == "!=" and value != threshold:
            triggered = True
        
        alert_id = f"{metric.name}_{operator}_{threshold}"
        
        if triggered:
            if alert_id not in self._alerts or self._alerts[alert_id].resolved:
                # 创建新告警
                message = rule["message_template"].format(
                    metric=metric.name,
                    value=value,
                    threshold=threshold
                )
                
                alert = Alert(
                    id=alert_id,
                    level=rule["level"],
                    title=f"Metric Alert: {metric.name}",
                    message=message,
                    timestamp=datetime.now(),
                    metric_name=metric.name,
                    threshold=threshold,
                    current_value=value
                )
                
                self._alerts[alert_id] = alert
                self._trigger_alert(alert)
        else:
            # 检查是否需要解决告警
            if alert_id in self._alerts and not self._alerts[alert_id].resolved:
                alert = self._alerts[alert_id]
                alert.resolved = True
                alert.resolved_at = datetime.now()
                self._resolve_alert(alert)
    
    def _trigger_alert(self, alert: Alert):
        """触发告警"""
        self.logger.warning(f"Alert triggered: {alert.title} - {alert.message}")
        
        for handler in self._handlers:
            try:
                handler(alert)
            except Exception as e:
                self.logger.error(f"Alert handler failed: {e}")
    
    def _resolve_alert(self, alert: Alert):
        """解决告警"""
        self.logger.info(f"Alert resolved: {alert.title}")
        
        # 可以在这里添加告警解决的处理逻辑
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [alert for alert in self._alerts.values() if not alert.resolved]
    
    def get_all_alerts(self) -> List[Alert]:
        """获取所有告警"""
        return list(self._alerts.values())

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, metrics_collector: Optional[MetricsCollector] = None):
        self.metrics = metrics_collector or MetricsCollector()
        self.logger = get_logger()
    
    @contextmanager
    def profile(self, operation_name: str, labels: Optional[Dict[str, str]] = None):
        """性能分析上下文管理器"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            yield
        finally:
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss
            
            duration_ms = (end_time - start_time) * 1000
            memory_delta = end_memory - start_memory
            
            # 记录性能指标
            self.metrics.record_timer(f"{operation_name}_duration", duration_ms, labels)
            self.metrics.set_gauge(f"{operation_name}_memory_delta", memory_delta, labels)
            
            self.logger.debug(
                f"Performance profile: {operation_name}",
                operation=operation_name,
                duration_ms=duration_ms,
                memory_delta_bytes=memory_delta,
                labels=labels
            )

def profile_function(operation_name: str = None):
    """函数性能分析装饰器"""
    def decorator(func):
        nonlocal operation_name
        if operation_name is None:
            operation_name = f"{func.__module__}.{func.__name__}"
        
        profiler = PerformanceProfiler()
        
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                with profiler.profile(operation_name):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                with profiler.profile(operation_name):
                    return func(*args, **kwargs)
            return sync_wrapper
    
    return decorator

# 内置健康检查函数
def check_database_connection(db_url: str) -> HealthCheck:
    """检查数据库连接"""
    try:
        # 这里应该根据实际的数据库类型实现连接检查
        # 示例使用SQLAlchemy
        from sqlalchemy import create_engine, text
        
        engine = create_engine(db_url)
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        return HealthCheck(
            component="database",
            status=HealthStatus.HEALTHY,
            message="Database connection successful",
            timestamp=datetime.now(),
            component_type=ComponentType.DATABASE
        )
    
    except Exception as e:
        return HealthCheck(
            component="database",
            status=HealthStatus.UNHEALTHY,
            message=f"Database connection failed: {str(e)}",
            timestamp=datetime.now(),
            component_type=ComponentType.DATABASE
        )

def check_redis_connection(redis_url: str) -> HealthCheck:
    """检查Redis连接"""
    try:
        import redis
        
        r = redis.from_url(redis_url)
        r.ping()
        
        return HealthCheck(
            component="redis",
            status=HealthStatus.HEALTHY,
            message="Redis connection successful",
            timestamp=datetime.now(),
            component_type=ComponentType.CACHE
        )
    
    except Exception as e:
        return HealthCheck(
            component="redis",
            status=HealthStatus.UNHEALTHY,
            message=f"Redis connection failed: {str(e)}",
            timestamp=datetime.now(),
            component_type=ComponentType.CACHE
        )

def check_external_api(api_url: str, timeout: int = 10) -> HealthCheck:
    """检查外部API"""
    try:
        response = requests.get(api_url, timeout=timeout)
        
        if response.status_code == 200:
            status = HealthStatus.HEALTHY
            message = "API is responding"
        else:
            status = HealthStatus.DEGRADED
            message = f"API returned status code {response.status_code}"
        
        return HealthCheck(
            component=f"external_api_{urlparse(api_url).netloc}",
            status=status,
            message=message,
            timestamp=datetime.now(),
            component_type=ComponentType.EXTERNAL_API,
            details={"status_code": response.status_code}
        )
    
    except Exception as e:
        return HealthCheck(
            component=f"external_api_{urlparse(api_url).netloc}",
            status=HealthStatus.UNHEALTHY,
            message=f"API check failed: {str(e)}",
            timestamp=datetime.now(),
            component_type=ComponentType.EXTERNAL_API
        )

def check_disk_space(path: str = "/", threshold_percent: float = 90.0) -> HealthCheck:
    """检查磁盘空间"""
    try:
        disk_usage = psutil.disk_usage(path)
        used_percent = (disk_usage.used / disk_usage.total) * 100
        
        if used_percent < threshold_percent:
            status = HealthStatus.HEALTHY
            message = f"Disk usage is {used_percent:.1f}%"
        else:
            status = HealthStatus.UNHEALTHY
            message = f"Disk usage is {used_percent:.1f}% (threshold: {threshold_percent}%)"
        
        return HealthCheck(
            component="disk_space",
            status=status,
            message=message,
            timestamp=datetime.now(),
            component_type=ComponentType.FILE_SYSTEM,
            details={
                "used_percent": used_percent,
                "threshold_percent": threshold_percent,
                "free_bytes": disk_usage.free,
                "total_bytes": disk_usage.total
            }
        )
    
    except Exception as e:
        return HealthCheck(
            component="disk_space",
            status=HealthStatus.UNHEALTHY,
            message=f"Disk space check failed: {str(e)}",
            timestamp=datetime.now(),
            component_type=ComponentType.FILE_SYSTEM
        )

# 全局实例
_metrics_collector: Optional[MetricsCollector] = None
_health_checker: Optional[HealthChecker] = None
_system_monitor: Optional[SystemMonitor] = None
_alert_manager: Optional[AlertManager] = None

def get_metrics_collector() -> MetricsCollector:
    """获取指标收集器"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector

def get_health_checker() -> HealthChecker:
    """获取健康检查器"""
    global _health_checker
    if _health_checker is None:
        _health_checker = HealthChecker()
    return _health_checker

def get_system_monitor() -> SystemMonitor:
    """获取系统监控器"""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor(get_metrics_collector())
    return _system_monitor

def get_alert_manager() -> AlertManager:
    """获取告警管理器"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager()
    return _alert_manager

# 便捷函数
def increment_counter(name: str, value: float = 1.0, **labels):
    """增加计数器"""
    get_metrics_collector().increment_counter(name, value, labels)

def set_gauge(name: str, value: float, **labels):
    """设置仪表值"""
    get_metrics_collector().set_gauge(name, value, labels)

def record_timer(name: str, duration_ms: float, **labels):
    """记录计时器"""
    get_metrics_collector().record_timer(name, duration_ms, labels)

def register_health_check(name: str, check_func: Callable, component_type: ComponentType = ComponentType.CUSTOM):
    """注册健康检查"""
    get_health_checker().register_check(name, check_func, component_type)

def add_alert_rule(metric_name: str, threshold: float, operator: str = ">", level: AlertLevel = AlertLevel.WARNING):
    """添加告警规则"""
    get_alert_manager().add_rule(metric_name, threshold, operator, level)

# 初始化函数
def init_monitoring():
    """初始化监控系统"""
    logger = get_logger()
    
    # 注册基本健康检查
    health_checker = get_health_checker()
    
    # 磁盘空间检查
    health_checker.register_check(
        "disk_space",
        lambda: check_disk_space("/", 90.0),
        ComponentType.FILE_SYSTEM
    )
    
    # 添加基本告警规则
    alert_manager = get_alert_manager()
    alert_manager.add_rule("system_cpu_usage_percent", 80.0, ">", AlertLevel.WARNING)
    alert_manager.add_rule("system_memory_usage_percent", 85.0, ">", AlertLevel.WARNING)
    alert_manager.add_rule("system_disk_usage_percent", 90.0, ">", AlertLevel.ERROR)
    
    logger.info("Monitoring system initialized")

async def start_monitoring(interval_seconds: int = 60):
    """启动监控"""
    system_monitor = get_system_monitor()
    await system_monitor.start_monitoring(interval_seconds)

def stop_monitoring():
    """停止监控"""
    system_monitor = get_system_monitor()
    system_monitor.stop_monitoring()