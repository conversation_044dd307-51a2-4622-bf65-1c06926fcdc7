#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务调度模块
提供定时任务、异步任务队列、任务监控、重试机制等功能
"""

import asyncio
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable, Awaitable
from dataclasses import dataclass, field, asdict
from enum import Enum
import json
import threading
from collections import defaultdict, deque
from contextlib import asynccontextmanager
import traceback
import pickle
import heapq
from concurrent.futures import ThreadPoolExecutor
import inspect
from functools import wraps

from .env_config import env_config
from .logging_utils import get_logger
from .monitoring_utils import get_metrics_collector, increment_counter, set_gauge, record_timer

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    RETRYING = "retrying"    # 重试中
    SCHEDULED = "scheduled"  # 已调度

class TaskPriority(int, Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 5
    HIGH = 10
    CRITICAL = 20

class TaskType(str, Enum):
    """任务类型枚举"""
    IMMEDIATE = "immediate"  # 立即执行
    DELAYED = "delayed"      # 延迟执行
    SCHEDULED = "scheduled"  # 定时执行
    RECURRING = "recurring"  # 循环执行
    CRON = "cron"           # Cron表达式

class RetryPolicy(str, Enum):
    """重试策略枚举"""
    NONE = "none"                    # 不重试
    FIXED_DELAY = "fixed_delay"      # 固定延迟
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"  # 线性退避

@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_ms": self.duration_ms,
            "retry_count": self.retry_count
        }

@dataclass
class RetryConfig:
    """重试配置"""
    policy: RetryPolicy = RetryPolicy.NONE
    max_attempts: int = 3
    initial_delay_ms: int = 1000
    max_delay_ms: int = 60000
    backoff_multiplier: float = 2.0
    jitter: bool = True
    
    def calculate_delay(self, attempt: int) -> float:
        """计算重试延迟（毫秒）"""
        if self.policy == RetryPolicy.NONE:
            return 0
        
        if self.policy == RetryPolicy.FIXED_DELAY:
            delay = self.initial_delay_ms
        elif self.policy == RetryPolicy.EXPONENTIAL_BACKOFF:
            delay = self.initial_delay_ms * (self.backoff_multiplier ** (attempt - 1))
        elif self.policy == RetryPolicy.LINEAR_BACKOFF:
            delay = self.initial_delay_ms * attempt
        else:
            delay = self.initial_delay_ms
        
        # 限制最大延迟
        delay = min(delay, self.max_delay_ms)
        
        # 添加抖动
        if self.jitter:
            import random
            delay = delay * (0.5 + random.random() * 0.5)
        
        return delay

@dataclass
class Task:
    """任务定义"""
    id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    task_type: TaskType = TaskType.IMMEDIATE
    priority: TaskPriority = TaskPriority.NORMAL
    scheduled_time: Optional[datetime] = None
    cron_expression: Optional[str] = None
    interval_seconds: Optional[int] = None
    retry_config: RetryConfig = field(default_factory=RetryConfig)
    timeout_seconds: Optional[int] = None
    tags: Dict[str, str] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if self.task_type == TaskType.DELAYED and not self.scheduled_time:
            raise ValueError("Delayed task must have scheduled_time")
        if self.task_type == TaskType.CRON and not self.cron_expression:
            raise ValueError("Cron task must have cron_expression")
        if self.task_type == TaskType.RECURRING and not self.interval_seconds:
            raise ValueError("Recurring task must have interval_seconds")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "task_type": self.task_type.value,
            "priority": self.priority.value,
            "scheduled_time": self.scheduled_time.isoformat() if self.scheduled_time else None,
            "cron_expression": self.cron_expression,
            "interval_seconds": self.interval_seconds,
            "timeout_seconds": self.timeout_seconds,
            "tags": self.tags,
            "created_at": self.created_at.isoformat(),
            "retry_config": asdict(self.retry_config)
        }

class CronParser:
    """Cron表达式解析器"""
    
    @staticmethod
    def parse_field(field: str, min_val: int, max_val: int) -> List[int]:
        """解析Cron字段"""
        if field == "*":
            return list(range(min_val, max_val + 1))
        
        values = []
        for part in field.split(","):
            if "/" in part:
                range_part, step = part.split("/")
                step = int(step)
                if range_part == "*":
                    values.extend(list(range(min_val, max_val + 1, step)))
                else:
                    start, end = map(int, range_part.split("-"))
                    values.extend(list(range(start, end + 1, step)))
            elif "-" in part:
                start, end = map(int, part.split("-"))
                values.extend(list(range(start, end + 1)))
            else:
                values.append(int(part))
        
        return sorted(set(values))
    
    @staticmethod
    def next_run_time(cron_expression: str, from_time: Optional[datetime] = None) -> datetime:
        """计算下次执行时间"""
        if from_time is None:
            from_time = datetime.now()
        
        # 简化的Cron解析，格式：分 时 日 月 周
        parts = cron_expression.split()
        if len(parts) != 5:
            raise ValueError("Invalid cron expression format")
        
        minute_field, hour_field, day_field, month_field, weekday_field = parts
        
        minutes = CronParser.parse_field(minute_field, 0, 59)
        hours = CronParser.parse_field(hour_field, 0, 23)
        days = CronParser.parse_field(day_field, 1, 31)
        months = CronParser.parse_field(month_field, 1, 12)
        weekdays = CronParser.parse_field(weekday_field, 0, 6)  # 0=Sunday
        
        # 从当前时间开始查找下一个匹配的时间
        current = from_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        
        for _ in range(366 * 24 * 60):  # 最多查找一年
            if (current.minute in minutes and
                current.hour in hours and
                current.day in days and
                current.month in months and
                current.weekday() + 1 in weekdays):  # Python weekday: 0=Monday
                return current
            
            current += timedelta(minutes=1)
        
        raise ValueError("Could not find next run time for cron expression")

class TaskQueue:
    """任务队列"""
    
    def __init__(self, max_size: Optional[int] = None):
        self._queue: List[Tuple[int, int, Task]] = []  # (priority, sequence, task)
        self._sequence = 0
        self._max_size = max_size
        self._lock = asyncio.Lock()
    
    async def put(self, task: Task):
        """添加任务到队列"""
        async with self._lock:
            if self._max_size and len(self._queue) >= self._max_size:
                raise RuntimeError("Task queue is full")
            
            # 使用负优先级实现最大堆
            priority = -task.priority.value
            heapq.heappush(self._queue, (priority, self._sequence, task))
            self._sequence += 1
    
    async def get(self) -> Optional[Task]:
        """从队列获取任务"""
        async with self._lock:
            if not self._queue:
                return None
            
            _, _, task = heapq.heappop(self._queue)
            return task
    
    async def size(self) -> int:
        """获取队列大小"""
        async with self._lock:
            return len(self._queue)
    
    async def empty(self) -> bool:
        """检查队列是否为空"""
        async with self._lock:
            return len(self._queue) == 0

class TaskExecutor:
    """任务执行器"""
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.logger = get_logger()
        self.metrics = get_metrics_collector()
    
    async def execute_task(self, task: Task) -> TaskResult:
        """执行任务"""
        task_result = TaskResult(
            task_id=task.id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now()
        )
        
        try:
            self.logger.info(f"Executing task: {task.name} ({task.id})")
            increment_counter("tasks_started", tags={"task_name": task.name})
            
            # 执行任务
            if asyncio.iscoroutinefunction(task.func):
                result = await self._execute_async_task(task)
            else:
                result = await self._execute_sync_task(task)
            
            task_result.result = result
            task_result.status = TaskStatus.COMPLETED
            
            self.logger.info(f"Task completed: {task.name} ({task.id})")
            increment_counter("tasks_completed", tags={"task_name": task.name})
        
        except asyncio.TimeoutError:
            task_result.status = TaskStatus.FAILED
            task_result.error = "Task execution timeout"
            self.logger.error(f"Task timeout: {task.name} ({task.id})")
            increment_counter("tasks_timeout", tags={"task_name": task.name})
        
        except Exception as e:
            task_result.status = TaskStatus.FAILED
            task_result.error = str(e)
            self.logger.error(f"Task failed: {task.name} ({task.id}): {e}")
            increment_counter("tasks_failed", tags={"task_name": task.name})
        
        finally:
            task_result.end_time = datetime.now()
            if task_result.start_time:
                duration = task_result.end_time - task_result.start_time
                task_result.duration_ms = duration.total_seconds() * 1000
                record_timer("task_duration", task_result.duration_ms, tags={"task_name": task.name})
        
        return task_result
    
    async def _execute_async_task(self, task: Task) -> Any:
        """执行异步任务"""
        if task.timeout_seconds:
            return await asyncio.wait_for(
                task.func(*task.args, **task.kwargs),
                timeout=task.timeout_seconds
            )
        else:
            return await task.func(*task.args, **task.kwargs)
    
    async def _execute_sync_task(self, task: Task) -> Any:
        """执行同步任务"""
        loop = asyncio.get_event_loop()
        
        if task.timeout_seconds:
            return await asyncio.wait_for(
                loop.run_in_executor(
                    self.thread_pool,
                    lambda: task.func(*task.args, **task.kwargs)
                ),
                timeout=task.timeout_seconds
            )
        else:
            return await loop.run_in_executor(
                self.thread_pool,
                lambda: task.func(*task.args, **task.kwargs)
            )
    
    def shutdown(self):
        """关闭执行器"""
        self.thread_pool.shutdown(wait=True)

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, max_workers: int = 10, max_queue_size: Optional[int] = None):
        self.max_workers = max_workers
        self.task_queue = TaskQueue(max_queue_size)
        self.executor = TaskExecutor(max_workers)
        self.scheduled_tasks: Dict[str, Task] = {}
        self.recurring_tasks: Dict[str, Task] = {}
        self.task_results: Dict[str, TaskResult] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        self._running = False
        self._scheduler_task: Optional[asyncio.Task] = None
        self._workers: List[asyncio.Task] = []
        
        self.logger = get_logger()
        self.metrics = get_metrics_collector()
    
    async def start(self):
        """启动调度器"""
        if self._running:
            return
        
        self._running = True
        self.logger.info(f"Starting task scheduler with {self.max_workers} workers")
        
        # 启动调度器任务
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        # 启动工作者任务
        for i in range(self.max_workers):
            worker_task = asyncio.create_task(self._worker_loop(f"worker-{i}"))
            self._workers.append(worker_task)
        
        set_gauge("scheduler_workers", self.max_workers)
        increment_counter("scheduler_started")
    
    async def stop(self):
        """停止调度器"""
        if not self._running:
            return
        
        self._running = False
        self.logger.info("Stopping task scheduler")
        
        # 取消调度器任务
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消工作者任务
        for worker in self._workers:
            worker.cancel()
        
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)
        
        # 取消正在运行的任务
        for task in self.running_tasks.values():
            task.cancel()
        
        if self.running_tasks:
            await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
        
        # 关闭执行器
        self.executor.shutdown()
        
        increment_counter("scheduler_stopped")
        self.logger.info("Task scheduler stopped")
    
    async def submit_task(
        self,
        func: Callable,
        *args,
        name: Optional[str] = None,
        task_type: TaskType = TaskType.IMMEDIATE,
        priority: TaskPriority = TaskPriority.NORMAL,
        scheduled_time: Optional[datetime] = None,
        delay_seconds: Optional[int] = None,
        cron_expression: Optional[str] = None,
        interval_seconds: Optional[int] = None,
        retry_config: Optional[RetryConfig] = None,
        timeout_seconds: Optional[int] = None,
        tags: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> str:
        """提交任务"""
        task_id = str(uuid.uuid4())
        
        if name is None:
            name = func.__name__ if hasattr(func, '__name__') else str(func)
        
        if delay_seconds and not scheduled_time:
            scheduled_time = datetime.now() + timedelta(seconds=delay_seconds)
            task_type = TaskType.DELAYED
        
        task = Task(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            task_type=task_type,
            priority=priority,
            scheduled_time=scheduled_time,
            cron_expression=cron_expression,
            interval_seconds=interval_seconds,
            retry_config=retry_config or RetryConfig(),
            timeout_seconds=timeout_seconds,
            tags=tags or {}
        )
        
        if task_type == TaskType.IMMEDIATE:
            await self.task_queue.put(task)
        elif task_type in [TaskType.DELAYED, TaskType.SCHEDULED]:
            self.scheduled_tasks[task_id] = task
        elif task_type in [TaskType.RECURRING, TaskType.CRON]:
            self.recurring_tasks[task_id] = task
            # 计算下次执行时间
            if task_type == TaskType.CRON:
                next_time = CronParser.next_run_time(cron_expression)
                task.scheduled_time = next_time
            elif task_type == TaskType.RECURRING:
                task.scheduled_time = datetime.now() + timedelta(seconds=interval_seconds)
            self.scheduled_tasks[task_id] = task
        
        self.logger.debug(f"Task submitted: {name} ({task_id})")
        increment_counter("tasks_submitted", tags={"task_type": task_type.value})
        
        return task_id
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        # 检查是否在运行中
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            del self.running_tasks[task_id]
            
            # 更新任务结果
            if task_id in self.task_results:
                self.task_results[task_id].status = TaskStatus.CANCELLED
            
            self.logger.info(f"Running task cancelled: {task_id}")
            increment_counter("tasks_cancelled")
            return True
        
        # 检查是否在调度中
        if task_id in self.scheduled_tasks:
            del self.scheduled_tasks[task_id]
            self.logger.info(f"Scheduled task cancelled: {task_id}")
            increment_counter("tasks_cancelled")
            return True
        
        # 检查是否是循环任务
        if task_id in self.recurring_tasks:
            del self.recurring_tasks[task_id]
            # 同时删除调度中的任务
            if task_id in self.scheduled_tasks:
                del self.scheduled_tasks[task_id]
            self.logger.info(f"Recurring task cancelled: {task_id}")
            increment_counter("tasks_cancelled")
            return True
        
        return False
    
    async def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        return self.task_results.get(task_id)
    
    async def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return TaskStatus.RUNNING
        elif task_id in self.scheduled_tasks:
            return TaskStatus.SCHEDULED
        elif task_id in self.task_results:
            return self.task_results[task_id].status
        else:
            return None
    
    async def list_tasks(self, status: Optional[TaskStatus] = None) -> List[Dict[str, Any]]:
        """列出任务"""
        tasks = []
        
        # 运行中的任务
        for task_id in self.running_tasks:
            if status is None or status == TaskStatus.RUNNING:
                tasks.append({
                    "id": task_id,
                    "status": TaskStatus.RUNNING.value
                })
        
        # 调度中的任务
        for task_id, task in self.scheduled_tasks.items():
            if status is None or status == TaskStatus.SCHEDULED:
                task_info = task.to_dict()
                task_info["status"] = TaskStatus.SCHEDULED.value
                tasks.append(task_info)
        
        # 已完成的任务
        for task_id, result in self.task_results.items():
            if status is None or status == result.status:
                tasks.append(result.to_dict())
        
        return tasks
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        queue_size = await self.task_queue.size()
        
        status_counts = defaultdict(int)
        for result in self.task_results.values():
            status_counts[result.status.value] += 1
        
        return {
            "queue_size": queue_size,
            "running_tasks": len(self.running_tasks),
            "scheduled_tasks": len(self.scheduled_tasks),
            "recurring_tasks": len(self.recurring_tasks),
            "total_results": len(self.task_results),
            "status_counts": dict(status_counts),
            "workers": self.max_workers,
            "is_running": self._running
        }
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        while self._running:
            try:
                await self._process_scheduled_tasks()
                await asyncio.sleep(1)  # 每秒检查一次
            except Exception as e:
                self.logger.error(f"Scheduler loop error: {e}")
                await asyncio.sleep(5)
    
    async def _process_scheduled_tasks(self):
        """处理调度任务"""
        now = datetime.now()
        ready_tasks = []
        
        for task_id, task in list(self.scheduled_tasks.items()):
            if task.scheduled_time and task.scheduled_time <= now:
                ready_tasks.append((task_id, task))
        
        for task_id, task in ready_tasks:
            # 移除已调度的任务
            del self.scheduled_tasks[task_id]
            
            # 如果是循环任务，重新调度下次执行
            if task_id in self.recurring_tasks:
                next_task = Task(
                    id=task_id,
                    name=task.name,
                    func=task.func,
                    args=task.args,
                    kwargs=task.kwargs,
                    task_type=task.task_type,
                    priority=task.priority,
                    cron_expression=task.cron_expression,
                    interval_seconds=task.interval_seconds,
                    retry_config=task.retry_config,
                    timeout_seconds=task.timeout_seconds,
                    tags=task.tags
                )
                
                if task.task_type == TaskType.CRON:
                    next_task.scheduled_time = CronParser.next_run_time(task.cron_expression, now)
                elif task.task_type == TaskType.RECURRING:
                    next_task.scheduled_time = now + timedelta(seconds=task.interval_seconds)
                
                self.scheduled_tasks[task_id] = next_task
            
            # 将任务加入执行队列
            await self.task_queue.put(task)
    
    async def _worker_loop(self, worker_name: str):
        """工作者循环"""
        self.logger.debug(f"Worker {worker_name} started")
        
        while self._running:
            try:
                task = await self.task_queue.get()
                if task is None:
                    await asyncio.sleep(0.1)
                    continue
                
                # 执行任务
                await self._execute_task_with_retry(task)
                
            except Exception as e:
                self.logger.error(f"Worker {worker_name} error: {e}")
                await asyncio.sleep(1)
        
        self.logger.debug(f"Worker {worker_name} stopped")
    
    async def _execute_task_with_retry(self, task: Task):
        """执行任务（带重试）"""
        attempt = 0
        max_attempts = task.retry_config.max_attempts
        
        while attempt < max_attempts:
            attempt += 1
            
            # 创建执行任务
            execution_task = asyncio.create_task(self.executor.execute_task(task))
            self.running_tasks[task.id] = execution_task
            
            try:
                result = await execution_task
                result.retry_count = attempt - 1
                self.task_results[task.id] = result
                
                if result.status == TaskStatus.COMPLETED:
                    break
                elif attempt < max_attempts:
                    # 计算重试延迟
                    delay_ms = task.retry_config.calculate_delay(attempt)
                    if delay_ms > 0:
                        self.logger.info(
                            f"Task {task.name} ({task.id}) failed, retrying in {delay_ms}ms "
                            f"(attempt {attempt}/{max_attempts})"
                        )
                        result.status = TaskStatus.RETRYING
                        await asyncio.sleep(delay_ms / 1000)
            
            except asyncio.CancelledError:
                result = TaskResult(
                    task_id=task.id,
                    status=TaskStatus.CANCELLED,
                    start_time=datetime.now()
                )
                result.end_time = datetime.now()
                self.task_results[task.id] = result
                break
            
            finally:
                if task.id in self.running_tasks:
                    del self.running_tasks[task.id]
        
        # 更新指标
        set_gauge("running_tasks", len(self.running_tasks))
        set_gauge("scheduled_tasks", len(self.scheduled_tasks))

# 装饰器
def scheduled_task(
    cron_expression: Optional[str] = None,
    interval_seconds: Optional[int] = None,
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    retry_config: Optional[RetryConfig] = None,
    timeout_seconds: Optional[int] = None,
    tags: Optional[Dict[str, str]] = None
):
    """定时任务装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        
        # 保存任务配置
        wrapper._task_config = {
            "cron_expression": cron_expression,
            "interval_seconds": interval_seconds,
            "name": name or func.__name__,
            "priority": priority,
            "retry_config": retry_config or RetryConfig(),
            "timeout_seconds": timeout_seconds,
            "tags": tags or {}
        }
        
        return wrapper
    
    return decorator

def background_task(
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    retry_config: Optional[RetryConfig] = None,
    timeout_seconds: Optional[int] = None,
    tags: Optional[Dict[str, str]] = None
):
    """后台任务装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            scheduler = get_task_scheduler()
            return await scheduler.submit_task(
                func,
                *args,
                name=name or func.__name__,
                priority=priority,
                retry_config=retry_config,
                timeout_seconds=timeout_seconds,
                tags=tags,
                **kwargs
            )
        
        return wrapper
    
    return decorator

# 全局实例
_task_scheduler: Optional[TaskScheduler] = None

def get_task_scheduler() -> TaskScheduler:
    """获取任务调度器"""
    global _task_scheduler
    if _task_scheduler is None:
        config = env_config
        max_workers = getattr(config, 'TASK_MAX_WORKERS', 10)
        max_queue_size = getattr(config, 'TASK_MAX_QUEUE_SIZE', None)
        _task_scheduler = TaskScheduler(max_workers, max_queue_size)
    return _task_scheduler

# 便捷函数
async def submit_task(
    func: Callable,
    *args,
    name: Optional[str] = None,
    delay_seconds: Optional[int] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    retry_config: Optional[RetryConfig] = None,
    timeout_seconds: Optional[int] = None,
    tags: Optional[Dict[str, str]] = None,
    **kwargs
) -> str:
    """提交任务"""
    scheduler = get_task_scheduler()
    return await scheduler.submit_task(
        func, *args,
        name=name,
        delay_seconds=delay_seconds,
        priority=priority,
        retry_config=retry_config,
        timeout_seconds=timeout_seconds,
        tags=tags,
        **kwargs
    )

async def submit_scheduled_task(
    func: Callable,
    scheduled_time: datetime,
    *args,
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    retry_config: Optional[RetryConfig] = None,
    timeout_seconds: Optional[int] = None,
    tags: Optional[Dict[str, str]] = None,
    **kwargs
) -> str:
    """提交定时任务"""
    scheduler = get_task_scheduler()
    return await scheduler.submit_task(
        func, *args,
        name=name,
        task_type=TaskType.SCHEDULED,
        scheduled_time=scheduled_time,
        priority=priority,
        retry_config=retry_config,
        timeout_seconds=timeout_seconds,
        tags=tags,
        **kwargs
    )

async def submit_cron_task(
    func: Callable,
    cron_expression: str,
    *args,
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    retry_config: Optional[RetryConfig] = None,
    timeout_seconds: Optional[int] = None,
    tags: Optional[Dict[str, str]] = None,
    **kwargs
) -> str:
    """提交Cron任务"""
    scheduler = get_task_scheduler()
    return await scheduler.submit_task(
        func, *args,
        name=name,
        task_type=TaskType.CRON,
        cron_expression=cron_expression,
        priority=priority,
        retry_config=retry_config,
        timeout_seconds=timeout_seconds,
        tags=tags,
        **kwargs
    )

async def submit_recurring_task(
    func: Callable,
    interval_seconds: int,
    *args,
    name: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    retry_config: Optional[RetryConfig] = None,
    timeout_seconds: Optional[int] = None,
    tags: Optional[Dict[str, str]] = None,
    **kwargs
) -> str:
    """提交循环任务"""
    scheduler = get_task_scheduler()
    return await scheduler.submit_task(
        func, *args,
        name=name,
        task_type=TaskType.RECURRING,
        interval_seconds=interval_seconds,
        priority=priority,
        retry_config=retry_config,
        timeout_seconds=timeout_seconds,
        tags=tags,
        **kwargs
    )

async def cancel_task(task_id: str) -> bool:
    """取消任务"""
    scheduler = get_task_scheduler()
    return await scheduler.cancel_task(task_id)

async def get_task_result(task_id: str) -> Optional[TaskResult]:
    """获取任务结果"""
    scheduler = get_task_scheduler()
    return await scheduler.get_task_result(task_id)

async def get_task_status(task_id: str) -> Optional[TaskStatus]:
    """获取任务状态"""
    scheduler = get_task_scheduler()
    return await scheduler.get_task_status(task_id)

async def list_tasks(status: Optional[TaskStatus] = None) -> List[Dict[str, Any]]:
    """列出任务"""
    scheduler = get_task_scheduler()
    return await scheduler.list_tasks(status)

async def get_scheduler_statistics() -> Dict[str, Any]:
    """获取调度器统计信息"""
    scheduler = get_task_scheduler()
    return await scheduler.get_statistics()

# 初始化和管理函数
async def start_scheduler():
    """启动任务调度器"""
    scheduler = get_task_scheduler()
    await scheduler.start()

async def stop_scheduler():
    """停止任务调度器"""
    scheduler = get_task_scheduler()
    await scheduler.stop()

def register_scheduled_tasks(module):
    """注册模块中的定时任务"""
    scheduler = get_task_scheduler()
    
    for name in dir(module):
        obj = getattr(module, name)
        if hasattr(obj, '_task_config'):
            config = obj._task_config
            
            if config['cron_expression']:
                task_type = TaskType.CRON
            elif config['interval_seconds']:
                task_type = TaskType.RECURRING
            else:
                continue
            
            asyncio.create_task(scheduler.submit_task(
                obj,
                name=config['name'],
                task_type=task_type,
                cron_expression=config['cron_expression'],
                interval_seconds=config['interval_seconds'],
                priority=config['priority'],
                retry_config=config['retry_config'],
                timeout_seconds=config['timeout_seconds'],
                tags=config['tags']
            ))

# 示例任务
@scheduled_task(interval_seconds=300)  # 每5分钟执行一次
async def cleanup_old_task_results():
    """清理旧的任务结果"""
    scheduler = get_task_scheduler()
    cutoff_time = datetime.now() - timedelta(hours=24)
    
    old_results = [
        task_id for task_id, result in scheduler.task_results.items()
        if result.end_time and result.end_time < cutoff_time
    ]
    
    for task_id in old_results:
        del scheduler.task_results[task_id]
    
    if old_results:
        scheduler.logger.info(f"Cleaned up {len(old_results)} old task results")

@scheduled_task(cron_expression="0 0 * * *")  # 每天午夜执行
async def daily_task_statistics():
    """每日任务统计"""
    scheduler = get_task_scheduler()
    stats = await scheduler.get_statistics()
    
    scheduler.logger.info("Daily task statistics", **stats)