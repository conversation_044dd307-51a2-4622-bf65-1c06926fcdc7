#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试medication_management_screen加载功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_medication_screen_import():
    """测试medication_management_screen模块导入"""
    try:
        print("正在测试medication_management_screen模块导入...")
        from screens.medication_management_screen import MedicationManagementScreen
        print("✅ MedicationManagementScreen类导入成功")
        
        # 测试类实例化
        screen = MedicationManagementScreen(name='medication_management_screen')
        print("✅ MedicationManagementScreen实例化成功")
        
        # 检查必要的属性和方法
        required_methods = [
            'refresh_medications_display',
            'refresh_history_display', 
            'show_medication_detail',
            'on_card_click'
        ]
        
        for method_name in required_methods:
            if hasattr(screen, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_screen_loader_config():
    """测试screen_loader配置"""
    try:
        print("\n正在测试screen_loader配置...")
        from utils.screen_loader import ScreenLoader
        
        loader = ScreenLoader()
        configs = loader._screen_configs
        
        if 'medication_management_screen' in configs:
            config = configs['medication_management_screen']
            print(f"✅ medication_management_screen配置存在")
            print(f"   模块: {config.get('module')}")
            print(f"   类名: {config.get('class')}")
            print(f"   名称: {config.get('name')}")
            return True
        else:
            print("❌ medication_management_screen配置不存在")
            return False
            
    except Exception as e:
        print(f"❌ screen_loader测试错误: {e}")
        return False

if __name__ == '__main__':
    print("=== medication_management_screen 测试 ===")
    
    # 测试模块导入
    import_success = test_medication_screen_import()
    
    # 测试screen_loader配置
    config_success = test_screen_loader_config()
    
    print("\n=== 测试结果 ===")
    if import_success and config_success:
        print("✅ 所有测试通过，medication_management_screen应该能正常加载")
        sys.exit(0)
    else:
        print("❌ 测试失败，medication_management_screen可能无法正常加载")
        sys.exit(1)