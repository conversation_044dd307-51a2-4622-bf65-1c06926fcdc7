{"total_fixed_files": 19, "total_replacements": 22, "fixed_files": ["screens\\assessment_screen.py", "screens\\companion_service_screen.py", "screens\\consultant_screen.py", "screens\\document_list_screen.py", "screens\\register_screen.py", "screens\\survey_screen.py", "screens\\voice_triage_screen.py", "screens_bak\\assessment_screen.py", "screens_bak\\companion_service_screen.py", "screens_bak\\consultant_screen.py", "screens_bak\\document_list_screen.py", "screens_bak\\register_screen.py", "screens_bak\\survey_screen.py", "screens_bak\\voice_triage_screen.py", "widgets\\camera_view.py", "utils\\health_data_aggregator.py", "utils\\triage_manager.py", "migration_tool.py", "simple_test.py"], "directories_processed": ["screens", "screens_bak", "widgets", "utils", "api", "."], "timestamp": "C:\\Users\\<USER>\\Desktop\\health-Trea\\mobile", "status": "completed"}