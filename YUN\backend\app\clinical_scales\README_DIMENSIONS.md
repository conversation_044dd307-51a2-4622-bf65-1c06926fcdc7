# 维度功能使用指南

本文档介绍了健康评估系统中新增的维度功能，包括如何创建带维度的量表和问卷、计算维度分数以及生成分析报告。

## 功能概述

维度功能允许您：
- 为量表和问卷定义多个评估维度
- 将问题归属到特定维度
- 自动计算各维度的分数和百分比
- 生成详细的维度分析报告
- 提供改进建议和优势分析

## 核心概念

### 维度定义

每个维度包含以下属性：

```json
{
  "key": "cognitive",           // 维度唯一标识
  "name": "认知功能",           // 中文名称
  "name_en": "Cognitive Function", // 英文名称
  "description": "评估认知能力相关指标", // 描述
  "weight": 0.4,               // 权重（0-1）
  "max_score": 20,             // 最大分数
  "question_ids": ["q1", "q2"] // 包含的问题ID列表
}
```

### 维度分数

系统会自动计算每个维度的：
- 原始分数
- 百分比分数
- 水平等级（优秀/良好/一般/需要改进）
- 有效问题数量

## 使用方法

### 1. 创建带维度的量表模板

```python
from app.clinical_scales.generators.assessment_generator import AssessmentGenerator

# 定义维度
dimensions = [
    {
        "key": "cognitive",
        "name": "认知功能",
        "name_en": "Cognitive Function",
        "description": "评估认知能力相关指标",
        "weight": 0.4,
        "max_score": 20,
        "question_ids": ["q1", "q2"]
    },
    {
        "key": "emotional",
        "name": "情绪状态",
        "name_en": "Emotional State",
        "description": "评估情绪状态相关指标",
        "weight": 0.3,
        "max_score": 15,
        "question_ids": ["q3"]
    }
]

# 定义问题（注意添加dimension_key）
questions = [
    {
        "question_id": "q1",
        "question_text": "您的记忆力如何？",
        "question_type": "single_choice",
        "order": 1,
        "dimension_key": "cognitive",  # 指定维度
        "options": [
            {"value": "excellent", "label": "非常好", "score": 10},
            {"value": "good", "label": "好", "score": 7},
            {"value": "fair", "label": "一般", "score": 4},
            {"value": "poor", "label": "差", "score": 1}
        ]
    }
    # ... 更多问题
]

# 创建量表模板
generator = AssessmentGenerator(db)
template = generator.create_assessment_template(
    name="综合健康评估量表",
    assessment_type=AssessmentType.PSYCHOLOGICAL,
    description="带维度功能的评估量表",
    instructions="请根据实际情况选择",
    scoring_method="各维度分数加权求和",
    max_score=50.0,
    result_ranges=result_ranges,
    questions=questions,
    dimensions=dimensions,  # 传入维度定义
    created_by=1
)
```

### 2. 计算维度分数

```python
from app.clinical_scales.services.dimension_service import DimensionService

# 创建维度服务
dimension_service = DimensionService(db)

# 计算评估量表的维度分数
dimension_scores = dimension_service.calculate_assessment_dimensions(response, template)

# 计算问卷的维度分数
dimension_scores = dimension_service.calculate_questionnaire_dimensions(response, template)
```

### 3. 生成维度分析报告

```python
# 生成详细的分析报告
report = dimension_service.generate_dimension_report(dimension_scores)

print(f"总结: {report['summary']}")
print(f"平均百分比: {report['average_percentage']}%")
print(f"优势维度: {report['strengths']}")
print(f"需要改进的维度: {report['weaknesses']}")
print(f"改进建议: {report['recommendations']}")
```

## API 接口

### 获取量表维度定义

```http
GET /api/dimensions/assessment/{template_id}/dimensions
```

响应示例：
```json
{
  "template_id": 1,
  "dimensions": [
    {
      "key": "cognitive",
      "name": "认知功能",
      "name_en": "Cognitive Function",
      "description": "评估认知能力相关指标",
      "weight": 0.4,
      "max_score": 20,
      "question_ids": ["q1", "q2"]
    }
  ]
}
```

### 获取评估回答的维度分数

```http
GET /api/dimensions/assessment/response/{response_id}/dimension-scores
```

响应示例：
```json
{
  "response_id": 1,
  "dimension_scores": {
    "cognitive": {
      "name": "认知功能",
      "score": 11,
      "max_score": 20,
      "percentage": 55.0,
      "level": "一般",
      "valid_questions": 2
    }
  },
  "report": {
    "summary": "整体表现一般，建议在认知功能方面加强训练",
    "average_percentage": 62.5,
    "total_dimensions": 3,
    "strengths": [],
    "weaknesses": [
      {
        "dimension": "认知功能",
        "percentage": 55.0,
        "level": "一般"
      }
    ],
    "recommendations": [
      "建议进行记忆力训练",
      "保持规律的作息时间"
    ]
  }
}
```

### 重新计算维度分数

```http
POST /api/dimensions/assessment/response/{response_id}/recalculate-dimensions
```

## 数据库结构

### 新增字段

1. **assessment_templates.dimensions** (JSON)
   - 存储量表的维度定义

2. **assessment_template_questions.dimension_key** (VARCHAR)
   - 存储问题所属的维度键值

3. **assessment_responses.dimension_scores** (JSON)
   - 存储计算后的维度分数

4. **questionnaire_templates.dimensions** (JSON)
   - 存储问卷的维度定义

5. **questionnaire_template_questions.dimension_key** (VARCHAR)
   - 存储问卷问题所属的维度键值

6. **questionnaire_responses.dimension_scores** (JSON)
   - 存储问卷回答的维度分数

### 数据库迁移

执行以下命令应用数据库迁移：

```bash
# 使用Alembic
alembic upgrade head

# 或手动执行SQL（见migrations/add_dimensions_support.py）
```

## 示例代码

完整的使用示例请参考：
- `examples/dimension_usage_example.py` - 维度功能使用示例
- `migrations/add_dimensions_support.py` - 数据库迁移脚本

## 最佳实践

### 1. 维度设计原则

- **互斥性**：确保维度之间相互独立，避免重叠
- **完整性**：维度应该覆盖评估的所有重要方面
- **平衡性**：各维度的权重应该合理分配
- **可理解性**：维度名称和描述应该清晰易懂

### 2. 问题分配

- 每个问题应该明确归属到一个维度
- 避免一个问题同时影响多个维度
- 确保每个维度至少包含2-3个问题

### 3. 权重设置

- 所有维度的权重总和应该等于1.0
- 根据临床重要性设置权重
- 考虑目标人群的特点调整权重

### 4. 分数解释

- 设置合理的分数范围和等级
- 提供有意义的改进建议
- 考虑文化背景和个体差异

## 注意事项

1. **数据一致性**：确保问题的dimension_key与维度定义中的key一致
2. **性能考虑**：大量数据时考虑异步计算维度分数
3. **版本兼容**：现有的量表和问卷会自动兼容，dimensions字段为可选
4. **权限控制**：确保只有授权用户可以修改维度定义

## 故障排除

### 常见问题

1. **维度分数为0**
   - 检查问题的dimension_key是否正确
   - 确认用户是否回答了该维度的问题

2. **维度定义不生效**
   - 检查JSON格式是否正确
   - 确认question_ids是否与实际问题ID匹配

3. **权重计算错误**
   - 确保所有维度权重总和为1.0
   - 检查max_score设置是否合理

### 调试方法

```python
# 启用调试日志
import logging
logging.getLogger('dimension_service').setLevel(logging.DEBUG)

# 检查维度定义
print(template.dimensions)

# 检查问题维度归属
for question in template.questions:
    print(f"Question {question.question_id}: {question.dimension_key}")

# 检查计算结果
print(dimension_scores)
```

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持量表和问卷的维度功能
- 提供完整的API接口
- 包含示例代码和文档

---

如有问题或建议，请联系开发团队。