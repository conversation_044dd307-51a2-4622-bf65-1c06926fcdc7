# screens/log_screen.py
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard

from theme import AppTheme, AppMetrics, FontStyles, FontManager
from widgets.logo import HealthLogo, add_logo_to_layout  # 导入统一的Logo组件

# 定义KV语言字符串
KV = '''
<LogEntry>:
    orientation: 'vertical'
    adaptive_height: True
    padding: [dp(12), dp(8), dp(12), dp(8)]
    spacing: dp(4)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(8)]
    elevation: 1
    ripple_behavior: True
    
    MDBoxLayout:
        adaptive_height: True
        spacing: dp(8)
        
        MDLabel:
            text: root.timestamp
            adaptive_height: True
            font_style: "Label"
            theme_text_color: "Secondary"
            size_hint_x: None
            width: dp(120)
        
        MDLabel:
            text: root.log_type
            adaptive_height: True
            font_style: "Label"
            theme_text_color: "Custom"
            text_color: root.type_color
            size_hint_x: None
            width: dp(80)
    
    MDLabel:
        text: root.message
        adaptive_height: True
        font_style: "Label"
        role: "medium"
        theme_text_color: "Primary"

<LogScreen>:
    canvas.before:
        Color:
            rgba: app.theme.BACKGROUND_COLOR
        Rectangle:
            pos: self.pos
            size: self.size
            
    BoxLayout:
        orientation: 'vertical'
        spacing: dp(16)
        padding: [dp(16), dp(16), dp(16), dp(16)]
        
        # 标题栏 - 使用统一的Logo组件
        BoxLayout:
            size_hint_y: None
            height: dp(150)
            canvas.before:
                Color:
                    rgba: app.theme.PRIMARY_COLOR
                Rectangle:
                    pos: self.pos
                    size: self.size
            
            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo
        
        MDBoxLayout:
            adaptive_height: True
            spacing: dp(16)
            padding: [0, 0, 0, dp(8)]
            
            MDLabel:
                text: "日志记录"
                adaptive_height: True
                font_style: "Headline"
                role: "small"
                theme_text_color: "Primary"
            
            Widget:
                size_hint_x: 1
            
            MDButton:
                style: "outlined"
                on_release: root.clear_logs()
                
                MDButtonText:
                    text: "清除日志"
                    theme_text_color: "Primary"
        
        MDBoxLayout:
            adaptive_height: True
            spacing: dp(8)
            padding: [0, 0, 0, dp(8)]
            
            MDButton:
                style: "outlined"
                on_release: root.filter_logs('all')
                md_bg_color: [1, 1, 1, 1] if root.current_filter != 'all' else app.theme.PRIMARY_LIGHT
                
                MDButtonText:
                    text: "全部"
                    theme_text_color: "Primary"
            
            MDButton:
                style: "outlined"
                on_release: root.filter_logs('info')
                md_bg_color: [1, 1, 1, 1] if root.current_filter != 'info' else app.theme.PRIMARY_LIGHT
                
                MDButtonText:
                    text: "信息"
                    theme_text_color: "Primary"
            
            MDButton:
                style: "outlined"
                on_release: root.filter_logs('warning')
                md_bg_color: [1, 1, 1, 1] if root.current_filter != 'warning' else app.theme.PRIMARY_LIGHT
                
                MDButtonText:
                    text: "警告"
                    theme_text_color: "Primary"
            
            MDButton:
                style: "outlined"
                on_release: root.filter_logs('error')
                md_bg_color: [1, 1, 1, 1] if root.current_filter != 'error' else app.theme.PRIMARY_LIGHT
                
                MDButtonText:
                    text: "错误"
                    theme_text_color: "Primary"
        
        ScrollView:
            id: log_scroll
            do_scroll_x: False
            do_scroll_y: True
            bar_width: dp(4)
            bar_color: app.theme.PRIMARY_COLOR
            bar_inactive_color: app.theme.PRIMARY_LIGHT
            effect_cls: "ScrollEffect"
            scroll_type: ['bars', 'content']
            
            MDBoxLayout:
                id: log_container
                orientation: 'vertical'
                adaptive_height: True
                spacing: dp(8)
                padding: [0, 0, dp(4), 0]  # 右侧留出滚动条空间
'''

# 注册KV语言字符串
Builder.load_string(KV)

# 日志条目组件
class LogEntry(MDCard):
    timestamp = StringProperty("")
    log_type = StringProperty("")
    message = StringProperty("")
    type_color = ListProperty([0, 0, 0, 1])

# 日志屏幕
class LogScreen(Screen):
    logs = ListProperty([])
    filtered_logs = ListProperty([])
    current_filter = StringProperty("all")
    
    def __init__(self, **kwargs):
        super(LogScreen, self).__init__(**kwargs)
        self.type_colors = {
            "INFO": AppTheme.INFO_COLOR,
            "WARNING": AppTheme.WARNING_COLOR,
            "ERROR": AppTheme.ERROR_COLOR
        }
        Clock.schedule_once(self._update_log_display, 0)
    
    def _update_log_display(self, dt):
        """更新日志显示"""
        self.ids.log_container.clear_widgets()
        
        for log in self.filtered_logs:
            entry = LogEntry(
                timestamp=log["timestamp"],
                log_type=log["type"],
                message=log["message"],
                type_color=self.type_colors.get(log["type"], AppTheme.TEXT_PRIMARY)
            )
            self.ids.log_container.add_widget(entry)
        
        # 滚动到底部
        Clock.schedule_once(lambda dt: setattr(self.ids.log_scroll, 'scroll_y', 0), 0.1)
    
    def add_log(self, message, log_type="INFO"):
        """添加日志"""
        from datetime import datetime
        
        # 创建日志条目
        log_entry = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "type": log_type.upper(),
            "message": message
        }
        
        # 添加到日志列表
        self.logs.append(log_entry)
        
        # 如果当前过滤器允许显示该类型的日志，则更新显示
        if self.current_filter == "all" or self.current_filter == log_type.lower():
            self.filtered_logs.append(log_entry)
            Clock.schedule_once(self._update_log_display, 0)
    
    def filter_logs(self, filter_type):
        """过滤日志"""
        self.current_filter = filter_type
        
        if filter_type == "all":
            self.filtered_logs = self.logs.copy()
        else:
            self.filtered_logs = [log for log in self.logs if log["type"].lower() == filter_type]
        
        Clock.schedule_once(self._update_log_display, 0)
    
    def clear_logs(self):
        """清除所有日志"""
        self.logs.clear()
        self.filtered_logs.clear()
        Clock.schedule_once(self._update_log_display, 0)

# 日志管理器单例
class LogManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LogManager, cls).__new__(cls)
            cls._instance.log_screen = None
        return cls._instance
    
    def set_log_screen(self, log_screen):
        """设置日志屏幕实例"""
        self.log_screen = log_screen
    
    def log(self, message, log_type="INFO"):
        """记录日志"""
        if self.log_screen:
            self.log_screen.add_log(message, log_type)
        else:
            print(f"[{log_type}] {message}")
    
    def info(self, message):
        """记录信息日志"""
        self.log(message, "INFO")
    
    def warning(self, message):
        """记录警告日志"""
        self.log(message, "WARNING")
    
    def error(self, message):
        """记录错误日志"""
        self.log(message, "ERROR")

# 创建全局日志管理器实例
log_manager = LogManager()

# 便捷函数，用于在其他模块中记录日志
def log_info(message):
    """记录信息日志"""
    log_manager.info(message)

def log_warning(message):
    """记录警告日志"""
    log_manager.warning(message)

def log_error(message):
    """记录错误日志"""
    log_manager.error(message)