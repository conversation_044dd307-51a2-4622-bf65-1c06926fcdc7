# 个人用户Homepage UI改版需求及计划

## 项目概述

本文档为健康管理APP个人用户主页UI改版的指导性文件，基于Kivy框架和KivyMD 2.0.1 dev0进行开发。改版遵循模块化、系统化、统一化、标准化原则，便于后续维护和扩展。

## 技术架构

### 前端技术栈
- **框架**: Kivy + KivyMD 2.0.1 dev0
- **UI组件**: Material Design 3.0规范
- **状态管理**: Kivy属性绑定机制
- **网络通信**: requests库 + 异步处理
- **数据存储**: SQLite本地缓存 + 后端API同步

### 后端集成
- **API接口**: 基于YUN/backend/的聚合API架构
- **数据格式**: JSON标准格式
- **认证机制**: Token-based认证
- **文件上传**: 多媒体文件处理支持

## UI设计规范

### 设计原则
1. **一致性**: 统一的颜色、字体、间距、图标风格
2. **可访问性**: 支持不同屏幕尺寸和分辨率
3. **响应式**: 适配手机和平板设备
4. **用户友好**: 简洁直观的交互设计

### 视觉规范
```python
# 颜色规范（基于theme.py实际定义）
COLOR_SCHEME = {
    # 基础主题色彩
    'primary': '#1976D2',           # 主色调（蓝色）
    'primary_variant': '#1565C0',   # 主色调变体
    'secondary': '#03DAC6',         # 辅助色（青色）
    'secondary_variant': '#018786', # 辅助色变体
    'accent': '#FF4081',            # 强调色（粉色）
    
    # 表面和背景色
    'surface': '#FFFFFF',           # 表面色
    'background': '#FAFAFA',        # 背景色
    'card_background': '#FFFFFF',   # 卡片背景
    
    # 状态色彩
    'error': '#B00020',             # 错误色（深红）
    'warning': '#FF6F00',           # 警告色（橙色）
    'success': '#00C853',           # 成功色（绿色）
    'info': '#2196F3',              # 信息色（蓝色）
    
    # 健康相关专用色彩
    'health_data_color': '#4CAF50',      # 健康数据色（绿色）
    'health_risk_color': '#FF9800',      # 健康风险色（橙色）
    'medical_service_color': '#2196F3',  # 医疗服务色（蓝色）
    'chronic_disease_color': '#9C27B0',  # 慢病管理色（紫色）
    
    # 医疗记录分类色彩
    'inpatient_color': '#F44336',        # 住院记录（红色）
    'outpatient_color': '#FF9800',       # 门诊记录（橙色）
    'lab_report_color': '#4CAF50',       # 检验报告（绿色）
    'imaging_color': '#2196F3',          # 影像报告（蓝色）
    'medication_color': '#9C27B0',       # 用药记录（紫色）
    'other_record_color': '#607D8B',     # 其他记录（灰蓝）
    
    # 文字色彩
    'text_primary': '#212121',      # 主要文字
    'text_secondary': '#757575',    # 次要文字
    'text_hint': '#9E9E9E',         # 提示文字
    'text_disabled': '#BDBDBD',     # 禁用文字
    'text_on_primary': '#FFFFFF',   # 主色上的文字
    'text_on_secondary': '#000000', # 辅助色上的文字
    
    # 分割线和边框
    'divider': '#E0E0E0',           # 分割线
    'outline': '#E0E0E0'            # 边框
}

# 字体规范
TYPOGRAPHY = {
    'h1': {'size': '24dp', 'weight': 'bold'},
    'h2': {'size': '20dp', 'weight': 'bold'},
    'h3': {'size': '18dp', 'weight': 'medium'},
    'body1': {'size': '16dp', 'weight': 'normal'},
    'body2': {'size': '14dp', 'weight': 'normal'},
    'caption': {'size': '12dp', 'weight': 'normal'}
}
FONT_FAMILY = "NotoSansSC"

# 间距规范
SPACING = {
    'xs': '4dp',
    'sm': '8dp',
    'md': '16dp',
    'lg': '24dp',
    'xl': '32dp'
}
```

## 主页架构设计

### 整体布局
```
┌─────────────────────────────────┐
│           顶部导航栏              │
├─────────────────────────────────┤
│                                 │
│         四大功能模块              │
│    ┌─────────┬─────────┐        │
│    │健康资料  │健康风险  │        │
│    │管理     │管理     │        │
│    ├─────────┼─────────┤        │
│    │慢病管理  │就医服务  │        │
│    └─────────┴─────────┘        │
│                                 │
├─────────────────────────────────┤
│           底部导航栏              │
└─────────────────────────────────┘
```

### 导航结构
```python
NAVIGATION_STRUCTURE = {
    'homepage': {
        'health_data_management': {
            'health_overview': '健康状态总览',
            'basic_health_info': '基本健康信息',
            'health_records': '健康资料传阅',
            'questionnaires': '调查问卷/评估量表',
            'medication_records': '用药记录',
            'medical_reports': '历年体检报告',
            'other_records': '其它记录',
            'health_diary': '健康日记',
            'management_log': '管理日志'
        },
        'health_risk_management': {
            'cancer_risk_assessment': '恶性病高风险评估',
            'chronic_disease_risk': '慢性病高风险评估',
            'personalized_checkup': '个性化健康体检方案'
        },
        'chronic_disease_management': {
            'hypertension_module': '高血压病模块',
            'diabetes_module': '糖尿病模块',
            'cardiovascular_module': '心血管病模块',
            'cerebrovascular_module': '脑血管病模块',
            'integrated_management': '异病共管模块'
        },
        'medical_services': {
            'voice_triage': '语音分诊',
            'companion_service': '陪诊服务'
        }
    }
}
```

## 模块详细设计

### 1. 健康资料管理模块

#### 1.1 健康状态总览
```python
class HealthOverviewScreen(MDScreen):
    """
    健康状态总览界面
    - 只读模式，展示自动总结的健康信息
    - 数据来源：后端聚合API
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.build_ui()
    
    def build_ui(self):
        # 实现UI布局
        pass
    
    def load_health_summary(self):
        # 从后端加载健康总结数据
        pass
```

#### 1.2 基本健康信息
```python
class BasicHealthInfoScreen(MDScreen):
    """
    基本健康信息问卷界面
    - 支持录入、修改、提交
    - 表单验证和数据同步
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.form_data = {}
        self.build_form()
    
    def build_form(self):
        # 动态构建表单
        pass
    
    def validate_form(self):
        # 表单验证逻辑
        pass
    
    def submit_form(self):
        # 提交到后端API
        pass
```

#### 1.3 健康资料传阅
```python
class HealthRecordsScreen(MDScreen):
    """
    健康资料传阅界面
    - 上传区域：资料类型、上传方式、文件选择
    - 文件列表：分类查看、搜索功能
    """
    
    # 资料类型配置
    RECORD_TYPES = {
        '住院资料': ['入院记录', '出院小结', '手术记录', '其它记录'],
        '门诊资料': ['门诊病历', '治疗记录'],
        '检验报告': ['血常规', '生化检查', '免疫检查', '微生物检查'],
        '技诊报告': ['心电图', '胸片', '胸部CT', '头颅MR', '胃肠镜']
    }
    
    # 上传方式
    UPLOAD_METHODS = ['直接上传', '二维码上传', '拍照上传']
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.build_upload_area()
        self.build_file_list()
    
    def build_upload_area(self):
        # 构建上传区域UI
        pass
    
    def build_file_list(self):
        # 构建文件列表UI
        pass
    
    def upload_file(self, file_path, record_type, sub_type):
        # 文件上传逻辑
        pass
    
    def view_file(self, file_id):
        # 查看文件（原始/结构化）
        pass
    
    def delete_file(self, file_id):
        # 删除文件
        pass
```

#### 1.4 调查问卷/评估量表
```python
class QuestionnaireScreen(MDScreen):
    """
    调查问卷/评估量表界面
    - 问卷列表：未完成、已完成
    - 搜索功能：从云端获取
    - 结果查询：查看评估结果
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.questionnaire_list = []
        self.build_ui()
    
    def load_questionnaires(self):
        # 从后端加载问卷列表
        pass
    
    def search_questionnaires(self, keyword):
        # 搜索问卷
        pass
    
    def fill_questionnaire(self, questionnaire_id):
        # 填写问卷
        pass
    
    def view_results(self, questionnaire_id):
        # 查看结果
        pass
```

#### 1.5 用药记录
```python
class MedicationRecordsScreen(MDScreen):
    """
    用药记录界面
    - 药品信息：名称、剂量、频次、时间
    - 监控功能：副作用、停药原因
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.medication_list = []
        self.build_ui()
    
    def add_medication(self):
        # 添加用药记录
        pass
    
    def edit_medication(self, med_id):
        # 编辑用药记录
        pass
    
    def set_reminder(self, med_id):
        # 设置用药提醒
        pass
```

#### 1.6 健康日记
```python
class HealthDiaryScreen(MDScreen):
    """
    健康日记界面
    - 模块化设计：血压、血糖、体重等
    - 数据可视化：折线图展示
    - 设备集成：支持可穿戴设备数据
    """
    
    # 健康指标模块
    HEALTH_MODULES = {
        'blood_pressure': {
            'name': '血压心率管理',
            'fields': ['收缩压', '舒张压', '心率', '测量时间', '备注'],
            'chart_type': 'line',
            'recommended_times': ['早上8点前', '晚餐前7点前']
        },
        'blood_sugar': {
            'name': '血糖管理',
            'fields': ['血糖值', '测量时间点', '进餐情况', '备注'],
            'chart_type': 'line',
            'time_points': ['早餐前', '早餐后2小时', '午餐前', '午餐后2小时', '晚餐前', '晚餐后2小时', '睡前']
        },
        'blood_oxygen': {
            'name': '血氧管理',
            'fields': ['血氧饱和度', '测量时间', '备注'],
            'chart_type': 'line'
        },
        'weight': {
            'name': '体重管理',
            'fields': ['体重', '测量时间', '备注'],
            'chart_type': 'line'
        },
        'exercise': {
            'name': '运动管理',
            'fields': ['运动类型', '运动时长', '运动强度', '消耗卡路里', '时间'],
            'chart_type': 'bar'
        },
        'sleep': {
            'name': '睡眠管理',
            'fields': ['入睡时间', '起床时间', '睡眠时长', '睡眠质量', '备注'],
            'chart_type': 'bar'
        },
        'symptoms': {
            'name': '症状记录',
            'fields': ['症状描述', '严重程度', '持续时间', '诱发因素', '记录时间'],
            'chart_type': 'text'
        }
    }
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.selected_modules = []
        self.build_ui()
    
    def add_module(self, module_type):
        # 添加健康管理模块
        pass
    
    def record_data(self, module_type, data):
        # 记录健康数据
        pass
    
    def sync_device_data(self, device_type):
        # 同步可穿戴设备数据
        pass
    
    def generate_chart(self, module_type, time_range):
        # 生成数据图表
        pass
```

### 2. 健康风险管理模块

```python
class HealthRiskManagementScreen(MDScreen):
    """
    健康风险管理界面
    - AI驱动的风险评估
    - 个性化建议生成
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.build_ui()
    
    def assess_cancer_risk(self):
        # 恶性病风险评估
        pass
    
    def assess_chronic_disease_risk(self):
        # 慢性病风险评估
        pass
    
    def generate_checkup_plan(self):
        # 生成个性化体检方案
        pass
```

### 3. 慢病管理模块

```python
class ChronicDiseaseManagementScreen(MDScreen):
    """
    慢病管理界面
    - 模块化疾病管理
    - 异病共管功能
    """
    
    # 慢病管理模块配置
    DISEASE_MODULES = {
        'hypertension': {
            'name': '高血压病管理',
            'components': ['血压管理', '体重管理', '用药管理', '副作用监测'],
            'target_values': {'收缩压': '<140mmHg', '舒张压': '<90mmHg'}
        },
        'diabetes': {
            'name': '糖尿病管理',
            'components': ['血糖管理', '体重管理', '用药管理', '副作用监测'],
            'target_values': {'空腹血糖': '<7.0mmol/L', '餐后2h血糖': '<10.0mmol/L'}
        },
        'cardiovascular': {
            'name': '心血管病管理',
            'components': ['心率管理', '血压管理', '用药管理', '运动管理']
        },
        'cerebrovascular': {
            'name': '脑血管病管理',
            'components': ['血压管理', '用药管理', '康复训练', '认知评估']
        }
    }
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.active_modules = []
        self.build_ui()
    
    def activate_module(self, disease_type):
        # 激活疾病管理模块
        pass
    
    def integrated_management(self):
        # 异病共管界面
        pass
```

### 4. 就医服务模块

```python
class MedicalServicesScreen(MDScreen):
    """
    就医服务界面
    - 语音分诊功能
    - 陪诊服务预约
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.build_ui()
    
    def voice_triage(self):
        # 语音分诊功能
        pass
    
    def companion_service(self):
        # 陪诊服务
        pass

class VoiceTriageScreen(MDScreen):
    """
    语音分诊界面
    - AI语音交互
    - 自动生成就诊建议
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.conversation_history = []
        self.build_ui()
    
    def start_voice_chat(self):
        # 开始语音对话
        pass
    
    def process_voice_input(self, audio_data):
        # 处理语音输入
        pass
    
    def generate_medical_summary(self):
        # 生成就诊摘要
        pass

class CompanionServiceScreen(MDScreen):
    """
    陪诊服务界面
    - 服务预约
    - 费用计算
    """
    
    SERVICE_OPTIONS = {
        'registration': '挂号服务',
        'transportation': '上门接送',
        'accommodation': '住宿安排',
        'meals': '饮食安排'
    }
    
    PRICING_MODEL = {
        'hourly': '计时收费',
        'per_service': '按次收费',
        'package': '套餐服务'
    }
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.build_ui()
    
    def book_companion_service(self):
        # 预约陪诊服务
        pass
    
    def calculate_cost(self, services, duration):
        # 计算服务费用
        pass
```

## API接口设计

### 接口规范
```python
API_ENDPOINTS = {
    # 健康资料管理
    'health_overview': '/api/health/overview',
    'basic_health_info': '/api/health/basic-info',
    'health_records': '/api/health/records',
    'questionnaires': '/api/questionnaires',
    'medication_records': '/api/medications',
    'health_diary': '/api/health/diary',
    
    # 健康风险管理
    'risk_assessment': '/api/risk/assessment',
    'checkup_plan': '/api/health/checkup-plan',
    
    # 慢病管理
    'chronic_disease': '/api/chronic-disease',
    
    # 就医服务
    'voice_triage': '/api/medical/voice-triage',
    'companion_service': '/api/medical/companion-service',
    
    # 文件上传
    'file_upload': '/api/files/upload',
    'file_download': '/api/files/download'
}

# 请求格式标准
REQUEST_FORMAT = {
    'headers': {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer {token}'
    },
    'response_format': {
        'success': True,
        'data': {},
        'message': '',
        'code': 200
    }
}
```

### 数据模型
```python
# 健康记录数据模型
class HealthRecord:
    def __init__(self):
        self.id = None
        self.user_id = None
        self.record_type = None  # 一级分类
        self.sub_type = None     # 二级分类
        self.file_path = None
        self.file_type = None
        self.upload_time = None
        self.structured_data = None
        self.tags = []

# 健康日记数据模型
class HealthDiaryEntry:
    def __init__(self):
        self.id = None
        self.user_id = None
        self.module_type = None
        self.data = {}
        self.timestamp = None
        self.device_source = None
        self.notes = None

# 陪诊服务数据模型
class CompanionService:
    def __init__(self):
        self.id = None
        self.user_id = None
        self.companion_id = None
        self.services = []
        self.appointment_time = None
        self.duration = None
        self.cost = None
        self.status = None
```

## 实施计划

### 第一阶段：基础架构搭建（2周）
1. **项目结构设计**
   - 创建模块化目录结构
   - 配置开发环境
   - 建立代码规范

2. **核心组件开发**
   - 基础UI组件库
   - 网络请求封装
   - 数据存储管理
   - 导航系统

### 第二阶段：健康资料管理模块（3周）
1. **Week 1**: 健康状态总览 + 基本健康信息
2. **Week 2**: 健康资料传阅 + 调查问卷
3. **Week 3**: 用药记录 + 健康日记

### 第三阶段：慢病管理模块（2周）
1. **Week 1**: 高血压病模块 + 糖尿病模块
2. **Week 2**: 心血管病模块 + 异病共管

### 第四阶段：就医服务模块（2周）
1. **Week 1**: 语音分诊功能
2. **Week 2**: 陪诊服务系统

### 第五阶段：健康风险管理模块（1周）
1. 风险评估算法集成
2. 个性化建议生成

### 第六阶段：测试与优化（2周）
1. **Week 1**: 功能测试 + 性能优化
2. **Week 2**: 用户体验测试 + Bug修复

## 质量保证

### 代码规范
```python
# 文件命名规范
# screens/health_data/health_overview_screen.py
# components/charts/line_chart_component.py
# services/api/health_api_service.py
# models/health_record_model.py

# 类命名规范
class HealthOverviewScreen(MDScreen):  # 大驼峰
    pass

# 方法命名规范
def load_health_data(self):  # 小写+下划线
    pass

# 常量命名规范
API_BASE_URL = "https://api.example.com"  # 大写+下划线
```

### 测试策略
1. **单元测试**: 每个组件独立测试
2. **集成测试**: API接口测试
3. **UI测试**: 界面交互测试
4. **性能测试**: 响应时间和内存使用
5. **兼容性测试**: 不同设备和系统版本

### 文档维护
1. **API文档**: 接口说明和示例
2. **组件文档**: UI组件使用说明
3. **部署文档**: 安装和配置指南
4. **用户手册**: 功能使用说明

## 扩展性考虑

### 模块扩展
- 新增健康指标模块
- 新增疾病管理模块
- 新增医疗服务类型

### 技术扩展
- AI算法集成
- 物联网设备接入
- 区块链健康数据
- 5G网络优化

### 国际化支持
- 多语言界面
- 本地化医疗标准
- 时区和日期格式

## 风险评估与应对

### 技术风险
1. **KivyMD版本兼容性**: 使用稳定版本，建立版本管理
2. **性能问题**: 代码优化，异步处理
3. **数据安全**: 加密传输，本地加密存储

### 业务风险
1. **需求变更**: 模块化设计，便于调整
2. **用户体验**: 持续用户反馈，迭代优化
3. **法规合规**: 遵循医疗数据保护法规

## 主题使用指南

### theme.py集成说明
基于项目中的`theme.py`文件，所有UI组件应严格按照以下方式使用主题颜色：

```python
# 正确的主题颜色引用方式
from theme import AppTheme

# 健康模块颜色使用
modules_data = [
    {
        'title': '健康资料管理',
        'icon': 'folder-heart',
        'color': AppTheme.HEALTH_DATA_COLOR,  # 使用正确的属性名
        'screen': 'health_data_management'
    },
    {
        'title': '健康风险管理', 
        'icon': 'shield-alert',
        'color': AppTheme.HEALTH_RISK_COLOR,  # 使用正确的属性名
        'screen': 'health_risk_management'
    },
    {
        'title': '慢病管理',
        'icon': 'heart-pulse',
        'color': AppTheme.WARNING_COLOR,      # 使用正确的属性名
        'screen': 'chronic_disease_management'
    },
    {
        'title': '就医服务',
        'icon': 'hospital',
        'color': AppTheme.MEDICAL_SERVICE_COLOR,  # 使用正确的属性名
        'screen': 'medical_services'
    }
]

# 医疗记录分类颜色使用
record_colors = {
    '住院资料': AppTheme.INPATIENT_COLOR,
    '门诊资料': AppTheme.OUTPATIENT_COLOR,
    '检验报告': AppTheme.LAB_REPORT_COLOR,
    '技诊报告': AppTheme.IMAGING_COLOR,
    '用药记录': AppTheme.MEDICATION_COLOR,
    '其他记录': AppTheme.OTHER_RECORD_COLOR
}
```

### 主题属性对照表
| 功能模块 | 推荐使用的theme.py属性 | 颜色值 |
|---------|---------------------|--------|
| 健康资料管理 | `AppTheme.HEALTH_DATA_COLOR` | #4CAF50 |
| 健康风险管理 | `AppTheme.HEALTH_RISK_COLOR` | #FF9800 |
| 慢病管理 | `AppTheme.WARNING_COLOR` | #FF6F00 |
| 就医服务 | `AppTheme.MEDICAL_SERVICE_COLOR` | #2196F3 |
| 住院记录 | `AppTheme.INPATIENT_COLOR` | #F44336 |
| 门诊记录 | `AppTheme.OUTPATIENT_COLOR` | #FF9800 |
| 检验报告 | `AppTheme.LAB_REPORT_COLOR` | #4CAF50 |
| 影像报告 | `AppTheme.IMAGING_COLOR` | #2196F3 |
| 用药记录 | `AppTheme.MEDICATION_COLOR` | #9C27B0 |
| 其他记录 | `AppTheme.OTHER_RECORD_COLOR` | #607D8B |

### 注意事项
1. **严禁硬编码颜色值**：所有颜色必须通过`AppTheme`类引用
2. **属性名称准确性**：确保使用theme.py中实际存在的属性名
3. **主题一致性**：保持整个应用的视觉风格统一
4. **扩展性考虑**：新增颜色应先在theme.py中定义，再在UI中使用

## 总结

本文档为健康管理APP个人用户主页UI改版提供了全面的技术指导和实施计划。通过模块化设计、标准化开发流程、系统化测试策略以及严格的主题管理，确保项目的高质量交付和后续维护的便利性。

改版将显著提升用户体验，增强功能完整性，为用户提供更加专业和便捷的健康管理服务。所有UI实现必须严格遵循theme.py中的颜色定义，确保视觉风格的一致性和可维护性。