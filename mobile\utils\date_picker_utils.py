"""
日期选择器工具模块
为KivyMD 2.0.1dev0提供统一的日期选择功能
"""

from datetime import datetime, timedelta
from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp


class DatePickerManager:
    """日期选择器管理器"""
    
    def __init__(self):
        self.current_picker = None
        self.callback_function = None
    
    def show_date_picker(self, callback_function, default_date=None, min_date=None, max_date=None, title="选择日期"):
        """
        显示日期选择器
        
        Args:
            callback_function: 回调函数，接收选中的日期
            default_date: 默认日期，datetime对象
            min_date: 最小日期，datetime对象
            max_date: 最大日期，datetime对象
            title: 对话框标题
        """
        try:
            # 尝试使用KivyMD 2.0.1dev0的新日期选择器
            self._show_modern_date_picker(callback_function, default_date, min_date, max_date, title)
        except ImportError:
            # 如果新版本不可用，使用备用方案
            self._show_fallback_date_picker(callback_function, default_date, title)
    
    def _show_modern_date_picker(self, callback_function, default_date, min_date, max_date, title):
        """使用KivyMD 2.0.1dev0的现代日期选择器"""
        try:
            # 尝试新的导入路径
            from kivymd.uix.pickers.datepicker import MDDatePicker
        except ImportError:
            try:
                # 尝试旧的导入路径
                from kivymd.uix.picker import MDDatePicker
            except ImportError:
                # 如果都不行，抛出异常让备用方案处理
                raise ImportError("无法导入MDDatePicker")
        
        # 设置默认日期
        if default_date is None:
            default_date = datetime.now()
        
        self.callback_function = callback_function
        
        # 创建日期选择器
        self.current_picker = MDDatePicker(
            year=default_date.year,
            month=default_date.month,
            day=default_date.day,
        )
        
        # 绑定事件
        self.current_picker.bind(
            on_save=self._on_date_selected,
            on_cancel=self._on_date_cancelled
        )
        
        # 显示选择器
        self.current_picker.open()
        
        KivyLogger.info(f"DatePicker: 显示现代日期选择器，默认日期: {default_date.strftime('%Y-%m-%d')}")
    
    def _show_fallback_date_picker(self, callback_function, default_date, title):
        """备用日期选择器（使用对话框输入）"""
        try:
            from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.app import MDApp
            
            if default_date is None:
                default_date = datetime.now()
            
            self.callback_function = callback_function
            
            # 创建输入框
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                size_hint_y=None,
                height=dp(120)
            )
            
            self.date_input = MDTextField(
                mode="outlined",
                text=default_date.strftime("%Y-%m-%d"),
                size_hint_y=None,
                height=dp(56)
            )
            self.date_input.add_widget(MDTextFieldHintText(text="请输入日期 (YYYY-MM-DD)"))
            content.add_widget(self.date_input)
            
            # 创建对话框
            self.current_picker = MDDialog(
                MDDialogHeadlineText(text=title),
                MDDialogSupportingText(text="请输入日期，格式：YYYY-MM-DD"),
                content,
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=self._on_fallback_cancelled
                    ),
                    MDButton(
                        MDButtonText(text="确定"),
                        style="filled",
                        on_release=self._on_fallback_confirmed
                    )
                ),
                size_hint=(0.8, None),
                height=dp(300)
            )
            
            self.current_picker.open()
            
            KivyLogger.info(f"DatePicker: 显示备用日期选择器，默认日期: {default_date.strftime('%Y-%m-%d')}")
            
        except Exception as e:
            KivyLogger.error(f"DatePicker: 显示备用日期选择器失败: {e}")
            # 最后的备用方案：直接返回当前日期
            if self.callback_function:
                self.callback_function(default_date or datetime.now())
    
    def _on_date_selected(self, instance, value, date_range=None):
        """现代日期选择器的日期选择回调"""
        try:
            if self.callback_function and value:
                self.callback_function(value)
            self._cleanup()
        except Exception as e:
            KivyLogger.error(f"DatePicker: 处理日期选择失败: {e}")
    
    def _on_date_cancelled(self, instance, value=None):
        """现代日期选择器的取消回调"""
        self._cleanup()
    
    def _on_fallback_confirmed(self, *args):
        """备用日期选择器的确认回调"""
        try:
            date_str = self.date_input.text.strip()
            selected_date = datetime.strptime(date_str, "%Y-%m-%d")
            
            if self.callback_function:
                self.callback_function(selected_date)
                
        except ValueError:
            KivyLogger.error(f"DatePicker: 无效的日期格式: {date_str}")
            # 可以在这里显示错误提示
        except Exception as e:
            KivyLogger.error(f"DatePicker: 处理备用日期确认失败: {e}")
        finally:
            self._cleanup()
    
    def _on_fallback_cancelled(self, *args):
        """备用日期选择器的取消回调"""
        self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        if self.current_picker:
            try:
                self.current_picker.dismiss()
            except:
                pass
            self.current_picker = None
        self.callback_function = None


# 全局日期选择器管理器实例
_date_picker_manager = None


def get_date_picker_manager():
    """获取全局日期选择器管理器实例"""
    global _date_picker_manager
    if _date_picker_manager is None:
        _date_picker_manager = DatePickerManager()
    return _date_picker_manager


def show_date_picker(callback_function, default_date=None, min_date=None, max_date=None, title="选择日期"):
    """
    便捷函数：显示日期选择器
    
    Args:
        callback_function: 回调函数，接收选中的日期
        default_date: 默认日期，datetime对象
        min_date: 最小日期，datetime对象  
        max_date: 最大日期，datetime对象
        title: 对话框标题
    """
    manager = get_date_picker_manager()
    manager.show_date_picker(callback_function, default_date, min_date, max_date, title)


def show_medication_start_date_picker(callback_function):
    """显示用药开始日期选择器"""
    show_date_picker(
        callback_function=callback_function,
        default_date=datetime.now(),
        title="选择用药开始日期"
    )


def show_review_date_picker(callback_function):
    """显示复查日期选择器"""
    # 默认设置为一个月后
    default_date = datetime.now() + timedelta(days=30)
    show_date_picker(
        callback_function=callback_function,
        default_date=default_date,
        min_date=datetime.now(),  # 不能选择过去的日期
        title="选择复查日期"
    )


def show_stop_date_picker(callback_function):
    """显示停药日期选择器"""
    show_date_picker(
        callback_function=callback_function,
        default_date=datetime.now(),
        title="选择停药日期"
    )
