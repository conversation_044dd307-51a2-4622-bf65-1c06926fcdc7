# -*- coding: utf-8 -*-
"""
维度功能使用示例

此脚本展示了如何使用新增的维度功能，包括：
1. 创建带维度的量表模板
2. 计算维度分数
3. 生成维度分析报告
"""

import sys
import os
from datetime import datetime
from sqlalchemy.orm import Session

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from app.db.base_session import get_db
from app.models.assessment import AssessmentTemplate, AssessmentTemplateQuestion, AssessmentResponse
from app.models.enums import AssessmentType
from app.clinical_scales.generators.assessment_generator import AssessmentGenerator
from app.clinical_scales.services.dimension_service import DimensionService


def create_sample_assessment_with_dimensions(db: Session):
    """创建一个带维度的示例量表"""
    
    # 定义维度
    dimensions = [
        {
            "key": "cognitive",
            "name": "认知功能",
            "name_en": "Cognitive Function",
            "description": "评估认知能力相关指标",
            "weight": 0.4,
            "max_score": 20,
            "question_ids": ["q1", "q2"]
        },
        {
            "key": "emotional",
            "name": "情绪状态",
            "name_en": "Emotional State",
            "description": "评估情绪状态相关指标",
            "weight": 0.3,
            "max_score": 15,
            "question_ids": ["q3"]
        },
        {
            "key": "behavioral",
            "name": "行为表现",
            "name_en": "Behavioral Performance",
            "description": "评估行为表现相关指标",
            "weight": 0.3,
            "max_score": 15,
            "question_ids": ["q4"]
        }
    ]
    
    # 定义问题
    questions = [
        {
            "question_id": "q1",
            "question_text": "您的记忆力如何？",
            "question_type": "single_choice",
            "order": 1,
            "dimension_key": "cognitive",
            "options": [
                {"value": "excellent", "label": "非常好", "score": 10},
                {"value": "good", "label": "好", "score": 7},
                {"value": "fair", "label": "一般", "score": 4},
                {"value": "poor", "label": "差", "score": 1}
            ]
        },
        {
            "question_id": "q2",
            "question_text": "您的注意力集中程度如何？",
            "question_type": "single_choice",
            "order": 2,
            "dimension_key": "cognitive",
            "options": [
                {"value": "excellent", "label": "非常好", "score": 10},
                {"value": "good", "label": "好", "score": 7},
                {"value": "fair", "label": "一般", "score": 4},
                {"value": "poor", "label": "差", "score": 1}
            ]
        },
        {
            "question_id": "q3",
            "question_text": "您最近的情绪状态如何？",
            "question_type": "single_choice",
            "order": 3,
            "dimension_key": "emotional",
            "options": [
                {"value": "very_positive", "label": "非常积极", "score": 15},
                {"value": "positive", "label": "积极", "score": 12},
                {"value": "neutral", "label": "中性", "score": 8},
                {"value": "negative", "label": "消极", "score": 4},
                {"value": "very_negative", "label": "非常消极", "score": 1}
            ]
        },
        {
            "question_id": "q4",
            "question_text": "您的日常活动参与度如何？",
            "question_type": "single_choice",
            "order": 4,
            "dimension_key": "behavioral",
            "options": [
                {"value": "very_active", "label": "非常积极", "score": 15},
                {"value": "active", "label": "积极", "score": 12},
                {"value": "moderate", "label": "适中", "score": 8},
                {"value": "passive", "label": "被动", "score": 4},
                {"value": "very_passive", "label": "非常被动", "score": 1}
            ]
        }
    ]
    
    # 定义结果范围
    result_ranges = [
        {"min_score": 0, "max_score": 20, "result": "需要关注", "description": "建议寻求专业帮助"},
        {"min_score": 21, "max_score": 35, "result": "一般", "description": "状态一般，建议适当调整"},
        {"min_score": 36, "max_score": 50, "result": "良好", "description": "状态良好，继续保持"}
    ]
    
    # 创建量表模板
    generator = AssessmentGenerator(db)
    template = generator.create_assessment_template(
        name="综合健康评估量表（维度示例）",
        assessment_type=AssessmentType.PSYCHOLOGICAL,
        description="这是一个展示维度功能的示例量表",
        instructions="请根据您的实际情况选择最符合的选项",
        scoring_method="各维度分数加权求和",
        max_score=50.0,
        result_ranges=result_ranges,
        questions=questions,
        dimensions=dimensions,
        created_by=1
    )
    
    print(f"创建了带维度的量表模板，ID: {template.id}")
    return template


def create_sample_response(db: Session, template: AssessmentTemplate):
    """创建一个示例回答"""
    
    # 模拟用户回答
    sample_answers = {
        "q1": "good",      # 记忆力：好 (7分)
        "q2": "fair",      # 注意力：一般 (4分)
        "q3": "positive",  # 情绪：积极 (12分)
        "q4": "active"     # 行为：积极 (12分)
    }
    
    # 创建评估回答记录
    response = AssessmentResponse(
        assessment_id=1,  # 假设评估ID为1
        custom_id="test_user",
        answers=sample_answers,
        score=35.0,  # 总分：7+4+12+12=35
        result="良好",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    db.add(response)
    db.commit()
    db.refresh(response)
    
    print(f"创建了示例回答，ID: {response.id}")
    return response


def demonstrate_dimension_calculation(db: Session, response: AssessmentResponse, template: AssessmentTemplate):
    """演示维度计算功能"""
    
    print("\n=== 维度计算演示 ===")
    
    # 创建维度服务
    dimension_service = DimensionService(db)
    
    # 计算维度分数
    dimension_scores = dimension_service.calculate_assessment_dimensions(response, template)
    
    print("\n维度分数计算结果：")
    for dimension_key, scores in dimension_scores.items():
        print(f"\n{scores['name']} ({dimension_key}):")
        print(f"  得分: {scores['score']}/{scores['max_score']}")
        print(f"  百分比: {scores['percentage']}%")
        print(f"  水平: {scores['level']}")
        print(f"  有效问题数: {scores['valid_questions']}")
    
    # 生成分析报告
    report = dimension_service.generate_dimension_report(dimension_scores)
    
    print("\n=== 维度分析报告 ===")
    print(f"总结: {report['summary']}")
    print(f"平均百分比: {report['average_percentage']}%")
    print(f"总维度数: {report['total_dimensions']}")
    
    if report['strengths']:
        print("\n优势维度:")
        for strength in report['strengths']:
            print(f"  - {strength['dimension']}: {strength['percentage']}% ({strength['level']})")
    
    if report['weaknesses']:
        print("\n需要改进的维度:")
        for weakness in report['weaknesses']:
            print(f"  - {weakness['dimension']}: {weakness['percentage']}% ({weakness['level']})")
    
    if report['recommendations']:
        print("\n改进建议:")
        for recommendation in report['recommendations']:
            print(f"  - {recommendation}")
    
    # 保存维度分数到数据库
    response.dimension_scores = dimension_scores
    db.commit()
    
    print("\n维度分数已保存到数据库")
    
    return dimension_scores, report


def main():
    """主函数"""
    print("维度功能使用示例")
    print("=" * 50)
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 1. 创建带维度的量表模板
        print("\n1. 创建带维度的量表模板...")
        template = create_sample_assessment_with_dimensions(db)
        
        # 2. 创建示例回答
        print("\n2. 创建示例回答...")
        response = create_sample_response(db, template)
        
        # 3. 演示维度计算
        print("\n3. 演示维度计算功能...")
        dimension_scores, report = demonstrate_dimension_calculation(db, response, template)
        
        print("\n=== 示例完成 ===")
        print("\n维度功能已成功集成到系统中，包括：")
        print("✓ 量表模板支持维度定义")
        print("✓ 问题支持维度归属")
        print("✓ 自动计算维度分数")
        print("✓ 生成维度分析报告")
        print("✓ 提供API接口")
        
        print("\n可以通过以下API端点使用维度功能：")
        print("- GET /api/dimensions/assessment/{template_id}/dimensions - 获取量表维度定义")
        print("- GET /api/dimensions/assessment/response/{response_id}/dimension-scores - 获取维度分数")
        print("- POST /api/dimensions/assessment/response/{response_id}/recalculate-dimensions - 重新计算维度分数")
        
    except Exception as e:
        print(f"\n错误: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    main()