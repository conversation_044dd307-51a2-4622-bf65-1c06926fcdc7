# 开发者指南

## 项目架构

### 技术栈

- **前端框架**: Kivy + KivyMD 2.0.1 dev0
- **编程语言**: Python 3.8+
- **UI库**: KivyMD Material Design
- **网络库**: Requests
- **数据存储**: SQLite / JSON

### 项目结构

```
health-management-mobile/
├── main_optimized.py          # 优化版主程序入口
├── main.py                    # 原版主程序入口
├── theme_optimized.py         # 优化版主题系统
├── api/                       # API相关模块
│   ├── api_config_optimized.py
│   ├── api_client_optimized.py
│   └── api_config.json
├── screens/                   # 界面模块
│   ├── homepage_screen_optimized.py
│   ├── base_screen.py
│   └── ...
├── utils/                     # 工具模块
│   ├── performance_monitor.py
│   └── ...
├── widgets/                   # 自定义组件
├── assets/                    # 资源文件
├── tests/                     # 测试文件
├── docs/                      # 文档
└── README.md
```

## 核心组件

### 1. 主题系统 (theme_optimized.py)

**OptimizedTheme类**:
- 统一的颜色管理
- 响应式字体系统
- 深色模式支持
- 模块化颜色方案

```python
from theme_optimized import OptimizedTheme

# 创建主题实例
theme = OptimizedTheme()

# 获取颜色
primary_color = theme.colors.primary

# 切换深色模式
theme.set_dark_mode(True)

# 获取模块颜色
health_color = theme.get_module_color('health')
```

### 2. API客户端 (api_client_optimized.py)

**OptimizedAPIClient类**:
- 异步请求支持
- 自动重试机制
- 备用服务器切换
- 统一错误处理

```python
from api.api_client_optimized import OptimizedAPIClient

# 创建客户端
client = OptimizedAPIClient()

# 发送请求
response = await client.request(
    method='GET',
    endpoint='/api/v1/health/records',
    headers={'Authorization': 'Bearer token'}
)
```

### 3. 性能监控 (performance_monitor.py)

**PerformanceMonitor类**:
- 实时性能监控
- 用户行为分析
- 性能报告生成
- 异常检测

```python
from utils.performance_monitor import PerformanceMonitor

# 创建监控器
monitor = PerformanceMonitor()

# 记录指标
monitor.record_metric('api_call_time', 150.0, 'ms')

# 获取报告
summary = monitor.get_performance_summary()
```

## 开发规范

### 代码风格

1. **PEP 8**: 遵循Python代码风格指南
2. **类型注解**: 使用类型提示增强代码可读性
3. **文档字符串**: 为所有公共方法添加docstring
4. **命名规范**: 使用有意义的变量和函数名

### 文件组织

1. **模块化**: 按功能划分模块
2. **单一职责**: 每个文件专注单一功能
3. **依赖管理**: 明确模块间依赖关系
4. **配置分离**: 配置与代码分离

### Git工作流

1. **分支策略**: 使用Git Flow分支模型
2. **提交规范**: 使用语义化提交信息
3. **代码审查**: 所有代码变更需要审查
4. **持续集成**: 自动化测试和部署

## 测试指南

### 测试框架

使用Python内置的unittest框架：

```python
import unittest
from unittest.mock import Mock, patch

class TestOptimizedTheme(unittest.TestCase):
    def setUp(self):
        self.theme = OptimizedTheme()
    
    def test_color_palette(self):
        self.assertIsNotNone(self.theme.colors.primary)
```

### 测试类型

1. **单元测试**: 测试单个函数或方法
2. **集成测试**: 测试模块间交互
3. **UI测试**: 测试用户界面
4. **性能测试**: 测试应用性能

### 运行测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试
python -m unittest tests.test_optimized_components

# 生成覆盖率报告
coverage run run_tests.py
coverage report
```

## 部署指南

### 构建应用

使用PyInstaller打包应用：

```bash
# 安装PyInstaller
pip install pyinstaller

# 打包应用
pyinstaller --onefile --windowed main_optimized.py
```

### 发布流程

1. **版本标记**: 更新版本号
2. **测试验证**: 运行完整测试套件
3. **构建包**: 生成安装包
4. **质量检查**: 验证安装包
5. **发布部署**: 发布到应用商店

### 监控和维护

1. **性能监控**: 监控应用性能指标
2. **错误追踪**: 收集和分析错误日志
3. **用户反馈**: 处理用户反馈和建议
4. **定期更新**: 定期发布更新版本

## 扩展开发

### 添加新功能

1. **需求分析**: 明确功能需求
2. **设计方案**: 设计技术方案
3. **编码实现**: 实现功能代码
4. **测试验证**: 编写和运行测试
5. **文档更新**: 更新相关文档

### 自定义组件

```python
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel

class CustomHealthCard(MDBoxLayout):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.add_widget(MDLabel(text='自定义健康卡片'))
```

### 插件系统

应用支持插件扩展：

1. **插件接口**: 定义标准插件接口
2. **插件加载**: 动态加载插件模块
3. **插件管理**: 启用/禁用插件
4. **插件商店**: 插件分发平台

## 性能优化

### 优化策略

1. **代码优化**: 优化算法和数据结构
2. **内存管理**: 避免内存泄漏
3. **异步处理**: 使用异步I/O
4. **缓存机制**: 合理使用缓存

### 监控指标

- **启动时间**: 应用启动耗时
- **内存使用**: 内存占用情况
- **CPU使用**: CPU使用率
- **网络延迟**: API请求延迟

## 安全考虑

### 数据安全

1. **加密存储**: 敏感数据加密存储
2. **安全传输**: HTTPS加密传输
3. **访问控制**: 严格的权限控制
4. **数据备份**: 定期数据备份

### 代码安全

1. **输入验证**: 验证所有用户输入
2. **SQL注入**: 防止SQL注入攻击
3. **XSS防护**: 防止跨站脚本攻击
4. **依赖安全**: 定期更新依赖库

## 贡献指南

### 参与贡献

1. **Fork项目**: 创建项目分支
2. **创建分支**: 为新功能创建分支
3. **编写代码**: 实现功能并测试
4. **提交PR**: 提交Pull Request
5. **代码审查**: 参与代码审查

### 问题报告

使用GitHub Issues报告问题：

1. **问题描述**: 详细描述问题
2. **重现步骤**: 提供重现步骤
3. **环境信息**: 提供环境信息
4. **期望行为**: 描述期望行为

欢迎所有开发者参与项目贡献！
