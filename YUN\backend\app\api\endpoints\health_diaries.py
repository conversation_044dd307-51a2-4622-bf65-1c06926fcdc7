from datetime import datetime
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.follow_up import HealthDiary, HealthDiaryType
from app.api import deps
from app.db.base_session import get_db

router = APIRouter()


@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_health_diaries(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    diary_type: Optional[str] = Query(None, description="日记类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的健康日记列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的健康日记"
        )

    # 构建查询
    query = db.query(HealthDiary).filter(HealthDiary.custom_id == custom_id)
    
    if diary_type:
        query = query.filter(HealthDiary.diary_type == diary_type)
    if start_date:
        query = query.filter(HealthDiary.diary_date >= start_date)
    if end_date:
        query = query.filter(HealthDiary.diary_date <= end_date)
    
    # 分页
    diaries = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "success": True,
        "data": [
            {
                "id": diary.id,
                "custom_id": diary.custom_id,
                "diary_type": diary.diary_type.value if diary.diary_type else None,
                "title": diary.title,
                "content": diary.content,
                "mood_level": diary.mood_level,
                "energy_level": diary.energy_level,
                "pain_level": diary.pain_level,
                "diary_date": diary.diary_date,
                "notes": diary.notes,
                "created_at": diary.created_at,
                "updated_at": diary.updated_at
            }
            for diary in diaries
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_health_diary(
    *,
    db: Session = Depends(get_db),
    diary_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    创建健康日记
    """
    # 验证用户存在
    custom_id = diary_data.get("custom_id")
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限为此用户创建健康日记"
        )

    # 创建健康日记
    diary = HealthDiary(
        custom_id=custom_id,
        diary_type=HealthDiaryType(diary_data.get("diary_type")) if diary_data.get("diary_type") else None,
        title=diary_data.get("title"),
        content=diary_data.get("content"),
        mood_level=diary_data.get("mood_level"),
        energy_level=diary_data.get("energy_level"),
        pain_level=diary_data.get("pain_level"),
        diary_date=diary_data.get("diary_date"),
        notes=diary_data.get("notes")
    )
    
    db.add(diary)
    db.commit()
    db.refresh(diary)
    
    return {
        "success": True,
        "data": {
            "id": diary.id,
            "custom_id": diary.custom_id,
            "diary_type": diary.diary_type.value if diary.diary_type else None,
            "title": diary.title,
            "content": diary.content,
            "mood_level": diary.mood_level,
            "energy_level": diary.energy_level,
            "pain_level": diary.pain_level,
            "diary_date": diary.diary_date,
            "notes": diary.notes,
            "created_at": diary.created_at,
            "updated_at": diary.updated_at
        }
    }


@router.get("/{diary_id}", response_model=Dict[str, Any])
def get_health_diary(
    *,
    db: Session = Depends(get_db),
    diary_id: int = Path(..., description="日记ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取单个健康日记详情
    """
    diary = db.query(HealthDiary).filter(HealthDiary.id == diary_id).first()
    if not diary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到健康日记ID: {diary_id}"
        )

    # 权限校验
    if current_user.custom_id != diary.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此健康日记"
        )

    return {
        "success": True,
        "data": {
            "id": diary.id,
            "custom_id": diary.custom_id,
            "diary_type": diary.diary_type.value if diary.diary_type else None,
            "title": diary.title,
            "content": diary.content,
            "mood_level": diary.mood_level,
            "energy_level": diary.energy_level,
            "pain_level": diary.pain_level,
            "diary_date": diary.diary_date,
            "notes": diary.notes,
            "created_at": diary.created_at,
            "updated_at": diary.updated_at
        }
    }


@router.put("/{diary_id}", response_model=Dict[str, Any])
def update_health_diary(
    *,
    db: Session = Depends(get_db),
    diary_id: int = Path(..., description="日记ID"),
    diary_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新健康日记
    """
    diary = db.query(HealthDiary).filter(HealthDiary.id == diary_id).first()
    if not diary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到健康日记ID: {diary_id}"
        )

    # 权限校验
    if current_user.custom_id != diary.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此健康日记"
        )

    # 更新字段
    for field, value in diary_data.items():
        if field == "diary_type" and value:
            setattr(diary, field, HealthDiaryType(value))
        elif hasattr(diary, field) and field != "id":
            setattr(diary, field, value)
    
    db.commit()
    db.refresh(diary)
    
    return {
        "success": True,
        "data": {
            "id": diary.id,
            "custom_id": diary.custom_id,
            "diary_type": diary.diary_type.value if diary.diary_type else None,
            "title": diary.title,
            "content": diary.content,
            "mood_level": diary.mood_level,
            "energy_level": diary.energy_level,
            "pain_level": diary.pain_level,
            "diary_date": diary.diary_date,
            "notes": diary.notes,
            "created_at": diary.created_at,
            "updated_at": diary.updated_at
        }
    }


@router.delete("/{diary_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_health_diary(
    *,
    db: Session = Depends(get_db),
    diary_id: int = Path(..., description="日记ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    删除健康日记
    """
    diary = db.query(HealthDiary).filter(HealthDiary.id == diary_id).first()
    if not diary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到健康日记ID: {diary_id}"
        )

    # 权限校验
    if current_user.custom_id != diary.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此健康日记"
        )

    db.delete(diary)
    db.commit()