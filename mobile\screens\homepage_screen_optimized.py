from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from datetime import datetime
import json
import os

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

# 导入主题和字体样式
from theme import OptimizedTheme
try:
    from widgets.logo import add_logo_to_layout
except ImportError:
    # 如果logo组件不存在，创建一个空函数
    def add_logo_to_layout(layout):
        pass

# 优化的KV布局定义
KV = '''
<OptimizedModuleCard>:
    orientation: 'vertical'
    size_hint: None, None
    size: dp(160), dp(120)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 3
    padding: dp(12)
    ripple_behavior: True
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        MDIconButton:
            id: module_icon
            icon: root.icon
            theme_icon_color: "Custom"
            icon_color: root.icon_color
            icon_size: dp(32)
            size_hint: None, None
            size: dp(48), dp(48)
            pos_hint: {'center_x': 0.5}
            
        MDLabel:
            id: module_title
            text: root.title
            theme_text_color: "Primary"
            font_style: "Body"
            size_hint_y: None
            height: self.texture_size[1]
            text_size: self.width, None
            halign: "center"
            valign: "middle"

<SectionHeader>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    padding: [dp(16), dp(8)]
    
    MDLabel:
        text: root.title
        theme_text_color: "Primary"
        font_style: "Headline"
        size_hint_x: 0.8
        
    MDIconButton:
        icon: "chevron-right"
        theme_icon_color: "Primary"
        size_hint_x: 0.2
        on_release: root.on_more_click() if hasattr(root, 'on_more_click') else None

<OptimizedHomepageScreen>:
    name: 'homepage'
    
    MDBoxLayout:
        orientation: 'vertical'
        
        # 顶部欢迎区域
        MDCard:
            size_hint_y: None
            height: dp(120)
            md_bg_color: app.theme.PRIMARY_COLOR
            radius: [0, 0, dp(20), dp(20)]
            elevation: 2
            
            MDBoxLayout:
                orientation: 'horizontal'
                padding: dp(20)
                spacing: dp(16)
                
                MDBoxLayout:
                    orientation: 'vertical'
                    
                    MDLabel:
                        id: welcome_label
                        text: "欢迎回来"
                        theme_text_color: "Custom"
                        text_color: app.theme.ON_PRIMARY
                        font_style: "Headline"
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                    MDLabel:
                        id: date_label
                        text: ""
                        theme_text_color: "Custom"
                        text_color: app.theme.ON_PRIMARY
                        font_style: "Body"
                        size_hint_y: None
                        height: self.texture_size[1]
                        
                MDIconButton:
                    icon: "account-circle"
                    theme_icon_color: "Custom"
                    icon_color: app.theme.TEXT_LIGHT
                    icon_size: dp(40)
                    size_hint: None, None
                    size: dp(56), dp(56)
                    on_release: root.navigate_to_profile()
        
        # 主要内容滚动区域
        MDScrollView:
            MDBoxLayout:
                orientation: 'vertical'
                spacing: dp(16)
                padding: dp(16)
                size_hint_y: None
                height: self.minimum_height
                
                # 快速操作区域
                SectionHeader:
                    id: quick_actions_header
                    title: "快速操作"
                    
                MDGridLayout:
                    id: quick_actions_grid
                    cols: 3
                    spacing: dp(12)
                    size_hint_y: None
                    height: self.minimum_height
                    size_hint_y: None
            height: self.minimum_height
                
                # 健康管理区域
                SectionHeader:
                    id: health_management_header
                    title: "健康管理"
                    
                MDGridLayout:
                    id: health_management_grid
                    cols: 2
                    spacing: dp(12)
                    size_hint_y: None
                    height: self.minimum_height
                    size_hint_y: None
            height: self.minimum_height
                
                # 医疗服务区域
                SectionHeader:
                    id: medical_services_header
                    title: "医疗服务"
                    
                MDGridLayout:
                    id: medical_services_grid
                    cols: 2
                    spacing: dp(12)
                    size_hint_y: None
                    height: self.minimum_height
                    size_hint_y: None
            height: self.minimum_height
        
        # 底部导航栏
        MDCard:
            size_hint_y: None
            height: dp(70)
            md_bg_color: app.theme.SURFACE_COLOR
            radius: [dp(20), dp(20), 0, 0]
            elevation: 8
            
            MDBoxLayout:
                id: bottom_nav
                orientation: 'horizontal'
                padding: dp(8)
                spacing: dp(4)
'''

Builder.load_string(KV)

class OptimizedModuleCard(MDCard):
    """优化的模块卡片组件"""
    title = StringProperty()
    icon = StringProperty()
    icon_color = ObjectProperty()
    callback = ObjectProperty()
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.bind(on_release=self.on_card_click)
    
    def on_card_click(self, *args):
        if self.callback:
            self.callback()

class SectionHeader(MDBoxLayout):
    """区域标题组件"""
    title = StringProperty()
    
class NavButton(MDButton):
    """导航按钮组件"""
    icon = StringProperty()
    text = StringProperty()
    active = BooleanProperty(False)
    callback = ObjectProperty()
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.bind(on_release=self.on_button_click)
        
    def on_button_click(self, *args):
        if self.callback:
            self.callback()

class OptimizedHomepageScreen(BaseScreen):
    """优化的首页界面"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.theme = AppTheme()
        self.metrics = AppMetrics()
        
        # 模块配置 - 整合并精简功能
        self.quick_actions = [
            {"title": "健康总览", "icon": "chart-line", "color": self.theme.HEALTH_BLUE, "callback": self.navigate_to_health_overview},
            {"title": "添加记录", "icon": "plus-circle", "color": self.theme.SUCCESS_COLOR, "callback": self.navigate_to_add_record},
            {"title": "用药提醒", "icon": "pill", "color": self.theme.WARNING_COLOR, "callback": self.navigate_to_medication}
        ]
        
        self.health_modules = [
            {"title": "基本信息", "icon": "account-details", "color": self.theme.HEALTH_DATA_COLOR, "callback": self.navigate_to_basic_info},
            {"title": "医疗记录", "icon": "file-document", "color": self.theme.HEALTH_DATA_COLOR, "callback": self.navigate_to_medical_records},
            {"title": "健康评估", "icon": "clipboard-check", "color": self.theme.HEALTH_RISK_COLOR, "callback": self.navigate_to_assessment},
            {"title": "健康日记", "icon": "book-open", "color": self.theme.HEALTH_DATA_COLOR, "callback": self.navigate_to_health_diary}
        ]
        
        self.medical_services = [
            {"title": "语音分诊", "icon": "microphone", "color": self.theme.MEDICAL_SERVICE_COLOR, "callback": self.navigate_to_voice_triage},
            {"title": "陪诊服务", "icon": "account-group", "color": self.theme.MEDICAL_SERVICE_COLOR, "callback": self.navigate_to_companion_service}
        ]
        
        self.nav_items = [
            {"icon": "home", "text": "首页", "callback": lambda: None},
            {"icon": "heart-pulse", "text": "健康", "callback": self.navigate_to_health_data},
            {"icon": "hospital", "text": "医疗", "callback": self.navigate_to_medical_service},
            {"icon": "account", "text": "我的", "callback": self.navigate_to_profile}
        ]
        
    def on_enter(self):
        """页面进入时的初始化"""
        super().on_enter()
        Clock.schedule_once(self.initialize_ui, 0.1)
        
    def initialize_ui(self, dt):
        """初始化UI组件"""
        try:
            self.load_user_data()
            self.set_welcome_message()
            self.setup_quick_actions()
            self.setup_health_modules()
            self.setup_medical_services()
            self.setup_navigation()
        except Exception as e:
            print(f"UI初始化错误: {e}")
            
    def load_user_data(self):
        """加载用户数据"""
        try:
            # 从API获取用户信息
            from api.api_client import APIClient
            api_client = APIClient()
            
            # 检查登录状态
            if not api_client.cloud_api.is_authenticated():
                self.navigate_to_login()
                return
                
            # 获取用户信息
            user_info = api_client.get_user_info()
            if user_info and user_info.get('success'):
                data = user_info.get('data', {})
                self.username = data.get('username', '访客')
                self.gender = data.get('gender', '先生')
            else:
                self.username = '访客'
                self.gender = '先生'
                
        except Exception as e:
            print(f"加载用户数据错误: {e}")
            self.username = '访客'
            self.gender = '先生'
            
    def set_welcome_message(self):
        """设置欢迎消息"""
        try:
            now = datetime.now()
            hour = now.hour
            
            if 5 <= hour < 12:
                greeting = "早上好"
            elif 12 <= hour < 18:
                greeting = "下午好"
            else:
                greeting = "晚上好"
                
            welcome_text = f"{greeting}，{self.username} {self.gender}"
            date_text = now.strftime("%Y年%m月%d日 %A")
            
            self.ids.welcome_label.text = welcome_text
            self.ids.date_label.text = date_text
            
        except Exception as e:
            print(f"设置欢迎消息错误: {e}")
            
    def setup_quick_actions(self):
        """设置快速操作区域"""
        grid = self.ids.quick_actions_grid
        grid.clear_widgets()
        
        for action in self.quick_actions:
            card = OptimizedModuleCard(
                title=action["title"],
                icon=action["icon"],
                icon_color=action["color"],
                callback=action["callback"]
            )
            grid.add_widget(card)
            
    def setup_health_modules(self):
        """设置健康管理模块"""
        grid = self.ids.health_management_grid
        grid.clear_widgets()
        
        for module in self.health_modules:
            card = OptimizedModuleCard(
                title=module["title"],
                icon=module["icon"],
                icon_color=module["color"],
                callback=module["callback"]
            )
            grid.add_widget(card)
            
    def setup_medical_services(self):
        """设置医疗服务模块"""
        grid = self.ids.medical_services_grid
        grid.clear_widgets()
        
        for service in self.medical_services:
            card = OptimizedModuleCard(
                title=service["title"],
                icon=service["icon"],
                icon_color=service["color"],
                callback=service["callback"]
            )
            grid.add_widget(card)
            
    def setup_navigation(self):
        """设置底部导航"""
        nav_box = self.ids.bottom_nav
        nav_box.clear_widgets()
        
        for i, item in enumerate(self.nav_items):
            btn = NavButton(
                icon=item["icon"],
                text=item["text"],
                active=(i == 0),  # 首页默认激活
                callback=item["callback"]
            )
            nav_box.add_widget(btn)
    
    # 导航方法 - 统一API调用
    def navigate_to_health_overview(self):
        """导航到健康总览"""
        self.manager.current = 'health_overview'
        
    def navigate_to_add_record(self):
        """导航到添加记录"""
        self.manager.current = 'add_record'
        
    def navigate_to_medication(self):
        """导航到用药管理"""
        self.manager.current = 'medication_management'
        
    def navigate_to_basic_info(self):
        """导航到基本信息"""
        self.manager.current = 'basic_health_info'
        
    def navigate_to_medical_records(self):
        """导航到医疗记录"""
        self.manager.current = 'medical_records'
        
    def navigate_to_assessment(self):
        """导航到健康评估"""
        self.manager.current = 'health_assessment'
        
    def navigate_to_health_diary(self):
        """导航到健康日记"""
        self.manager.current = 'health_diary'
        
    def navigate_to_voice_triage(self):
        """导航到语音分诊"""
        self.manager.current = 'voice_triage'
        
    def navigate_to_companion_service(self):
        """导航到陪诊服务"""
        self.manager.current = 'companion_service'
        
    def navigate_to_health_data(self):
        """导航到健康资料"""
        self.manager.current = 'health_data'
        
    def navigate_to_medical_service(self):
        """导航到医疗服务"""
        self.manager.current = 'medical_service'
        
    def navigate_to_profile(self):
        """导航到个人中心"""
        self.manager.current = 'profile'
        
    def navigate_to_login(self):
        """导航到登录页面"""
        self.manager.current = 'login'
        
    def show_message(self, message):
        """显示消息提示"""
        snackbar = MDSnackbar(
            MDSnackbarText(text=message),
            y=dp(24),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.9
        )
        snackbar.open()