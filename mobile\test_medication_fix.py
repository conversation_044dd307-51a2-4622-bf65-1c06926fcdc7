#!/usr/bin/env python3
"""
测试用药管理屏幕的token认证修复

这个脚本测试修复后的medication_management_screen是否能正确处理token认证，
不再出现"未找到有效的认证token，无法同步到后端"的警告。
"""

import os
import sys
import logging

# 添加mobile目录到Python路径
mobile_dir = os.path.dirname(os.path.abspath(__file__))
if mobile_dir not in sys.path:
    sys.path.insert(0, mobile_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_medication_screen_auth():
    """测试用药管理屏幕的认证处理"""
    print("\n=== 测试用药管理屏幕认证修复 ===")
    
    try:
        # 模拟应用环境
        class MockApp:
            def __init__(self):
                self.user_data = {
                    'custom_id': 'SM_008',
                    'username': 'markey',
                    'access_token': 'test_token_123'
                }
                self.font_styles = {
                    'BODY_MEDIUM': {
                        'font_size': '14sp',
                        'font_name': 'Roboto'
                    }
                }
        
        # 导入必要的模块
        from kivymd.app import MDApp
        from screens_bak.medication_management_screen import MedicationManagementScreen
        
        # 创建模拟应用
        mock_app = MockApp()
        
        # 创建用药管理屏幕实例
        screen = MedicationManagementScreen()
        screen.app = mock_app
        
        # 模拟ids容器
        class MockContainer:
            def clear_widgets(self):
                pass
            def add_widget(self, widget):
                pass
        
        class MockIds:
            def __init__(self):
                self.medications_container = MockContainer()
        
        screen.ids = MockIds()
        
        print("✓ 成功创建用药管理屏幕实例")
        
        # 测试load_medications方法
        print("\n测试load_medications方法...")
        screen.load_medications()
        print("✓ load_medications方法执行成功，无认证警告")
        
        # 测试save_medication方法
        print("\n测试save_medication方法...")
        screen.dialog = type('MockDialog', (), {'dismiss': lambda: None})()
        screen.editing_medication = None
        screen.medications = []
        
        # 模拟show_info方法
        screen.show_info = lambda msg: print(f"INFO: {msg}")
        
        screen.save_medication(
            name="测试药物",
            dosage="100mg",
            schedule="每日1次",
            start_date="2024-01-01",
            end_date="2024-12-31",
            notes="测试备注"
        )
        print("✓ save_medication方法执行成功，无认证警告")
        
        # 测试delete_medication方法
        print("\n测试delete_medication方法...")
        test_medication = {
            'id': '1',
            'name': '测试药物'
        }
        screen.medications = [test_medication]
        screen.delete_medication(test_medication)
        print("✓ delete_medication方法执行成功，无认证警告")
        
        print("\n🎉 所有测试通过！用药管理屏幕认证修复成功")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auth_manager_integration():
    """测试认证管理器集成"""
    print("\n=== 测试认证管理器集成 ===")
    
    try:
        from utils.auth_manager import get_auth_manager
        
        # 获取认证管理器
        auth_manager = get_auth_manager()
        print("✓ 成功获取认证管理器实例")
        
        # 测试获取用户信息（应该不会触发警告）
        user_info = auth_manager.get_current_user_info()
        if user_info:
            print(f"✓ 获取到用户信息: {user_info.get('custom_id', 'Unknown')}")
        else:
            print("ℹ 当前无用户信息（正常情况）")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证管理器测试失败: {str(e)}")
        return False

def test_cloud_api_silent_mode():
    """测试云API静默模式"""
    print("\n=== 测试云API静默模式 ===")
    
    try:
        from utils.cloud_api import get_cloud_api
        
        # 获取云API实例
        cloud_api = get_cloud_api()
        print("✓ 成功获取云API实例")
        
        # 测试静默设置认证信息
        cloud_api.token = "test_token_123"
        cloud_api.custom_id = "SM_008"
        print("✓ 静默设置认证信息成功")
        
        # 测试认证状态检查
        is_auth = cloud_api.is_authenticated()
        print(f"✓ 认证状态检查: {is_auth}")
        
        return True
        
    except Exception as e:
        print(f"❌ 云API测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试用药管理屏幕token认证修复...")
    
    # 运行所有测试
    tests = [
        test_auth_manager_integration,
        test_cloud_api_silent_mode,
        test_medication_screen_auth
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试异常: {str(e)}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！用药管理屏幕token认证问题已修复")
        print("\n修复内容:")
        print("1. 优化了load_medications方法，使用静默方式获取认证信息")
        print("2. 修复了save_medication方法，避免不必要的认证警告")
        print("3. 修复了delete_medication方法，使用静默认证方式")
        print("4. 改进了错误日志级别，减少不必要的警告信息")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
