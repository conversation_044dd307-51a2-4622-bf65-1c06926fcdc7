"""慢性病管理API路由
"""
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.user import User
from app.core.auth import get_current_active_user_custom

router = APIRouter()

@router.get("/chronic_diseases", response_model=Dict[str, Any])
def get_chronic_diseases(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取用户慢性病管理信息
    """
    try:
        # 返回慢性病管理数据
        diseases = [
            {
                "id": 1,
                "name": "高血压",
                "status": "控制良好",
                "plan": "每日监测血压，按时服药，低盐饮食",
                "last_check": "2025-07-20",
                "next_check": "2025-08-20"
            },
            {
                "id": 2,
                "name": "糖尿病",
                "status": "需要关注",
                "plan": "控制血糖，定期检查，合理饮食",
                "last_check": "2025-07-15",
                "next_check": "2025-08-15"
            },
            {
                "id": 3,
                "name": "高血脂",
                "status": "稳定",
                "plan": "低脂饮食，适量运动，定期复查",
                "last_check": "2025-07-10",
                "next_check": "2025-10-10"
            }
        ]
        
        return {
            "status": "success",
            "diseases": diseases,
            "total": len(diseases),
            "message": "获取慢性病管理信息成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取慢性病管理信息失败: {str(e)}"
        )

@router.get("/chronic_diseases/{disease_id}", response_model=Dict[str, Any])
def get_chronic_disease_detail(
    disease_id: int = Path(..., description="慢性病ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取特定慢性病的详细信息
    """
    try:
        # 模拟数据，实际应从数据库获取
        disease_details = {
            1: {
                "id": 1,
                "name": "高血压",
                "status": "控制良好",
                "plan": "每日监测血压，按时服药，低盐饮食",
                "medications": ["氨氯地平", "缬沙坦"],
                "lifestyle": "低盐饮食，适量运动，戒烟限酒",
                "monitoring": "每日测量血压，记录数据",
                "last_check": "2025-07-20",
                "next_check": "2025-08-20",
                "doctor": "李医生",
                "hospital": "市人民医院"
            },
            2: {
                "id": 2,
                "name": "糖尿病",
                "status": "需要关注",
                "plan": "控制血糖，定期检查，合理饮食",
                "medications": ["二甲双胍", "格列齐特"],
                "lifestyle": "控制碳水化合物摄入，规律运动",
                "monitoring": "每日测量血糖，记录饮食",
                "last_check": "2025-07-15",
                "next_check": "2025-08-15",
                "doctor": "王医生",
                "hospital": "市中心医院"
            },
            3: {
                "id": 3,
                "name": "高血脂",
                "status": "稳定",
                "plan": "低脂饮食，适量运动，定期复查",
                "medications": ["阿托伐他汀"],
                "lifestyle": "低脂低胆固醇饮食，有氧运动",
                "monitoring": "定期检查血脂水平",
                "last_check": "2025-07-10",
                "next_check": "2025-10-10",
                "doctor": "张医生",
                "hospital": "市第二医院"
            }
        }
        
        if disease_id not in disease_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="慢性病信息不存在"
            )
        
        return {
            "status": "success",
            "disease": disease_details[disease_id],
            "message": "获取慢性病详细信息成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取慢性病详细信息失败: {str(e)}"
        )

@router.post("/chronic_diseases/{disease_id}/update_plan", response_model=Dict[str, Any])
def update_disease_plan(
    disease_id: int = Path(..., description="慢性病ID"),
    plan_data: Dict[str, Any] = Body(..., description="管理计划数据"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    更新慢性病管理计划
    """
    try:
        # 这里应该更新数据库中的管理计划
        # 目前返回成功响应
        
        return {
            "status": "success",
            "message": "慢性病管理计划更新成功",
            "updated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新慢性病管理计划失败: {str(e)}"
        )