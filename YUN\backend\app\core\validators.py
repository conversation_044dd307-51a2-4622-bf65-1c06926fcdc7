#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证模块
提供统一的数据验证规则和方法
"""

import re
import json
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union, Callable
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, validator, ValidationError
from email_validator import validate_email, EmailNotValidError

class ValidationType(str, Enum):
    """验证类型枚举"""
    REQUIRED = "required"
    EMAIL = "email"
    PHONE = "phone"
    ID_CARD = "id_card"
    PASSWORD = "password"
    URL = "url"
    JSON = "json"
    NUMERIC = "numeric"
    DATE = "date"
    DATETIME = "datetime"
    LENGTH = "length"
    RANGE = "range"
    REGEX = "regex"
    CUSTOM = "custom"

@dataclass
class ValidationRule:
    """验证规则"""
    type: ValidationType
    message: str
    params: Dict[str, Any] = None
    validator_func: Optional[Callable] = None

@dataclass
class ValidationError:
    """验证错误"""
    field: str
    message: str
    code: str
    value: Any = None

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[ValidationError]
    warnings: List[str] = None
    cleaned_data: Dict[str, Any] = None

class BaseValidator:
    """基础验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.cleaned_data = {}
    
    def validate(self, data: Dict[str, Any], rules: Dict[str, List[ValidationRule]]) -> ValidationResult:
        """执行验证"""
        self.errors = []
        self.warnings = []
        self.cleaned_data = {}
        
        for field, field_rules in rules.items():
            value = data.get(field)
            self._validate_field(field, value, field_rules)
        
        return ValidationResult(
            is_valid=len(self.errors) == 0,
            errors=self.errors,
            warnings=self.warnings,
            cleaned_data=self.cleaned_data
        )
    
    def _validate_field(self, field: str, value: Any, rules: List[ValidationRule]):
        """验证单个字段"""
        for rule in rules:
            try:
                if rule.type == ValidationType.REQUIRED:
                    self._validate_required(field, value, rule)
                elif rule.type == ValidationType.EMAIL:
                    self._validate_email(field, value, rule)
                elif rule.type == ValidationType.PHONE:
                    self._validate_phone(field, value, rule)
                elif rule.type == ValidationType.ID_CARD:
                    self._validate_id_card(field, value, rule)
                elif rule.type == ValidationType.PASSWORD:
                    self._validate_password(field, value, rule)
                elif rule.type == ValidationType.URL:
                    self._validate_url(field, value, rule)
                elif rule.type == ValidationType.JSON:
                    self._validate_json(field, value, rule)
                elif rule.type == ValidationType.NUMERIC:
                    self._validate_numeric(field, value, rule)
                elif rule.type == ValidationType.DATE:
                    self._validate_date(field, value, rule)
                elif rule.type == ValidationType.DATETIME:
                    self._validate_datetime(field, value, rule)
                elif rule.type == ValidationType.LENGTH:
                    self._validate_length(field, value, rule)
                elif rule.type == ValidationType.RANGE:
                    self._validate_range(field, value, rule)
                elif rule.type == ValidationType.REGEX:
                    self._validate_regex(field, value, rule)
                elif rule.type == ValidationType.CUSTOM:
                    self._validate_custom(field, value, rule)
                
                # 如果验证通过，保存清理后的数据
                if field not in [error.field for error in self.errors]:
                    self.cleaned_data[field] = value
                    
            except Exception as e:
                self.errors.append(ValidationError(
                    field=field,
                    message=f"验证过程中发生错误: {str(e)}",
                    code="validation_error",
                    value=value
                ))
    
    def _validate_required(self, field: str, value: Any, rule: ValidationRule):
        """验证必填项"""
        if value is None or value == "" or (isinstance(value, list) and len(value) == 0):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or f"{field}是必填项",
                code="required",
                value=value
            ))
    
    def _validate_email(self, field: str, value: Any, rule: ValidationRule):
        """验证邮箱格式"""
        if value is None or value == "":
            return
        
        try:
            validate_email(str(value))
        except EmailNotValidError:
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "邮箱格式不正确",
                code="invalid_email",
                value=value
            ))
    
    def _validate_phone(self, field: str, value: Any, rule: ValidationRule):
        """验证手机号格式"""
        if value is None or value == "":
            return
        
        # 中国手机号正则
        pattern = r'^1[3-9]\d{9}$'
        if not re.match(pattern, str(value)):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "手机号格式不正确",
                code="invalid_phone",
                value=value
            ))
    
    def _validate_id_card(self, field: str, value: Any, rule: ValidationRule):
        """验证身份证号格式"""
        if value is None or value == "":
            return
        
        id_card = str(value)
        
        # 18位身份证号正则
        pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
        
        if not re.match(pattern, id_card):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "身份证号格式不正确",
                code="invalid_id_card",
                value=value
            ))
            return
        
        # 验证校验位
        if not self._validate_id_card_checksum(id_card):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "身份证号校验位错误",
                code="invalid_id_card_checksum",
                value=value
            ))
    
    def _validate_id_card_checksum(self, id_card: str) -> bool:
        """验证身份证号校验位"""
        if len(id_card) != 18:
            return False
        
        # 权重因子
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        # 校验码
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        try:
            # 计算校验位
            sum_val = sum(int(id_card[i]) * weights[i] for i in range(17))
            check_index = sum_val % 11
            
            return id_card[17].upper() == check_codes[check_index]
        except (ValueError, IndexError):
            return False
    
    def _validate_password(self, field: str, value: Any, rule: ValidationRule):
        """验证密码强度"""
        if value is None or value == "":
            return
        
        password = str(value)
        params = rule.params or {}
        
        min_length = params.get("min_length", 8)
        require_uppercase = params.get("require_uppercase", True)
        require_lowercase = params.get("require_lowercase", True)
        require_digit = params.get("require_digit", True)
        require_special = params.get("require_special", False)
        
        errors = []
        
        if len(password) < min_length:
            errors.append(f"密码长度至少{min_length}位")
        
        if require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("密码必须包含大写字母")
        
        if require_lowercase and not re.search(r'[a-z]', password):
            errors.append("密码必须包含小写字母")
        
        if require_digit and not re.search(r'\d', password):
            errors.append("密码必须包含数字")
        
        if require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含特殊字符")
        
        if errors:
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "; ".join(errors),
                code="weak_password",
                value=value
            ))
    
    def _validate_url(self, field: str, value: Any, rule: ValidationRule):
        """验证URL格式"""
        if value is None or value == "":
            return
        
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+'  # domain...
            r'(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'  # host...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(str(value)):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "URL格式不正确",
                code="invalid_url",
                value=value
            ))
    
    def _validate_json(self, field: str, value: Any, rule: ValidationRule):
        """验证JSON格式"""
        if value is None or value == "":
            return
        
        try:
            if isinstance(value, str):
                json.loads(value)
            elif not isinstance(value, (dict, list)):
                raise ValueError("不是有效的JSON数据")
        except (json.JSONDecodeError, ValueError):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "JSON格式不正确",
                code="invalid_json",
                value=value
            ))
    
    def _validate_numeric(self, field: str, value: Any, rule: ValidationRule):
        """验证数字格式"""
        if value is None or value == "":
            return
        
        try:
            float(value)
        except (ValueError, TypeError):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "必须是数字",
                code="invalid_numeric",
                value=value
            ))
    
    def _validate_date(self, field: str, value: Any, rule: ValidationRule):
        """验证日期格式"""
        if value is None or value == "":
            return
        
        params = rule.params or {}
        date_format = params.get("format", "%Y-%m-%d")
        
        try:
            if isinstance(value, str):
                datetime.strptime(value, date_format)
            elif not isinstance(value, (date, datetime)):
                raise ValueError("不是有效的日期")
        except ValueError:
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or f"日期格式不正确，应为{date_format}",
                code="invalid_date",
                value=value
            ))
    
    def _validate_datetime(self, field: str, value: Any, rule: ValidationRule):
        """验证日期时间格式"""
        if value is None or value == "":
            return
        
        params = rule.params or {}
        datetime_format = params.get("format", "%Y-%m-%d %H:%M:%S")
        
        try:
            if isinstance(value, str):
                datetime.strptime(value, datetime_format)
            elif not isinstance(value, datetime):
                raise ValueError("不是有效的日期时间")
        except ValueError:
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or f"日期时间格式不正确，应为{datetime_format}",
                code="invalid_datetime",
                value=value
            ))
    
    def _validate_length(self, field: str, value: Any, rule: ValidationRule):
        """验证长度"""
        if value is None:
            return
        
        params = rule.params or {}
        min_length = params.get("min")
        max_length = params.get("max")
        
        length = len(str(value)) if value is not None else 0
        
        if min_length is not None and length < min_length:
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or f"长度不能少于{min_length}个字符",
                code="too_short",
                value=value
            ))
        
        if max_length is not None and length > max_length:
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or f"长度不能超过{max_length}个字符",
                code="too_long",
                value=value
            ))
    
    def _validate_range(self, field: str, value: Any, rule: ValidationRule):
        """验证数值范围"""
        if value is None or value == "":
            return
        
        params = rule.params or {}
        min_value = params.get("min")
        max_value = params.get("max")
        
        try:
            numeric_value = float(value)
            
            if min_value is not None and numeric_value < min_value:
                self.errors.append(ValidationError(
                    field=field,
                    message=rule.message or f"值不能小于{min_value}",
                    code="too_small",
                    value=value
                ))
            
            if max_value is not None and numeric_value > max_value:
                self.errors.append(ValidationError(
                    field=field,
                    message=rule.message or f"值不能大于{max_value}",
                    code="too_large",
                    value=value
                ))
        except (ValueError, TypeError):
            self.errors.append(ValidationError(
                field=field,
                message="必须是数字",
                code="invalid_numeric",
                value=value
            ))
    
    def _validate_regex(self, field: str, value: Any, rule: ValidationRule):
        """验证正则表达式"""
        if value is None or value == "":
            return
        
        params = rule.params or {}
        pattern = params.get("pattern")
        
        if not pattern:
            return
        
        if not re.match(pattern, str(value)):
            self.errors.append(ValidationError(
                field=field,
                message=rule.message or "格式不正确",
                code="pattern_mismatch",
                value=value
            ))
    
    def _validate_custom(self, field: str, value: Any, rule: ValidationRule):
        """自定义验证"""
        if rule.validator_func:
            try:
                result = rule.validator_func(value)
                if not result:
                    self.errors.append(ValidationError(
                        field=field,
                        message=rule.message or "验证失败",
                        code="custom_validation_failed",
                        value=value
                    ))
            except Exception as e:
                self.errors.append(ValidationError(
                    field=field,
                    message=f"自定义验证错误: {str(e)}",
                    code="custom_validation_error",
                    value=value
                ))

class UserValidator(BaseValidator):
    """用户数据验证器"""
    
    def get_user_rules(self) -> Dict[str, List[ValidationRule]]:
        """获取用户验证规则"""
        return {
            "username": [
                ValidationRule(ValidationType.REQUIRED, "用户名是必填项"),
                ValidationRule(ValidationType.LENGTH, "用户名长度必须在3-20个字符之间", {"min": 3, "max": 20}),
                ValidationRule(ValidationType.REGEX, "用户名只能包含字母、数字和下划线", {"pattern": r'^[a-zA-Z0-9_]+$'})
            ],
            "email": [
                ValidationRule(ValidationType.EMAIL, "邮箱格式不正确")
            ],
            "phone": [
                ValidationRule(ValidationType.PHONE, "手机号格式不正确")
            ],
            "password": [
                ValidationRule(ValidationType.REQUIRED, "密码是必填项"),
                ValidationRule(ValidationType.PASSWORD, "密码强度不够", {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_digit": True,
                    "require_special": False
                })
            ],
            "real_name": [
                ValidationRule(ValidationType.LENGTH, "真实姓名长度必须在2-10个字符之间", {"min": 2, "max": 10})
            ],
            "id_card": [
                ValidationRule(ValidationType.ID_CARD, "身份证号格式不正确")
            ],
            "age": [
                ValidationRule(ValidationType.NUMERIC, "年龄必须是数字"),
                ValidationRule(ValidationType.RANGE, "年龄必须在0-150之间", {"min": 0, "max": 150})
            ],
            "gender": [
                ValidationRule(ValidationType.CUSTOM, "性别值不正确", 
                              validator_func=lambda x: x in ["male", "female", "other"])
            ]
        }
    
    def validate_user_registration(self, data: Dict[str, Any]) -> ValidationResult:
        """验证用户注册数据"""
        rules = {
            "username": self.get_user_rules()["username"],
            "email": [ValidationRule(ValidationType.REQUIRED, "邮箱是必填项")] + self.get_user_rules()["email"],
            "password": self.get_user_rules()["password"],
            "phone": self.get_user_rules()["phone"]
        }
        return self.validate(data, rules)
    
    def validate_user_profile(self, data: Dict[str, Any]) -> ValidationResult:
        """验证用户资料数据"""
        rules = {
            "real_name": self.get_user_rules()["real_name"],
            "id_card": self.get_user_rules()["id_card"],
            "age": self.get_user_rules()["age"],
            "gender": self.get_user_rules()["gender"]
        }
        return self.validate(data, rules)

class AssessmentValidator(BaseValidator):
    """评估数据验证器"""
    
    def get_assessment_rules(self) -> Dict[str, List[ValidationRule]]:
        """获取评估验证规则"""
        return {
            "name": [
                ValidationRule(ValidationType.REQUIRED, "评估名称是必填项"),
                ValidationRule(ValidationType.LENGTH, "评估名称长度必须在2-100个字符之间", {"min": 2, "max": 100})
            ],
            "description": [
                ValidationRule(ValidationType.LENGTH, "描述长度不能超过500个字符", {"max": 500})
            ],
            "type": [
                ValidationRule(ValidationType.REQUIRED, "评估类型是必填项"),
                ValidationRule(ValidationType.CUSTOM, "评估类型不正确", 
                              validator_func=lambda x: x in ["scale", "survey", "assessment", "screening"])
            ],
            "questions": [
                ValidationRule(ValidationType.REQUIRED, "问题列表是必填项"),
                ValidationRule(ValidationType.CUSTOM, "至少需要一个问题", 
                              validator_func=lambda x: isinstance(x, list) and len(x) > 0)
            ],
            "score_ranges": [
                ValidationRule(ValidationType.CUSTOM, "评分范围格式不正确", 
                              validator_func=self._validate_score_ranges)
            ]
        }
    
    def _validate_score_ranges(self, score_ranges: Any) -> bool:
        """验证评分范围"""
        if not isinstance(score_ranges, list):
            return False
        
        for score_range in score_ranges:
            if not isinstance(score_range, dict):
                return False
            
            required_fields = ["min_score", "max_score", "level", "description"]
            if not all(field in score_range for field in required_fields):
                return False
            
            try:
                min_score = float(score_range["min_score"])
                max_score = float(score_range["max_score"])
                if min_score >= max_score:
                    return False
            except (ValueError, TypeError):
                return False
        
        return True
    
    def validate_assessment_creation(self, data: Dict[str, Any]) -> ValidationResult:
        """验证评估创建数据"""
        rules = self.get_assessment_rules()
        return self.validate(data, rules)
    
    def validate_assessment_answer(self, data: Dict[str, Any]) -> ValidationResult:
        """验证评估回答数据"""
        rules = {
            "question_id": [
                ValidationRule(ValidationType.REQUIRED, "问题ID是必填项")
            ],
            "answer_value": [
                ValidationRule(ValidationType.REQUIRED, "答案是必填项")
            ],
            "assessment_id": [
                ValidationRule(ValidationType.REQUIRED, "评估ID是必填项")
            ]
        }
        return self.validate(data, rules)

class QuestionnaireValidator(BaseValidator):
    """问卷数据验证器"""
    
    def get_questionnaire_rules(self) -> Dict[str, List[ValidationRule]]:
        """获取问卷验证规则"""
        return {
            "title": [
                ValidationRule(ValidationType.REQUIRED, "问卷标题是必填项"),
                ValidationRule(ValidationType.LENGTH, "问卷标题长度必须在2-200个字符之间", {"min": 2, "max": 200})
            ],
            "description": [
                ValidationRule(ValidationType.LENGTH, "描述长度不能超过1000个字符", {"max": 1000})
            ],
            "type": [
                ValidationRule(ValidationType.REQUIRED, "问卷类型是必填项"),
                ValidationRule(ValidationType.CUSTOM, "问卷类型不正确", 
                              validator_func=lambda x: x in ["scale", "survey", "assessment", "screening"])
            ],
            "questions": [
                ValidationRule(ValidationType.REQUIRED, "问题列表是必填项"),
                ValidationRule(ValidationType.CUSTOM, "问题格式不正确", 
                              validator_func=self._validate_questions)
            ]
        }
    
    def _validate_questions(self, questions: Any) -> bool:
        """验证问题格式"""
        if not isinstance(questions, list) or len(questions) == 0:
            return False
        
        for question in questions:
            if not isinstance(question, dict):
                return False
            
            # 检查必需字段
            required_fields = ["id", "title", "type"]
            if not all(field in question for field in required_fields):
                return False
            
            # 验证问题类型
            question_type = question.get("type")
            if question_type not in ["single_choice", "multiple_choice", "text", "number", "scale", "date"]:
                return False
            
            # 对于选择题，验证选项
            if question_type in ["single_choice", "multiple_choice", "scale"]:
                options = question.get("options")
                if not isinstance(options, list) or len(options) == 0:
                    return False
                
                for option in options:
                    if not isinstance(option, dict) or "value" not in option or "label" not in option:
                        return False
        
        return True
    
    def validate_questionnaire_creation(self, data: Dict[str, Any]) -> ValidationResult:
        """验证问卷创建数据"""
        rules = self.get_questionnaire_rules()
        return self.validate(data, rules)

class HealthRecordValidator(BaseValidator):
    """健康记录验证器"""
    
    def get_health_record_rules(self) -> Dict[str, List[ValidationRule]]:
        """获取健康记录验证规则"""
        return {
            "height": [
                ValidationRule(ValidationType.NUMERIC, "身高必须是数字"),
                ValidationRule(ValidationType.RANGE, "身高必须在50-250cm之间", {"min": 50, "max": 250})
            ],
            "weight": [
                ValidationRule(ValidationType.NUMERIC, "体重必须是数字"),
                ValidationRule(ValidationType.RANGE, "体重必须在10-500kg之间", {"min": 10, "max": 500})
            ],
            "blood_pressure_systolic": [
                ValidationRule(ValidationType.NUMERIC, "收缩压必须是数字"),
                ValidationRule(ValidationType.RANGE, "收缩压必须在60-300mmHg之间", {"min": 60, "max": 300})
            ],
            "blood_pressure_diastolic": [
                ValidationRule(ValidationType.NUMERIC, "舒张压必须是数字"),
                ValidationRule(ValidationType.RANGE, "舒张压必须在40-200mmHg之间", {"min": 40, "max": 200})
            ],
            "heart_rate": [
                ValidationRule(ValidationType.NUMERIC, "心率必须是数字"),
                ValidationRule(ValidationType.RANGE, "心率必须在30-200次/分之间", {"min": 30, "max": 200})
            ],
            "temperature": [
                ValidationRule(ValidationType.NUMERIC, "体温必须是数字"),
                ValidationRule(ValidationType.RANGE, "体温必须在30-45°C之间", {"min": 30, "max": 45})
            ],
            "blood_sugar": [
                ValidationRule(ValidationType.NUMERIC, "血糖必须是数字"),
                ValidationRule(ValidationType.RANGE, "血糖必须在1-50mmol/L之间", {"min": 1, "max": 50})
            ]
        }
    
    def validate_health_record(self, data: Dict[str, Any]) -> ValidationResult:
        """验证健康记录数据"""
        rules = self.get_health_record_rules()
        return self.validate(data, rules)

# 便捷函数
def validate_user_data(data: Dict[str, Any], validation_type: str = "registration") -> ValidationResult:
    """验证用户数据"""
    validator = UserValidator()
    
    if validation_type == "registration":
        return validator.validate_user_registration(data)
    elif validation_type == "profile":
        return validator.validate_user_profile(data)
    else:
        raise ValueError(f"不支持的验证类型: {validation_type}")

def validate_assessment_data(data: Dict[str, Any], validation_type: str = "creation") -> ValidationResult:
    """验证评估数据"""
    validator = AssessmentValidator()
    
    if validation_type == "creation":
        return validator.validate_assessment_creation(data)
    elif validation_type == "answer":
        return validator.validate_assessment_answer(data)
    else:
        raise ValueError(f"不支持的验证类型: {validation_type}")

def validate_questionnaire_data(data: Dict[str, Any]) -> ValidationResult:
    """验证问卷数据"""
    validator = QuestionnaireValidator()
    return validator.validate_questionnaire_creation(data)

def validate_health_record_data(data: Dict[str, Any]) -> ValidationResult:
    """验证健康记录数据"""
    validator = HealthRecordValidator()
    return validator.validate_health_record(data)

def create_custom_validator(rules: Dict[str, List[ValidationRule]]) -> BaseValidator:
    """创建自定义验证器"""
    validator = BaseValidator()
    return validator

# 常用验证规则预设
COMMON_RULES = {
    "required_string": ValidationRule(ValidationType.REQUIRED, "此项是必填项"),
    "email": ValidationRule(ValidationType.EMAIL, "邮箱格式不正确"),
    "phone": ValidationRule(ValidationType.PHONE, "手机号格式不正确"),
    "id_card": ValidationRule(ValidationType.ID_CARD, "身份证号格式不正确"),
    "strong_password": ValidationRule(ValidationType.PASSWORD, "密码强度不够", {
        "min_length": 8,
        "require_uppercase": True,
        "require_lowercase": True,
        "require_digit": True,
        "require_special": True
    }),
    "url": ValidationRule(ValidationType.URL, "URL格式不正确"),
    "json": ValidationRule(ValidationType.JSON, "JSON格式不正确"),
    "positive_number": ValidationRule(ValidationType.RANGE, "必须是正数", {"min": 0}),
    "date": ValidationRule(ValidationType.DATE, "日期格式不正确"),
    "datetime": ValidationRule(ValidationType.DATETIME, "日期时间格式不正确")
}

async def validate_user_access(current_user, custom_id, session):
    """开发阶段占位，后续可扩展权限校验逻辑"""
    return True

class DataValidator(BaseValidator):
    """数据验证器 - 用于数据导入导出的验证"""
    
    def __init__(self):
        super().__init__()
        self.logger = None
        try:
            from .logging_utils import get_logger
            self.logger = get_logger(self.__class__.__name__)
        except ImportError:
            pass
    
    def validate_import_data(self, data: List[Dict[str, Any]], table_name: str) -> ValidationResult:
        """验证导入数据"""
        errors = []
        warnings = []
        valid_rows = 0
        
        for i, row in enumerate(data):
            try:
                # 基本数据验证
                if not isinstance(row, dict):
                    errors.append(ValidationError(
                        field=f"row_{i}",
                        message="数据行必须是字典格式",
                        code="invalid_format",
                        value=row
                    ))
                    continue
                
                # 检查空行
                if not any(row.values()):
                    warnings.append(f"第{i+1}行为空行，将被跳过")
                    continue
                
                valid_rows += 1
                
            except Exception as e:
                errors.append(ValidationError(
                    field=f"row_{i}",
                    message=f"验证失败: {str(e)}",
                    code="validation_error",
                    value=row
                ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data={"valid_rows": valid_rows, "total_rows": len(data)}
        )
    
    def validate_export_data(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> ValidationResult:
        """验证导出数据"""
        errors = []
        warnings = []
        
        if not data:
            warnings.append("没有数据可导出")
        
        # 检查数据格式
        for i, row in enumerate(data[:10]):  # 只检查前10行
            if not isinstance(row, dict):
                errors.append(ValidationError(
                    field=f"row_{i}",
                    message="数据行必须是字典格式",
                    code="invalid_format",
                    value=row
                ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data={"total_rows": len(data)}
        )
    
    def validate_table_schema(self, table_name: str, columns: List[str]) -> ValidationResult:
        """验证表结构"""
        errors = []
        warnings = []
        
        if not table_name:
            errors.append(ValidationError(
                field="table_name",
                message="表名不能为空",
                code="required",
                value=table_name
            ))
        
        if not columns:
            errors.append(ValidationError(
                field="columns",
                message="列名列表不能为空",
                code="required",
                value=columns
            ))
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            cleaned_data={"table_name": table_name, "columns": columns}
        )