from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, Dict, Any
from app.api.deps import get_current_active_user
from app.models.user import User
from app.utils.performance_monitor import (
    get_performance_metrics,
    get_api_metrics,
    get_system_metrics,
    performance_monitor
)
from app.middleware.error_handler import (
    HealthRecordsErrorHandler,
    permission_denied_error,
    validation_error
)

router = APIRouter()

@router.get("/performance/summary", response_model=dict)
async def get_performance_summary(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取性能摘要
    """
    try:
        # 检查用户权限（只有管理员可以查看性能指标）
        if not getattr(current_user, 'is_superuser', False):
            return permission_denied_error("只有管理员可以查看性能指标")
        
        summary = get_performance_metrics()
        
        return HealthRecordsErrorHandler.create_success_response(
            data=summary,
            message="性能摘要获取成功"
        )
        
    except Exception as e:
        return HealthRecordsErrorHandler.handle_error(
            e, "获取性能摘要失败"
        )

@router.get("/performance/api-metrics", response_model=dict)
async def get_api_performance_metrics(
    endpoint: Optional[str] = Query(None, description="特定端点名称"),
    current_user: User = Depends(get_current_active_user)
):
    """
    获取API性能指标
    """
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_superuser', False):
            return permission_denied_error("只有管理员可以查看API性能指标")
        
        metrics = get_api_metrics(endpoint)
        
        return HealthRecordsErrorHandler.create_success_response(
            data=metrics,
            message="API性能指标获取成功"
        )
        
    except Exception as e:
        return HealthRecordsErrorHandler.handle_error(
            e, "获取API性能指标失败"
        )

@router.get("/performance/system-metrics", response_model=dict)
async def get_system_performance_metrics(
    current_user: User = Depends(get_current_active_user)
):
    """
    获取系统性能指标
    """
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_superuser', False):
            return permission_denied_error("只有管理员可以查看系统性能指标")
        
        metrics = get_system_metrics()
        
        return HealthRecordsErrorHandler.create_success_response(
            data=metrics,
            message="系统性能指标获取成功"
        )
        
    except Exception as e:
        return HealthRecordsErrorHandler.handle_error(
            e, "获取系统性能指标失败"
        )

@router.delete("/performance/clear-metrics", response_model=dict)
async def clear_performance_metrics(
    current_user: User = Depends(get_current_active_user)
):
    """
    清空性能指标数据
    """
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_superuser', False):
            return permission_denied_error("只有管理员可以清空性能指标")
        
        performance_monitor.clear_metrics()
        
        return HealthRecordsErrorHandler.create_success_response(
            data={"cleared": True},
            message="性能指标数据已清空"
        )
        
    except Exception as e:
        return HealthRecordsErrorHandler.handle_error(
            e, "清空性能指标失败"
        )

@router.get("/performance/health-check", response_model=dict)
async def performance_health_check():
    """
    性能健康检查（无需认证）
    """
    try:
        import psutil
        import time
        
        # 基本系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 简单的健康状态评估
        health_status = "healthy"
        issues = []
        
        if cpu_percent > 80:
            health_status = "warning"
            issues.append(f"CPU使用率过高: {cpu_percent}%")
        
        if memory.percent > 85:
            health_status = "warning"
            issues.append(f"内存使用率过高: {memory.percent}%")
        
        if (disk.used / disk.total) * 100 > 90:
            health_status = "warning"
            issues.append(f"磁盘使用率过高: {(disk.used / disk.total) * 100:.1f}%")
        
        if cpu_percent > 95 or memory.percent > 95:
            health_status = "critical"
        
        health_data = {
            "status": health_status,
            "timestamp": time.time(),
            "system_info": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": (disk.used / disk.total) * 100
            },
            "issues": issues
        }
        
        return HealthRecordsErrorHandler.create_success_response(
            data=health_data,
            message="健康检查完成"
        )
        
    except Exception as e:
        return HealthRecordsErrorHandler.handle_error(
            e, "健康检查失败"
        )