"""问卷调查结果API"""
from typing import Any, Dict, List, Optional
from datetime import datetime
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.db.base_session import get_db
from app.models.user import User
from app.models.result import QuestionnaireResult, ReportTemplate
from app.models.questionnaire import Questionnaire, QuestionnaireTemplate, QuestionnaireResponse
from app.api import deps
from app.core.auth import get_current_active_user_custom

router = APIRouter()

@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_questionnaire_results(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    questionnaire_type: Optional[str] = Query(None, description="问卷类型"),
    result_level: Optional[str] = Query(None, description="结果等级"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的问卷调查结果列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的问卷结果"
        )

    # 构建查询，join questionnaire表获取问卷名称
    query = db.query(QuestionnaireResult, Questionnaire.title.label('questionnaire_name')).join(
        Questionnaire, QuestionnaireResult.questionnaire_id == Questionnaire.id
    ).filter(QuestionnaireResult.custom_id == custom_id)
    
    if questionnaire_type:
        query = query.filter(Questionnaire.questionnaire_type == questionnaire_type)
    if result_level:
        query = query.filter(QuestionnaireResult.result_level == result_level)
    if start_date:
        query = query.filter(QuestionnaireResult.calculated_at >= start_date)
    if end_date:
        query = query.filter(QuestionnaireResult.calculated_at <= end_date)
    
    # 分页
    results = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "success": True,
        "data": [
            {
                "id": result.QuestionnaireResult.id,
                "questionnaire_id": result.QuestionnaireResult.questionnaire_id,
                "response_id": result.QuestionnaireResult.response_id,
                "custom_id": result.QuestionnaireResult.custom_id,
                "template_id": result.QuestionnaireResult.template_id,
                "total_score": result.QuestionnaireResult.total_score,
                "max_score": result.QuestionnaireResult.max_score,
                "percentage": result.QuestionnaireResult.percentage,
                "result_level": result.QuestionnaireResult.result_level,
                "result_category": result.QuestionnaireResult.result_category,
                "interpretation": result.QuestionnaireResult.interpretation,
                "recommendations": result.QuestionnaireResult.recommendations,
                "dimension_scores": result.QuestionnaireResult.dimension_scores,
                "report_generated": result.QuestionnaireResult.report_generated,
                "status": result.QuestionnaireResult.status,
                "calculated_at": result.QuestionnaireResult.calculated_at,
                "created_at": result.QuestionnaireResult.created_at,
                "updated_at": result.QuestionnaireResult.updated_at,
                "questionnaire_name": result.questionnaire_name,
                "title": result.questionnaire_name  # 添加title字段以兼容移动端
            }
            for result in results
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_questionnaire_result(
    *,
    db: Session = Depends(get_db),
    result_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    创建问卷调查结果
    """
    # 验证问卷存在
    questionnaire_id = result_data.get("questionnaire_id")
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not questionnaire:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到问卷ID: {questionnaire_id}"
        )

    # 验证用户存在
    custom_id = result_data.get("custom_id")
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限创建问卷结果"
        )

    # 创建问卷结果
    result = QuestionnaireResult(
        questionnaire_id=questionnaire_id,
        response_id=result_data.get("response_id"),
        custom_id=custom_id,
        template_id=result_data.get("template_id"),
        total_score=result_data.get("total_score"),
        max_score=result_data.get("max_score"),
        percentage=result_data.get("percentage"),
        result_level=result_data.get("result_level"),
        result_category=result_data.get("result_category"),
        interpretation=result_data.get("interpretation"),
        recommendations=result_data.get("recommendations"),
        dimension_scores=result_data.get("dimension_scores"),
        calculation_details=result_data.get("calculation_details"),
        raw_answers=result_data.get("raw_answers"),
        report_generated=result_data.get("report_generated", False),
        report_content=result_data.get("report_content"),
        report_format=result_data.get("report_format", "html"),
        report_template=result_data.get("report_template"),
        status=result_data.get("status", "calculated")
    )
    
    db.add(result)
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "data": {
            "id": result.id,
            "questionnaire_id": result.questionnaire_id,
            "response_id": result.response_id,
            "custom_id": result.custom_id,
            "template_id": result.template_id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "result_category": result.result_category,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "dimension_scores": result.dimension_scores,
            "report_generated": result.report_generated,
            "status": result.status,
            "calculated_at": result.calculated_at,
            "created_at": result.created_at,
            "updated_at": result.updated_at
        }
    }


@router.get("/{result_id}", response_model=Dict[str, Any])
def get_questionnaire_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取单个问卷调查结果详情
    """
    # join questionnaire表获取问卷名称
    result_query = db.query(QuestionnaireResult, Questionnaire.title.label('questionnaire_name')).join(
        Questionnaire, QuestionnaireResult.questionnaire_id == Questionnaire.id
    ).filter(QuestionnaireResult.id == result_id).first()
    
    if not result_query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到问卷结果ID: {result_id}"
        )
    
    result = result_query.QuestionnaireResult
    questionnaire_name = result_query.questionnaire_name

    # 权限校验
    if current_user.custom_id != result.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此问卷结果"
        )

    return {
        "success": True,
        "data": {
            "id": result.id,
            "questionnaire_id": result.questionnaire_id,
            "response_id": result.response_id,
            "custom_id": result.custom_id,
            "template_id": result.template_id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "result_category": result.result_category,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "dimension_scores": result.dimension_scores,
            "calculation_details": result.calculation_details,
            "raw_answers": result.raw_answers,
            "report_generated": result.report_generated,
            "report_content": result.report_content,
            "report_format": result.report_format,
            "report_template": result.report_template,
            "status": result.status,
            "calculated_at": result.calculated_at,
            "created_at": result.created_at,
            "updated_at": result.updated_at,
            "questionnaire_name": questionnaire_name,
            "title": questionnaire_name  # 添加title字段以兼容移动端
        }
    }


@router.put("/{result_id}", response_model=Dict[str, Any])
def update_questionnaire_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    result_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新问卷调查结果
    """
    result = db.query(QuestionnaireResult).filter(QuestionnaireResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到问卷结果ID: {result_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改问卷结果"
        )

    # 更新字段
    for field, value in result_data.items():
        if hasattr(result, field) and field not in ["id", "questionnaire_id", "custom_id", "created_at"]:
            setattr(result, field, value)
    
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "data": {
            "id": result.id,
            "questionnaire_id": result.questionnaire_id,
            "response_id": result.response_id,
            "custom_id": result.custom_id,
            "template_id": result.template_id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "result_category": result.result_category,
            "interpretation": result.interpretation,
            "recommendations": result.recommendations,
            "dimension_scores": result.dimension_scores,
            "report_generated": result.report_generated,
            "status": result.status,
            "calculated_at": result.calculated_at,
            "created_at": result.created_at,
            "updated_at": result.updated_at
        }
    }


@router.delete("/{result_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_questionnaire_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    删除问卷调查结果
    """
    result = db.query(QuestionnaireResult).filter(QuestionnaireResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到问卷结果ID: {result_id}"
        )

    # 权限校验
    if current_user.role not in ["admin", "super_admin", "unit_admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除问卷结果"
        )

    db.delete(result)
    db.commit()


@router.post("/{result_id}/generate-report", response_model=Dict[str, Any])
def generate_questionnaire_report(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    template_name: Optional[str] = Query(None, description="报告模板名称"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    生成问卷调查报告
    """
    result = db.query(QuestionnaireResult).filter(QuestionnaireResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到问卷结果ID: {result_id}"
        )

    # 权限校验
    if current_user.custom_id != result.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限生成此问卷报告"
        )

    # 查找报告模板
    template_query = db.query(ReportTemplate).filter(
        ReportTemplate.template_type == "questionnaire",
        ReportTemplate.is_active == True
    )
    
    if template_name:
        template = template_query.filter(ReportTemplate.name == template_name).first()
    else:
        template = template_query.filter(ReportTemplate.is_default == True).first()
    
    if not template:
        # 使用默认报告内容
        report_content = f"""
        <h2>问卷调查结果报告</h2>
        <p><strong>总分：</strong>{result.total_score}/{result.max_score}</p>
        <p><strong>得分百分比：</strong>{result.percentage}%</p>
        <p><strong>结果等级：</strong>{result.result_level}</p>
        <p><strong>结果分类：</strong>{result.result_category}</p>
        <p><strong>结果解释：</strong>{result.interpretation}</p>
        <p><strong>建议：</strong>{result.recommendations}</p>
        <p><strong>生成时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        """
    else:
        # 使用模板生成报告（这里简化处理，实际应该有模板引擎）
        report_content = template.template_content.format(
            total_score=result.total_score,
            max_score=result.max_score,
            percentage=result.percentage,
            result_level=result.result_level,
            result_category=result.result_category,
            interpretation=result.interpretation,
            recommendations=result.recommendations,
            generated_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    # 更新结果记录
    result.report_content = report_content
    result.report_generated = True
    result.report_template = template.name if template else "default"
    
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "data": {
            "result_id": result.id,
            "report_content": result.report_content,
            "report_generated": result.report_generated,
            "report_template": result.report_template,
            "generated_at": result.updated_at
        }
    }


@router.post("/calculate-from-response/{response_id}", response_model=Dict[str, Any])
def calculate_result_from_response(
    *,
    db: Session = Depends(get_db),
    response_id: int = Path(..., description="问卷回复ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    根据问卷回复计算结果
    """
    # 查找问卷回复
    response = db.query(QuestionnaireResponse).filter(QuestionnaireResponse.id == response_id).first()
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到问卷回复ID: {response_id}"
        )

    # 权限校验
    if current_user.custom_id != response.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限计算此问卷结果"
        )

    # 检查是否已存在结果
    existing_result = db.query(QuestionnaireResult).filter(
        QuestionnaireResult.response_id == response_id
    ).first()
    
    if existing_result:
        return {
            "success": True,
            "message": "结果已存在",
            "data": {
                "id": existing_result.id,
                "total_score": existing_result.total_score,
                "result_level": existing_result.result_level
            }
        }

    # 简化的计算逻辑（实际应该根据具体问卷类型和评分规则计算）
    total_score = response.total_score or 0
    max_score = response.questionnaire.max_score or 100
    percentage = (total_score / max_score * 100) if max_score > 0 else 0
    
    # 简单的等级判断
    if percentage >= 80:
        result_level = "优秀"
    elif percentage >= 60:
        result_level = "良好"
    elif percentage >= 40:
        result_level = "一般"
    else:
        result_level = "需要改进"

    # 创建结果记录
    result = QuestionnaireResult(
        questionnaire_id=response.questionnaire_id,
        response_id=response_id,
        custom_id=response.custom_id,
        template_id=response.questionnaire.template_id,
        total_score=total_score,
        max_score=max_score,
        percentage=percentage,
        result_level=result_level,
        result_category=response.questionnaire.questionnaire_type,
        raw_answers=response.answers,
        status="calculated"
    )
    
    db.add(result)
    db.commit()
    db.refresh(result)
    
    return {
        "success": True,
        "message": "计算完成",
        "data": {
            "id": result.id,
            "total_score": result.total_score,
            "max_score": result.max_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "calculated_at": result.calculated_at
        }
    }


@router.get("/export/{result_id}")
def export_questionnaire_result(
    *,
    db: Session = Depends(get_db),
    result_id: int = Path(..., description="结果ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> StreamingResponse:
    """
    导出问卷结果为PDF报告
    """
    # 获取结果记录
    result = db.query(QuestionnaireResult).filter(QuestionnaireResult.id == result_id).first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到问卷结果"
        )
    
    # 权限校验
    user = db.query(User).filter(User.custom_id == result.custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到用户"
        )
    
    if current_user.custom_id != user.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限导出此问卷结果"
        )
    
    # 获取问卷信息
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == result.questionnaire_id).first()
    
    # 创建PDF
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # 标题
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # 居中
    )
    story.append(Paragraph(f"问卷结果报告 - {questionnaire.title if questionnaire else '未知问卷'}", title_style))
    story.append(Spacer(1, 12))
    
    # 基本信息表格
    basic_info = [
        ['用户ID', user.custom_id],
        ['问卷名称', questionnaire.title if questionnaire else '未知'],
        ['完成时间', result.created_at.strftime('%Y-%m-%d %H:%M:%S')],
        ['总分', f"{result.total_score}/{result.max_score}"],
        ['百分比', f"{result.percentage:.1f}%"],
        ['结果等级', result.result_level or '无'],
        ['结果类别', result.result_category or '无']
    ]
    
    basic_table = Table(basic_info, colWidths=[2*inch, 4*inch])
    basic_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.grey),
        ('TEXTCOLOR', (0, 0), (0, -1), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ('BACKGROUND', (1, 0), (1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(basic_table)
    story.append(Spacer(1, 20))
    
    # 结果解释
    if result.interpretation:
        story.append(Paragraph("结果解释", styles['Heading2']))
        story.append(Paragraph(result.interpretation, styles['Normal']))
        story.append(Spacer(1, 12))
    
    # 建议
    if result.recommendations:
        story.append(Paragraph("建议", styles['Heading2']))
        story.append(Paragraph(result.recommendations, styles['Normal']))
        story.append(Spacer(1, 12))
    
    # 原始答案
    if result.raw_answers:
        story.append(Paragraph("详细答案", styles['Heading2']))
        for i, answer in enumerate(result.raw_answers, 1):
            story.append(Paragraph(f"问题 {i}: {answer.get('question', '未知问题')}", styles['Normal']))
            story.append(Paragraph(f"答案: {answer.get('answer', '无答案')}", styles['Normal']))
            story.append(Spacer(1, 6))
    
    # 生成PDF
    doc.build(story)
    buffer.seek(0)
    
    # 返回文件流
    filename = f"questionnaire_result_{result_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    return StreamingResponse(
        io.BytesIO(buffer.read()),
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/trend/{custom_id}/{questionnaire_id}")
def get_questionnaire_trend(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    questionnaire_id: int = Path(..., description="问卷ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Dict[str, Any]:
    """
    获取用户特定问卷的趋势数据
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )
    
    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看此用户的趋势数据"
        )
    
    # 获取趋势数据
    results = db.query(QuestionnaireResult).filter(
        QuestionnaireResult.custom_id == custom_id,
        QuestionnaireResult.questionnaire_id == questionnaire_id
    ).order_by(desc(QuestionnaireResult.created_at)).all()
    
    if not results:
        return {
            "total_count": 0,
            "latest_date": None,
            "details": [],
            "trend_data": []
        }
    
    # 处理趋势数据
    trend_details = []
    trend_data = []
    
    for result in results:
        detail = {
            "date": result.created_at.strftime('%Y-%m-%d'),
            "score": result.total_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "notes": result.interpretation[:100] + "..." if result.interpretation and len(result.interpretation) > 100 else result.interpretation
        }
        trend_details.append(detail)
        
        trend_data.append({
            "date": result.created_at.strftime('%Y-%m-%d'),
            "value": result.percentage
        })
    
    return {
        "total_count": len(results),
        "latest_date": results[0].created_at.strftime('%Y-%m-%d') if results else None,
        "details": trend_details,
        "trend_data": trend_data
    }

@router.get("/list", response_model=Dict[str, Any])
def get_questionnaire_results_list(
    *,
    db: Session = Depends(get_db),
    questionnaire_type: Optional[str] = Query(None, description="问卷类型"),
    result_level: Optional[str] = Query(None, description="结果等级"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取问卷调查结果列表（聚合API）
    """
    # 构建查询
    query = db.query(QuestionnaireResult).join(Questionnaire).join(User)
    
    # 添加过滤条件
    if questionnaire_type:
        query = query.filter(Questionnaire.questionnaire_type == questionnaire_type)
    if result_level:
        query = query.filter(QuestionnaireResult.result_level == result_level)
    if start_date:
        query = query.filter(QuestionnaireResult.created_at >= start_date)
    if end_date:
        query = query.filter(QuestionnaireResult.created_at <= end_date)
    
    # 获取总数
    total = query.count()
    
    # 分页查询
    results = query.order_by(desc(QuestionnaireResult.created_at)).offset(skip).limit(limit).all()
    
    # 格式化结果
    formatted_results = []
    for result in results:
        formatted_results.append({
            "id": result.id,
            "user_id": result.questionnaire.user.custom_id,
            "user_name": result.questionnaire.user.username,
            "questionnaire_type": result.questionnaire.questionnaire_type,
            "template_name": result.questionnaire.template.name if result.questionnaire.template else "未知模板",
            "total_score": result.total_score,
            "percentage": result.percentage,
            "result_level": result.result_level,
            "interpretation": result.interpretation,
            "created_at": result.created_at.isoformat(),
            "completed_at": result.questionnaire.completed_at.isoformat() if result.questionnaire.completed_at else None
        })
    
    return {
        "code": 200,
        "message": "获取成功",
        "data": {
            "total": total,
            "results": formatted_results,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total
            }
        }
    }
