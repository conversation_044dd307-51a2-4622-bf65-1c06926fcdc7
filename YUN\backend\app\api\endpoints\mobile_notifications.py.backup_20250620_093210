#!/usr/bin/env python3
"""
移动端通知和量表获取API
提供移动端获取通知、量表、问卷的接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Body, Request
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Dict, Any
from datetime import datetime

from app.db.session import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.alert import Alert
from app.models.assessment import Assessment, AssessmentTemplate, AssessmentTemplateQuestion
from app.models.questionnaire import Questionnaire, QuestionnaireTemplate, QuestionnaireTemplateQuestion
from app.models.distribution import AssessmentDistribution, QuestionnaireDistribution
from app.models.assessment import AssessmentResponse
from app.models.questionnaire import QuestionnaireResponse
from app.models.result import AssessmentResult, QuestionnaireResult

router = APIRouter(prefix="/mobile", tags=["移动端-通知和量表"])

@router.get("/notifications", response_model=Dict[str, Any])
def get_mobile_notifications(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    status: str = Query(None, description="通知状态过滤：unread/read"),
    db: Session = Depends(get_db)
):
    """
    获取移动端用户通知列表
    """
    try:
        # 从请求状态获取用户信息
        current_user = getattr(request.state, 'user', None)
        if not current_user:
            raise HTTPException(status_code=401, detail="未认证")
            
        query = db.query(Alert).filter(
            Alert.user_id == current_user.id
        )
        
        if status:
            query = query.filter(Alert.status == status)
        
        # 按创建时间倒序排列
        query = query.order_by(desc(Alert.created_at))
        
        total = query.count()
        alerts = query.offset(skip).limit(limit).all()
        
        alert_list = []
        for alert in alerts:
            alert_list.append({
                "id": alert.id,
                "title": alert.title,
                "content": alert.content,
                "type": alert.type,
                "status": alert.status,
                "created_at": alert.created_at.isoformat() if alert.created_at else None
            })
        
        return {
            "status": "success",
            "data": {
                "notifications": alert_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取通知失败: {str(e)}")

@router.put("/notifications/{notification_id}/read", response_model=Dict[str, Any])
def mark_notification_read(
    notification_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    标记通知为已读
    """
    try:
        # 从请求状态获取用户信息
        current_user = getattr(request.state, 'user', None)
        if not current_user:
            raise HTTPException(status_code=401, detail="未认证")
            
        alert = db.query(Alert).filter(
            Alert.id == notification_id,
            Alert.user_id == current_user.id
        ).first()
        
        if not alert:
            raise HTTPException(status_code=404, detail="通知不存在")
        
        alert.status = "read"
        db.commit()
        
        return {
            "status": "success",
            "message": "通知已标记为已读"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"标记通知失败: {str(e)}")

@router.get("/assessments", response_model=Dict[str, Any])
def get_mobile_assessments(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    status: str = Query(None, description="状态过滤：pending/completed"),
    custom_id: str = Query(None, description="用户ID（可选）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取移动端用户的量表列表，聚合模板和题目信息
    """
    try:
        # 获取用户ID，优先从header中获取
        user_id = custom_id
        if not user_id:
            user_id = request.headers.get("X-User-ID")
        
        # 直接查询Assessment表，不使用join
        query = db.query(Assessment)
        if user_id:
            # 直接通过Assessment表的custom_id字段过滤
            query = query.filter(Assessment.custom_id == user_id)
        else:
            # 如果没有指定用户ID，返回所有数据（用于测试）
            pass
        if status:
            query = query.filter(Assessment.status == status)
        query = query.order_by(desc(Assessment.created_at))
        # 计算总数
        total = query.count()
        assessments = query.offset(skip).limit(limit).all()
        assessment_list = []
        for assessment in assessments:
            # ---------修正：兜底补全template_id---------
            template = None
            questions = []
            if not assessment.template_id:
                # 优先通过template_key匹配模板，再用name兜底匹配
                template_obj = None
                if hasattr(assessment, 'template_key') and assessment.template_key:
                    template_obj = db.query(AssessmentTemplate).filter(AssessmentTemplate.template_key == assessment.template_key).first()
                if not template_obj:
                    template_obj = db.query(AssessmentTemplate).filter(AssessmentTemplate.name == assessment.name).first()
                if template_obj:
                    assessment.template_id = template_obj.id
                    db.commit()
            if assessment.template_id:
                template_obj = db.query(AssessmentTemplate).filter(AssessmentTemplate.id == assessment.template_id).first()
                if template_obj:
                    template = {
                        "id": template_obj.id,
                        "name": template_obj.name,
                        "assessment_type": str(template_obj.assessment_type) if template_obj.assessment_type else None,
                        "version": template_obj.version,
                        "description": template_obj.description,
                        "instructions": template_obj.instructions,
                    }
                    # 获取题目
                    questions = [
                        {
                            "id": q.id,
                            "question_id": q.question_id,
                            "question_text": q.question_text,
                            "question_type": q.question_type,
                            "options": q.options,
                            "order": q.order,
                            "is_required": q.is_required,
                            "jump_logic": q.jump_logic
                        }
                        for q in db.query(AssessmentTemplateQuestion).filter(AssessmentTemplateQuestion.template_id == template_obj.id).order_by(AssessmentTemplateQuestion.order)
                    ]
                    template["questions"] = questions
            assessment_list.append({
                "id": assessment.id,
                "name": assessment.name or "未命名量表",
                "title": assessment.name or "未命名量表",
                "assessment_type": str(assessment.assessment_type) if assessment.assessment_type else None,
                "status": assessment.status,
                "created_at": assessment.created_at.isoformat() if assessment.created_at else None,
                "completed_at": assessment.completed_at.isoformat() if assessment.completed_at else None,
                "template": template
            })
        return {
            "status": "success",
            "data": {
                "assessments": assessment_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取量表失败: {str(e)}")

@router.get("/questionnaires", response_model=Dict[str, Any])
def get_mobile_questionnaires(
    request: Request,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    status: str = Query(None, description="状态过滤：pending/completed"),
    custom_id: str = Query(None, description="用户ID（可选）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """
    获取移动端用户的问卷列表，聚合模板和题目信息（优化版本，减少数据库查询）
    """
    try:
        # 获取用户ID，优先从header中获取
        user_id = custom_id
        if not user_id:
            user_id = request.headers.get("X-User-ID")
        
        # 直接查询Questionnaire表，不使用join
        query = db.query(Questionnaire)
        if user_id:
            # 直接通过Questionnaire表的custom_id字段过滤
            query = query.filter(Questionnaire.custom_id == user_id)
        else:
            # 如果没有指定用户ID，返回所有数据（用于测试）
            pass
        if status:
            query = query.filter(Questionnaire.status == status)
        query = query.order_by(desc(Questionnaire.created_at))
        total = query.count()
        questionnaires = query.offset(skip).limit(limit).all()
        
        # 批量获取模板信息，避免循环查询
        template_ids = [getattr(q, 'template_id', None) for q in questionnaires if getattr(q, 'template_id', None)]
        templates_dict = {}
        if template_ids:
            templates = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id.in_(template_ids)).all()
            templates_dict = {t.id: t for t in templates}
        
        # 批量获取问题，避免循环查询
        questions_dict = {}
        if template_ids:
            questions = db.query(QuestionnaireTemplateQuestion).filter(
                QuestionnaireTemplateQuestion.template_id.in_(template_ids)
            ).order_by(QuestionnaireTemplateQuestion.template_id, QuestionnaireTemplateQuestion.order).all()
            
            for q in questions:
                if q.template_id not in questions_dict:
                    questions_dict[q.template_id] = []
                questions_dict[q.template_id].append({
                    "id": q.id,
                    "question_id": q.question_id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": q.options,
                    "order": q.order,
                    "is_required": q.is_required,
                    "jump_logic": q.jump_logic
                })
        
        # 批量获取分发记录
        distributions_dict = {}
        if user_id:
            questionnaire_ids = [q.id for q in questionnaires]
            distributions = db.query(QuestionnaireDistribution).filter(
                QuestionnaireDistribution.questionnaire_id.in_(questionnaire_ids),
                QuestionnaireDistribution.custom_id == current_user.custom_id
            ).all()
            distributions_dict = {d.questionnaire_id: d for d in distributions}
        
        questionnaire_list = []
        for questionnaire in questionnaires:
            template = None
            template_id = getattr(questionnaire, 'template_id', None)
            
            if template_id and template_id in templates_dict:
                template_obj = templates_dict[template_id]
                template = {
                    "id": template_obj.id,
                    "name": template_obj.name,
                    "questionnaire_type": template_obj.questionnaire_type,
                    "version": template_obj.version,
                    "description": template_obj.description,
                    "instructions": template_obj.instructions,
                    "questions": questions_dict.get(template_id, [])
                }
            
            distribution = distributions_dict.get(questionnaire.id)
            
            questionnaire_list.append({
                "id": questionnaire.id,
                "name": questionnaire.title,  # 添加name字段以保持一致性
                "title": questionnaire.title,
                "description": questionnaire.notes,
                "status": questionnaire.status,
                "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
                "updated_at": questionnaire.updated_at.isoformat() if questionnaire.updated_at else None,
                "completed_at": distribution.completed_at.isoformat() if distribution and distribution.completed_at else None,
                "template": template
            })
        return {
            "status": "success",
            "data": {
                "questionnaires": questionnaire_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取问卷失败: {str(e)}")

@router.get("/assessments/{assessment_id}", response_model=Dict[str, Any])
def get_mobile_assessment_detail(
    assessment_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    获取量表详情
    """
    try:
        # 从请求状态获取用户信息
        current_user = getattr(request.state, 'user', None)
        if not current_user:
            raise HTTPException(status_code=401, detail="未认证")
            
        # 验证用户是否有权限访问该量表
        distribution = db.query(AssessmentDistribution).filter(
            AssessmentDistribution.assessment_id == assessment_id,
            AssessmentDistribution.custom_id == current_user.custom_id
        ).first()
        
        if not distribution:
            raise HTTPException(status_code=404, detail="量表不存在或无权限访问")
        
        assessment = db.query(Assessment).filter(
            Assessment.id == assessment_id
        ).first()
        
        if not assessment:
            raise HTTPException(status_code=404, detail="量表不存在")
        
        # 获取量表项目
        items = []
        for item in assessment.items:
            items.append({
                "id": item.id,
                "question_id": item.question_id,
                "question_text": item.question_text,
                "question_type": item.question_type,
                "options": item.options,
                "order_num": item.order_num,
                "is_required": item.is_required,
                "answer": item.answer,
                "score": item.score
            })
        
        return {
            "status": "success",
            "data": {
                "id": assessment.id,
                "name": assessment.name or "未命名量表",
                "assessment_type": assessment.assessment_type.value if assessment.assessment_type else None,
                "status": assessment.status,
                "items": items,
                "created_at": assessment.created_at.isoformat() if assessment.created_at else None,
                "completed_at": assessment.completed_at.isoformat() if assessment.completed_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取量表详情失败: {str(e)}")

@router.get("/questionnaires/{questionnaire_id}", response_model=Dict[str, Any])
def get_mobile_questionnaire_detail(
    questionnaire_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    获取问卷详情
    """
    try:
        # 从请求状态获取用户信息
        current_user = getattr(request.state, 'user', None)
        if not current_user:
            raise HTTPException(status_code=401, detail="未认证")
            
        # 验证用户是否有权限访问该问卷
        distribution = db.query(QuestionnaireDistribution).filter(
            QuestionnaireDistribution.questionnaire_id == questionnaire_id,
            QuestionnaireDistribution.custom_id == current_user.custom_id
        ).first()
        
        if not distribution:
            raise HTTPException(status_code=404, detail="问卷不存在或无权限访问")
        
        questionnaire = db.query(Questionnaire).filter(
            Questionnaire.id == questionnaire_id
        ).first()
        
        if not questionnaire:
            raise HTTPException(status_code=404, detail="问卷不存在")
        
        return {
            "status": "success",
            "data": {
                "id": questionnaire.id,
                "title": questionnaire.title,
                "description": questionnaire.description,
                "questions": questionnaire.questions,
                "status": questionnaire.status,
                "created_at": questionnaire.created_at.isoformat() if questionnaire.created_at else None,
                "updated_at": questionnaire.updated_at.isoformat() if questionnaire.updated_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取问卷详情失败: {str(e)}")

@router.post("/assessments/{assessment_id}/submit", response_model=Dict[str, Any])
def submit_mobile_assessment_result(
    assessment_id: int,
    request: Request,
    request_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db)
):
    """
    移动端提交评估量表结果，保存到数据库
    """
    try:
        # 从请求状态获取用户信息
        current_user = getattr(request.state, 'user', None)
        if not current_user:
            return {"status": "error", "message": "未认证"}
            
        if "answers" not in request_data:
            return {"status": "error", "message": "缺少answers字段"}
        answers = request_data["answers"]
        # 验证分发权限
        distribution = db.query(AssessmentDistribution).filter(
            AssessmentDistribution.assessment_id == assessment_id,
            AssessmentDistribution.custom_id == current_user.custom_id
        ).first()
        if not distribution:
            return {"status": "error", "message": "无权限提交此评估量表"}
        assessment = db.query(Assessment).filter(Assessment.id == assessment_id).first()
        if not assessment:
            return {"status": "error", "message": "评估量表不存在"}
        if assessment.status == "completed":
            return {"status": "error", "message": "评估量表已完成，无法重复提交"}
        # 获取模板
        template = None
        if assessment.template_id:
            template = db.query(AssessmentTemplate).filter(AssessmentTemplate.id == assessment.template_id).first()
        if not template:
            return {"status": "error", "message": "评估量表模板不存在"}
        # 获取模板题目
        template_questions = db.query(AssessmentTemplateQuestion).filter(AssessmentTemplateQuestion.template_id == template.id).all()
        question_map = {str(q.question_id): q for q in template_questions}
        # 验证答案
        validated_answers = []
        total_score = 0
        for answer in answers:
            if not isinstance(answer, dict):
                return {"status": "error", "message": "答案格式错误"}
            question_id = str(answer.get("question_id", ""))
            if question_id not in question_map:
                return {"status": "error", "message": f"问题ID {question_id} 不存在"}
            question = question_map[question_id]
            if question.is_required and ("answer" not in answer or answer["answer"] is None):
                return {"status": "error", "message": f"问题 {question_id} 为必填项"}
            validated_answer = {
                "question_id": question_id,
                "answer": answer.get("answer"),
                "score": answer.get("score", 0)
            }
            validated_answers.append(validated_answer)
            total_score += answer.get("score", 0)
        # 结果分析（简单示例）
        result_category = "正常"
        conclusion = "评估完成"
        if hasattr(template, "max_score") and template.max_score:
            if total_score >= template.max_score * 0.8:
                result_category = "高分"
                conclusion = "请关注异常"
        # 更新assessment
        assessment.status = "completed"
        assessment.completed_at = datetime.now()
        assessment.score = total_score
        assessment.max_score = template.max_score
        assessment.result = result_category
        assessment.conclusion = conclusion
        assessment.notes = str({"answers": validated_answers})
        # 更新分发记录
        distribution.status = "completed"
        distribution.completed_at = datetime.now()
        # 保存填写记录
        response = AssessmentResponse(
            assessment_id=assessment_id,
            custom_id=current_user.custom_id,
            answers={"answers": validated_answers},
            score=total_score,
            result=result_category,
            notes=conclusion
        )
        db.add(response)
        
        # 创建评估结果记录（用于历史记录显示）
        assessment_result = AssessmentResult(
            assessment_id=assessment_id,
            custom_id=current_user.custom_id,
            template_id=assessment.template_id,
            total_score=total_score,
            max_score=template.max_score,
            percentage=round((total_score / template.max_score * 100), 2) if template.max_score and template.max_score > 0 else 0,
            result_level=result_category,
            result_category=result_category,
            interpretation=conclusion,
            recommendations="请根据评估结果咨询专业医生",
            dimension_scores={"total": total_score},
            calculation_details={"answers": validated_answers, "total_questions": len(validated_answers)},
            raw_answers={"answers": validated_answers},
            report_generated=True,
            report_content=f"评估结果：{result_category}，总分：{total_score}/{template.max_score}，结论：{conclusion}",
            report_format="html",
            status="calculated",
            calculated_at=datetime.now()
        )
        db.add(assessment_result)
        
        db.commit()
        return {
            "status": "success",
            "message": "评估量表提交成功",
            "data": {
                "assessment_id": assessment_id,
                "total_score": total_score,
                "max_score": template.max_score,
                "result_category": result_category,
                "conclusion": conclusion,
                "completed_at": assessment.completed_at.isoformat(),
                "answers_count": len(validated_answers),
                "response_id": response.id
            }
        }
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": f"提交评估量表失败: {str(e)}"}

@router.post("/questionnaires/{questionnaire_id}/submit", response_model=Dict[str, Any])
def submit_mobile_questionnaire_result(
    questionnaire_id: int,
    request: Request,
    request_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db)
):
    """
    移动端提交问卷结果，保存到数据库
    """
    try:
        # 从请求状态获取用户信息
        current_user = getattr(request.state, 'user', None)
        if not current_user:
            return {"status": "error", "message": "未认证"}
            
        if "answers" not in request_data:
            return {"status": "error", "message": "缺少answers字段"}
        answers = request_data["answers"]
        # 验证分发权限
        distribution = db.query(QuestionnaireDistribution).filter(
            QuestionnaireDistribution.questionnaire_id == questionnaire_id,
            QuestionnaireDistribution.custom_id == current_user.custom_id
        ).first()
        if not distribution:
            return {"status": "error", "message": "无权限提交此问卷"}
        questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
        if not questionnaire:
            return {"status": "error", "message": "问卷不存在"}
        if questionnaire.status == "completed":
            return {"status": "error", "message": "问卷已完成，无法重复提交"}
        # 获取模板
        template = None
        if hasattr(questionnaire, 'template_id') and questionnaire.template_id:
            template = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id == questionnaire.template_id).first()
        if not template:
            return {"status": "error", "message": "问卷模板不存在"}
        # 获取模板题目
        template_questions = db.query(QuestionnaireTemplateQuestion).filter(QuestionnaireTemplateQuestion.template_id == template.id).all()
        question_map = {str(q.question_id): q for q in template_questions}
        # 验证答案
        validated_answers = []
        for answer in answers:
            if not isinstance(answer, dict):
                return {"status": "error", "message": "答案格式错误"}
            question_id = str(answer.get("question_id", ""))
            if question_id not in question_map:
                return {"status": "error", "message": f"问题ID {question_id} 不存在"}
            question = question_map[question_id]
            if question.is_required and ("answer" not in answer or answer["answer"] is None):
                return {"status": "error", "message": f"问题 {question_id} 为必填项"}
            validated_answer = {
                "question_id": question_id,
                "answer": answer.get("answer")
            }
            validated_answers.append(validated_answer)
        # 结果分析（简单示例）
        result_category = "正常"
        conclusion = "问卷完成"
        # 更新questionnaire
        questionnaire.status = "completed"
        questionnaire.completed_at = datetime.now() if hasattr(questionnaire, 'completed_at') else None
        questionnaire.conclusion = conclusion
        # 更新分发记录
        distribution.status = "completed"
        distribution.completed_at = datetime.now()
        # 保存填写记录
        response = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            custom_id=current_user.custom_id,
            answers={"answers": validated_answers},
            status="completed"
        )
        db.add(response)
        
        # 创建问卷结果记录（用于历史记录显示）
        questionnaire_result = QuestionnaireResult(
            questionnaire_id=questionnaire_id,
            response_id=response.id,
            custom_id=current_user.custom_id,
            template_id=getattr(questionnaire, 'template_id', None),
            total_score=0,  # 问卷通常不计分
            max_score=0,
            percentage=100,  # 完成度100%
            result_level="已完成",
            result_category=result_category,
            interpretation=conclusion,
            recommendations="感谢您完成问卷调查",
            dimension_scores={"completed": True},
            calculation_details={"answers": validated_answers, "total_questions": len(validated_answers)},
            raw_answers={"answers": validated_answers},
            report_generated=True,
            report_content=f"问卷调查结果：{result_category}，共回答{len(validated_answers)}个问题，结论：{conclusion}",
            report_format="html",
            status="calculated",
            calculated_at=datetime.now()
        )
        db.add(questionnaire_result)
        
        db.commit()
        return {
            "status": "success",
            "message": "问卷提交成功",
            "data": {
                "questionnaire_id": questionnaire_id,
                "response_id": response.id,
                "completed_at": questionnaire.completed_at.isoformat() if hasattr(questionnaire, 'completed_at') and questionnaire.completed_at else None,
                "answers_count": len(validated_answers),
                "result_category": result_category,
                "conclusion": conclusion
            }
        }
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": f"提交问卷失败: {str(e)}"}