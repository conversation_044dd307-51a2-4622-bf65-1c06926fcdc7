"""体检报告屏幕模块

提供体检报告的查看、添加、编辑、删除和分析功能。
"""

import logging
from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ListProperty, ObjectProperty, BooleanProperty
from .base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.divider import MDDivider
from theme import AppTheme, AppMetrics, FontStyles
from utils.cloud_api import get_cloud_api
from utils.health_data_manager import get_health_data_manager
from kivymd.uix.dialog import MDDialog
from kivymd.uix.textfield import MDTextField
from kivymd.uix.filemanager import MDFileManager
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from widgets.logo import HealthLogo
import os
import matplotlib.pyplot as plt
from kivy.uix.image import Image
from kivy.core.image import Image as CoreImage
import io
from kivy.factory import Factory

# 获取日志记录器
logger = logging.getLogger(__name__)

KV = '''
<PhysicalExamCard>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(120)
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]
    elevation: 2
    padding: [dp(16), dp(8), dp(16), dp(8)]
    MDLabel:
        text: root.title
        font_size: app.font_styles.TITLE_MEDIUM['font_size']
        font_name: app.font_styles.TITLE_MEDIUM['font_name']
        bold: app.font_styles.TITLE_MEDIUM['bold']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]
    MDLabel:
        text: root.date
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
    MDLabel:
        text: root.summary
        font_size: app.font_styles.BODY_SMALL['font_size']
        font_name: app.font_styles.BODY_SMALL['font_name']
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
    MDButton:
        style: "text"
        on_release: root.on_analyze()
        MDButtonText:
            text: "动态分析"
            font_size: app.font_styles.BUTTON_MEDIUM['font_size']
            font_name: app.font_styles.BUTTON_MEDIUM['font_name']
            bold: app.font_styles.BUTTON_MEDIUM['bold']
            theme_text_color: "Primary"

<PhysicalExamScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR
    
    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(8)
        
        # 顶部应用栏
        MDBoxLayout:
            id: app_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(4), dp(0), dp(4), dp(0)]
            
            MDIconButton:
                icon: "arrow-left"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.go_back()
            
            MDLabel:
                text: "体检报告"
                font_size: app.font_styles.TITLE_LARGE['font_size']
                font_name: app.font_styles.TITLE_LARGE['font_name']
                bold: app.font_styles.TITLE_LARGE['bold']
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                halign: "center"
                valign: "center"
            
            MDIconButton:
                icon: "plus"
                icon_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                on_release: root.add_report()
        
        # 添加Logo组件
        HealthLogo:
            id: health_logo
            size_hint_y: None
            height: dp(120)
            logo_size: dp(80), dp(80)
            title_font_size: dp(18)
            subtitle_font_size: dp(14)
        MDScrollView:
            do_scroll_x: False
            do_scroll_y: True
            MDBoxLayout:
                id: reports_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(12)
'''

class PhysicalExamCard(MDCard):
    title = StringProperty("")
    date = StringProperty("")
    summary = StringProperty("")
    report_data = ObjectProperty(None)
    selected = BooleanProperty(False)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.checkbox = None
        self.delete_btn = None
        self.build_extra_ui()
    
    def build_extra_ui(self):
        self.delete_btn = MDIconButton(
            icon="delete", 
            pos_hint={"right": 1, "top": 1}, 
            theme_icon_color="Custom",
            icon_color=(1, 0, 0, 1), 
            on_release=self.on_delete
        )
        self.add_widget(self.delete_btn)
    
    def on_delete(self, *args):
        """删除按钮点击事件"""
        try:
            # 获取父屏幕并调用删除方法
            screen = self.get_root_window().children[0]
            if hasattr(screen, 'delete_report'):
                screen.delete_report(self.report_data)
        except Exception as e:
            logger.error(f"删除报告时出错: {e}")
    
    def on_analyze(self, *args):
        """分析按钮点击事件"""
        try:
            # 获取父屏幕并调用分析方法
            screen = self.get_root_window().children[0]
            if hasattr(screen, 'analyze_report'):
                screen.analyze_report(self.report_data)
        except Exception as e:
            logger.error(f"分析报告时出错: {e}")
    
    def setup_checkbox(self):
        """设置复选框"""
        self.checkbox = MDCheckbox(
            size_hint=(None, None), 
            size=(dp(24), dp(24)), 
            pos_hint={"x": 0, "top": 1}, 
            opacity=0
        )
        self.checkbox.bind(active=self.on_select)
        self.add_widget(self.checkbox)
    
    def on_select(self, checkbox, active):
        """复选框选择事件"""
        self.selected = active
        try:
            # 通知父屏幕选择状态变化
            screen = self.get_root_window().children[0]
            if hasattr(screen, 'on_card_select'):
                screen.on_card_select(self, active)
        except Exception as e:
            logger.error(f"处理卡片选择时出错: {e}")
    
    def toggle_batch_mode(self, batch_mode):
        """切换批量操作模式"""
        if self.checkbox:
            self.checkbox.opacity = 1 if batch_mode else 0
        if self.delete_btn:
            self.delete_btn.opacity = 0 if batch_mode else 1
    
    def set_selectable(self, selectable):
        """设置是否可选择"""
        if self.checkbox:
            self.checkbox.opacity = 1 if selectable else 0
            self.checkbox.active = False
    
    def on_delete(self):
        """删除报告"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app.root.current_screen, 'confirm_delete_report'):
                app.root.current_screen.confirm_delete_report(self.report_data)
        except Exception as e:
            logger.error(f"删除报告时出错: {e}")
    
    def on_analyze(self):
        """分析报告"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app.root.current_screen, 'show_analysis_dialog'):
                app.root.current_screen.show_analysis_dialog([self.report_data])
        except Exception as e:
            logger.error(f"分析报告时出错: {e}")
    def on_remind(self):
        """设置复查提醒"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification("复查提醒功能待实现")
            else:
                from kivymd.uix.dialog import MDDialog
                from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText
                dialog = MDDialog(
                    MDDialogHeadlineText(
                        text="复查提醒",
                        halign="center",
                    ),
                    MDDialogSupportingText(
                        text="此处展示体检报告的复查提醒设置（待接入）",
                        halign="center",
                    ),
                    size_hint=(0.8, 0.4)
                )
                dialog.open()
        except Exception as e:
            logger.error(f"设置复查提醒时出错: {e}")
    
    def on_edit(self):
        """编辑报告"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app.root.current_screen, 'edit_report'):
                app.root.current_screen.edit_report(self.report_data)
        except Exception as e:
            logger.error(f"编辑报告时出错: {e}")

class PhysicalExamScreen(BaseScreen):
    reports = ListProperty([])
    file_manager = None
    dialog = None
    editing_report = None
    batch_mode = False
    selected_reports = ListProperty([])
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        self.exam_records = []
        Clock.schedule_once(self.init_ui, 0.2)

    def on_enter(self):
        super().on_enter()
        self.init_ui()

    def init_ui(self, dt=0):
        self.load_exam_records()

    def load_exam_records(self):
        """加载体检报告列表"""
        try:
            user_id = getattr(self.app, 'user_data', {}).get('custom_id', None)
            if not user_id:
                self.show_error("未获取到用户ID，请重新登录")
                if hasattr(self, 'ids') and 'reports_container' in self.ids:
                    self.ids.reports_container.clear_widgets()
                    empty_label = MDLabel(
                        text="暂无体检报告\n请先登录后查看",
                        halign="center",
                        theme_text_color="Secondary",
                        font_size=app.font_styles.BODY_MEDIUM['font_size'],
                        font_name=app.font_styles.BODY_MEDIUM['font_name'],
                        role="medium"
                    )
                    self.ids.reports_container.add_widget(empty_label)
                return
            if not hasattr(self, 'ids') or 'reports_container' not in self.ids:
                return
            if hasattr(self, "ids") and "reports_container" in self.ids and self.ids.reports_container:
                try:
                    self.ids.reports_container.clear_widgets()
                except ReferenceError:
                    pass
            cloud_api = get_cloud_api()
            result = cloud_api.get_documents(document_type="physical_exam", custom_id=user_id)
            self.reports = result.get('documents', []) if result else []
            
            for rec in self.reports:
                card = PhysicalExamCard(title=rec.get("title", ""), date=rec.get("date", ""), summary=rec.get("summary", ""), report_data=rec)
                card.set_selectable(self.batch_mode)
                card.on_edit = lambda *a, rec=rec: self.edit_report(rec)
                self.ids.reports_container.add_widget(card)
            
            # 如果没有报告，显示空状态提示
            if not self.reports:
                self.show_info("暂无体检报告，点击右上角添加按钮开始记录")
                
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 加载报告失败: {e}")
            self.show_error(f"加载报告失败: {str(e)}")
            self.reports = []
            if hasattr(self, 'ids') and 'reports_container' in self.ids:
                self.ids.reports_container.clear_widgets()

    def go_back(self):
        """返回上一页"""
        try:
            self.exit_batch_mode()
            # 直接返回主页，避免调用不存在的super().go_back()
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 返回失败: {e}")
            app = MDApp.get_running_app()
            app.root.current = 'homepage_screen'

    def add_report(self):
        """添加体检报告"""
        try:
            self.editing_report = None
            self.show_report_dialog()
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 添加报告失败: {e}")
            self.show_error(f"添加报告失败: {str(e)}")

    def edit_report(self, report):
        """编辑体检报告"""
        try:
            self.editing_report = report
            self.show_report_dialog(report)
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 编辑报告失败: {e}")
            self.show_error(f"编辑报告失败: {str(e)}")

    def show_report_dialog(self, report=None):
        """显示体检报告对话框"""
        try:
            if self.dialog:
                self.dialog.dismiss(force=True)
            title_field = MDTextField(hint_text="报告标题", text=report.get('title', '') if report else '')
            date_field = MDTextField(hint_text="日期(YYYY-MM-DD)", text=report.get('date', '') if report else '')
            summary_field = MDTextField(hint_text="摘要", text=report.get('summary', '') if report else '')
            file_path = {'value': report.get('file_path', '') if report else ''}
            def select_file(*_):
                if not self.file_manager:
                    self.file_manager = MDFileManager(select_path=lambda path: set_file(path), exit_manager=lambda *a: self.file_manager.close())
                self.file_manager.show(os.path.expanduser("~"))
            def set_file(path):
                file_path['value'] = path
                self.file_manager.close()
            content = MDBoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(200))
            content.add_widget(title_field)
            content.add_widget(date_field)
            content.add_widget(summary_field)
            file_btn = MDButton(MDButtonText(text="选择文件"), on_release=select_file)
            content.add_widget(file_btn)
            from kivymd.uix.dialog import MDDialogHeadlineText
            self.dialog = MDDialog(
                MDDialogHeadlineText(
                    text="体检报告",
                    halign="center",
                ),
                content=content,
                buttons=[
                    MDButton(MDButtonText(text="取消"), on_release=lambda *a: self.dialog.dismiss()),
                    MDButton(MDButtonText(text="保存"), on_release=lambda *a: self.save_report(title_field.text, date_field.text, summary_field.text, file_path['value']))
                ])
            self.dialog.open()
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 显示报告对话框失败: {e}")
            self.show_error(f"显示报告对话框失败: {str(e)}")

    def save_report(self, title, date, summary, file_path):
        """保存体检报告"""
        try:
            self.dialog.dismiss()
            cloud_api = get_cloud_api()
            user_id = getattr(self.app, 'user_data', {}).get('user_id', None)
            meta = {"title": title, "date": date, "summary": summary, "user_id": user_id}
            if self.editing_report:
                meta['document_id'] = self.editing_report.get('document_id')
            if file_path:
                result = cloud_api.upload_file(file_path, metadata=meta, document_type="physical_exam")
            else:
                result = cloud_api._make_request("POST", "documents/update", json_data=meta)
            if result and (result.get('success') or result.get('status') == 'success'):
                self.show_info("保存成功")
                self.load_exam_records()
            else:
                self.show_info("保存失败: {}".format(result.get('message', '未知错误')))
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 保存报告失败: {e}")
            self.show_error(f"保存报告失败: {str(e)}")

    def confirm_delete_report(self, report):
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="确认删除",
                halign="center",
            ),
            MDDialogSupportingText(
                text="确定要删除该体检报告吗？",
                halign="center",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="取消"), on_release=lambda *a: dialog.dismiss()),
                MDButton(MDButtonText(text="删除"), on_release=lambda *a: self.delete_report(report, dialog)),
                spacing="8dp",
            ),
        )
        dialog.open()

    def delete_report(self, report, dialog=None):
        """删除体检报告"""
        try:
            cloud_api = get_cloud_api()
            result = cloud_api._make_request("POST", "documents/delete", json_data={"document_id": report.get('document_id')})
            if dialog:
                dialog.dismiss()
            if result and (result.get('success') or result.get('status') == 'success'):
                self.show_info("删除成功")
                self.load_exam_records()
            else:
                self.show_info("删除失败: {}".format(result.get('message', '未知错误')))
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 删除报告失败: {e}")
            self.show_error(f"删除报告失败: {str(e)}")

    def enter_batch_mode(self):
        self.batch_mode = True
        self.selected_reports = []
        self.load_exam_records()
        self.show_info("已进入批量操作模式，可多选卡片进行批量删除/分析")

    def exit_batch_mode(self):
        self.batch_mode = False
        self.selected_reports = []
        self.load_exam_records()

    def on_card_select(self, card, selected):
        if selected:
            if card.report_data not in self.selected_reports:
                self.selected_reports.append(card.report_data)
        else:
            if card.report_data in self.selected_reports:
                self.selected_reports.remove(card.report_data)

    def batch_delete(self):
        if not self.selected_reports:
            self.show_info("请先选择要删除的报告")
            return
        from kivymd.uix.dialog import MDDialogHeadlineText, MDDialogSupportingText, MDDialogButtonContainer
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="批量删除",
                halign="center",
            ),
            MDDialogSupportingText(
                text=f"确定要删除选中的{len(self.selected_reports)}份体检报告吗？",
                halign="center",
            ),
            MDDialogButtonContainer(
                MDButton(MDButtonText(text="取消"), on_release=lambda *a: dialog.dismiss()),
                MDButton(MDButtonText(text="删除"), on_release=lambda *a: self._batch_delete_confirm(dialog)),
                spacing="8dp",
            ),
        )
        dialog.open()

    def _batch_delete_confirm(self, dialog):
        cloud_api = get_cloud_api()
        failed = 0
        for report in self.selected_reports:
            result = cloud_api._make_request("POST", "documents/delete", json_data={"document_id": report.get('document_id')})
            if not (result and (result.get('success') or result.get('status') == 'success')):
                failed += 1
        dialog.dismiss()
        if failed == 0:
            self.show_info("批量删除成功")
        else:
            self.show_info(f"部分删除失败，共{failed}份失败")
        self.exit_batch_mode()

    def batch_analyze(self):
        if not self.selected_reports:
            self.show_info("请先选择要分析的报告")
            return
        self.show_analysis_dialog(self.selected_reports)

    def show_analysis_dialog(self, reports):
        dialog = None
        def plot_to_image():
            plt.figure(figsize=(4,2))
            for i, r in enumerate(reports):
                x = list(range(5))
                y = [i*2 + v for v in [1,2,3,2,1]]
                plt.plot(x, y, label=r.get('title', f'报告{i+1}'))
            plt.legend()
            buf = io.BytesIO()
            plt.savefig(buf, format='png')
            buf.seek(0)
            return buf
        img_buf = plot_to_image()
        img = CoreImage(img_buf, ext='png').texture
        image_widget = Image(texture=img, size_hint_y=None, height=dp(160))
        ai_result = "AI分析结果：\n" + "\n".join([f"{r.get('title','')}: 指标正常" for r in reports])
        content = MDBoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(220))
        content.add_widget(image_widget)
        content.add_widget(MDLabel(text=ai_result, theme_text_color="Custom", text_color=(0,0,0,1)))
        from kivymd.uix.dialog import MDDialogHeadlineText
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="趋势分析",
                halign="center",
            ),
            type="custom", content_cls=content, buttons=[
            MDButton(MDButtonText(text="关闭"), on_release=lambda *a: dialog.dismiss())
        ])
        dialog.open()

    def show_info(self, message):
        """显示信息提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_notification'):
                app.show_notification(message)
            else:
                from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    pos_hint={"center_x": 0.5},
                    duration=2,
                )
                snackbar.open()
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 显示信息失败: {e}")
    
    def show_error(self, message):
        """显示错误提示"""
        try:
            app = MDApp.get_running_app()
            if hasattr(app, 'show_error'):
                app.show_error(message)
            else:
                self.show_info(f"错误: {message}")
        except Exception as e:
            KivyLogger.error(f"PhysicalExamScreen: 显示错误失败: {e}")

    def show_exam_detail(self, exam_data):
        content = MDBoxLayout(orientation='vertical', spacing=dp(8), size_hint_y=None, height=dp(200))
        content.add_widget(MDLabel(text=exam_data["summary"], font_size=app.font_styles.BODY_MEDIUM['font_size'], font_name=app.font_styles.BODY_MEDIUM['font_name'], role="medium", theme_text_color="Primary"))
        dialog = MDDialog()
        dialog.add_widget(MDLabel(text="体检详情", font_size=app.font_styles.TITLE_MEDIUM['font_size'], font_name=app.font_styles.TITLE_MEDIUM['font_name'], size_hint_y=None, height=dp(40)))
        dialog.add_widget(content)
        button_container = MDBoxLayout(orientation="horizontal", spacing=dp(8), size_hint_y=None, height=dp(48), padding=[dp(16), 0, dp(16), dp(8)])
        close_btn = MDButton(MDButtonText(text="关闭", font_size=app.font_styles.BUTTON_MEDIUM['font_size'], font_name=app.font_styles.BUTTON_MEDIUM['font_name'], style="text", on_release=lambda x: dialog.dismiss()))
        button_container.add_widget(close_btn)
        dialog.add_widget(button_container)
        dialog.open()

# 在类定义后注册Factory
Factory.register('PhysicalExamScreen', cls=PhysicalExamScreen)
Builder.load_string(KV)