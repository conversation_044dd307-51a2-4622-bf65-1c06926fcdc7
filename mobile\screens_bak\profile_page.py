from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from kivy.uix.screenmanager import Screen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
import os
import json

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDListItem, MDListItemLeadingIcon, MDListItemHeadlineText, MDListItemSupportingText, MDList, MDListItemTrailingIcon
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldHelperText, MDTextFieldMaxLengthText, MDTextFieldLeadingIcon
from kivymd.uix.segmentedbutton import MDSegmentedButton, MDSegmentedButtonItem
from kivymd.uix.menu import MDDropdownMenu  # Correct import for MDDropdownMenu

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入Logo组件
from widgets.logo import HealthLogo, add_logo_to_layout

# 导入二维码工具
from utils.qrcode_utils import DynamicQRCode, QRCodeScanner

# 导入用户管理器
from utils.user_manager import get_user_manager

# 尝试加载KV，确保它在类定义之前注册
try:
    KV = '''
<ProfileSettingItem>:
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(10)]
    elevation: 0
    padding: [dp(15), dp(10), dp(10), dp(10)]
    on_release: root.on_item_press()

    MDListItemLeadingIcon:
        icon: root.icon
        theme_icon_color: "Custom"
        icon_color: app.theme.PRIMARY_COLOR

    MDListItemHeadlineText:
        text: root.title
        theme_text_color: "Primary"
        font_style: "Body"

<ProfileSection>:
    orientation: 'vertical'
    adaptive_height: True
    padding: [dp(16), dp(8), dp(16), dp(8)]
    spacing: dp(8)

    MDLabel:
        text: root.title
        font_size: app.font_styles.TITLE_MEDIUM['font_size']
        font_name: app.font_styles.TITLE_MEDIUM['font_name']
        bold: app.font_styles.TITLE_MEDIUM['bold']
        theme_text_color: "Primary"
        adaptive_height: True

<ProfilePage>:
    name: "profile_page"
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    MDScrollView:
        id: scroll_view
        do_scroll_x: False
        do_scroll_y: True

        MDBoxLayout:
            id: main_layout
            orientation: 'vertical'
            adaptive_height: True
            spacing: dp(8)

            MDBoxLayout:
                id: logo_container
                orientation: 'vertical'
                size_hint_y: None
                height: dp(200)
                padding: [0, dp(10), 0, dp(5)]
                size_hint_x: 0.8
                pos_hint: {"center_x": 0.5}

                HealthLogo:
                    id: health_logo

            MDCard:
                id: user_info_card
                orientation: 'vertical'
                size_hint: None, None
                size: dp(340), dp(240)
                pos_hint: {"center_x": 0.5}
                padding: [dp(15), dp(15), dp(15), dp(15)]
                spacing: dp(10)
                elevation: 3
                radius: [dp(15)]
                md_bg_color: app.theme.CARD_BACKGROUND

                MDLabel:
                    text: "个人健康码"
                    font_style: "Body"
                    role: "medium"
                    bold: True
                    theme_text_color: "Primary"
                    halign: 'center'
                    size_hint_y: None
                    height: dp(30)

                MDBoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: dp(140)
                    spacing: dp(8)

                    MDBoxLayout:
                        orientation: 'vertical'
                        size_hint_x: 0.5
                        spacing: dp(8)
                        padding: [dp(5), dp(0), dp(0), dp(0)]
                        pos_hint: {"center_y": 0.5}

                        MDBoxLayout:
                            orientation: 'horizontal'
                            adaptive_height: True
                            spacing: dp(2)

                            MDLabel:
                                text: '真实姓名'
                                font_style: "Label"
                                theme_text_color: "Secondary"
                                size_hint_y: None
                                height: dp(18)

                            MDLabel:
                                id: username_label
                                text: root.username
                                font_style: "Label"
                                bold: True
                                theme_text_color: "Primary"
                                size_hint_y: None
                                height: dp(25)

                        MDBoxLayout:
                            orientation: 'horizontal'
                            adaptive_height: True
                            spacing: dp(2)
                            padding: [0, dp(5), 0, 0]

                            MDLabel:
                                text: '身份'
                                font_style: "Label"
                                theme_text_color: "Secondary"
                                size_hint_y: None
                                height: dp(18)

                            MDBoxLayout:
                                orientation: 'horizontal'
                                adaptive_height: True
                                size_hint_y: None
                                height: dp(25)

                                MDLabel:
                                    id: identity_label
                                    text: "个人用户"
                                    font_style: "Label"
                                    theme_text_color: "Primary"
                                    adaptive_height: True

                        MDBoxLayout:
                            orientation: 'horizontal'
                            adaptive_height: True
                            spacing: dp(2)
                            padding: [0, dp(5), 0, 0]

                            MDLabel:
                                text: '用户ID'
                                font_style: "Label"
                                theme_text_color: "Secondary"
                                size_hint_y: None
                                height: dp(18)

                            MDLabel:
                                id: user_id_label
                                text: root.custom_id
                                font_style: "Label"
                                theme_text_color: "Primary"
                                size_hint_y: None
                                height: dp(25)

                    MDBoxLayout:
                        id: qrcode_container
                        orientation: 'vertical'
                        size_hint_x: 0.5
                        pos_hint: {"center_x": 0.5, "center_y": 0.5}

                MDLabel:
                    text: "请保持健康码更新，以便及时获取健康状态"
                    font_style: "Label"
                    theme_text_color: "Secondary"
                    halign: 'center'
                    size_hint_y: None
                    height: dp(25)

            MDBoxLayout:
                id: settings_container
                orientation: 'vertical'
                adaptive_height: True
                spacing: dp(5)
                padding: [dp(10), dp(5), dp(10), dp(5)]

            MDBoxLayout:
                orientation: 'horizontal'
                adaptive_height: True
                padding: [dp(16), dp(16), dp(16), dp(16)]

                MDButton:
                    style: "filled"
                    md_bg_color: app.theme.PRIMARY_COLOR
                    on_release: root.on_back()
                    size_hint: None, None
                    size: dp(120), dp(45)
                    pos_hint: {"center_y": 0.5}

                    MDButtonText:
                        text: "返回主页"
                        font_size: app.font_styles.BUTTON_MEDIUM['font_size']
                        font_name: app.font_styles.BUTTON_MEDIUM['font_name']
                        bold: app.font_styles.BUTTON_MEDIUM['bold']
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_LIGHT
'''
    Builder.load_string(KV)
    print("KV预先加载成功")
except Exception as e:
    print(f"配置文件加载失败: {e}")

class ProfileSettingItem(MDListItem):
    """个人资料设置项"""
    icon = StringProperty("account")
    title = StringProperty("设置项")

    def __init__(self, on_press_callback=None, **kwargs):
        super(ProfileSettingItem, self).__init__(**kwargs)
        self.on_press_callback = on_press_callback

    def on_item_press(self):
        if self.on_press_callback:
            self.on_press_callback()

class ProfileSection(MDBoxLayout):
    """个人资料设置分区"""
    title = StringProperty("设置分区")

    def __init__(self, **kwargs):
        super(ProfileSection, self).__init__(**kwargs)
        self.items_container = MDList()
        self.add_widget(self.items_container)

    def add_item(self, icon, title, on_press_callback=None):
        item = ProfileSettingItem(
            icon=icon,
            title=title,
            on_press_callback=on_press_callback
        )
        self.items_container.add_widget(item)
        return item

class ProfilePage(Screen):
    """个人资料页面"""
    username = StringProperty("未登录")
    custom_id = StringProperty("未注册")
    qr_code_texture = ObjectProperty(None)
    qr_manager = ObjectProperty(None)

    def __init__(self, **kwargs):
        try:
            # 初始化属性
            self.qr_manager = None
            self.qrcode_image = None
            self.dialog = None
            self.code_dialog = None
            self.password_dialog = None
            self.scanner = None
            self.doctor_manager = None  # 添加医生管理器
            self.logo_added = False  # 添加标志变量，跟踪Logo是否已添加

            # 调用父类初始化
            super(ProfilePage, self).__init__(**kwargs)

        except Exception as e:
            print(f"个人资料页面初始化失败: {e}")

    def on_kv_post(self, base_widget):
        """在KV规则应用后调用的方法"""
        # 延迟初始化UI，确保Kivy完全构建界面
        Clock.schedule_once(self._delayed_init, 0.5)

    def _delayed_init(self, dt):
        """延迟初始化，确保Kivy完全构建界面"""
        try:
            # 检查ids是否存在
            if not hasattr(self, 'ids') or not self.ids:
                # 再次延迟初始化
                Clock.schedule_once(self._delayed_init, 0.5)
                return

            # 初始化UI
            self.init_ui()

            # 初始化二维码扫描器
            try:
                self.scanner = QRCodeScanner(callback=self.on_qr_scan_result)
            except Exception:
                pass

            # 初始化二维码管理器，确保UI已加载
            Clock.schedule_once(self.init_qrcode_manager, 0.5)
        except Exception as e:
            print(f"界面初始化延迟失败: {e}")

    def init_ui(self, dt=0):
        """初始化UI"""
        try:
            # 设置ScrollView大小
            self.ids.scroll_view.size_hint = (1, None)
            self.ids.scroll_view.height = Window.height

            # 清理可能已存在的设置区域
            self._check_and_cleanup_sections()

            # 检查settings_container是否存在
            if 'settings_container' not in self.ids and 'main_layout' in self.ids:
                # 尝试在main_layout中创建settings_container
                settings_container = MDBoxLayout(
                    id='settings_container',
                    orientation='vertical',
                    adaptive_height=True,
                    spacing=dp(5),
                    padding=[dp(10), dp(5), dp(10), dp(5)]
                )

                # 添加到main_layout的合适位置（在logo和用户卡片之后，底部按钮之前）
                if len(self.ids.main_layout.children) >= 2:
                    self.ids.main_layout.add_widget(settings_container, index=1)
                else:
                    self.ids.main_layout.add_widget(settings_container)

                # 添加到self.ids字典
                self.ids['settings_container'] = settings_container

            # 添加各种设置区域
            self.add_personal_info_section()
            self.add_account_security_section()
            self.add_legal_support_section()
            self.add_system_section()

            # 初始化二维码管理器
            Clock.schedule_once(self.init_qrcode_manager, 0.5)

            # 获取用户管理器并加载当前用户信息
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            print("加载用户信息：", current_user.username if current_user else "未登录")
            if current_user:
                # 只有当用户已登录时才加载用户信息
                # 使用标准字段名full_name，如果没有则使用real_name，最后使用username
                self.username = (current_user.full_name if hasattr(current_user, 'full_name') else None) or \
                               (current_user.real_name if hasattr(current_user, 'real_name') else None) or \
                               current_user.username
                print(f"个人页面显示用户信息: full_name={getattr(current_user, 'full_name', None)}, real_name={getattr(current_user, 'real_name', None)}, username={current_user.username}")

                # 检查是否有custom_id字段 (后端生成的带前缀的格式化ID)
                # 优先使用后端返回的custom_id
                app = MDApp.get_running_app()
                backend_custom_id = None
                if hasattr(app, 'user_data') and isinstance(app.user_data, dict) and 'custom_id' in app.user_data:
                    backend_custom_id = app.user_data.get('custom_id')
                    print(f"从后端获取custom_id: {backend_custom_id}")

                # 如果后端没有返回custom_id，则使用本地存储的custom_id
                if not backend_custom_id and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    custom_id = current_user.custom_id
                    print(f"使用本地custom_id: {custom_id}")
                elif backend_custom_id:
                    custom_id = backend_custom_id
                    if hasattr(current_user, 'custom_id'):
                        current_user.custom_id = backend_custom_id
                        user_manager.save_accounts()
                        print(f"更新本地用户的custom_id: {backend_custom_id}")
                else:
                    custom_id = None
                    print(f"未获取到custom_id")
                role = getattr(current_user, 'role', getattr(current_user, 'identity', '')).lower()
                print(f"用户角色: {role}, 当前custom_id: {custom_id}")
                self.custom_id = custom_id
                print(f"个人页面最终显示: 姓名={self.username}, custom_id={self.custom_id}")
                print(f"设置用户信息：用户名={self.username}, custom_id={self.custom_id}")
                if hasattr(self, 'ids') and 'id_number_label' in self.ids:
                    self.ids.id_number_label.text = current_user.id_number or ""
                if hasattr(self, 'ids'):
                    if 'username_label' in self.ids:
                        self.ids.username_label.text = self.username
                        print(f"更新用户名标签：{self.username}")
                    if 'user_id_label' in self.ids:
                        self.ids.user_id_label.text = self.custom_id or "未注册"
                    if 'identity_label' in self.ids and hasattr(current_user, 'role'):
                        role = current_user.role
                        if role.lower() == "personal":
                            self.ids.identity_label.text = "个人用户"
                        else:
                            self.ids.identity_label.text = role
                # 加载用户信息
                self.load_user_info()
            else:
                # 用户未登录，使用默认值
                self.username = "未登录"
                self.custom_id = "未注册"
                # 更新UI上的显示
                if hasattr(self, 'ids'):
                    if 'username_label' in self.ids:
                        self.ids.username_label.text = self.username
                    if 'user_id_label' in self.ids:
                        self.ids.user_id_label.text = self.custom_id

            return True
        except Exception as e:
            print(f"界面初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _check_and_cleanup_sections(self):
        """检查并清理已有的设置区域，避免重复添加"""
        try:
            if not hasattr(self, 'ids'):
                return

            # 先检查settings_container是否存在
            if 'settings_container' in self.ids:
                # 直接清除settings_container中的所有子组件
                self.ids.settings_container.clear_widgets()
            elif 'main_layout' in self.ids:
                # 兼容旧版，从main_layout中查找并移除ProfileSection
                main_layout = self.ids.main_layout
                sections_to_remove = []

                # 遍历所有子组件，找到ProfileSection类型的组件
                for child in main_layout.children:
                    if isinstance(child, ProfileSection):
                        sections_to_remove.append(child)

                # 移除找到的设置区域
                for section in sections_to_remove:
                    main_layout.remove_widget(section)
        except Exception:
            pass

    def init_qrcode_manager(self, dt=0):
        """初始化二维码管理器"""
        # 确保UI已经初始化
        if not hasattr(self, 'ids') or 'qrcode_container' not in self.ids:
            Clock.schedule_once(self.init_qrcode_manager, 0.5)
            return

        try:
            custom_id = getattr(self, 'custom_id', None) or "未注册"
            self.qr_manager = DynamicQRCode(custom_id, update_interval=60)

            # 设置二维码更新回调
            self.qr_manager.start(
                user_data={
                    "name": self.username,
                    "custom_id": str(custom_id)
                },
                health_data={
                    "blood_pressure": {"systolic": 120, "diastolic": 80},
                    "blood_glucose": {"value": 5.5},
                    "temperature": 36.5
                },
                logo_path="assets/icons/health-Logo.png",
                callback=self.update_qrcode_display
            )
            return True
        except Exception as e:
            print(f"二维码初始化失败: {e}")
            # 创建一个占位的二维码区域
            self.create_placeholder_qrcode()
            return False

    def update_qrcode_display(self, texture):
        """更新二维码显示"""
        try:
            # 确保控件已经完全初始化
            if not hasattr(self, 'ids') or 'qrcode_container' not in self.ids:
                Clock.schedule_once(lambda dt: self.update_qrcode_display(texture), 0.5)
                return

            # 创建图像组件显示二维码 - 增大二维码尺寸
            if not hasattr(self, 'qrcode_image') or not self.qrcode_image:
                # 第一次创建图像组件，增大尺寸
                self.qrcode_image = Image(
                    size_hint=(None, None),
                    size=(dp(140), dp(140)),  # 增大尺寸
                    pos_hint={"center_x": 0.5, "center_y": 0.5}  # 确保居中显示
                )

                # 清除qrcode_container中可能已有的内容
                qrcode_container = self.ids.qrcode_container
                qrcode_container.clear_widgets()  # 确保容器为空

                # 直接添加到qrcode_container
                qrcode_container.add_widget(self.qrcode_image)

            # 更新纹理
            if self.qrcode_image and texture:
                self.qrcode_image.texture = texture

        except Exception as e:
            print(f"二维码显示更新失败: {e}")
            import traceback
            traceback.print_exc()

    def load_user_info(self):
        """加载用户信息"""
        try:
            # 从用户管理器获取当前用户信息
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            # 尝试从应用中获取用户真实姓名和用户ID
            app = MDApp.get_running_app()
            user_id = None
            if hasattr(app, 'user_data'):
                user_data = getattr(app, 'user_data', {})
                if isinstance(user_data, dict):
                    user_id = user_data.get('user_id')
                    # 检查是否有custom_id
                    if 'custom_id' in user_data:
                        user_id = user_data.get('custom_id')

            if current_user:
                # 设置用户名和用户ID
                # 优先使用full_name，如果没有则使用real_name，最后使用username
                self.username = (current_user.full_name if hasattr(current_user, 'full_name') else None) or \
                               (current_user.real_name if hasattr(current_user, 'real_name') else None) or \
                               current_user.username
                print(f"load_user_info: full_name={getattr(current_user, 'full_name', None)}, real_name={getattr(current_user, 'real_name', None)}, username={current_user.username}")

                # 检查是否有custom_id字段 (后端生成的带前缀的格式化ID)
                # 优先使用后端返回的custom_id
                backend_custom_id = None
                if hasattr(app, 'user_data') and isinstance(app.user_data, dict) and 'custom_id' in app.user_data:
                    backend_custom_id = app.user_data.get('custom_id')
                    print(f"load_user_info: 从后端获取custom_id: {backend_custom_id}")

                # 如果后端没有返回custom_id，则使用本地存储的custom_id
                if not backend_custom_id and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    custom_id = current_user.custom_id
                    print(f"load_user_info: 使用本地custom_id: {custom_id}")
                elif backend_custom_id:
                    custom_id = backend_custom_id
                    if hasattr(current_user, 'custom_id'):
                        current_user.custom_id = backend_custom_id
                        user_manager.save_accounts()
                        print(f"load_user_info: 更新本地用户的custom_id: {backend_custom_id}")
                else:
                    custom_id = None
                    print(f"未获取到custom_id")
                role = getattr(current_user, 'role', getattr(current_user, 'identity', '')).lower()
                print(f"load_user_info: 用户角色: {role}, 当前custom_id: {custom_id}")

                self.custom_id = custom_id
                print(f"load_user_info: 最终显示: 姓名={self.username}, custom_id={self.custom_id}")

                # 获取用户身份
                user_identity = getattr(current_user, 'role', getattr(current_user, 'identity', '个人用户'))

                # 将英文角色转换为中文显示
                if user_identity.lower() == "personal":
                    user_identity = "个人用户"

                # 更新UI上的显示
                if hasattr(self, 'ids'):
                    if 'username_label' in self.ids:
                        self.ids.username_label.text = self.username
                    if 'user_id_label' in self.ids:
                        self.ids.user_id_label.text = self.custom_id or "未注册"
                    if 'identity_label' in self.ids:
                        self.ids.identity_label.text = user_identity

                # 更新二维码中的用户信息
                if hasattr(self, 'qr_manager') and self.qr_manager:
                    self.qr_manager.user_data = {
                        "name": self.username,
                        "custom_id": str(self.custom_id),
                        "identity": user_identity
                    }
            else:
                # 如果用户管理器中没有用户信息，使用默认值
                # 注意：不再尝试从user_data.json加载，因为这可能导致启动时的错误
                self.username = "未登录"
                self.custom_id = "未注册"

                # 更新UI上的显示
                if hasattr(self, 'ids'):
                    if 'username_label' in self.ids:
                        self.ids.username_label.text = self.username
                    if 'user_id_label' in self.ids:
                        self.ids.user_id_label.text = self.custom_id
                    if 'identity_label' in self.ids:
                        # 确保显示中文的"个人用户"
                        self.ids.identity_label.text = "个人用户"
        except Exception as e:
            print(f"加载用户信息失败: {e}")
            # 设置默认值
            self.username = "未登录"
            self.custom_id = "未注册"

            # 更新UI上的显示
            if hasattr(self, 'ids'):
                if 'username_label' in self.ids:
                    self.ids.username_label.text = self.username
                if 'user_id_label' in self.ids:
                    self.ids.user_id_label.text = self.custom_id
                if 'identity_label' in self.ids:
                    # 确保显示中文的"个人用户"
                    self.ids.identity_label.text = "个人用户"

    def add_personal_info_section(self):
        """添加个人信息设置区域"""
        try:
            section = ProfileSection(title="个人信息")

            # 优先使用新的settings_container
            if hasattr(self, 'ids') and 'settings_container' in self.ids:
                self.ids.settings_container.add_widget(section)
                print("已添加个人信息区域到settings_container")
            elif hasattr(self, 'ids') and 'main_layout' in self.ids:
                # 兼容旧版，添加到main_layout
                main_layout = self.ids.main_layout

                # 添加到合适的位置
                if len(main_layout.children) >= 2:
                    insert_index = len(main_layout.children) - 2
                    main_layout.add_widget(section, index=insert_index)
                else:
                    main_layout.add_widget(section)
                print("已添加个人信息区域到main_layout")
            else:
                print("警告: 未找到可用的容器")
                return False

            # 添加设置项
            section.add_item("account-edit", "个人信息修改", self.on_edit_personal_info)
            section.add_item("account-details", "健康档案", self.on_health_records)
            # 移除家庭成员管理，将其功能合并到切换用户中
            section.add_item("doctor", "医生管理", self.on_doctor_management)
            return True
        except Exception as e:
            print(f"添加个人信息设置区域失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def add_account_security_section(self):
        """添加账户与安全设置区域"""
        try:
            section = ProfileSection(title="账户与安全")

            # 优先使用新的settings_container
            if hasattr(self, 'ids') and 'settings_container' in self.ids:
                self.ids.settings_container.add_widget(section)
            elif hasattr(self, 'ids') and 'main_layout' in self.ids:
                # 兼容旧版，添加到main_layout
                main_layout = self.ids.main_layout

                # 添加到合适的位置
                if len(main_layout.children) >= 2:
                    insert_index = len(main_layout.children) - 2
                    main_layout.add_widget(section, index=insert_index)
                else:
                    main_layout.add_widget(section)
            else:
                return False

            # 添加设置项 - 添加切换用户和身份切换按钮
            section.add_item("key", "密码修改", self.on_change_password)
            section.add_item("shield-account", "隐私设置", self.on_privacy_settings)
            section.add_item("cellphone", "手机绑定", self.on_bind_phone)
            section.add_item("message-badge", "消息通知", self.on_notification_settings)
            section.add_item("account-switch", "切换用户", self.on_switch_user)
            section.add_item("account-convert", "身份切换", self.on_switch_identity)
            return True
        except Exception:
            return False

    def add_legal_support_section(self):
        """添加法律与支持设置区域"""
        try:
            section = ProfileSection(title="法律与支持")

            # 优先使用新的settings_container
            if hasattr(self, 'ids') and 'settings_container' in self.ids:
                self.ids.settings_container.add_widget(section)
            elif hasattr(self, 'ids') and 'main_layout' in self.ids:
                # 兼容旧版，添加到main_layout
                main_layout = self.ids.main_layout

                # 添加到合适的位置
                if len(main_layout.children) >= 2:
                    insert_index = len(main_layout.children) - 2
                    main_layout.add_widget(section, index=insert_index)
                else:
                    main_layout.add_widget(section)
            else:
                return False

            # 添加设置项
            section.add_item("file-document", "用户协议", self.on_user_agreement)
            section.add_item("shield-lock", "隐私政策", self.on_privacy_policy)
            section.add_item("help-circle", "帮助中心", self.on_help_center)
            section.add_item("headset", "联系客服", self.on_contact_support)
            return True
        except Exception:
            return False

    def add_system_section(self):
        """添加系统设置区域"""
        try:
            section = ProfileSection(title="系统设置")

            # 优先使用新的settings_container
            if hasattr(self, 'ids') and 'settings_container' in self.ids:
                self.ids.settings_container.add_widget(section)
            elif hasattr(self, 'ids') and 'main_layout' in self.ids:
                # 兼容旧版，添加到main_layout
                main_layout = self.ids.main_layout

                # 添加到合适的位置
                if len(main_layout.children) >= 2:
                    insert_index = len(main_layout.children) - 2
                    main_layout.add_widget(section, index=insert_index)
                else:
                    main_layout.add_widget(section)
            else:
                return False

            # 添加设置项
            section.add_item("theme-light-dark", "主题设置", self.on_theme_settings)
            section.add_item("alert", "关于我们", self.on_about)
            section.add_item("logout", "退出登录", self.on_logout)
            return True
        except Exception as e:
            print(f"添加系统设置区域失败: {e}")
            return False

    def on_edit_personal_info(self):
        """编辑个人信息"""
        try:
            # 获取当前用户信息
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("无法获取当前用户信息")
                return

            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(400)
            )

            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True,
                size_hint_y=None
            )

            # 添加不可修改信息部分
            content.add_widget(MDLabel(
                text="基本信息（不可修改）",
                font_style="Body",
                role="medium",
                bold=True,
                adaptive_height=True,
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 获取用户ID，优先使用custom_id
            user_id = current_user.custom_id if hasattr(current_user, 'custom_id') and current_user.custom_id else current_user.user_id

            # 显示不可修改的信息
            fixed_info = [
                ("用户名", current_user.username),
                ("用户ID", user_id),
                ("姓名", current_user.real_name or current_user.username)
            ]

            for label, value in fixed_info:
                item_layout = MDBoxLayout(
                    orientation='horizontal',
                    adaptive_height=True,
                    spacing=dp(10)
                )
                item_layout.add_widget(MDLabel(
                    text=f"{label}:",
                    font_style="Body",
                    role="small",
                    theme_text_color="Secondary",
                    adaptive_height=True,
                    size_hint_x=0.3
                ))
                item_layout.add_widget(MDLabel(
                    text=str(value),
                    font_style="Body",
                    role="small",
                    adaptive_height=True,
                    size_hint_x=0.7
                ))
                content.add_widget(item_layout)

            # 添加可修改信息部分
            content.add_widget(MDLabel(
                text="可修改信息",
                font_style="Body",
                role="medium",
                bold=True,
                adaptive_height=True,
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 性别选择
            gender_layout = MDBoxLayout(
                orientation='vertical',
                adaptive_height=True,
                spacing=dp(5)
            )

            gender_label = MDLabel(
                text="性别",
                theme_text_color="Secondary",
                font_style="Body",
                adaptive_height=True
            )
            gender_layout.add_widget(gender_label)

            # 创建分段按钮作为性别选择器
            self.gender_segment = MDSegmentedButton(
                size_hint_y=None,
                height=dp(48)
            )

            # 添加性别选项
            genders = ["男", "女", "其他"]
            current_gender = getattr(current_user, 'gender', '男')

            for gender in genders:
                button_item = MDSegmentedButtonItem()
                button_item.add_widget(MDButtonText(text=gender))
                self.gender_segment.add_widget(button_item)
                if gender == current_gender:
                    button_item.active = True

            gender_layout.add_widget(self.gender_segment)
            content.add_widget(gender_layout)

            # 年龄输入
            self.age_input = MDTextField(
                hint_text="年龄",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                input_filter="int",
                text=str(getattr(current_user, 'age', ''))
            )
            content.add_widget(self.age_input)

            # 手机号输入
            self.phone_input = MDTextField(
                hint_text="手机号",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                input_filter="int",
                text=str(getattr(current_user, 'phone', ''))
            )
            content.add_widget(self.phone_input)

            # 身份证号输入
            self.id_card_input = MDTextField(
                hint_text="身份证号（选填）",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                text=str(getattr(current_user, 'id_number', ''))
            )
            content.add_widget(self.id_card_input)

            # 添加内容到滚动视图
            scroll.add_widget(content)

            # 显示对话框
            self.personal_info_dialog = MDDialog()
            self.personal_info_dialog.ids.container.add_widget(MDLabel(text="编辑个人信息", adaptive_height=True, pos_hint={"center_x": 0.5}))
            self.personal_info_dialog.ids.container.add_widget(scroll)

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.personal_info_dialog)
            )

            save_button = MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=lambda x: self.save_personal_info()
            )

            self.personal_info_dialog.ids.button_container.add_widget(cancel_button)
            self.personal_info_dialog.ids.button_container.add_widget(save_button)
            self.personal_info_dialog.open()
        except Exception as e:
            print(f"显示个人信息编辑对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示个人信息编辑对话框")

    def save_personal_info(self):
        """保存个人信息"""
        try:
            # 获取输入的个人信息
            name = ""
            gender = "男"
            age = ""
            phone = ""
            id_card = ""

            if hasattr(self, 'name_input') and self.name_input:
                name = self.name_input.text.strip()

            if hasattr(self, 'gender_segment'):
                for item in self.gender_segment.children:
                    if hasattr(item, 'active') and item.active:
                        gender = item.text
                        break

            if hasattr(self, 'age_input') and self.age_input:
                age = self.age_input.text.strip()

            if hasattr(self, 'phone_input') and self.phone_input:
                phone = self.phone_input.text.strip()

            if hasattr(self, 'id_card_input') and self.id_card_input:
                id_card = self.id_card_input.text.strip()

            # 验证输入
            if not name:
                self.show_error("请输入姓名")
                return

            if not age:
                self.show_error("请输入年龄")
                return

            if not phone or len(phone) != 11:
                self.show_error("请输入有效的手机号")
                return

            if id_card and len(id_card) != 18 and len(id_card) != 15:
                self.show_error("请输入有效的身份证号")
                return

            # 在实际应用中，这里应该将个人信息保存到数据库或本地存储
            # 这里简单地更新UI显示
            self.username = name
            if hasattr(self, 'ids') and 'username_label' in self.ids:
                self.ids.username_label.text = name

            # 保存用户信息到本地文件
            self.save_user_info_to_file({
                "name": name,
                "gender": gender,
                "age": age,
                "phone": phone,
                "id_card": id_card
            })

            # 关闭对话框
            if hasattr(self, 'personal_info_dialog') and self.personal_info_dialog:
                self.dismiss_dialog(self.personal_info_dialog)

            self.show_success("个人信息已更新")
        except Exception as e:
            print(f"保存个人信息失败: {e}")
            self.show_error("保存个人信息失败")

    def save_user_info_to_file(self, user_info):
        """保存用户信息到文件"""
        try:
            # 获取用户凭证文件路径
            import os
            credentials_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "user_data.json")

            # 读取现有数据
            import json
            data = {}
            if os.path.exists(credentials_file):
                try:
                    with open(credentials_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                except Exception:
                    data = {}

            # 更新用户信息
            custom_id = self.custom_id or "user123"  # 使用当前用户的custom_id
            if custom_id in data:
                # 更新现有用户信息
                data[custom_id].update(user_info)
            else:
                # 添加新用户信息
                data[custom_id] = user_info

            # 保存数据
            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            print(f"保存用户信息到文件失败: {e}")
            return False

    def on_health_records(self):
        """查看健康档案"""
        self.show_info("正在开发健康档案功能...")

    def on_doctor_management(self):
        """医生管理"""
        try:
            # 初始化医生管理器（如果尚未初始化）
            if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
                from utils.doctor_manager import DoctorManager
                self.doctor_manager = DoctorManager("user123")  # 在实际应用中应使用当前用户ID

            # 创建对话框内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                adaptive_height=True,
                size_hint_y=None,
                height=dp(400)
            )

            # 添加标题
            title_layout = MDBoxLayout(
                orientation='horizontal',
                adaptive_height=True,
                size_hint_y=None,
                height=dp(40)
            )

            title_label = MDLabel(
                text="我的医生",
                font_style="Body",
                role="medium",
                bold=True,
                adaptive_height=True,
                size_hint_x=0.7
            )
            title_layout.add_widget(title_label)

            # 添加医生按钮
            add_btn = MDIconButton(
                icon="account-plus",
                on_release=lambda x: self.show_add_doctor_dialog(),
                pos_hint={"center_y": 0.5}
            )
            title_layout.add_widget(add_btn)

            content.add_widget(title_layout)

            # 创建医生列表滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(250)
            )

            # 创建医生列表容器
            self.doctors_list = MDList()

            # 更新医生列表
            self.update_doctors_list()

            scroll.add_widget(self.doctors_list)
            content.add_widget(scroll)

            # 添加按钮
            btn_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                adaptive_height=True,
                pos_hint={"center_x": 0.5},
                padding=[0, dp(10), 0, 0]
            )

            # 扫码添加按钮
            scan_btn = MDButton(
                style="filled",
                on_release=lambda x: self.on_add_doctor_by_scan(),
                size_hint=(None, None),
                size=(dp(130), dp(45))
            )
            scan_btn.add_widget(MDButtonText(text="扫码添加"))
            btn_layout.add_widget(scan_btn)

            # 搜索医生按钮
            search_btn = MDButton(
                style="outlined",
                on_release=lambda x: self.on_search_doctor(),
                size_hint=(None, None),
                size=(dp(130), dp(45))
            )
            search_btn.add_widget(MDButtonText(text="搜索医生"))
            btn_layout.add_widget(search_btn)

            content.add_widget(btn_layout)

            # 显示对话框
            self.doctor_dialog = MDDialog()
            self.doctor_dialog.ids.container.add_widget(content)
            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.doctor_dialog)
            )
            self.doctor_dialog.ids.button_container.add_widget(close_button)
            self.doctor_dialog.open()
        except Exception as e:
            print(f"显示医生管理失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示医生管理")

    def update_doctors_list(self):
        """更新医生列表"""
        if not hasattr(self, 'doctors_list') or not self.doctors_list:
            return

        if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
            from utils.doctor_manager import DoctorManager
            self.doctor_manager = DoctorManager("user123")  # 在实际应用中应使用当前用户ID

        # 清空列表
        self.doctors_list.clear_widgets()

        # 获取所有医生
        doctors = self.doctor_manager.get_all_doctors()

        if not doctors:
            # 如果没有医生，显示提示
            empty_label = MDLabel(
                text="暂无关联医生，请添加",
                halign="center",
                theme_text_color="Secondary",
                font_style="Body",
                adaptive_height=True
            )
            self.doctors_list.add_widget(empty_label)
            return

        # 添加医生项
        for doctor in doctors:
            item = MDListItem()

            # 添加医生图标
            item.add_widget(MDListItemLeadingIcon(
                icon="doctor",
                theme_icon_color="Custom",
                icon_color=self.get_safe_primary_color()
            ))

            # 添加医生信息
            item.add_widget(MDListItemHeadlineText(
                text=f"{doctor['name']} ({doctor['department']})"
            ))

            # 添加医院信息
            item.add_widget(MDListItemSupportingText(
                text=f"{doctor['hospital']}"
            ))

            # 创建操作按钮容器 - 使用一个MDListItemTrailingIcon来包含操作按钮
            actions_container = MDListItemTrailingIcon(
                icon="dots-vertical",  # 使用菜单图标
                theme_icon_color="Custom",
                on_release=lambda x, doc=doctor: self.show_doctor_actions(doc)
            )

            item.add_widget(actions_container)

            self.doctors_list.add_widget(item)

    def show_doctor_actions(self, doctor):
        """显示医生操作菜单"""
        try:
            # 创建菜单项
            menu_items = [
                {
                    "text": "电话联系",
                    "on_release": lambda x=None: self.on_contact_doctor(doctor['phone'])
                },
                {
                    "text": "删除医生",
                    "on_release": lambda x=None: self.confirm_delete_doctor(doctor['doctor_id'])
                }
            ]

            # 获取最近一次点击的控件作为caller
            from kivy.core.window import Window
            caller = Window.focus_widget or self

            # 创建下拉菜单
            dropdown_menu = MDDropdownMenu(
                caller=caller,  # 使用焦点控件或self作为caller
                items=menu_items,
                position="auto",
                width=dp(200)
            )

            dropdown_menu.open()
        except Exception as e:
            print(f"显示医生操作菜单失败: {e}")
            self.show_error("无法显示医生操作菜单")

    def show_add_doctor_dialog(self):
        """显示添加医生对话框"""
        if hasattr(self, 'doctor_dialog') and self.doctor_dialog:
            self.dismiss_dialog(self.doctor_dialog)

        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=[dp(20), dp(20), dp(20), dp(0)],
            adaptive_height=True
        )

        # 添加输入框
        self.doctor_name_input = MDTextField(
            hint_text="医生姓名",
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(self.doctor_name_input)

        self.doctor_department_input = MDTextField(
            hint_text="科室",
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(self.doctor_department_input)

        self.doctor_hospital_input = MDTextField(
            hint_text="医院",
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(self.doctor_hospital_input)

        # 使用on_text回调替代max_text_length，限制输入11位数字
        self.doctor_phone_input = MDTextField(
            hint_text="联系电话",
            mode="outlined",
            size_hint_y=None,
            height=dp(48),
            input_filter="int"
        )
        self.doctor_phone_input.bind(text=self.limit_text_length)
        content.add_widget(self.doctor_phone_input)

        # 显示对话框
        self.add_doctor_dialog = MDDialog()
        self.add_doctor_dialog.ids.container.add_widget(MDLabel(text="添加医生", adaptive_height=True, pos_hint={"center_x": 0.5}))
        self.add_doctor_dialog.ids.container.add_widget(content)

        cancel_button = MDButton(
            MDButtonText(text="取消"),
            style="text",
            on_release=lambda x: self.dismiss_dialog(self.add_doctor_dialog)
        )

        add_button = MDButton(
            MDButtonText(text="添加"),
            style="text",
            on_release=lambda x: self.confirm_add_doctor()
        )

        self.add_doctor_dialog.ids.button_container.add_widget(cancel_button)
        self.add_doctor_dialog.ids.button_container.add_widget(add_button)
        self.add_doctor_dialog.open()

    def limit_text_length(self, instance, text):
        """限制文本输入长度"""
        max_length = 11  # 手机号最大长度
        if len(text) > max_length:
            instance.text = text[:max_length]

    def confirm_add_doctor(self):
        """确认添加医生"""
        # 获取输入的医生信息
        name = ""
        department = ""
        hospital = ""
        phone = ""

        if hasattr(self, 'doctor_name_input') and self.doctor_name_input:
            name = self.doctor_name_input.text.strip()

        if hasattr(self, 'doctor_department_input') and self.doctor_department_input:
            department = self.doctor_department_input.text.strip()

        if hasattr(self, 'doctor_hospital_input') and self.doctor_hospital_input:
            hospital = self.doctor_hospital_input.text.strip()

        if hasattr(self, 'doctor_phone_input') and self.doctor_phone_input:
            phone = self.doctor_phone_input.text.strip()

        # 验证输入
        if not name:
            self.show_error("请输入医生姓名")
            return

        if not department:
            self.show_error("请输入科室")
            return

        if not hospital:
            self.show_error("请输入医院")
            return

        if not phone or len(phone) != 11:
            self.show_error("请输入有效的联系电话")
            return

        # 添加医生
        if hasattr(self, 'doctor_manager') and self.doctor_manager:
            doctor = self.doctor_manager.add_doctor(name, department, hospital, phone)
            if doctor:
                self.show_success(f"已添加医生：{name}")
                # 关闭添加对话框
                if hasattr(self, 'add_doctor_dialog') and self.add_doctor_dialog:
                    self.dismiss_dialog(self.add_doctor_dialog)
                # 重新打开医生管理对话框
                self.on_doctor_management()
            else:
                self.show_error("添加医生失败")
        else:
            self.show_error("医生管理器未初始化")

    def confirm_delete_doctor(self, doctor_id):
        """确认删除医生"""
        if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
            self.show_error("医生管理器未初始化")
            return

        # 获取医生信息
        doctor = None
        for d in self.doctor_manager.get_all_doctors():
            if d['doctor_id'] == doctor_id:
                doctor = d
                break

        if not doctor:
            self.show_error("找不到该医生信息")
            return

        # 创建确认对话框
        confirm_dialog = MDDialog()
        confirm_dialog.ids.container.add_widget(MDLabel(text="删除医生", adaptive_height=True, pos_hint={"center_x": 0.5}))
        confirm_dialog.ids.container.add_widget(MDLabel(text=f"确定要删除{doctor['name']}医生吗？此操作不可撤销。", adaptive_height=True))

        cancel_button = MDButton(
            MDButtonText(text="取消"),
            style="text",
            on_release=lambda x: self.dismiss_dialog(confirm_dialog)
        )

        delete_button = MDButton(
            MDButtonText(text="删除"),
            style="text",
            on_release=lambda x: self.do_delete_doctor(confirm_dialog, doctor_id)
        )

        confirm_dialog.ids.button_container.add_widget(cancel_button)
        confirm_dialog.ids.button_container.add_widget(delete_button)
        confirm_dialog.open()

    def do_delete_doctor(self, dialog, doctor_id):
        """执行删除医生操作"""
        if dialog:
            self.dismiss_dialog(dialog)

        if hasattr(self, 'doctor_manager') and self.doctor_manager:
            success = self.doctor_manager.remove_doctor(doctor_id)
            if success:
                self.show_success("已删除医生")
                # 更新医生列表
                self.update_doctors_list()
            else:
                self.show_error("删除医生失败")
        else:
            self.show_error("医生管理器未初始化")

    def on_contact_doctor(self, phone):
        """联系医生"""
        # 在实际应用中，这里应该调用系统电话功能拨打电话
        self.show_info(f"正在拨打电话：{phone}")

    def on_add_doctor_by_scan(self):
        """通过扫描二维码添加医生"""
        if hasattr(self, 'doctor_dialog') and self.doctor_dialog:
            self.dismiss_dialog(self.doctor_dialog)

        # 启动摄像头扫描
        if hasattr(self, 'scanner') and self.scanner:
            success = self.scanner.start_scanning()
            if not success:
                self.show_error("无法启动相机，请检查相机权限")
        else:
            self.show_error("扫描器未初始化")

    def on_search_doctor(self):
        """搜索医生"""
        if hasattr(self, 'doctor_dialog') and self.doctor_dialog:
            self.dismiss_dialog(self.doctor_dialog)

        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=[dp(20), dp(20), dp(20), dp(0)],
            adaptive_height=True
        )

        # 添加搜索框
        self.doctor_search_input = MDTextField(
            hint_text="输入医生姓名、科室或医院",
            mode="outlined",
            size_hint_y=None,
            height=dp(48),
            icon_left="magnify"
        )
        content.add_widget(self.doctor_search_input)

        # 添加搜索结果列表
        result_label = MDLabel(
            text="搜索结果将显示在这里",
            theme_text_color="Secondary",
            font_style="Body",
            adaptive_height=True,
            halign="center"
        )
        content.add_widget(result_label)

        # 显示对话框
        self.search_dialog = MDDialog()
        self.search_dialog.ids.container.add_widget(MDLabel(text="搜索医生", adaptive_height=True, pos_hint={"center_x": 0.5}))
        self.search_dialog.ids.container.add_widget(content)

        cancel_button = MDButton(
            MDButtonText(text="取消"),
            style="text",
            on_release=lambda x: self.dismiss_dialog(self.search_dialog)
        )

        search_button = MDButton(
            MDButtonText(text="搜索"),
            style="text",
            on_release=lambda x: self.do_search_doctor()
        )

        self.search_dialog.ids.button_container.add_widget(cancel_button)
        self.search_dialog.ids.button_container.add_widget(search_button)
        self.search_dialog.open()

    def do_search_doctor(self):
        """执行搜索医生操作"""
        if hasattr(self, 'doctor_search_input') and self.doctor_search_input:
            keyword = self.doctor_search_input.text.strip()
            if not keyword:
                self.show_error("请输入搜索关键词")
                return

            # 在实际应用中，这里应该调用API搜索医生
            self.show_info(f"正在搜索：{keyword}")

            # 模拟搜索结果
            # 在实际应用中，这里应该显示搜索结果列表
            self.show_info("暂无搜索结果，请尝试其他关键词")
        else:
            self.show_error("搜索框未初始化")

    def on_change_password(self):
        """修改密码"""
        try:
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(300)
            )

            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True,
                size_hint_y=None
            )

            # 添加密码要求说明标签
            password_req_label = MDLabel(
                text="密码要求：8-20位字符，包含字母和数字",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            )
            content.add_widget(password_req_label)

            # 原密码输入框
            self.old_password = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                password=True
            )

            # 添加提示文本
            self.old_password.add_widget(MDTextFieldHintText(
                text="请输入原密码"
            ))

            # 添加辅助文本
            self.old_password.add_widget(MDTextFieldHelperText(
                text="请输入您的当前密码",
                mode="persistent"  # 设置为持久显示
            ))

            content.add_widget(self.old_password)

            # 新密码输入框
            self.new_password = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                password=True
            )

            # 添加提示文本
            self.new_password.add_widget(MDTextFieldHintText(
                text="请输入新密码"
            ))

            # 添加辅助文本
            self.new_password.add_widget(MDTextFieldHelperText(
                text="8-20位字符，必须包含字母和数字",
                mode="persistent"  # 设置为持久显示
            ))

            content.add_widget(self.new_password)

            # 确认密码输入框
            self.confirm_password = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                password=True
            )

            # 添加提示文本
            self.confirm_password.add_widget(MDTextFieldHintText(
                text="请再次输入新密码"
            ))

            # 添加辅助文本
            self.confirm_password.add_widget(MDTextFieldHelperText(
                text="请确保两次输入的密码一致",
                mode="persistent"  # 设置为持久显示
            ))

            content.add_widget(self.confirm_password)

            # 添加内容到滚动视图
            scroll.add_widget(content)

            # 显示对话框
            self.password_dialog = MDDialog()
            self.password_dialog.ids.container.add_widget(MDLabel(text="修改密码", adaptive_height=True, pos_hint={"center_x": 0.5}))
            self.password_dialog.ids.container.add_widget(scroll)

            # 添加操作按钮
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.password_dialog)
            )

            confirm_button = MDButton(
                MDButtonText(text="确认"),
                style="text",
                on_release=lambda x: self.confirm_change_password()
            )

            self.password_dialog.ids.button_container.add_widget(cancel_button)
            self.password_dialog.ids.button_container.add_widget(confirm_button)
            self.password_dialog.open()
        except Exception as e:
            print(f"显示密码修改对话框失败: {e}")
            self.show_error("无法显示密码修改对话框")

    def confirm_change_password(self):
        """确认修改密码"""
        try:
            # 获取输入的密码
            old_pwd = self.old_password.text.strip() if hasattr(self, 'old_password') and self.old_password else ""
            new_pwd = self.new_password.text.strip() if hasattr(self, 'new_password') and self.new_password else ""
            confirm_pwd = self.confirm_password.text.strip() if hasattr(self, 'confirm_password') and self.confirm_password else ""

            # 验证输入
            if not old_pwd or not new_pwd or not confirm_pwd:
                self.show_error("请填写完整信息")
                return

            # 验证新密码格式
            import re
            if len(new_pwd) < 8 or len(new_pwd) > 20:
                self.show_error("新密码长度必须在8-20位之间")
                return

            if not re.search(r'[A-Za-z]', new_pwd) or not re.search(r'[0-9]', new_pwd):
                self.show_error("新密码必须同时包含字母和数字")
                return

            if new_pwd != confirm_pwd:
                self.show_error("两次输入的新密码不一致")
                return

            # 获取当前用户
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("无法获取当前用户信息")
                return

            # 验证原密码
            if hasattr(current_user, 'password'):
                if current_user.password != old_pwd:
                    self.show_error("原密码错误")
                    return
            else:
                self.show_error("用户密码信息不完整")
                return

            # 更新密码
            current_user.password = new_pwd
            user_manager.save_accounts()

            # 关闭对话框
            if hasattr(self, 'password_dialog') and self.password_dialog:
                self.dismiss_dialog(self.password_dialog)

            self.show_success("密码修改成功")
        except Exception as e:
            print(f"修改密码失败: {e}")
            self.show_error("修改密码失败")

    def on_privacy_settings(self):
        """隐私设置"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加说明文本
            privacy_info = MDLabel(
                text="设置您的隐私选项和数据共享偏好",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            )
            content.add_widget(privacy_info)

            # 个人信息可见性设置
            content.add_widget(MDLabel(
                text="个人信息可见性",
                font_style="Body",
                role="medium",
                bold=True,
                adaptive_height=True,
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 可见性选项 - 使用按钮代替开关
            visibility_options = [
                {"title": "健康数据", "desc": "允许医生和健康顾问查看您的健康数据"},
                {"title": "个人资料", "desc": "允许其他用户查看您的基本资料"},
                {"title": "联系方式", "desc": "允许系统向您发送通知和提醒"}
            ]

            for option in visibility_options:
                option_layout = MDBoxLayout(
                    orientation='vertical',
                    size_hint_y=None,
                    height=dp(60),
                    padding=[0, dp(5), 0, dp(5)]
                )
                header = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(30)
                )

                header.add_widget(MDLabel(
                    text=option["title"],
                    font_style="Body",
                    role="medium",
                    adaptive_height=True,
                    size_hint_x=0.7
                ))

                # 使用按钮替代开关
                toggle_button = MDButton(
                    style="text",
                    size_hint_x=0.3,
                    pos_hint={"center_y": 0.5},
                    on_release=lambda x: x.parent.parent.ids.get('status_label', None).text == '启用' and setattr(x.parent.parent.ids.get('status_label', None), 'text', '禁用') or setattr(x.parent.parent.ids.get('status_label', None), 'text', '启用')
                )
                toggle_button.add_widget(MDButtonText(text="启用"))
                header.add_widget(toggle_button)

                option_layout.add_widget(header)
                option_layout.add_widget(MDLabel(
                    text=option["desc"],
                    font_style="Body",
                    role="small",
                    theme_text_color="Secondary",
                    adaptive_height=True
                ))

                content.add_widget(option_layout)

            # 数据共享设置
            content.add_widget(MDLabel(
                text="数据共享设置",
                font_style="Body",
                role="medium",
                bold=True,
                adaptive_height=True,
                padding=[0, dp(15), 0, dp(5)]
            ))

            data_sharing = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(40)
            )

            checkbox_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40)
            )

            # 使用按钮替代复选框
            check_button = MDButton(
                style="text",
                size_hint_x=0.2,
                on_release=lambda x: x.parent.ids.get('check_status', None).text == '✓' and setattr(x.parent.ids.get('check_status', None), 'text', '') or setattr(x.parent.ids.get('check_status', None), 'text', '✓')
            )
            check_button.add_widget(MDButtonText(text="□", id="check_status"))
            checkbox_layout.add_widget(check_button)

            checkbox_layout.add_widget(MDLabel(
                text="允许匿名数据用于健康研究",
                font_style="Body",
                role="small",
                adaptive_height=True,
                size_hint_x=0.8
            ))

            data_sharing.add_widget(checkbox_layout)
            content.add_widget(data_sharing)

            # 显示对话框
            privacy_dialog = MDDialog()
            privacy_dialog.ids.container.add_widget(MDLabel(text="隐私设置", adaptive_height=True, pos_hint={"center_x": 0.5}))
            privacy_dialog.ids.container.add_widget(content)

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(privacy_dialog)
            )

            save_button = MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=lambda x: self.save_privacy_settings(privacy_dialog)
            )

            privacy_dialog.ids.button_container.add_widget(cancel_button)
            privacy_dialog.ids.button_container.add_widget(save_button)
            privacy_dialog.open()
        except Exception as e:
            print(f"显示隐私设置失败: {e}")
            self.show_error("隐私设置功能暂时不可用")

    def save_privacy_settings(self, dialog):
        """保存隐私设置"""
        if dialog:
            self.dismiss_dialog(dialog)
        self.show_success("隐私设置已保存")

    def on_bind_phone(self):
        """手机绑定"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加说明文本
            bind_info = MDLabel(
                text="绑定手机号可以提高账号安全性，并接收重要通知。",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            )
            content.add_widget(bind_info)

            # 手机号输入框
            self.phone_bind_input = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                input_filter="int"
            )

            # 添加手机图标
            self.phone_bind_input.add_widget(MDTextFieldLeadingIcon(
                icon="cellphone"
            ))

            # 添加提示文本
            self.phone_bind_input.add_widget(MDTextFieldHintText(
                text="请输入手机号"
            ))

            # 添加辅助文本
            self.phone_bind_input.add_widget(MDTextFieldHelperText(
                text="请输入11位手机号码",
                mode="persistent"  # 设置为持久显示
            ))

            # 添加最大长度限制
            self.phone_bind_input.add_widget(MDTextFieldMaxLengthText(
                max_text_length=11
            ))

            content.add_widget(self.phone_bind_input)

            # 验证码输入区域
            code_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            # 验证码输入框
            self.verification_code_input = MDTextField(
                mode="outlined",
                size_hint_x=0.6,
                input_filter="int"
            )

            # 添加数字图标
            self.verification_code_input.add_widget(MDTextFieldLeadingIcon(
                icon="numeric"
            ))

            # 添加提示文本
            self.verification_code_input.add_widget(MDTextFieldHintText(
                text="验证码"
            ))

            # 添加辅助文本
            self.verification_code_input.add_widget(MDTextFieldHelperText(
                text="请输入6位验证码",
                mode="persistent"  # 设置为持久显示
            ))

            # 添加最大长度限制
            self.verification_code_input.add_widget(MDTextFieldMaxLengthText(
                max_text_length=6
            ))

            code_layout.add_widget(self.verification_code_input)

            # 获取验证码按钮
            self.get_code_button = MDButton(
                style="outlined",
                size_hint_x=0.4,
                on_release=lambda x: self.send_verification_code()
            )
            self.get_code_button.add_widget(MDButtonText(text="获取验证码"))
            code_layout.add_widget(self.get_code_button)

            content.add_widget(code_layout)

            # 显示对话框
            self.phone_bind_dialog = MDDialog()
            self.phone_bind_dialog.ids.container.add_widget(MDLabel(text="手机绑定", adaptive_height=True, pos_hint={"center_x": 0.5}))
            self.phone_bind_dialog.ids.container.add_widget(content)

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.phone_bind_dialog)
            )

            bind_button = MDButton(
                MDButtonText(text="绑定"),
                style="text",
                on_release=lambda x: self.confirm_bind_phone()
            )

            # 添加按钮到对话框
            self.phone_bind_dialog.ids.button_container.add_widget(cancel_button)
            self.phone_bind_dialog.ids.button_container.add_widget(bind_button)
            self.phone_bind_dialog.open()
        except Exception as e:
            print(f"手机绑定对话框创建失败: {e}")
            self.show_error("手机绑定功能暂时不可用")

    def confirm_bind_phone(self):
        """确认绑定手机号"""
        try:
            if not hasattr(self, 'phone_bind_input') or not self.phone_bind_input:
                self.show_error("手机号输入框未初始化")
                return

            if not hasattr(self, 'verification_code_input') or not self.verification_code_input:
                self.show_error("验证码输入框未初始化")
                return

            phone = self.phone_bind_input.text.strip()
            code = self.verification_code_input.text.strip()

            # 验证输入
            if not phone or len(phone) != 11:
                self.show_error("请输入有效的手机号")
                return

            if not code:
                self.show_error("请输入验证码")
                return

            # 在实际应用中，这里应该验证验证码是否正确
            # 这里只是模拟验证过程
            self.show_success("手机号绑定成功")

            # 关闭对话框
            if hasattr(self, 'phone_bind_dialog') and self.phone_bind_dialog:
                self.dismiss_dialog(self.phone_bind_dialog)

            # 更新用户信息
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            if current_user:
                if isinstance(current_user, dict):
                    current_user['phone'] = phone
                else:
                    current_user.phone = phone
                user_manager.save_accounts()
        except Exception as e:
            print(f"绑定手机号失败: {e}")
            self.show_error("绑定手机号失败")

    def on_notification_settings(self):
        """通知设置"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加说明文本
            settings_info = MDLabel(
                text="设置通知提醒方式和频率",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            )
            content.add_widget(settings_info)

            # 通知开关
            switch_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            switch_layout.add_widget(MDLabel(
                text="接收通知",
                font_style="Body",
                role="medium",
                adaptive_height=True,
                size_hint_x=0.7
            ))

            # 使用按钮替代开关
            toggle_button = MDButton(
                style="text",
                size_hint_x=0.3,
                pos_hint={"center_y": 0.5},
                on_release=lambda x: x.ids.get('status_text', None).text == '启用' and setattr(x.ids.get('status_text', None), 'text', '禁用') or setattr(x.ids.get('status_text', None), 'text', '启用')
            )
            toggle_button.add_widget(MDButtonText(text="启用", id="status_text"))
            switch_layout.add_widget(toggle_button)
            content.add_widget(switch_layout)

            # 通知类型选择
            content.add_widget(MDLabel(
                text="通知类型",
                font_style="Body",
                role="medium",
                adaptive_height=True,
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 通知类型复选框
            types = ["健康提醒", "预约提醒", "报告通知", "系统消息"]
            for type_name in types:
                type_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(40)
                )

                # 使用按钮替代复选框
                check_button = MDButton(
                    style="text",
                    size_hint_x=0.2,
                    on_release=lambda x, btn=type_name: self.toggle_checkbox(x)
                )
                check_button.add_widget(MDButtonText(text="✓"))
                type_layout.add_widget(check_button)

                type_layout.add_widget(MDLabel(
                    text=type_name,
                    font_style="Body",
                    role="small",
                    adaptive_height=True,
                    size_hint_x=0.8
                ))

                content.add_widget(type_layout)

            # 显示对话框
            notification_dialog = MDDialog()
            notification_dialog.ids.container.add_widget(MDLabel(text="通知设置", adaptive_height=True, pos_hint={"center_x": 0.5}))
            notification_dialog.ids.container.add_widget(content)

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(notification_dialog)
            )

            save_button = MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=lambda x: self.save_notification_settings(notification_dialog)
            )

            notification_dialog.ids.button_container.add_widget(cancel_button)
            notification_dialog.ids.button_container.add_widget(save_button)
            notification_dialog.open()
        except Exception as e:
            print(f"显示通知设置失败: {e}")
            self.show_error("通知设置功能暂时不可用")

    def save_notification_settings(self, dialog):
        """保存通知设置"""
        if dialog:
            self.dismiss_dialog(dialog)
        self.show_success("通知设置已保存")

    def on_switch_identity(self):
        """显示身份切换菜单"""
        try:
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("无法获取当前用户信息")
                return

            # 获取当前身份
            if hasattr(current_user, 'identity'):
                current_identity = current_user.identity
            elif isinstance(current_user, dict) and 'identity' in current_user:
                current_identity = current_user['identity']
            else:
                current_identity = '个人用户'

            # 获取用户可用的身份列表
            user_identities = user_manager.get_available_identities(current_user)

            # 如果用户没有可用身份或只有一个身份
            if not user_identities or len(user_identities) <= 1:
                self.show_info("您只有一个身份，不能进行身份切换！")
                return

            # 创建菜单项
            menu_items = []

            for identity in user_identities:
                # 为当前身份添加勾选标记
                display_text = f"{identity} ✓" if identity == current_identity else identity

                menu_items.append({
                    "text": display_text,
                    "on_release": lambda x=identity: self.do_switch_identity(x)
                })

            # 查找身份切换按钮
            identity_button = None
            for item in self.ids.settings_container.children:
                if isinstance(item, ProfileSection):
                    for child in item.children:
                        if isinstance(child, MDList):
                            for list_item in child.children:
                                if isinstance(list_item, ProfileSettingItem) and list_item.title == "身份切换":
                                    identity_button = list_item
                                    break

            # 如果找不到按钮，使用当前聚焦的部件
            if not identity_button:
                from kivy.core.window import Window
                identity_button = Window.focus_widget

            # 创建并显示下拉菜单 - 使用KivyMD 2.0.1版本的属性
            dropdown_menu = MDDropdownMenu(
                caller=identity_button,
                items=menu_items,
                position="auto", # 使用auto而不是bottom
                width=dp(200),   # 使用width而不是width_mult
            )

            dropdown_menu.open()
        except Exception as e:
            self.show_error(f"显示身份切换菜单失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def navigate_to_correct_screen(self, identity, screen_manager):
        """根据用户身份导航到相应的界面"""
        try:
            # 身份到屏幕的映射 - 更正映射关系
            identity_screen_map = {
                "个人用户": "homepage_screen",
                "健康顾问": "consultant_screen",
                "单位管理者": "unit_screen",
                "超级管理员": "supermanager_screen"
            }

            target_screen = identity_screen_map.get(identity)

            # 检查屏幕是否存在
            available_screens = [screen.name for screen in screen_manager.screens]
            print(f"可用屏幕: {available_screens}")
            print(f"目标屏幕: {target_screen}")

            if target_screen and target_screen in available_screens:
                screen_manager.current = target_screen
                print(f"已导航至 {target_screen}")
                return True
            else:
                self.show_error(f"无法导航到 {identity}({target_screen}) 对应的界面")
                return False
        except Exception as e:
            self.show_error(f"导航失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def do_switch_identity(self, identity):
        """切换用户身份"""
        try:
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("无法获取当前用户信息")
                return

            # 获取当前身份
            if hasattr(current_user, 'identity'):
                current_identity = current_user.identity
            elif isinstance(current_user, dict) and 'identity' in current_user:
                current_identity = current_user['identity']
            else:
                current_identity = '个人用户'

            # 如果选择的身份与当前身份相同，则不需要切换
            if identity == current_identity:
                self.show_info(f"您当前已经是 {identity} 身份")
                return

            # 更新用户身份
            try:
                # 更新用户身份
                if isinstance(current_user, dict):
                    current_user['identity'] = identity
                else:
                    current_user.identity = identity

                # 保存用户信息
                user_manager.save_accounts()

                # 更新当前用户
                user_manager.set_current_user(current_user)

                # 更新应用的用户数据
                app = MDApp.get_running_app()
                if app is not None:
                    if hasattr(app, 'set_user_data'):
                        app.set_user_data({
                            "username": current_user.username if hasattr(current_user, 'username') else "",
                            "real_name": current_user.real_name if hasattr(current_user, 'real_name') else "",
                            "user_id": current_user.user_id if hasattr(current_user, 'user_id') else "",
                            "identity": identity
                        })
                    elif hasattr(app, 'user_data'):
                        app.user_data.update({
                            "identity": identity
                        })

                # 提示用户切换成功
                self.show_success(f"已成功切换为 {identity} 身份")

                # 根据新身份导航到相应界面
                if app is not None and hasattr(app, 'root'):
                    screen_manager = app.root
                    self.navigate_to_correct_screen(identity, screen_manager)
                elif hasattr(self, 'manager'):
                    # 后备方法：使用当前屏幕管理器
                    self.navigate_to_correct_screen(identity, self.manager)
                else:
                    self.show_error("无法导航到相应界面")

            except Exception as e:
                self.show_error(f"更新用户身份失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return

        except Exception as e:
            self.show_error(f"切换身份失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_placeholder_qrcode(self):
        """创建占位二维码，当二维码工具无法正常工作时使用"""
        try:
            # 创建占位图像
            placeholder = MDBoxLayout(
                size_hint=(None, None),
                size=(dp(120), dp(120)),
                pos_hint={"center_x": 0.5},
                md_bg_color=(0.9, 0.9, 0.9, 1)
            )

            # 添加文本提示
            label = MDLabel(
                text="健康码\n暂不可用",
                halign="center",
                font_style="Body",
                theme_text_color="Secondary"
            )
            placeholder.add_widget(label)

            if hasattr(self, 'ids') and 'qrcode_container' in self.ids:
                self.ids.qrcode_container.add_widget(placeholder)
            return True
        except Exception:
            return False

    def show_error(self, message):
        """显示错误消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2
            )
            snackbar.open()
        except Exception:
            pass

    def show_success(self, message):
        """显示成功消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2
            )
            snackbar.open()
        except Exception:
            pass

    def show_info(self, message):
        """显示信息消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                duration=2
            )
            snackbar.open()
        except Exception:
            pass

    def dismiss_dialog(self, dialog):
        """安全地关闭对话框"""
        if dialog:
            try:
                dialog.dismiss()
            except Exception:
                pass

    # 添加标志变量，避免重复初始化UI
    _logo_added = False
    _settings_added = False

    def on_pre_enter(self):
        """在页面被显示前调用"""
        print("ProfilePage on_pre_enter 调用")
        try:
            # 延迟初始化UI，确保Kivy完全构建界面
            Clock.schedule_once(self._delayed_init, 0.1)
        except Exception as e:
            print(f"on_pre_enter 失败: {e}")
            import traceback
            traceback.print_exc()

    def get_safe_primary_color(self):
        """Safely get the primary color even if app or theme is None"""
        app = MDApp.get_running_app()
        # Default blue color if app or theme is None
        return (0.12, 0.58, 0.95, 1) if app is None or not hasattr(app, 'theme') or app.theme is None else app.theme.PRIMARY_COLOR

    def on_back(self):
        """返回主页"""
        # 停止二维码更新
        try:
            if hasattr(self, 'qr_manager') and self.qr_manager:
                self.qr_manager.stop()
        except Exception:
            pass

        # 停止扫描器
        try:
            if hasattr(self, 'scanner') and self.scanner:
                self.scanner.stop_scanning()
        except Exception:
            pass

        # 转到主页
        try:
            if hasattr(self, 'manager') and self.manager:
                self.manager.current = 'homepage_screen'
        except Exception as e:
            print(f"返回主页失败: {e}")

    def on_switch_user(self):
        """显示用户管理对话框，允许切换、添加和删除用户"""
        try:
            # 获取用户管理器
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("无法获取当前用户信息")
                return

            # 获取所有用户账户
            accounts = user_manager.accounts

            # 创建对话框内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True,
                size_hint_y=None,
                height=dp(400)
            )

            # 添加标题和说明
            title_layout = MDBoxLayout(
                orientation='horizontal',
                adaptive_height=True,
                size_hint_y=None,
                height=dp(40)
            )

            title_label = MDLabel(
                text="用户管理",
                font_style="Body",
                role="medium",
                bold=True,
                adaptive_height=True,
                size_hint_x=0.7
            )
            title_layout.add_widget(title_label)

            # 添加用户按钮
            add_btn = MDIconButton(
                icon="account-plus",
                on_release=lambda x: self.show_add_user_dialog(),
                pos_hint={"center_y": 0.5}
            )
            title_layout.add_widget(add_btn)

            content.add_widget(title_layout)

            # 添加说明文本
            content.add_widget(MDLabel(
                text="选择要切换的用户，或添加新用户",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            ))

            # 创建用户列表滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(200)
            )

            # 创建用户列表容器
            users_list = MDList()

            if not accounts or len(accounts) == 0:
                # 如果没有账户，显示提示信息
                empty_label = MDLabel(
                    text="暂无其他用户，请添加新用户",
                    halign="center",
                    theme_text_color="Secondary",
                    font_style="Body",
                    adaptive_height=True
                )
                users_list.add_widget(empty_label)
            else:
                # 添加用户列表项
                for account in accounts:
                    # 跳过当前用户，将其显示在顶部
                    if account.user_id == current_user.user_id:
                        continue

                    item = MDListItem(
                        on_release=lambda x, uid=account.user_id: self.confirm_switch_user(uid)
                    )

                    # 添加用户图标
                    item.add_widget(MDListItemLeadingIcon(
                        icon="account",
                        theme_icon_color="Custom",
                        icon_color=self.get_safe_primary_color()
                    ))

                    # 添加用户名信息 - 优先显示真实姓名
                    display_name = account.real_name or account.username
                    item.add_widget(MDListItemHeadlineText(
                        text=display_name
                    ))

                    # 添加用户ID信息
                    item.add_widget(MDListItemSupportingText(
                        text=f"ID: {account.user_id}"
                    ))

                    # 添加操作按钮 - 只使用一个TrailingIcon
                    item.add_widget(MDListItemTrailingIcon(
                        icon="dots-vertical",
                        theme_icon_color="Custom",
                        on_release=lambda x, uid=account.user_id: self.show_user_actions(uid)
                    ))

                    users_list.add_widget(item)

            scroll.add_widget(users_list)
            content.add_widget(scroll)

            # 显示当前用户信息
            current_user_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(60),
                padding=[0, dp(10), 0, 0]
            )

            current_user_layout.add_widget(MDLabel(
                text="当前用户",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            ))

            current_user_item = MDListItem()

            # 添加用户图标
            current_user_item.add_widget(MDListItemLeadingIcon(
                icon="account-check",
                theme_icon_color="Custom",
                icon_color=self.get_safe_primary_color()
            ))

            # 添加用户名信息 - 优先显示真实姓名
            current_display_name = current_user.real_name or current_user.username
            current_user_item.add_widget(MDListItemHeadlineText(
                text=current_display_name
            ))

            # 添加用户ID信息
            current_user_item.add_widget(MDListItemSupportingText(
                text=f"ID: {current_user.user_id}"
            ))

            current_user_layout.add_widget(current_user_item)
            content.add_widget(current_user_layout)

            # 添加按钮
            btn_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                adaptive_height=True,
                pos_hint={"center_x": 0.5},
                padding=[0, dp(10), 0, 0]
            )

            # 扫码添加按钮
            scan_btn = MDButton(
                style="filled",
                on_release=lambda x: self.on_add_user_by_scan(),
                size_hint=(None, None),
                size=(dp(130), dp(45))
            )
            scan_btn.add_widget(MDButtonText(text="扫码添加"))
            btn_layout.add_widget(scan_btn)

            # 手动添加按钮
            add_btn = MDButton(
                style="outlined",
                on_release=lambda x: self.show_add_user_dialog(),
                size_hint=(None, None),
                size=(dp(130), dp(45))
            )
            add_btn.add_widget(MDButtonText(text="手动添加"))
            btn_layout.add_widget(add_btn)

            content.add_widget(btn_layout)

            # 显示对话框
            self.user_dialog = MDDialog()
            self.user_dialog.ids.container.add_widget(content)
            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.user_dialog)
            )
            self.user_dialog.ids.button_container.add_widget(close_button)
            self.user_dialog.open()

        except Exception as e:
            print(f"显示用户管理对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示用户管理对话框")

    def show_user_actions(self, user_id):
        """显示用户操作菜单"""
        try:
            # 创建菜单项
            menu_items = [
                {
                    "text": "删除用户",
                    "on_release": lambda x=None: self.confirm_delete_user(user_id)
                }
            ]

            # 获取最近一次点击的控件作为caller
            from kivy.core.window import Window
            caller = Window.focus_widget or self

            # 创建下拉菜单
            dropdown_menu = MDDropdownMenu(
                caller=caller,
                items=menu_items,
                position="auto",
                width=dp(150)
            )

            dropdown_menu.open()
        except Exception as e:
            print(f"显示用户操作菜单失败: {e}")
            self.show_error("无法显示用户操作菜单")

    def on_contact_support(self):
        """联系客服"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加联系方式
            contact_text = MDLabel(
                text="客服支持\n\n工作时间：周一至周五 9:00-18:00\n\n联系方式：\n- 电话：************\n- 邮箱：<EMAIL>\n\n在线客服：\n- 微信：health_support\n- QQ：123456789",
                font_style="Body",
                role="small",
                theme_text_color="Primary",
                adaptive_height=True,
                halign="left"
            )
            content.add_widget(contact_text)

            # 显示对话框
            contact_dialog = MDDialog()
            contact_dialog.ids.container.add_widget(MDLabel(text="联系客服", adaptive_height=True, pos_hint={"center_x": 0.5}))
            contact_dialog.ids.container.add_widget(content)

            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(contact_dialog)
            )
            contact_dialog.ids.button_container.add_widget(close_button)
            contact_dialog.open()
        except Exception as e:
            print(f"显示客服信息失败: {e}")
            self.show_error("无法显示客服信息")

    def on_user_agreement(self):
        """用户协议"""
        try:
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(400),
                do_scroll_x=False,
                do_scroll_y=True
            )

            # 创建内容布局
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(20)],
                adaptive_height=True,
                size_hint_y=None
            )

            # 添加标题
            title = MDLabel(
                text="用户协议",
                font_style="Headline",
                role="medium",
                adaptive_height=True,
                halign="center"
            )
            content.add_widget(title)

            # 添加协议内容
            sections = [
                {
                    "title": "1. 服务条款",
                    "content": "欢迎使用健康管理应用。使用本应用即表示您同意遵守本协议的所有条款。本应用提供健康数据管理、医生咨询等服务。"
                },
                {
                    "title": "2. 隐私保护",
                    "content": "我们重视您的隐私保护。您的个人信息和健康数据将受到严格保密，仅用于提供必要的服务。未经您的同意，我们不会向第三方分享您的信息。"
                },
                {
                    "title": "3. 用户责任",
                    "content": "您需要对自己的账号安全负责，妥善保管登录信息。您在使用本应用时应遵守相关法律法规，不得从事任何违法或不当行为。"
                },
                {
                    "title": "4. 服务变更",
                    "content": "我们保留随时修改或终止服务的权利。服务变更会提前通知用户，重大变更可能需要重新确认协议。"
                },
                {
                    "title": "5. 法律适用",
                    "content": "本协议受中华人民共和国法律管辖。任何争议应通过友好协商解决，协商不成可提交有管辖权的人民法院处理。"
                }
            ]

            for section in sections:
                # 添加章节标题
                section_title = MDLabel(
                    text=section["title"],
                    font_style="Body",
                    role="medium",
                    bold=True,
                    adaptive_height=True,
                    padding=[0, dp(10), 0, dp(5)]
                )
                content.add_widget(section_title)

                # 添加章节内容
                section_content = MDLabel(
                    text=section["content"],
                    font_style="Body",
                    role="small",
                    adaptive_height=True,
                    text_color=(0.6, 0.6, 0.6, 1)
                )
                content.add_widget(section_content)

            # 将内容添加到滚动视图
            scroll.add_widget(content)

            # 创建对话框
            dialog = MDDialog()
            dialog.ids.container.add_widget(scroll)

            # 添加关闭按钮
            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(dialog)
            )
            dialog.ids.button_container.add_widget(close_button)

            dialog.open()
        except Exception as e:
            print(f"显示用户协议失败: {e}")
            self.show_error("无法显示用户协议")

    def on_privacy_policy(self):
        """隐私政策"""
        try:
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(400),
                do_scroll_x=False,
                do_scroll_y=True
            )

            # 创建内容布局
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(20)],
                adaptive_height=True,
                size_hint_y=None
            )

            # 添加标题
            title = MDLabel(
                text="隐私政策",
                font_style="Headline",
                role="medium",
                adaptive_height=True,
                halign="center"
            )
            content.add_widget(title)

            # 添加政策内容
            sections = [
                {
                    "title": "1. 信息收集",
                    "content": "我们收集的信息包括：\n- 基本信息：姓名、性别、年龄等\n- 健康数据：血压、血糖等\n- 设备信息：设备型号、操作系统等\n- 使用数据：使用记录、操作日志等"
                },
                {
                    "title": "2. 信息使用",
                    "content": "我们使用收集的信息：\n- 提供个性化服务\n- 改进应用功能\n- 发送服务通知\n- 进行数据分析"
                },
                {
                    "title": "3. 信息保护",
                    "content": "我们采取多种安全措施保护您的信息：\n- 数据加密存储\n- 访问权限控制\n- 定期安全审计\n- 员工保密培训"
                },
                {
                    "title": "4. 信息共享",
                    "content": "我们仅在以下情况共享您的信息：\n- 获得您的明确同意\n- 法律法规要求\n- 保护我们的合法权益"
                },
                {
                    "title": "5. 信息存储",
                    "content": "您的信息存储在中国境内的服务器中。我们承诺遵守相关法律法规，确保数据安全。"
                }
            ]

            for section in sections:
                # 添加章节标题
                section_title = MDLabel(
                    text=section["title"],
                    font_style="Body",
                    role="medium",
                    bold=True,
                    adaptive_height=True,
                    padding=[0, dp(10), 0, dp(5)]
                )
                content.add_widget(section_title)

                # 添加章节内容
                section_content = MDLabel(
                    text=section["content"],
                    font_style="Body",
                    role="small",
                    adaptive_height=True,
                    text_color=(0.6, 0.6, 0.6, 1)
                )
                content.add_widget(section_content)

            # 将内容添加到滚动视图
            scroll.add_widget(content)

            # 创建对话框
            dialog = MDDialog()
            dialog.ids.container.add_widget(scroll)

            # 添加关闭按钮
            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(dialog)
            )
            dialog.ids.button_container.add_widget(close_button)

            dialog.open()
        except Exception as e:
            print(f"显示隐私政策失败: {e}")
            self.show_error("无法显示隐私政策")

    def on_help_center(self):
        """查看帮助中心"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加帮助内容
            help_text = MDLabel(
                text="帮助中心\n\n1. 常见问题\n- 如何修改密码？\n- 如何绑定手机号？\n- 如何切换用户身份？\n\n2. 使用指南\n- 健康数据记录\n- 医生管理\n- 通知设置\n\n3. 联系支持\n如有问题，请联系客服。",
                font_style="Body",
                role="small",
                theme_text_color="Primary",
                adaptive_height=True,
                halign="left"
            )
            content.add_widget(help_text)

            # 显示对话框
            help_dialog = MDDialog()
            help_dialog.ids.container.add_widget(MDLabel(text="帮助中心", adaptive_height=True, pos_hint={"center_x": 0.5}))
            help_dialog.ids.container.add_widget(content)

            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(help_dialog)
            )
            help_dialog.ids.button_container.add_widget(close_button)
            help_dialog.open()
        except Exception as e:
            print(f"显示帮助中心失败: {e}")
            self.show_error("无法显示帮助中心")

    def on_theme_settings(self):
        """主题设置"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加主题说明
            theme_info = MDLabel(
                text="选择应用的主题样式",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            )
            content.add_widget(theme_info)

            # 主题选择
            theme_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                adaptive_height=True
            )

            # 浅色主题选项
            light_theme = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            light_theme.add_widget(MDLabel(
                text="浅色主题",
                font_style="Body",
                role="medium",
                adaptive_height=True,
                size_hint_x=0.7
            ))

            light_button = MDButton(
                style="text",
                size_hint_x=0.3,
                pos_hint={"center_y": 0.5},
                on_release=lambda x: self.apply_theme("Light")
            )
            light_button.add_widget(MDButtonText(text="启用"))
            light_theme.add_widget(light_button)

            theme_layout.add_widget(light_theme)

            # 深色主题选项
            dark_theme = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            dark_theme.add_widget(MDLabel(
                text="深色主题",
                font_style="Body",
                role="medium",
                adaptive_height=True,
                size_hint_x=0.7
            ))

            dark_button = MDButton(
                style="text",
                size_hint_x=0.3,
                pos_hint={"center_y": 0.5},
                on_release=lambda x: self.apply_theme("Dark")
            )
            dark_button.add_widget(MDButtonText(text="启用"))
            dark_theme.add_widget(dark_button)

            theme_layout.add_widget(dark_theme)

            content.add_widget(theme_layout)

            # 显示对话框
            theme_dialog = MDDialog()
            theme_dialog.ids.container.add_widget(MDLabel(text="主题设置", adaptive_height=True, pos_hint={"center_x": 0.5}))
            theme_dialog.ids.container.add_widget(content)

            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(theme_dialog)
            )
            theme_dialog.ids.button_container.add_widget(close_button)
            theme_dialog.open()
        except Exception as e:
            print(f"显示主题设置失败: {e}")
            self.show_error("无法显示主题设置")

    def apply_theme(self, theme_style):
        """应用主题"""
        try:
            app = MDApp.get_running_app()
            if app:
                app.theme_cls.theme_style = theme_style
                self.show_success(f"已切换到{theme_style}主题")
        except Exception as e:
            print(f"应用主题失败: {e}")
            self.show_error("应用主题失败")

    def on_ocr_settings(self):
        """OCR识别设置"""
        # 显示云端OCR信息对话框，不再跳转到OCR设置页面
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                adaptive_height=True
            )

            content.add_widget(MDLabel(
                text="云端OCR服务信息",
                font_style="Title",
                halign="center"
            ))

            content.add_widget(MDLabel(
                text="本应用使用云端OCR服务自动识别文档内容，无需本地设置。\n\n服务器地址：http://8.138.188.26/api",
                font_style="Body",
                halign="left"
            ))

            dialog = MDDialog()
            dialog.ids.container.add_widget(content)

            close_button = MDButton(
                MDButtonText(text="确定"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(dialog)
            )
            dialog.ids.button_container.add_widget(close_button)
            dialog.open()
        except Exception as e:
            print(f"显示OCR服务信息失败: {e}")
            self.show_error("无法显示OCR服务信息")

    def on_about(self):
        """关于我们"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加应用信息
            about_text = MDLabel(
                text="健康管理应用 V1.0.0\n\n本应用致力于为用户提供便捷的健康管理服务。\n\n主要功能：\n- 健康数据记录\n- 医生管理\n- 身份切换\n- 通知提醒\n\n技术支持：\n- 开发团队\n- 客服支持\n\n© 2024 健康管理. All rights reserved.",
                font_style="Body",
                role="small",
                theme_text_color="Primary",
                adaptive_height=True,
                halign="center"
            )
            content.add_widget(about_text)

            # 显示对话框
            about_dialog = MDDialog()
            about_dialog.ids.container.add_widget(MDLabel(text="关于我们", adaptive_height=True, pos_hint={"center_x": 0.5}))
            about_dialog.ids.container.add_widget(content)

            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(about_dialog)
            )
            about_dialog.ids.button_container.add_widget(close_button)
            about_dialog.open()
        except Exception as e:
            print(f"显示关于我们失败: {e}")
            self.show_error("无法显示关于我们")

    def on_logout(self):
        """退出登录"""
        try:
            # 创建确认对话框
            confirm_dialog = MDDialog()
            confirm_dialog.ids.container.add_widget(MDLabel(text="退出登录", adaptive_height=True, pos_hint={"center_x": 0.5}))
            confirm_dialog.ids.container.add_widget(MDLabel(text="确定要退出登录吗？", adaptive_height=True))

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(confirm_dialog)
            )

            confirm_button = MDButton(
                MDButtonText(text="确认"),
                style="text",
                on_release=lambda x: self.do_logout(confirm_dialog)
            )

            confirm_dialog.ids.button_container.add_widget(cancel_button)
            confirm_dialog.ids.button_container.add_widget(confirm_button)
            confirm_dialog.open()
        except Exception as e:
            print(f"显示退出登录确认对话框失败: {e}")
            self.show_error("无法显示退出登录确认对话框")

    def do_logout(self, dialog):
        """执行退出登录操作"""
        try:
            # 关闭确认对话框
            if dialog:
                self.dismiss_dialog(dialog)

            # 停止二维码更新
            if hasattr(self, 'qr_manager') and self.qr_manager:
                self.qr_manager.stop()

            # 停止扫描器
            if hasattr(self, 'scanner') and self.scanner:
                self.scanner.stop_scanning()

            # 清除用户数据
            app = MDApp.get_running_app()
            if app:
                app.clear_user_data()

            # 完全退出应用
            from kivy.app import App
            app = App.get_running_app()
            if app:
                app.stop()

        except Exception as e:
            print(f"退出登录失败: {e}")
            self.show_error("退出登录失败")

    def confirm_switch_user(self, user_id):
        """确认切换用户"""
        try:
            # 关闭对话框
            if hasattr(self, 'user_dialog') and self.user_dialog:
                self.dismiss_dialog(self.user_dialog)

            # 获取用户管理器
            user_manager = get_user_manager()

            # 确保切换到一个不同的用户
            current_user = user_manager.get_current_user()
            if current_user and current_user.user_id == user_id:
                self.show_info("您已经是该用户，无需切换")
                return

            # 切换用户
            success = user_manager.switch_user(user_id)

            if success:
                self.show_success("用户已切换")

                # 重新加载用户信息
                self.load_user_info()

                # 更新UI
                self.init_ui()

                # 获取应用实例并更新全局用户信息
                app = MDApp.get_running_app()
                if app is not None and hasattr(app, 'update_user_info'):
                    app.update_user_info()

                # 如果有必要的话，切换到适合该用户的界面
                new_user = user_manager.get_current_user()
                if new_user and hasattr(new_user, 'identity'):
                    app = MDApp.get_running_app()
                    if app is not None and hasattr(app, 'root'):
                        self.navigate_to_correct_screen(new_user.identity, app.root)
            else:
                self.show_error("用户切换失败")
        except Exception as e:
            print(f"切换用户失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("切换用户失败")

    def show_add_user_dialog(self):
        """显示添加用户对话框"""
        # 关闭之前的对话框
        if hasattr(self, 'user_dialog') and self.user_dialog:
            self.dismiss_dialog(self.user_dialog)

        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                adaptive_height=True
            )

            # 添加说明文本
            content.add_widget(MDLabel(
                text="输入用户ID添加用户",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                adaptive_height=True
            ))

            # 用户ID输入框
            self.user_id_input = MDTextField(
                hint_text="用户ID",
                mode="outlined",
                size_hint_y=None,
                height=dp(48)
            )
            content.add_widget(self.user_id_input)

            # 显示对话框
            self.add_user_dialog = MDDialog()
            self.add_user_dialog.ids.container.add_widget(MDLabel(text="添加用户", adaptive_height=True, pos_hint={"center_x": 0.5}))
            self.add_user_dialog.ids.container.add_widget(content)

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.add_user_dialog)
            )

            add_button = MDButton(
                MDButtonText(text="添加"),
                style="text",
                on_release=lambda x: self.confirm_add_user()
            )

            self.add_user_dialog.ids.button_container.add_widget(cancel_button)
            self.add_user_dialog.ids.button_container.add_widget(add_button)
            self.add_user_dialog.open()
        except Exception as e:
            print(f"显示添加用户对话框失败: {e}")
            self.show_error("无法显示添加用户对话框")

    def confirm_add_user(self):
        """确认添加用户"""
        try:
            if not hasattr(self, 'user_id_input') or not self.user_id_input:
                self.show_error("输入框未初始化")
                return

            user_id = self.user_id_input.text.strip()

            if not user_id:
                self.show_error("请输入用户ID")
                return

            # 获取用户管理器
            user_manager = get_user_manager()

            # 检查是否已添加该用户
            if user_manager.get_account_by_id(user_id):
                self.show_error("该用户已存在")
                return

            # 添加用户
            # 定义基本的用户信息
            username = "新用户"
            identity = "个人用户"
            password = ""  # 空密码

            # 使用正确的参数调用add_account方法
            success = user_manager.add_account(
                username=username,
                password=password,
                real_name=username,
                identity=identity,
                user_id=user_id,
                roles=["user"]
            )

            # 如果add_account返回的是账户对象而不是布尔值，则认为成功
            if success is not None and not isinstance(success, bool):
                success = True

            if success:
                self.show_success("用户添加成功")
                # 关闭添加对话框
                if hasattr(self, 'add_user_dialog') and self.add_user_dialog:
                    self.dismiss_dialog(self.add_user_dialog)
                # 重新打开用户管理对话框
                self.on_switch_user()
            else:
                self.show_error("用户添加失败")
        except Exception as e:
            print(f"添加用户失败: {e}")
            self.show_error(f"添加用户失败: {str(e)}")

    def add_scanned_user(self, user_id, username):
        """添加扫描到的用户"""
        try:
            # 获取用户管理器
            user_manager = get_user_manager()

            # 检查是否已添加该用户
            if user_manager.get_account_by_id(user_id):
                self.show_error("该用户已存在")
                return

            # 添加用户 - 使用正确的参数调用方法
            identity = "个人用户"
            password = ""  # 空密码

            # 使用正确的参数调用add_account方法
            success = user_manager.add_account(
                username=username,
                password=password,
                real_name=username,
                identity=identity,
                user_id=user_id,
                roles=["user"]
            )

            # 如果add_account返回的是账户对象而不是布尔值，则认为成功
            if success is not None and not isinstance(success, bool):
                success = True

            if success:
                self.show_success(f"已添加用户: {username}")
                # 重新打开用户管理对话框
                self.on_switch_user()
            else:
                self.show_error("用户添加失败")
        except Exception as e:
            print(f"添加扫描用户失败: {e}")
            self.show_error("添加用户失败")

    def confirm_delete_user(self, user_id):
        """确认删除用户"""
        try:
            # 获取用户管理器
            user_manager = get_user_manager()

            # 获取用户信息
            account = user_manager.get_account_by_id(user_id)

            if not account:
                self.show_error("找不到该用户")
                return

            # 获取显示名
            display_name = account.real_name or account.username

            # 创建确认对话框
            confirm_dialog = MDDialog()
            confirm_dialog.ids.container.add_widget(MDLabel(text="删除用户", adaptive_height=True, pos_hint={"center_x": 0.5}))
            # 修复括号未关闭的问题
            confirm_text = f"确定要删除用户\"{display_name}\"吗？此操作不可撤销。"
            confirm_dialog.ids.container.add_widget(MDLabel(text=confirm_text, adaptive_height=True))

            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(confirm_dialog)
            )

            delete_button = MDButton(
                MDButtonText(text="删除"),
                style="text",
                on_release=lambda x: self.do_delete_user(confirm_dialog, user_id)
            )

            confirm_dialog.ids.button_container.add_widget(cancel_button)
            confirm_dialog.ids.button_container.add_widget(delete_button)
            confirm_dialog.open()
        except Exception as e:
            print(f"确认删除用户失败: {e}")
            self.show_error("确认删除用户失败")

    def do_delete_user(self, dialog, user_id):
        """执行删除用户操作"""
        # 关闭确认对话框
        if dialog:
            self.dismiss_dialog(dialog)

        try:
            # 获取用户管理器
            user_manager = get_user_manager()

            # 确保不是当前用户
            current_user = user_manager.get_current_user()
            if current_user and current_user.user_id == user_id:
                self.show_error("不能删除当前登录的用户")
                return

            # 删除用户
            success = user_manager.remove_account(user_id)

            if success:
                self.show_success("用户已删除")
                # 重新打开用户管理对话框
                self.on_switch_user()
            else:
                self.show_error("删除用户失败")
        except Exception as e:
            print(f"删除用户失败: {e}")
            self.show_error("删除用户失败")

    def send_verification_code(self):
        """发送手机验证码"""
        try:
            if not hasattr(self, 'phone_bind_input') or not self.phone_bind_input:
                self.show_error("手机号输入框未初始化")
                return

            phone = self.phone_bind_input.text.strip()

            # 验证手机号格式
            if not phone or len(phone) != 11:
                self.show_error("请输入有效的手机号")
                return

            # 在实际应用中，这里应该调用短信服务发送验证码
            # 这里只是模拟发送过程
            self.show_info("验证码已发送，请注意查收")

            # 禁用发送按钮60秒
            if hasattr(self, 'get_code_button') and self.get_code_button:
                self.get_code_button.disabled = True

                # 初始倒计时时间
                self.countdown_seconds = 60
                self.update_button_text()

                # 创建定时器
                self.countdown_timer = Clock.schedule_interval(self.update_countdown, 1)
        except Exception as e:
            print(f"发送验证码失败: {e}")
            self.show_error("发送验证码失败")

    def update_countdown(self, dt):
        """更新按钮倒计时"""
        # 倒计时减一
        self.countdown_seconds -= 1

        if self.countdown_seconds <= 0:
            # 倒计时结束，恢复按钮
            if hasattr(self, 'get_code_button') and self.get_code_button:
                # 恢复按钮状态
                self.get_code_button.disabled = False

                # 恢复按钮文本
                for child in self.get_code_button.children:
                    if isinstance(child, MDButtonText):
                        child.text = "获取验证码"
                        break

            # 取消定时器
            if hasattr(self, 'countdown_timer'):
                self.countdown_timer.cancel()
        else:
            # 更新按钮文本
            self.update_button_text()

    def update_button_text(self):
        """更新按钮文本显示倒计时"""
        if hasattr(self, 'get_code_button') and self.get_code_button:
            for child in self.get_code_button.children:
                if isinstance(child, MDButtonText):
                    child.text = f"{self.countdown_seconds}秒后重试"
                    break

    def on_add_user_by_scan(self):
        """通过扫描二维码添加用户"""
        # 关闭对话框
        if hasattr(self, 'user_dialog') and self.user_dialog:
            self.dismiss_dialog(self.user_dialog)

        try:
            # 检查扫描器是否初始化
            if not hasattr(self, 'scanner') or not self.scanner:
                try:
                    self.scanner = QRCodeScanner(callback=self.on_user_qr_scanned)
                except Exception as e:
                    print(f"初始化扫描器失败: {e}")
                    self.show_error("无法初始化扫描器")
                    return

            # 启动扫描
            self.show_info("请扫描用户的健康码或ID")
            success = self.scanner.start_scanning()

            if not success:
                self.show_error("无法启动相机，请检查相机权限")
        except Exception as e:
            print(f"扫描二维码失败: {e}")
            self.show_error("扫描二维码功能暂时不可用")

    def on_user_qr_scanned(self, result):
        """处理用户二维码扫描结果"""
        try:
            if not result:
                self.show_error("未能识别二维码")
                return

            print(f"扫描结果: {result}")

            # 尝试解析JSON格式
            try:
                import json
                data = json.loads(result)

                # 提取用户ID
                if isinstance(data, dict) and 'user_id' in data:
                    user_id = data['user_id']
                    username = data.get('name', '未知用户')

                    # 添加用户
                    self.add_scanned_user(user_id, username)
                    return
            except json.JSONDecodeError:
                # 不是JSON格式，尝试其他格式
                pass

            # 尝试直接作为用户ID使用
            if result.strip().startswith("user_"):
                self.add_scanned_user(result.strip(), "扫描用户")
                return

            # 如果都不匹配，显示错误
            self.show_error("无效的用户二维码")
        except Exception as e:
            print(f"处理扫描结果失败: {e}")
            self.show_error("处理扫描结果失败")

    # OCR设置页面已移除，改为使用云端OCR服务
    # def go_to_ocr_settings(self, *args):
    #     """导航到OCR设置页面"""
    #     if self.manager:
    #         self.manager.current = "ocr_settings_screen"

# 测试代码
if __name__ == '__main__':
    from kivymd.uix.screenmanager import MDScreenManager

    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles

        def build(self):
            sm = MDScreenManager()
            sm.add_widget(ProfilePage(name='profile_page'))
            return sm

    TestApp().run()


