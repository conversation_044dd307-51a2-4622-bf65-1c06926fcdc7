from datetime import datetime
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.follow_up import FollowUpRecord, FollowUpType
from app.api import deps
from app.db.base_session import get_db

router = APIRouter()


@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_follow_up_records(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    follow_up_type: Optional[str] = Query(None, description="随访类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的随访记录列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的随访记录"
        )

    # 构建查询
    query = db.query(FollowUpRecord).filter(FollowUpRecord.custom_id == custom_id)
    
    if follow_up_type:
        query = query.filter(FollowUpRecord.follow_up_type == follow_up_type)
    if start_date:
        query = query.filter(FollowUpRecord.follow_up_date >= start_date)
    if end_date:
        query = query.filter(FollowUpRecord.follow_up_date <= end_date)
    
    # 分页
    records = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "success": True,
        "data": [
            {
                "id": record.id,
                "custom_id": record.custom_id,
                "follow_up_type": record.follow_up_type.value if record.follow_up_type else None,
                "title": record.title,
                "content": record.content,
                "follow_up_date": record.follow_up_date,
                "next_follow_up_date": record.next_follow_up_date,
                "recommendation": record.recommendation,
                "notes": record.notes,
                "created_at": record.created_at,
                "updated_at": record.updated_at
            }
            for record in records
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_follow_up_record(
    *,
    db: Session = Depends(get_db),
    record_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    创建随访记录
    """
    # 验证用户存在
    custom_id = record_data.get("custom_id")
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限为此用户创建随访记录"
        )

    # 创建随访记录
    record = FollowUpRecord(
        custom_id=custom_id,
        follow_up_type=FollowUpType(record_data.get("follow_up_type")) if record_data.get("follow_up_type") else None,
        title=record_data.get("title"),
        content=record_data.get("content"),
        follow_up_date=record_data.get("follow_up_date"),
        next_follow_up_date=record_data.get("next_follow_up_date"),
        recommendation=record_data.get("recommendation"),
        notes=record_data.get("notes")
    )
    
    db.add(record)
    db.commit()
    db.refresh(record)
    
    return {
        "success": True,
        "data": {
            "id": record.id,
            "custom_id": record.custom_id,
            "follow_up_type": record.follow_up_type.value if record.follow_up_type else None,
            "title": record.title,
            "content": record.content,
            "follow_up_date": record.follow_up_date,
            "next_follow_up_date": record.next_follow_up_date,
            "recommendation": record.recommendation,
            "notes": record.notes,
            "created_at": record.created_at,
            "updated_at": record.updated_at
        }
    }


@router.get("/{record_id}", response_model=Dict[str, Any])
def get_follow_up_record(
    *,
    db: Session = Depends(get_db),
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取单个随访记录详情
    """
    record = db.query(FollowUpRecord).filter(FollowUpRecord.id == record_id).first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到随访记录ID: {record_id}"
        )

    # 权限校验
    if current_user.custom_id != record.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此随访记录"
        )

    return {
        "success": True,
        "data": {
            "id": record.id,
            "custom_id": record.custom_id,
            "follow_up_type": record.follow_up_type.value if record.follow_up_type else None,
            "title": record.title,
            "content": record.content,
            "follow_up_date": record.follow_up_date,
            "next_follow_up_date": record.next_follow_up_date,
            "recommendation": record.recommendation,
            "notes": record.notes,
            "created_at": record.created_at,
            "updated_at": record.updated_at
        }
    }


@router.put("/{record_id}", response_model=Dict[str, Any])
def update_follow_up_record(
    *,
    db: Session = Depends(get_db),
    record_id: int = Path(..., description="记录ID"),
    record_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新随访记录
    """
    record = db.query(FollowUpRecord).filter(FollowUpRecord.id == record_id).first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到随访记录ID: {record_id}"
        )

    # 权限校验
    if current_user.custom_id != record.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此随访记录"
        )

    # 更新字段
    for field, value in record_data.items():
        if field == "follow_up_type" and value:
            setattr(record, field, FollowUpType(value))
        elif hasattr(record, field) and field != "id":
            setattr(record, field, value)
    
    db.commit()
    db.refresh(record)
    
    return {
        "success": True,
        "data": {
            "id": record.id,
            "custom_id": record.custom_id,
            "follow_up_type": record.follow_up_type.value if record.follow_up_type else None,
            "title": record.title,
            "content": record.content,
            "follow_up_date": record.follow_up_date,
            "next_follow_up_date": record.next_follow_up_date,
            "recommendation": record.recommendation,
            "notes": record.notes,
            "created_at": record.created_at,
            "updated_at": record.updated_at
        }
    }


@router.delete("/{record_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_follow_up_record(
    *,
    db: Session = Depends(get_db),
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    删除随访记录
    """
    record = db.query(FollowUpRecord).filter(FollowUpRecord.id == record_id).first()
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到随访记录ID: {record_id}"
        )

    # 权限校验
    if current_user.custom_id != record.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此随访记录"
        )

    db.delete(record)
    db.commit()