from datetime import datetime
from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.imaging_report import ImagingReport, ImagingType
from app.api import deps
from app.db.base_session import get_db

router = APIRouter()


@router.get("/user/{custom_id}", response_model=Dict[str, Any])
def get_user_imaging_reports(
    *,
    db: Session = Depends(get_db),
    custom_id: str = Path(..., description="用户ID"),
    imaging_type: Optional[str] = Query(None, description="影像类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取指定用户的影像报告列表
    """
    # 查找用户
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的影像报告"
        )

    # 构建查询
    query = db.query(ImagingReport).filter(ImagingReport.custom_id == custom_id)
    
    if imaging_type:
        query = query.filter(ImagingReport.imaging_type == imaging_type)
    if start_date:
        query = query.filter(ImagingReport.exam_date >= start_date)
    if end_date:
        query = query.filter(ImagingReport.exam_date <= end_date)
    
    # 分页
    reports = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return {
        "success": True,
        "data": [
            {
                "id": report.id,
                "custom_id": report.custom_id,
                "imaging_type": report.imaging_type.value if report.imaging_type else None,
                "body_part": report.body_part,
                "hospital_name": report.hospital_name,
                "department": report.department,
                "doctor_name": report.doctor_name,
                "exam_date": report.exam_date,
                "report_date": report.report_date,
                "findings": report.findings,
                "impression": report.impression,
                "recommendation": report.recommendation,
                "notes": report.notes,
                "is_abnormal": report.is_abnormal,
                "created_at": report.created_at,
                "updated_at": report.updated_at
            }
            for report in reports
        ],
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
def create_imaging_report(
    *,
    db: Session = Depends(get_db),
    report_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    创建影像报告
    """
    # 验证用户存在
    custom_id = report_data.get("custom_id")
    user = db.query(User).filter(User.custom_id == custom_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到用户ID: {custom_id}"
        )

    # 权限校验
    if current_user.custom_id != custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限为此用户创建影像报告"
        )

    # 处理日期字段
    exam_date = None
    if report_data.get("exam_date"):
        try:
            if isinstance(report_data.get("exam_date"), str):
                exam_date = datetime.fromisoformat(report_data.get("exam_date").replace("Z", "+00:00"))
            else:
                exam_date = report_data.get("exam_date")
        except ValueError:
            try:
                exam_date = datetime.strptime(report_data.get("exam_date"), "%Y-%m-%d")
            except ValueError:
                exam_date = None
    
    report_date = None
    if report_data.get("report_date"):
        try:
            if isinstance(report_data.get("report_date"), str):
                report_date = datetime.fromisoformat(report_data.get("report_date").replace("Z", "+00:00"))
            else:
                report_date = report_data.get("report_date")
        except ValueError:
            try:
                report_date = datetime.strptime(report_data.get("report_date"), "%Y-%m-%d")
            except ValueError:
                report_date = None

    # 创建影像报告
    report = ImagingReport(
        custom_id=custom_id,
        imaging_type=ImagingType(report_data.get("imaging_type")) if report_data.get("imaging_type") else None,
        body_part=report_data.get("body_part"),
        hospital_name=report_data.get("hospital_name"),
        department=report_data.get("department"),
        doctor_name=report_data.get("doctor_name"),
        exam_date=exam_date,
        report_date=report_date,
        findings=report_data.get("findings"),
        impression=report_data.get("impression"),
        recommendation=report_data.get("recommendation"),
        notes=report_data.get("notes"),
        is_abnormal=report_data.get("is_abnormal", False)
    )
    
    db.add(report)
    db.commit()
    db.refresh(report)
    
    return {
        "success": True,
        "data": {
            "id": report.id,
            "custom_id": report.custom_id,
            "imaging_type": report.imaging_type.value if report.imaging_type else None,
            "body_part": report.body_part,
            "hospital_name": report.hospital_name,
            "department": report.department,
            "doctor_name": report.doctor_name,
            "exam_date": report.exam_date,
            "report_date": report.report_date,
            "findings": report.findings,
            "impression": report.impression,
            "recommendation": report.recommendation,
            "notes": report.notes,
            "is_abnormal": report.is_abnormal,
            "created_at": report.created_at,
            "updated_at": report.updated_at
        }
    }


@router.get("/{report_id}", response_model=Dict[str, Any])
def get_imaging_report(
    *,
    db: Session = Depends(get_db),
    report_id: int = Path(..., description="报告ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    获取单个影像报告详情
    """
    report = db.query(ImagingReport).filter(ImagingReport.id == report_id).first()
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到影像报告ID: {report_id}"
        )

    # 权限校验
    if current_user.custom_id != report.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此影像报告"
        )

    return {
        "success": True,
        "data": {
            "id": report.id,
            "custom_id": report.custom_id,
            "imaging_type": report.imaging_type.value if report.imaging_type else None,
            "body_part": report.body_part,
            "hospital_name": report.hospital_name,
            "department": report.department,
            "doctor_name": report.doctor_name,
            "exam_date": report.exam_date,
            "report_date": report.report_date,
            "findings": report.findings,
            "impression": report.impression,
            "recommendation": report.recommendation,
            "notes": report.notes,
            "is_abnormal": report.is_abnormal,
            "created_at": report.created_at,
            "updated_at": report.updated_at
        }
    }


@router.put("/{report_id}", response_model=Dict[str, Any])
def update_imaging_report(
    *,
    db: Session = Depends(get_db),
    report_id: int = Path(..., description="报告ID"),
    report_data: dict,
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> Any:
    """
    更新影像报告
    """
    report = db.query(ImagingReport).filter(ImagingReport.id == report_id).first()
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到影像报告ID: {report_id}"
        )

    # 权限校验
    if current_user.custom_id != report.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此影像报告"
        )

    # 更新字段
    for field, value in report_data.items():
        if field == "imaging_type" and value:
            setattr(report, field, ImagingType(value))
        elif hasattr(report, field) and field != "id":
            setattr(report, field, value)
    
    db.commit()
    db.refresh(report)
    
    return {
        "success": True,
        "data": {
            "id": report.id,
            "custom_id": report.custom_id,
            "imaging_type": report.imaging_type.value if report.imaging_type else None,
            "body_part": report.body_part,
            "hospital_name": report.hospital_name,
            "department": report.department,
            "doctor_name": report.doctor_name,
            "exam_date": report.exam_date,
            "report_date": report.report_date,
            "findings": report.findings,
            "impression": report.impression,
            "recommendation": report.recommendation,
            "notes": report.notes,
            "is_abnormal": report.is_abnormal,
            "created_at": report.created_at,
            "updated_at": report.updated_at
        }
    }


@router.delete("/{report_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_imaging_report(
    *,
    db: Session = Depends(get_db),
    report_id: int = Path(..., description="报告ID"),
    current_user: User = Depends(deps.get_current_active_user_custom)
) -> None:
    """
    删除影像报告
    """
    report = db.query(ImagingReport).filter(ImagingReport.id == report_id).first()
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到影像报告ID: {report_id}"
        )

    # 权限校验
    if current_user.custom_id != report.custom_id and current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此影像报告"
        )

    db.delete(report)
    db.commit()