"""模板管理API

此模块提供了评估量表和调查问卷模板的API端点，使用新的临床量表和问卷模块。
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, File, UploadFile, Body
from sqlalchemy.orm import Session
from pydantic import ValidationError, BaseModel
from sqlalchemy import or_  # 顶部导入

from app.db.session import get_db
from app.models.assessment import AssessmentTemplate, AssessmentTemplateQuestion
from app.models.questionnaire import QuestionnaireTemplate, QuestionnaireTemplateQuestion
from app.schemas.assessment import (
    AssessmentTemplateCreate, AssessmentTemplateUpdate, AssessmentTemplateResponse,
    QuestionnaireTemplateCreate, QuestionnaireTemplateUpdate, QuestionnaireTemplateResponse,
    AssessmentTemplateQuestionCreate, QuestionnaireTemplateQuestionCreate
)
from app.core.auth import get_current_user, get_current_active_user, get_current_user_optional, get_current_active_user_custom
from app.models.user import User

# 导入临床量表和问卷模块
from app.clinical_scales.assessment import ALL_ASSESSMENT_TEMPLATES as STANDARD_ASSESSMENT_TEMPLATES
from app.clinical_scales.questionnaire import ALL_QUESTIONNAIRE_TEMPLATES as STANDARD_QUESTIONNAIRE_TEMPLATES

router = APIRouter()

# 量表模板相关接口
@router.get("/assessment-templates", response_model=dict)
def get_assessment_templates(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(-1, ge=-1, le=100),  # 支持 limit=-1
    source: str = Query(None, description="模板来源：custom_created-自定义创建，standard-标准模板"),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取量表模板列表（仅模板元数据，不含实例）"""
    try:
        result = []
        
        # 如果请求自定义创建的模板
        if source == 'custom_created':
            # 从数据库获取用户自定义创建的模板
            custom_templates = db.query(AssessmentTemplate).filter(
                AssessmentTemplate.created_by == current_user.id
            ).all()
            
            for template in custom_templates:
                result.append({
                    "id": template.id,
                    "name": template.name,
                    "description": template.description,
                    "assessment_type": template.assessment_type,
                    "version": template.version,
                    "status": getattr(template, 'status', 'draft'),
                    "created_by": template.created_by,
                    "source": "custom_created",
                    "created_at": template.created_at.isoformat() if template.created_at else None
                })
        else:
            # 返回标准模板
            # 确保每个模板都有template_key字段
            template_key_mapping = {
                "抑郁自评量表": "sds",
                "汉密尔顿抑郁量表": "hamilton_depression", 
                "焦虑自评量表": "sas",
                "简易精神状态检查量表": "mmse",
                "蒙特利尔认知评估量表": "moca"
            }
            
            for template in STANDARD_ASSESSMENT_TEMPLATES:
                # 如果模板缺少template_key，根据名称添加
                template_key = template.get('template_key')
                if not template_key:
                    template_name = template.get('name', '')
                    if template_name in template_key_mapping:
                        template_key = template_key_mapping[template_name]
                        template['template_key'] = template_key
                
                # 只有有template_key的模板才添加到结果中
                if template_key:
                    result.append({
                        "id": f"standard_{template_key}",  # 使用 template_key 作为唯一标识
                        "name": template.get("name", ""),
                        "description": template.get("description", ""),
                        "assessment_type": template.get("assessment_type", ""),
                        "version": template.get("version", ""),
                        "status": "published",
                        "source": "standard",
                        "category": template.get("category", "精神心理"),
                        "question_count": len(template.get("questions", [])),
                        "max_score": template.get("max_score"),
                        "scoring_method": template.get("scoring_method"),
                        "template_key": template_key,  # 添加template_key字段
                        "created_at": None
                    })
        
        if limit != -1:
            result = result[skip:skip+limit]
        else:
            result = result[skip:]
        return {
            "status": "success",
            "data": result
        }
    except Exception as e:
        print(f"获取量表模板列表出错: {str(e)}")
        return {
            "status": "error",
            "message": f"获取量表模板列表出错: {str(e)}",
            "data": []
        }

@router.post("/assessment-templates", response_model=AssessmentTemplateResponse)
def create_assessment_template(
    template: AssessmentTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """创建量表模板"""
    # 检查是否已存在同名模板
    existing = db.query(AssessmentTemplate).filter(
        AssessmentTemplate.name == template.name
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="模板名称已存在")

    # 创建模板
    db_template = AssessmentTemplate(
        name=template.name,
        description=template.description,
        assessment_type=template.assessment_type,
        version=template.version,
        instructions=template.instructions,
        created_by=current_user.id  # 添加创建者ID
    )
    db.add(db_template)
    db.flush()

    # 创建问题
    for question in template.questions:
        db_question = AssessmentTemplateQuestion(
            template_id=db_template.id,
            question_id=question.question_id,
            question_text=question.question_text,
            question_type=question.question_type,
            options=question.options,
            order=question.order,
            is_required=question.is_required
        )
        db.add(db_question)

    db.commit()
    db.refresh(db_template)
    return db_template

@router.get("/assessment-templates/{template_id}", response_model=dict)
def get_assessment_template(
    template_id: str = Path(...),  # 改为字符串以支持standard_sds格式
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取量表模板详情（支持自定义模板和标准模板）"""
    # 检查是否为标准模板
    if template_id.startswith("standard_"):
        template_key = template_id.split("standard_", 1)[1]
        
        # 优先从数据库查找模板
        db_template = db.query(AssessmentTemplate).filter(AssessmentTemplate.template_key == template_key).first()
        if db_template:
            template = {
                "id": template_id,
                "name": db_template.name,
                "description": db_template.description,
                "instructions": db_template.instructions,
                "scoring_method": db_template.scoring_method,
                "max_score": db_template.max_score,
                "result_ranges": db_template.result_ranges,
                "assessment_type": db_template.assessment_type.value if db_template.assessment_type else None,
                "sub_type": db_template.sub_type,
                "version": db_template.version,
                "questions": [
                    {
                        "question_id": q.question_id,
                        "question_text": q.question_text,
                        "question_type": q.question_type,
                        "options": q.options,
                        "scoring": q.scoring,
                        "order": q.order,
                        "is_required": q.is_required,
                        "jump_logic": q.jump_logic
                    }
                    for q in db_template.questions
                ]
            }
            return {
                "status": "success",
                "data": template
            }
        
        # 如果数据库中没有，再从内存中的标准模板查找
        found_template = None
        for t in STANDARD_ASSESSMENT_TEMPLATES:
            if t.get('template_key') == template_key:
                found_template = t
                break
        
        if found_template:
            template = found_template
            return {
                "status": "success",
                "data": {
                    "id": template_id, # 返回请求的ID，例如 standard_sds
                    "name": template["name"],
                    "description": template["description"],
                    "assessment_type": template["assessment_type"],
                    "version": template["version"],
                    "instructions": template["instructions"],
                    "category": template.get("category", "精神心理"),
                    "scoring_method": template.get("scoring_method"),
                    "scoring_rule": template.get("scoring_rule"),
                    "max_score": template.get("max_score"),
                    "result_ranges": template.get("result_ranges", []),
                    "questions": template.get("questions", []),
                    "source": "standard",
                    "status": "published"
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"标准模板 '{template_key}' 不存在")

    
    # 从数据库获取自定义模板
    try:
        template_id_int = int(template_id)
        template = db.query(AssessmentTemplate).filter(
            AssessmentTemplate.id == template_id_int
        ).first()
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
    except ValueError:
        raise HTTPException(status_code=404, detail="无效的模板ID")
    
    # 组装data字段，字段名与前端一致
    data = {
        "id": template.id,
        "name": template.name,
        "description": template.description,
        "assessment_type": template.assessment_type,
        "questions": [
            {
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "is_required": q.is_required,
                "options": q.options,
            } for q in template.questions
        ],
        "scoring_method": template.scoring_method,
        "result_ranges": template.result_ranges,
    }
    return {"status": "success", "message": "获取成功", "data": data}

@router.put("/assessment-templates/{template_id}", response_model=AssessmentTemplateResponse)
def update_assessment_template(
    template_id: int = Path(..., gt=0),
    template: AssessmentTemplateUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """更新量表模板"""
    db_template = db.query(AssessmentTemplate).filter(
        AssessmentTemplate.id == template_id
    ).first()
    if not db_template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 更新基本信息
    if template.name is not None:
        db_template.name = template.name
    if template.description is not None:
        db_template.description = template.description
    if template.assessment_type is not None:
        db_template.assessment_type = template.assessment_type
    if template.version is not None:
        db_template.version = template.version
    if template.instructions is not None:
        db_template.instructions = template.instructions

    # 更新问题
    if template.questions is not None:
        # 删除旧问题
        db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == template_id
        ).delete()

        # 添加新问题
        for question in template.questions:
            db_question = AssessmentTemplateQuestion(
                template_id=db_template.id,
                question_id=question.question_id,
                question_text=question.question_text,
                question_type=question.question_type,
                options=question.options,
                order=question.order,
                is_required=question.is_required
            )
            db.add(db_question)

    db.commit()
    db.refresh(db_template)
    return db_template

@router.delete("/assessment-templates/{template_id}")
def delete_assessment_template(
    template_id: int = Path(..., gt=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """删除量表模板"""
    db_template = db.query(AssessmentTemplate).filter(
        AssessmentTemplate.id == template_id
    ).first()
    if not db_template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 删除关联的问题
    db.query(AssessmentTemplateQuestion).filter(
        AssessmentTemplateQuestion.template_id == template_id
    ).delete()

    # 删除模板
    db.delete(db_template)
    db.commit()
    return {"message": "模板已删除"}

@router.post("/assessment-templates/{template_id}/distribute", response_model=dict)
async def distribute_assessment_template(
    template_id: str,  # 改为字符串以支持标准模板ID格式
    distributionData: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    分发量表模板 - 支持按角色分发和指定用户分发，分发对象为实例，分发后自动通知
    支持标准量表模板（standard_xxx格式）和自定义模板（整数ID）
    """
    template = None
    template_questions = []
    
    # 检查是否为标准量表模板
    if isinstance(template_id, str) and template_id.startswith("standard_"):
        # 处理标准量表模板
        template_key = template_id.replace("standard_", "")
        
        found_template = None
        # 首先尝试按template_key匹配
        for t in STANDARD_ASSESSMENT_TEMPLATES:
            if t.get('template_key') == template_key:
                found_template = t
                break
        
        # 如果按template_key没找到，尝试按id匹配（处理数字ID情况）
        if not found_template:
            try:
                numeric_id = int(template_key)
                for t in STANDARD_ASSESSMENT_TEMPLATES:
                    if t.get('id') == numeric_id:
                        found_template = t
                        break
            except ValueError:
                pass  # template_key不是数字，继续
        
        if not found_template:
            raise HTTPException(status_code=404, detail=f"标准量表模板不存在: {template_id}")
        
        # 创建临时模板对象用于分发
        class StandardTemplate:
            def __init__(self, template_data):
                self.assessment_type = template_data.get('assessment_type', 'MENTAL_HEALTH')
                self.name = template_data.get('name', '')
                self.version = template_data.get('version', '1.0')
                self.description = template_data.get('description', '')
                self.max_score = template_data.get('max_score', 0)
        
        template = StandardTemplate(found_template)
        
        # 转换标准量表的问题格式
        questions = found_template.get('questions', [])
        for i, q in enumerate(questions):
            class StandardQuestion:
                def __init__(self, question_data, order):
                    self.question_id = question_data.get('question_id', f'q_{order}')
                    self.question_text = question_data.get('question_text', '')
                    self.question_type = question_data.get('question_type', 'text')
                    self.options = question_data.get('options', [])
                    self.order = order
                    self.is_required = question_data.get('is_required', True)
                    self.jump_logic = question_data.get('jump_logic', None)
            
            template_questions.append(StandardQuestion(q, i + 1))
    else:
        # 处理自定义量表模板
        try:
            template_id_int = int(template_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的模板ID格式")
        
        template = db.query(AssessmentTemplate).filter(AssessmentTemplate.id == template_id_int).first()
        if not template:
            raise HTTPException(status_code=404, detail="量表模板不存在")
        
        # 获取自定义模板的问题
        from app.models.assessment import AssessmentTemplateQuestion
        template_questions = db.query(AssessmentTemplateQuestion).filter(
            AssessmentTemplateQuestion.template_id == template_id_int
        ).all()
    distribution_type = distributionData.get("distribution_type")
    due_date_str = distributionData.get("due_date")
    message = distributionData.get("message", "")
    
    # 处理due_date格式转换
    from datetime import datetime, timedelta
    if due_date_str:
        try:
            due_date = datetime.fromisoformat(due_date_str.replace("Z", "+00:00"))
        except ValueError:
            due_date = datetime.now() + timedelta(days=7)  # 默认7天后
    else:
        due_date = datetime.now() + timedelta(days=7)  # 默认7天后
    users = []
    # 新 custom_id 规则
    prefix_map = {
        "health_advisor": "D",
        "organization_admin": "U",
        "regular_user": "P",
        "super_admin": "SM"
    }
    if distribution_type == "all":
        users = db.query(User).filter(User.is_active == True).all()
    elif distribution_type == "role":
        roles = distributionData.get("roles", [])
        prefixes = [prefix_map[r] for r in roles if r in prefix_map]
        if not prefixes:
            raise HTTPException(status_code=400, detail="角色前缀未定义")
        users = db.query(User).filter(
            User.is_active == True,
            or_(*[User.custom_id.startswith(p) for p in prefixes])
        ).all()
    elif distribution_type == "specific":
        custom_ids = distributionData.get("custom_ids", [])
        if not custom_ids:
            raise HTTPException(status_code=400, detail="custom_ids不能为空")
        users = db.query(User).filter(User.custom_id.in_(custom_ids)).all()
    else:
        raise HTTPException(status_code=400, detail="分发类型不支持")
    from app.models.assessment import Assessment, AssessmentItem, AssessmentTemplateQuestion
    from app.models.distribution import AssessmentDistribution
    # 通知模型假设如下
    from app.models.notification import Notification
    from datetime import datetime
    count = 0
    for user in users:
        # 计算该用户对此模板的评估轮次
        existing_assessments = db.query(Assessment).filter(
            Assessment.custom_id == user.custom_id,
            Assessment.name == template.name
        ).order_by(Assessment.round_number.desc()).all()
        
        # 确定新的轮次和序号
        if existing_assessments:
            latest_round = existing_assessments[0].round_number
            # 检查最新轮次是否已完成，如果未完成则继续使用当前轮次
            latest_assessment = existing_assessments[0]
            if latest_assessment.status == "completed":
                new_round = latest_round + 1
                new_sequence = 1
            else:
                # 如果最新评估未完成，不创建新的评估实例
                continue
        else:
            new_round = 1
            new_sequence = 1
        
        # 生成唯一标识符
        template_id_for_identifier = template_id if isinstance(template_id, str) and not template_id.startswith("standard_") else f"standard_{template_id}"
        unique_identifier = f"{template_id_for_identifier}_{user.custom_id}_{new_round}_{new_sequence}"
        
        assessment = Assessment(
            custom_id=user.custom_id,
            assessment_type=template.assessment_type,
            name=template.name,
            version=template.version,
            round_number=new_round,
            sequence_number=new_sequence,
            unique_identifier=unique_identifier,
            notes=template.description,
            max_score=template.max_score,
            status="pending"
        )
        db.add(assessment)
        db.flush()
        # 使用之前准备好的template_questions
        for q in template_questions:
            item = AssessmentItem(
                assessment_id=assessment.id,
                question_id=q.question_id,
                question_text=q.question_text,
                answer='',
                score=None,
                notes='',
                question_type=q.question_type,
                options=q.options,
                order_num=getattr(q, 'order', getattr(q, 'order_num', 1)),
                is_required=q.is_required,
                jump_logic=getattr(q, 'jump_logic', None),
            )
            db.add(item)
        distribution = AssessmentDistribution(
            assessment_id=assessment.id,
            user_id=user.id,
            distributor_id=current_user.id,
            status="pending",
            due_date=due_date,
            message=message
        )
        db.add(distribution)
        # 分发后通知
        notify = Notification(
            user_id=user.id,
            title="新量表分发",
            content=f"您有新的量表待填写：{template.name}",
            type="assessment",
            status="unread",
            created_at=datetime.utcnow()
        )
        db.add(notify)
        count += 1
    db.commit()
    return {"status": "success", "message": "量表分发成功", "count": count}

# 问卷模板相关接口
@router.get("/questionnaire-templates", response_model=dict)
async def get_questionnaire_templates(
    source: str = Query(None, description="模板来源：custom_created-自定义创建，standard-标准模板"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user_optional)  # 改为可选认证
):
    """
    获取调查问卷模板列表
    
    Args:
        source: 模板来源筛选
        db: 数据库会话
        current_user: 当前用户（可选）
    
    Returns:
        包含模板列表的响应
    """
    try:
        result = []
        
        if source == 'custom_created':
            # 只返回自定义创建的模板，需要用户认证
            if not current_user:
                raise HTTPException(status_code=401, detail="需要认证才能访问自定义模板")
            
            try:
                db_templates = db.query(QuestionnaireTemplate).all()
                for template in db_templates:
                    result.append({
                        "id": template.id,
                        "name": template.name,
                        "description": template.description,
                        "questionnaire_type": template.questionnaire_type,
                        "version": template.version,
                        "status": getattr(template, 'status', 'draft'),
                        "created_by": template.created_by,
                        "source": "custom_created",
                        "created_at": template.created_at.isoformat() if template.created_at else None
                    })
            except Exception as db_error:
                print(f"查询自定义模板出错: {str(db_error)}")
        elif source == 'standard':
            # 返回标准调查问卷模板，无需认证
            for template in STANDARD_QUESTIONNAIRE_TEMPLATES:
                result.append({
                    "id": f"standard_{template['template_key']}",  # 使用 template_key 作为唯一标识，与量表保持一致
                    "name": template["name"],
                    "description": template["description"],
                    "questionnaire_type": template["questionnaire_type"],
                    "version": template["version"],
                    "status": "published",
                    "source": "standard",
                    "category": template.get("category", "健康评估"),
                    "question_count": len(template.get("questions", [])),
                    "instructions": template.get("instructions", ""),
                    "template_key": template["template_key"],
                    "created_at": None
                })
        else:
            # 返回所有模板（标准+自定义），自定义模板需要认证
            # 返回标准调查问卷模板（无需认证）
            for template in STANDARD_QUESTIONNAIRE_TEMPLATES:
                result.append({
                    "id": f"standard_{template['template_key']}",  # 使用 template_key 作为唯一标识，与量表保持一致
                    "name": template["name"],
                    "description": template["description"],
                    "questionnaire_type": template["questionnaire_type"],
                    "version": template["version"],
                    "status": "published",
                    "source": "standard",
                    "category": template.get("category", "健康评估"),
                    "question_count": len(template.get("questions", [])),
                    "instructions": template.get("instructions", ""),
                    "template_key": template["template_key"],
                    "created_at": None
                })
            
            # 自定义模板（需要认证）
            if current_user:
                try:
                    db_templates = db.query(QuestionnaireTemplate).all()
                    for tpl in db_templates:
                        try:
                            # 安全地获取问题，使用try-except处理可能的数据库错误
                            questions = []
                            try:
                                # 尝试查询问题
                                questions = db.query(QuestionnaireQuestion).filter(
                                    QuestionnaireQuestion.template_id == tpl.id
                                ).order_by(QuestionnaireQuestion.order).all()
                            except Exception as q_error:
                                print(f"查询模板 {tpl.id} 的问题时出错: {str(q_error)}")
                                questions = []
                            
                            result.append({
                                "id": tpl.id,
                                "name": tpl.name,
                                "description": tpl.description,
                                "questionnaire_type": tpl.questionnaire_type,
                                "version": tpl.version,
                                "status": getattr(tpl, 'status', 'draft'),
                                "created_by": tpl.created_by,
                                "source": "custom_created",
                                "question_count": len(questions),
                                "created_at": tpl.created_at.isoformat() if tpl.created_at else None
                            })
                        except Exception as template_error:
                            print(f"处理模板 {tpl.id} 时出错: {str(template_error)}")
                            continue
                except Exception as db_error:
                    print(f"查询自定义模板出错: {str(db_error)}")
        
        return {
            "code": 200,
            "message": "获取成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取调查问卷模板列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")

@router.post("/questionnaire-templates", response_model=QuestionnaireTemplateResponse)
def create_questionnaire_template(
    template: QuestionnaireTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """创建问卷模板"""
    # 检查是否已存在同名模板
    existing = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.name == template.name
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="模板名称已存在")

    # 创建模板
    db_template = QuestionnaireTemplate(
        name=template.name,
        description=template.description,
        questionnaire_type=template.questionnaire_type,
        version=template.version,
        instructions=template.instructions,
        is_active=True,
        created_by=current_user.id  # 添加创建者ID
    )
    db.add(db_template)
    db.flush()

    # 创建问题
    for question in template.questions:
        db_question = QuestionnaireTemplateQuestion(
            template_id=db_template.id,
            question_id=question.question_id,
            question_text=question.question_text,
            question_type=question.question_type,
            options=question.options,
            order=question.order,
            is_required=question.is_required
        )
        db.add(db_question)

    db.commit()
    db.refresh(db_template)
    return db_template

# 已删除从标准模板创建自定义模板的功能

@router.get("/questionnaire-templates/{template_id}", response_model=dict)
def get_questionnaire_template(
    template_id: str = Path(...),  # 改为字符串以支持standard_q_health_questionnaire格式
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """获取问卷模板详情（支持自定义模板和标准模板）"""
    # 检查是否为标准模板
    if template_id.startswith("standard_q_") or template_id.startswith("standard_"):
        if template_id.startswith("standard_q_"):
            template_key = template_id.split("standard_q_", 1)[1]
        else:
            template_key = template_id.split("standard_", 1)[1]
        
        # 优先从数据库查找模板
        db_template = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.template_key == template_key).first()
        if db_template:
            template = {
                "id": template_id,
                "name": db_template.name,
                "description": db_template.description,
                "questionnaire_type": db_template.questionnaire_type,
                "version": db_template.version,
                "instructions": db_template.instructions or "",
                "questions": [
                    {
                        "question_id": q.question_id,
                        "question_text": q.question_text,
                        "question_type": q.question_type,
                        "options": q.options,
                        "order": q.order,
                        "is_required": q.is_required,
                        "jump_logic": q.jump_logic
                    }
                    for q in db_template.questions
                ],
                "source": "standard",
                "status": "published"
            }
            return {
                "status": "success",
                "message": "获取成功",
                "data": template
            }
        
        # 如果数据库中没有，再从内存中的标准模板查找
        found_template = None
        for t in STANDARD_QUESTIONNAIRE_TEMPLATES:
            if t.get('template_key') == template_key:
                found_template = t
                break
        
        if found_template:
            template = found_template
            return {
                "status": "success",
                "message": "获取成功",
                "data": {
                    "id": template_id, # 返回请求的ID，保持原格式
                    "name": template["name"],
                    "description": template["description"],
                    "questionnaire_type": template["questionnaire_type"],
                    "version": template["version"],
                    "instructions": template.get("instructions", ""),
                    "category": template.get("category", "健康评估"),
                    "questions": template.get("questions", []),
                    "source": "standard",
                    "status": "published"
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"标准问卷模板 '{template_key}' 不存在")

    
    # 从数据库获取自定义模板
    try:
        template_id_int = int(template_id)
        template = db.query(QuestionnaireTemplate).filter(
            QuestionnaireTemplate.id == template_id_int
        ).first()
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
    except ValueError:
        raise HTTPException(status_code=404, detail="无效的模板ID")
    
    # 组装data字段，字段名与前端一致
    data = {
        "id": template.id,
        "name": template.name,
        "description": template.description,
        "questionnaire_type": template.questionnaire_type,
        "questions": [
            {
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "is_required": q.is_required,
                "options": q.options,
            } for q in template.questions
        ],
    }
    return {"status": "success", "message": "获取成功", "data": data}

@router.put("/questionnaire-templates/{template_id}", response_model=QuestionnaireTemplateResponse)
def update_questionnaire_template(
    template_id: int = Path(..., gt=0),
    template: QuestionnaireTemplateUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """更新问卷模板"""
    db_template = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.id == template_id
    ).first()
    if not db_template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 更新基本信息
    if template.name is not None:
        db_template.name = template.name
    if template.description is not None:
        db_template.description = template.description
    if template.questionnaire_type is not None:
        db_template.questionnaire_type = template.questionnaire_type
    if template.version is not None:
        db_template.version = template.version
    if template.instructions is not None:
        db_template.instructions = template.instructions
    if template.status is not None:
        db_template.is_active = (template.status == "published")

    # 更新问题
    if template.questions is not None:
        # 删除旧问题
        db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == template_id
        ).delete()

        # 添加新问题
        for question in template.questions:
            db_question = QuestionnaireTemplateQuestion(
                template_id=db_template.id,
                question_id=question.question_id,
                question_text=question.question_text,
                question_type=question.question_type,
                options=question.options,
                order=question.order,
                is_required=question.is_required
            )
            db.add(db_question)

    db.commit()
    db.refresh(db_template)
    return db_template

@router.delete("/questionnaire-templates/{template_id}")
def delete_questionnaire_template(
    template_id: int = Path(..., gt=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """删除问卷模板"""
    db_template = db.query(QuestionnaireTemplate).filter(
        QuestionnaireTemplate.id == template_id
    ).first()
    if not db_template:
        raise HTTPException(status_code=404, detail="模板不存在")

    # 删除关联的问题
    db.query(QuestionnaireTemplateQuestion).filter(
        QuestionnaireTemplateQuestion.template_id == template_id
    ).delete()

    # 删除模板
    db.delete(db_template)
    db.commit()
    return {"message": "模板已删除"}


@router.put("/assessment-templates/{template_id}/status", response_model=dict)
def update_assessment_template_status(
    template_id: int,
    status: str = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """更新量表模板状态"""
    try:
        # 查找模板
        template = db.query(AssessmentTemplate).filter(
            AssessmentTemplate.id == template_id,
            AssessmentTemplate.created_by == current_user.id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在或无权限")
        
        # 更新状态
        if hasattr(template, 'status'):
            template.status = status
        template.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(template)
        
        return {
            "status": "success",
            "message": "状态更新成功",
            "data": {
                "id": template.id,
                "status": getattr(template, 'status', status)
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"更新量表模板状态出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新状态出错: {str(e)}")


@router.put("/questionnaire-templates/{template_id}/status", response_model=dict)
def update_questionnaire_template_status(
    template_id: int,
    status: str = Body(..., embed=True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """更新问卷模板状态"""
    try:
        # 查找模板
        template = db.query(QuestionnaireTemplate).filter(
            QuestionnaireTemplate.id == template_id,
            QuestionnaireTemplate.created_by == current_user.id
        ).first()
        
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在或无权限")
        
        # 更新状态
        if hasattr(template, 'status'):
            template.status = status
        template.updated_at = datetime.utcnow()
        
        db.commit()
        db.refresh(template)
        
        return {
            "status": "success",
            "message": "状态更新成功",
            "data": {
                "id": template.id,
                "status": getattr(template, 'status', status)
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"更新问卷模板状态出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新状态出错: {str(e)}")


@router.get("/questionnaire-templates/all", response_model=dict)
def get_all_questionnaire_templates():
    """获取所有标准问卷（含标准问卷和自定义模板）"""
    try:
        standard = [tpl for tpl in STANDARD_QUESTIONNAIRE_TEMPLATES]
        return {
            "status": "success",
            "data": standard
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取标准问卷出错: {str(e)}",
            "data": []
        }

@router.post("/questionnaire-templates/{template_id}/distribute", response_model=dict)
async def distribute_questionnaire_template(
    template_id: str,  # 改为字符串以支持标准模板ID格式
    distributionData: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom),
):
    """
    分发问卷模板 - 支持按角色分发和指定用户分发，分发对象为实例，分发后自动通知
    支持标准问卷模板（standard_xxx格式）和自定义模板（整数ID）
    """
    template = None
    template_questions = []
    
    # 检查是否为标准问卷模板
    if isinstance(template_id, str) and (template_id.startswith("standard_q_") or template_id.startswith("standard_")):
        # 处理标准问卷模板
        if template_id.startswith("standard_q_"):
            template_key = template_id.replace("standard_q_", "")
        else:
            template_key = template_id.replace("standard_", "")
        
        found_template = None
        # 首先尝试按template_key匹配
        for t in STANDARD_QUESTIONNAIRE_TEMPLATES:
            if t.get('template_key') == template_key:
                found_template = t
                break
        
        # 如果按template_key没找到，尝试按id匹配（处理数字ID情况）
        if not found_template:
            try:
                numeric_id = int(template_key)
                for t in STANDARD_QUESTIONNAIRE_TEMPLATES:
                    if t.get('id') == numeric_id:
                        found_template = t
                        break
            except ValueError:
                pass  # template_key不是数字，继续
        
        if not found_template:
            raise HTTPException(status_code=404, detail=f"标准问卷模板不存在: {template_id}")
        
        # 创建临时模板对象用于分发
        class StandardTemplate:
            def __init__(self, template_data):
                self.questionnaire_type = template_data.get('questionnaire_type', 'health')
                self.name = template_data.get('name', '')
                self.version = template_data.get('version', '1.0')
                self.description = template_data.get('description', '')
        
        template = StandardTemplate(found_template)
        
        # 转换标准问卷的问题格式
        questions = found_template.get('questions', [])
        for i, q in enumerate(questions):
            class StandardQuestion:
                def __init__(self, question_data, order):
                    self.question_id = question_data.get('question_id', f'q_{order}')
                    self.question_text = question_data.get('question_text', '')
                    self.question_type = question_data.get('question_type', 'text')
                    self.options = question_data.get('options', [])
                    self.order = order
                    self.is_required = question_data.get('is_required', True)
            
            template_questions.append(StandardQuestion(q, i + 1))
    else:
        # 处理自定义问卷模板
        try:
            template_id_int = int(template_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的模板ID格式")
        
        template = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id == template_id_int).first()
        if not template:
            raise HTTPException(status_code=404, detail="问卷模板不存在")
        
        # 获取自定义模板的问题
        template_questions = db.query(QuestionnaireTemplateQuestion).filter(
            QuestionnaireTemplateQuestion.template_id == template_id_int
        ).all()
    distribution_type = distributionData.get("distribution_type")
    due_date_str = distributionData.get("due_date")
    message = distributionData.get("message", "")
    
    # 处理due_date格式转换
    from datetime import datetime, timedelta
    if due_date_str:
        try:
            due_date = datetime.fromisoformat(due_date_str.replace("Z", "+00:00"))
        except ValueError:
            due_date = datetime.now() + timedelta(days=7)  # 默认7天后
    else:
        due_date = datetime.now() + timedelta(days=7)  # 默认7天后
    users = []
    prefix_map = {
        "health_advisor": "D",
        "organization_admin": "U",
        "regular_user": "P",
        "super_admin": "SM"
    }
    if distribution_type == "all":
        users = db.query(User).filter(User.is_active == True).all()
    elif distribution_type == "role":
        roles = distributionData.get("roles", [])
        prefixes = [prefix_map[r] for r in roles if r in prefix_map]
        if not prefixes:
            raise HTTPException(status_code=400, detail="角色前缀未定义")
        users = db.query(User).filter(
            User.is_active == True,
            or_(*[User.custom_id.startswith(p) for p in prefixes])
        ).all()
    elif distribution_type == "specific":
        custom_ids = distributionData.get("custom_ids", [])
        if not custom_ids:
            raise HTTPException(status_code=400, detail="custom_ids不能为空")
        users = db.query(User).filter(User.custom_id.in_(custom_ids)).all()
    else:
        raise HTTPException(status_code=400, detail="分发类型不支持")
    from app.models.questionnaire import Questionnaire, QuestionnaireItem
    from app.models.distribution import QuestionnaireDistribution
    from app.models.notification import Notification
    from datetime import datetime
    count = 0
    for user in users:
        questionnaire = Questionnaire(
            custom_id=user.custom_id,
            questionnaire_type=template.questionnaire_type,
            name=template.name,
            version=template.version,
            notes=template.description,
            status="pending",
            template_id=template.id if hasattr(template, 'id') else None
        )
        db.add(questionnaire)
        db.flush()
        # 使用之前准备好的template_questions
        for q in template_questions:
            item = QuestionnaireItem(
                questionnaire_id=questionnaire.id,
                question_id=q.question_id,
                question_text=q.question_text,
                answer='',
                notes='',
                question_type=q.question_type,
                options=q.options,
                order=q.order,
                is_required=q.is_required,
                jump_logic=getattr(q, 'jump_logic', None),
            )
            db.add(item)
        distribution = QuestionnaireDistribution(
            questionnaire_id=questionnaire.id,
            user_id=user.id,
            distributor_id=current_user.id,
            status="pending",
            due_date=due_date,
            message=message
        )
        db.add(distribution)
        # 分发后通知
        notify = Notification(
            user_id=user.id,
            title="新问卷分发",
            content=f"您有新的问卷待填写：{template.name}",
            type="questionnaire",
            status="unread",
            created_at=datetime.utcnow()
        )
        db.add(notify)
        count += 1
    db.commit()
    return {"status": "success", "message": "问卷分发成功", "count": count}

class QuestionnaireReviewRequest(BaseModel):
    status: Optional[str] = "published"
    revision_reason: Optional[str] = None

@router.post("/questionnaire-templates/{template_id}/review", response_model=dict)
def review_questionnaire_template(
    template_id: int = Path(..., gt=0),
    review_data: QuestionnaireReviewRequest = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
):
    """审核问卷模板（通过/退回）"""
    template = db.query(QuestionnaireTemplate).filter(QuestionnaireTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="问卷模板不存在")
    # 权限校验（仅管理员可审核）
    if getattr(current_user, 'role', '') not in ["admin", "super_admin", "consultant"]:
        raise HTTPException(status_code=403, detail="无权限审核")
    # 审核通过/退回
    if review_data:
        if review_data.status:
            template.status = review_data.status
        if review_data.revision_reason is not None:
            template.revision_reason = review_data.revision_reason
    db.commit()
    db.refresh(template)
    return {
        "status": "success",
        "message": "审核操作成功",
        "data": {
            "id": template.id,
            "status": template.status,
            "revision_reason": getattr(template, 'revision_reason', None)
        }
    }