from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional

from app.db.session import get_db
from app.core.auth import get_current_active_user_custom
from app.models.user import User
from app.models.assessment import AssessmentResponse
from app.models.questionnaire import QuestionnaireResponse
from app.models.result import AssessmentResult, QuestionnaireResult

router = APIRouter()

@router.get("/assessment/response/{response_id}/dimension-scores")
def get_assessment_dimension_scores(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
) -> Dict[str, Any]:
    """
    获取评估回答的维度分数
    """
    try:
        # 查找评估回答
        response = db.query(AssessmentResponse).filter(
            AssessmentResponse.id == response_id
        ).first()
        
        if not response:
            raise HTTPException(status_code=404, detail="评估回答不存在")
        
        # 权限检查
        if (current_user.custom_id != response.custom_id and 
            not current_user.is_superuser and 
            current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]):
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 查找评估结果
        result = db.query(AssessmentResult).filter(
            AssessmentResult.assessment_id == response.assessment_id,
            AssessmentResult.custom_id == response.custom_id
        ).first()
        
        # 模拟维度分数数据（实际应用中应该从数据库或计算得出）
        dimension_scores = {
            "认知功能": response.score * 0.8 if response.score else 60,
            "情绪状态": response.score * 0.9 if response.score else 70,
            "行为表现": response.score * 0.7 if response.score else 50,
            "社交能力": response.score * 0.85 if response.score else 65,
            "生活质量": response.score * 0.75 if response.score else 55
        }
        
        analysis_report = f"基于评估结果的维度分析：\n" + \
                         f"总体得分：{response.score or 'N/A'}\n" + \
                         f"结果等级：{result.result_level if result else 'N/A'}\n" + \
                         f"各维度表现均衡，建议继续保持。"
        
        return {
            "status": "success",
            "dimension_scores": dimension_scores,
            "analysis_report": analysis_report,
            "total_score": response.score,
            "result_level": result.result_level if result else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取评估维度分数失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取维度分数失败")

@router.get("/questionnaire/response/{response_id}/dimension-scores")
def get_questionnaire_dimension_scores(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
) -> Dict[str, Any]:
    """
    获取问卷回答的维度分数
    """
    try:
        # 查找问卷回答
        response = db.query(QuestionnaireResponse).filter(
            QuestionnaireResponse.id == response_id
        ).first()
        
        if not response:
            raise HTTPException(status_code=404, detail="问卷回答不存在")
        
        # 权限检查
        if (current_user.custom_id != response.custom_id and 
            not current_user.is_superuser and 
            current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]):
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 查找问卷结果
        result = db.query(QuestionnaireResult).filter(
            QuestionnaireResult.response_id == response.id
        ).first()
        
        # 模拟维度分数数据
        dimension_scores = {
            "健康状况": response.total_score * 0.8 if hasattr(response, 'total_score') and response.total_score else 60,
            "生活习惯": response.total_score * 0.9 if hasattr(response, 'total_score') and response.total_score else 70,
            "心理状态": response.total_score * 0.7 if hasattr(response, 'total_score') and response.total_score else 50,
            "社会支持": response.total_score * 0.85 if hasattr(response, 'total_score') and response.total_score else 65
        }
        
        analysis_report = f"基于问卷结果的维度分析：\n" + \
                         f"总体得分：{getattr(response, 'total_score', 'N/A')}\n" + \
                         f"结果等级：{result.result_level if result else 'N/A'}\n" + \
                         f"各维度表现良好，建议继续关注健康管理。"
        
        return {
            "status": "success",
            "dimension_scores": dimension_scores,
            "analysis_report": analysis_report,
            "total_score": getattr(response, 'total_score', None),
            "result_level": result.result_level if result else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取问卷维度分数失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取维度分数失败")

@router.post("/assessment/response/{response_id}/recalculate-dimensions")
def recalculate_assessment_dimensions(
    response_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user_custom)
) -> Dict[str, Any]:
    """
    重新计算评估维度分数
    """
    try:
        # 查找评估回答
        response = db.query(AssessmentResponse).filter(
            AssessmentResponse.id == response_id
        ).first()
        
        if not response:
            raise HTTPException(status_code=404, detail="评估回答不存在")
        
        # 权限检查
        if (current_user.role not in ["admin", "super_admin", "unit_admin", "consultant"]):
            raise HTTPException(status_code=403, detail="权限不足")
        
        # 重新计算维度分数（这里是模拟逻辑）
        dimension_scores = {
            "认知功能": response.score * 0.8 if response.score else 60,
            "情绪状态": response.score * 0.9 if response.score else 70,
            "行为表现": response.score * 0.7 if response.score else 50,
            "社交能力": response.score * 0.85 if response.score else 65,
            "生活质量": response.score * 0.75 if response.score else 55
        }
        
        return {
            "status": "success",
            "message": "维度分数重新计算完成",
            "dimension_scores": dimension_scores
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"重新计算评估维度分数失败: {str(e)}")
        raise HTTPException(status_code=500, detail="重新计算维度分数失败")