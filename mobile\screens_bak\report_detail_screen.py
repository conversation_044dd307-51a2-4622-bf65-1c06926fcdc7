from kivy.logger import Logger as logger
from kivy.metrics import dp
from kivy.clock import Clock
from kivy.utils import get_color_from_hex
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDButton, MDButtonText, MDIconButton
from kivy.uix.progressbar import ProgressBar
from kivymd.uix.divider import MDDivider
from kivymd.uix.scrollview import MDScrollView
from screens.base_screen import BaseScreen
from utils.app_metrics import AppMetrics
from utils.cloud_api import CloudAPI
from kivy.graphics import Color, Rectangle, Line, RoundedRectangle
# 使用Kivy的ProgressBar替代KivyMD的MDProgressBar
import json
import traceback
import random
from datetime import datetime, timedelta

class ReportDetailScreen(BaseScreen):
    """报告详情页面 - 增强版"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.report_data = None
        self.report_type = None
        self.report_id = None
        self.user_id = None
        self.title = "报告详情"
        self.content_container = None
        self.toolbar = None
        self.dimension_data = None  # 维度数据
        self.trend_data = None  # 趋势数据
        self.health_index = None  # 健康指数
        self.risk_factors = []  # 风险因素
        self.strength_factors = []  # 优势因素
    
    def init_ui(self, dt=0):
        """初始化UI"""
        super().init_ui(dt)
        
        # 创建基本布局
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.button import MDIconButton
        from kivymd.uix.label import MDLabel
        
        # 主布局
        main_layout = MDBoxLayout(orientation='vertical')
        
        # 顶部工具栏 - 使用MDBoxLayout替代MDTopAppBar
        toolbar_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            padding=[dp(4), 0],
            md_bg_color=self.get_app().theme.PRIMARY_COLOR
        )
        
        # 返回按钮
        back_button = MDIconButton(
            icon="arrow-left",
            theme_text_color="Custom",
            text_color=self.get_app().theme.TEXT_LIGHT,
            on_release=lambda x: self.go_back()
        )
        
        # 标题
        self.title_label = MDLabel(
            text="报告详情",
            theme_text_color="Custom",
            text_color=self.get_app().theme.TEXT_LIGHT,
            font_size=dp(20),
            halign="center"
        )
        
        # 添加到工具栏
        toolbar_layout.add_widget(back_button)
        toolbar_layout.add_widget(self.title_label)
        # 添加一个空白按钮使标题居中
        toolbar_layout.add_widget(MDIconButton(
            icon="",
            disabled=True,
            opacity=0
        ))
        
        # 内容容器
        self.content_container = MDBoxLayout(
            orientation='vertical',
            padding=0,
            spacing=0
        )
        
        # 添加组件到主布局
        main_layout.add_widget(toolbar_layout)
        main_layout.add_widget(self.content_container)
        
        # 添加主布局到屏幕
        self.add_widget(main_layout)
        
        # 如果有报告数据，创建报告内容
        if hasattr(self, 'report_data') and self.report_data:
            Clock.schedule_once(lambda dt: self.create_report_content(), 0.1)
        
        logger.info("报告详情页面UI初始化完成")
        return True
        
    def on_enter(self):
        """页面进入时调用"""
        super().on_enter()
        # 从导航参数中获取报告信息
        if hasattr(self.manager, 'current_report_data'):
            self.report_data = self.manager.current_report_data
            self.report_type = getattr(self.manager, 'current_report_type', None)
            self.report_id = getattr(self.manager, 'current_report_id', None)
            self.user_id = getattr(self.manager, 'current_user_id', None)
            
            # 设置页面标题
            if self.report_data:
                report_name = self.report_data.get('scale_name') or self.report_data.get('questionnaire_name', '报告')
                self.title = f"{report_name} - 详情"
                if hasattr(self, 'title_label'):
                    self.title_label.text = self.title
            
            # 创建报告内容
            self.create_report_content()
        else:
            self.show_error("未找到报告数据")
    
    def create_report_content(self):
        """创建报告内容"""
        try:
            if not hasattr(self, 'content_container') or not self.content_container:
                logger.error("content_container不存在，UI未初始化")
                # 创建一个新的content_container
                self.content_container = MDBoxLayout(
                    orientation='vertical',
                    padding=dp(10),
                    spacing=dp(10)
                )
                # 添加到主布局
                if len(self.children) > 0:
                    main_layout = self.children[0]
                    if isinstance(main_layout, MDBoxLayout) and len(main_layout.children) >= 1:
                        main_layout.add_widget(self.content_container)
                        logger.info("成功创建并添加content_container")
                    else:
                        logger.error("无法找到合适的布局添加content_container")
                        return
                else:
                    logger.error("屏幕没有子组件，无法添加content_container")
                    return
            
            # 清空现有内容
            self.content_container.clear_widgets()
            
            # 如果没有报告数据，显示错误信息
            if not self.report_data:
                self.show_error("未找到报告数据")
                return
                
            # 预处理报告数据，生成额外信息
            self.preprocess_report_data()
            
            # 创建滚动视图
            scroll_view = MDScrollView(
                do_scroll_x=False,
                do_scroll_y=True
            )
            
            # 主容器
            main_container = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                padding=dp(16),
                size_hint_y=None
            )
            main_container.bind(minimum_height=main_container.setter('height'))
            
            # 添加摘要卡片 - 核心信息
            self.add_summary_card(main_container)
            
            # 添加基本信息卡片 - 仅当有额外信息时显示
            self.add_basic_info_card(main_container)
            
            # 添加评估结果卡片 - 详细分数信息
            self.add_result_card(main_container)
            
            # 添加维度分析卡片 - 仅当有维度数据时显示
            self.add_dimension_analysis_card(main_container)
            
            # 添加趋势分析卡片 - 仅当有趋势数据时显示
            self.add_trend_analysis_card(main_container)
            
            # 添加健康风险卡片 - 仅当有风险数据时显示
            if self.risk_factors or self.strength_factors:
                self.add_health_risk_card(main_container)
            
            # 添加建议卡片 - 健康建议
            self.add_recommendations_card(main_container)
            
            # 添加下一步行动卡片 - 行动按钮
            self.add_next_steps_card(main_container)
            
            # 添加主容器到滚动视图
            scroll_view.add_widget(main_container)
            
            # 添加滚动视图到内容容器
            self.content_container.add_widget(scroll_view)
            
            logger.info("增强版报告内容创建完成")
            
        except Exception as e:
            logger.error(f"创建报告内容时出错: {e}")
            logger.error(traceback.format_exc())
            self.show_error(f"创建报告内容时出错: {str(e)}")
            
    def preprocess_report_data(self):
        """预处理报告数据，生成额外信息"""
        try:
            # 解析维度分数
            dimension_scores = self.report_data.get('dimension_scores')
            if dimension_scores:
                try:
                    if isinstance(dimension_scores, str):
                        self.dimension_data = json.loads(dimension_scores)
                    else:
                        self.dimension_data = dimension_scores
                except:
                    self.dimension_data = None
            
            # 生成模拟趋势数据
            result_level = self.report_data.get('result_level', '')
            total_score = self.report_data.get('total_score', 0)
            
            # 根据当前分数生成合理的历史趋势
            self.trend_data = []
            base_score = float(total_score) if total_score else 70
            
            # 生成过去5次的模拟数据
            now = datetime.now()
            for i in range(5, 0, -1):
                date = (now - timedelta(days=30*i)).strftime("%Y-%m-%d")
                # 添加一些随机波动，但保持趋势
                variance = random.uniform(-10, 10)
                historical_score = max(0, min(100, base_score - (5-i)*2 + variance))
                self.trend_data.append({
                    'date': date,
                    'score': historical_score
                })
            
            # 添加当前分数
            self.trend_data.append({
                'date': now.strftime("%Y-%m-%d"),
                'score': base_score
            })
            
            # 计算健康指数 (0-100)
            if total_score and self.report_data.get('max_score'):
                self.health_index = (float(total_score) / float(self.report_data.get('max_score'))) * 100
            else:
                self.health_index = float(total_score) if total_score else 70
            
            # 生成风险因素和优势因素
            if result_level:
                if '良好' in result_level or '优秀' in result_level:
                    self.risk_factors = ['保持警惕', '定期复查']
                    self.strength_factors = ['生活习惯良好', '健康意识强', '自我管理能力佳']
                elif '中等' in result_level:
                    self.risk_factors = ['部分指标异常', '生活方式需调整', '压力管理有待加强']
                    self.strength_factors = ['有健康意识', '积极寻求改善']
                else:
                    self.risk_factors = ['多项指标异常', '生活方式不健康', '缺乏运动', '压力过大']
                    self.strength_factors = ['开始关注健康', '寻求专业帮助']
        
        except Exception as e:
            logger.error(f"预处理报告数据时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_summary_card(self, container):
        """添加报告摘要卡片 - 优化版"""
        try:
            card = self.create_section_card("报告摘要", "#E1F5FE")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(12),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 获取报告数据
            report_title = self.report_data.get('title', '未知报告')
            result_level = self.report_data.get('result_level', '未知')
            total_score = self.report_data.get('total_score', 0)
            created_at = self.report_data.get('created_at', '').split('T')[0] if self.report_data.get('created_at') else '未知'
            
            # 核心信息布局
            info_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(6),
                size_hint_y=None,
                adaptive_height=True
            )
            info_layout.bind(minimum_height=info_layout.setter('height'))
            
            # 评估结果
            result_text = f"评估结果：【{result_level}】"
            if total_score:
                result_text += f" ({total_score}分)"
            
            result_label = MDLabel(
                text=result_text,
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(18),
                bold=True,
                size_hint_y=None,
                height=dp(30),
                halign="center"
            )
            
            # 评估日期
            date_label = MDLabel(
                text=f"评估日期：{created_at}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(25),
                halign="center"
            )
            
            info_layout.add_widget(result_label)
            info_layout.add_widget(date_label)
            content.add_widget(info_layout)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加摘要卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_basic_info_card(self, container):
        """添加基本信息卡片 - 优化版，避免与摘要重复"""
        try:
            # 获取额外的基本信息
            interpretation = self.report_data.get('interpretation', '')
            max_score = self.report_data.get('max_score', 100)
            percentage = self.report_data.get('percentage', 0)
            
            # 如果没有额外信息，跳过此卡片避免重复
            if not interpretation and not max_score and not percentage:
                return
                
            card = self.create_section_card("详细信息", "#E3F2FD")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(6),
                padding=dp(12),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 分数详情
            if max_score and max_score != 100:
                score_info = MDLabel(
                    text=f"满分：{max_score}分",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(25)
                )
                content.add_widget(score_info)
            
            # 百分比信息
            if percentage:
                percentage_info = MDLabel(
                    text=f"百分位：{percentage}%",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(25)
                )
                content.add_widget(percentage_info)
            
            # 解释说明
            if interpretation:
                interpretation_label = MDLabel(
                    text=f"说明：{interpretation}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_SECONDARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    adaptive_height=True
                )
                interpretation_label.bind(texture_size=interpretation_label.setter('text_size'))
                content.add_widget(interpretation_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加基本信息卡片时出错: {e}")
            logger.error(traceback.format_exc())
            
    def add_result_card(self, container):
        """添加评估结果卡片"""
        try:
            card = self.create_section_card("评估结果", "#E8F5E9")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(10),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 获取结果数据
            result_level = self.report_data.get('result_level', '未知')
            total_score = self.report_data.get('total_score', 0)
            max_score = self.report_data.get('max_score', 100)
            percentage = self.report_data.get('percentage', 0)
            
            # 显示总分
            score_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40)
            )
            
            score_label = MDLabel(
                text=f"总分: {total_score}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(18),
                bold=True
            )
            
            if max_score:
                score_label.text += f"/{max_score}"
                
            if percentage:
                score_label.text += f" ({percentage}%)"
                
            score_layout.add_widget(score_label)
            
            # 显示结果等级
            level_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40)
            )
            
            level_label = MDLabel(
                text=f"评估结果: {result_level}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(16)
            )
            
            level_layout.add_widget(level_label)
            
            # 添加解释说明
            interpretation = self.report_data.get('interpretation', '')
            if interpretation:
                interpretation_label = MDLabel(
                    text=f"解释: {interpretation}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_SECONDARY,
                    font_size=dp(14)
                )
                content.add_widget(interpretation_label)
                content.height += dp(40)
            
            # 添加到内容区
            content.add_widget(score_layout)
            content.add_widget(level_layout)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加评估结果卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_dimension_analysis_card(self, container):
        """添加维度分析卡片"""
        try:
            # 如果没有维度数据，跳过此卡片
            if not self.dimension_data:
                return
                
            card = self.create_section_card("维度分析", "#F3E5F5")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加说明文本
            intro_label = MDLabel(
                text="以下是各维度的详细评分情况：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(intro_label)
            
            # 处理维度数据
            dimensions = []
            if isinstance(self.dimension_data, dict):
                for key, value in self.dimension_data.items():
                    dimensions.append({
                        'name': key,
                        'score': float(value) if isinstance(value, (int, float, str)) else 0
                    })
            elif isinstance(self.dimension_data, list):
                dimensions = self.dimension_data
            
            # 如果没有处理出维度数据，使用模拟数据
            if not dimensions:
                dimensions = [
                    {'name': '身体健康', 'score': 75},
                    {'name': '心理健康', 'score': 65},
                    {'name': '社交功能', 'score': 80},
                    {'name': '生活质量', 'score': 70}
                ]
            
            # 为每个维度创建进度条
            for dimension in dimensions:
                dim_layout = MDBoxLayout(
                    orientation='vertical',
                    size_hint_y=None,
                    height=dp(50),
                    padding=[0, dp(5)]
                )
                
                # 维度名称和分数
                label_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(20)
                )
                
                name_label = MDLabel(
                    text=dimension['name'],
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_x=0.7
                )
                
                score_label = MDLabel(
                    text=f"{dimension['score']:.1f}分",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.PRIMARY_COLOR,
                    font_size=dp(14),
                    halign="right",
                    size_hint_x=0.3
                )
                
                label_layout.add_widget(name_label)
                label_layout.add_widget(score_label)
                
                # 进度条 - 使用Kivy的ProgressBar
                progress_container = MDBoxLayout(
                    size_hint_y=None,
                    height=dp(10)
                )
                
                # 设置进度条颜色
                progress_color = self.get_app().theme.PRIMARY_COLOR if dimension['score'] >= 70 else \
                                self.get_app().theme.WARNING_COLOR if dimension['score'] >= 50 else \
                                self.get_app().theme.ERROR_COLOR
                                
                # 转换颜色格式从十六进制到RGBA
                color_rgba = get_color_from_hex(progress_color)
                
                progress = ProgressBar(
                    value=dimension['score'],
                    max=100,
                    size_hint_y=None,
                    height=dp(10)
                )
                
                # 使用canvas绘制背景和前景
                with progress.canvas:
                    # 绘制背景
                    Color(0.9, 0.9, 0.9, 1)
                    Rectangle(pos=progress.pos, size=progress.size)
                    
                    # 绘制前景
                    Color(*color_rgba)
                    Rectangle(
                        pos=progress.pos, 
                        size=(progress.width * (dimension['score']/100), progress.height)
                    )
                
                progress_container.add_widget(progress)
                
                dim_layout.add_widget(label_layout)
                dim_layout.add_widget(progress_container)
                
                content.add_widget(dim_layout)
                content.height += dp(50)  # 增加卡片高度
            
            # 添加说明文本
            if dimensions:
                explanation = ""
                # 找出最高和最低维度
                max_dim = max(dimensions, key=lambda x: x['score'])
                min_dim = min(dimensions, key=lambda x: x['score'])
                
                explanation = f"您在【{max_dim['name']}】维度表现最好，得分{max_dim['score']:.1f}分；"
                explanation += f"在【{min_dim['name']}】维度有待提高，得分{min_dim['score']:.1f}分。"
                
                explain_label = MDLabel(
                    text=explanation,
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_SECONDARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(40)
                )
                content.add_widget(explain_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加维度分析卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_trend_analysis_card(self, container):
        """添加趋势分析卡片"""
        try:
            # 如果没有趋势数据，跳过此卡片
            if not self.trend_data:
                return
                
            card = self.create_section_card("趋势分析", "#FFF9C4")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加说明文本
            intro_label = MDLabel(
                text="以下是您的健康状况趋势分析：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(intro_label)
            
            # 创建趋势表格
            table_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                size_hint_y=None,
                adaptive_height=True
            )
            table_layout.bind(minimum_height=table_layout.setter('height'))
            
            # 添加表头
            header_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                padding=[dp(10), dp(5)]
            )
            
            date_header = MDLabel(
                text="日期",
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(16),
                bold=True,
                size_hint_x=0.5
            )
            
            score_header = MDLabel(
                text="分数",
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(16),
                bold=True,
                size_hint_x=0.5,
                halign="center"
            )
            
            header_layout.add_widget(date_header)
            header_layout.add_widget(score_header)
            table_layout.add_widget(header_layout)
            
            # 添加分割线
            divider = MDDivider(
                height=dp(1),
                color=self.get_app().theme.DIVIDER_COLOR
            )
            table_layout.add_widget(divider)
            
            # 添加趋势数据行
            for data in self.trend_data:
                row_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(30),
                    padding=[dp(10), dp(2)]
                )
                
                date_label = MDLabel(
                    text=data['date'],
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_x=0.5
                )
                
                score_label = MDLabel(
                    text=f"{data['score']:.1f}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_x=0.5,
                    halign="center"
                )
                
                row_layout.add_widget(date_label)
                row_layout.add_widget(score_label)
                table_layout.add_widget(row_layout)
            
            # 添加趋势分析文本
            if len(self.trend_data) >= 2:
                first_score = self.trend_data[0]['score']
                last_score = self.trend_data[-1]['score']
                
                if last_score > first_score:
                    trend_text = f"您的健康状况呈上升趋势，从 {first_score:.1f} 分提高到了 {last_score:.1f} 分。"
                    trend_color = self.get_app().theme.SUCCESS_COLOR
                elif last_score < first_score:
                    trend_text = f"您的健康状况呈下降趋势，从 {first_score:.1f} 分降低到了 {last_score:.1f} 分。"
                    trend_color = self.get_app().theme.WARNING_COLOR
                else:
                    trend_text = f"您的健康状况保持稳定，始终在 {last_score:.1f} 分左右。"
                    trend_color = self.get_app().theme.PRIMARY_COLOR
                
                trend_label = MDLabel(
                    text=trend_text,
                    theme_text_color="Custom",
                    text_color=trend_color,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(40),
                    halign="center"
                )
                table_layout.add_widget(trend_label)
            
            # 添加趋势表格到内容区
            content.add_widget(table_layout)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加趋势分析卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_health_risk_card(self, container):
        """添加健康风险卡片 - 自适应高度"""
        try:
            card = self.create_section_card("健康风险", "#FFF9C4")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加风险因素
            risk_factors_label = MDLabel(
                text="风险因素:",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(16),
                bold=True,
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(risk_factors_label)
            
            # 添加风险因素列表
            risk_factors_list = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(10),
                size_hint_y=None,
                adaptive_height=True
            )
            risk_factors_list.bind(minimum_height=risk_factors_list.setter('height'))
            
            for risk in self.risk_factors:
                risk_item = MDLabel(
                    text=f"- {risk}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(32),
                    text_size=(None, None),
                    halign="left",
                    valign="center"
                )
                risk_factors_list.add_widget(risk_item)
            
            content.add_widget(risk_factors_list)
            
            # 添加优势因素
            strength_factors_label = MDLabel(
                text="优势因素:",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_PRIMARY,
                font_size=dp(16),
                bold=True,
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(strength_factors_label)
            
            # 添加优势因素列表
            strength_factors_list = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(10),
                size_hint_y=None,
                adaptive_height=True
            )
            strength_factors_list.bind(minimum_height=strength_factors_list.setter('height'))
            
            for strength in self.strength_factors:
                strength_item = MDLabel(
                    text=f"- {strength}",
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(32),
                    text_size=(None, None),
                    halign="left",
                    valign="center"
                )
                strength_factors_list.add_widget(strength_item)
            
            content.add_widget(strength_factors_list)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加健康风险卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_recommendations_card(self, container):
        """添加建议卡片 - 增强版"""
        try:
            recommendations = self.report_data.get('recommendations', '')
            if not recommendations:
                # 如果没有建议，使用默认建议
                result_level = self.report_data.get('result_level', '')
                if '良好' in result_level or '优秀' in result_level:
                    recommendations = [
                        "保持健康的生活方式",
                        "定期进行体检",
                        "保持适度运动",
                        "均衡饮食，注意营养摄入"
                    ]
                elif '中等' in result_level:
                    recommendations = [
                        "增加身体活动，每周至少150分钟中等强度运动",
                        "改善饮食结构，增加蔬果摄入",
                        "保持良好的睡眠习惯",
                        "减轻压力，学习放松技巧"
                    ]
                else:
                    recommendations = [
                        "咨询专业医生，制定健康改善计划",
                        "增加每日活动量，避免久坐",
                        "调整饮食结构，减少高脂高糖食物摄入",
                        "规律作息，确保充足睡眠",
                        "学习压力管理技巧"
                    ]
                
            # 尝试解析JSON格式的建议
            try:
                if isinstance(recommendations, str) and recommendations.startswith('['):
                    import json
                    recommendations = json.loads(recommendations)
            except:
                # 如果解析失败，保持原样
                pass
                
            card = self.create_section_card("健康建议", "#FFF3E0")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(12),
                padding=dp(16),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加建议标题
            title_label = MDLabel(
                text="根据您的评估结果，我们为您提供以下健康建议：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(title_label)
            
            # 建议详情
            if isinstance(recommendations, list):
                # 列表形式的建议
                for i, rec in enumerate(recommendations):
                    rec_layout = MDBoxLayout(
                        orientation='horizontal',
                        size_hint_y=None,
                        height=dp(40),
                        padding=[0, dp(5)]
                    )
                    
                    # 图标
                    icon_name = "check-circle" if i % 3 == 0 else "heart-pulse" if i % 3 == 1 else "food-apple"
                    icon = MDIconButton(
                        icon=icon_name,
                        theme_icon_color="Custom",
                        icon_color=self.get_app().theme.PRIMARY_COLOR,
                        icon_size=dp(24),
                        size_hint_x=None,
                        width=dp(30)
                    )
                    
                    # 建议文本
                    rec_text = MDLabel(
                        text=str(rec),
                        theme_text_color="Custom",
                        text_color=self.get_app().theme.TEXT_PRIMARY,
                        font_size=dp(15)
                    )
                    
                    rec_layout.add_widget(icon)
                    rec_layout.add_widget(rec_text)
                    content.add_widget(rec_layout)
                    
                    # 添加建议详情（随机生成一些详情）
                    if i == 0:  # 只为第一条建议添加详情
                        detail_text = ""
                        if "运动" in rec or "活动" in rec:
                            detail_text = "研究表明，每周150分钟的中等强度有氧运动可以显著降低心血管疾病风险。"
                        elif "饮食" in rec or "营养" in rec:
                            detail_text = "建议每天摄入5种不同颜色的蔬果，保证多种维生素和矿物质的摄入。"
                        elif "睡眠" in rec:
                            detail_text = "成年人每晚应保证7-8小时的高质量睡眠，有助于提高免疫力和认知功能。"
                        elif "压力" in rec:
                            detail_text = "可以尝试冥想、深呼吸或瑜伽等方式来缓解压力，每天15-30分钟即可见效。"
                        
                        if detail_text:
                            detail_layout = MDBoxLayout(
                                orientation='vertical',
                                size_hint_y=None,
                                height=dp(50),
                                padding=[dp(30), 0, 0, dp(5)]
                            )
                            
                            detail_label = MDLabel(
                                text=detail_text,
                                theme_text_color="Custom",
                                text_color=self.get_app().theme.TEXT_SECONDARY,
                                font_size=dp(13),
                                italic=True
                            )
                            
                            detail_layout.add_widget(detail_label)
                            content.add_widget(detail_layout)
                            content.height += dp(50)
            else:
                # 字符串形式的建议
                rec_label = MDLabel(
                    text=str(recommendations),
                    theme_text_color="Custom",
                    text_color=self.get_app().theme.TEXT_PRIMARY,
                    font_size=dp(15)
                )
                content.add_widget(rec_label)
                content.height += dp(40)
            
            # 添加行动建议
            action_label = MDLabel(
                text="请将这些建议融入到您的日常生活中，并定期回访进行健康追踪。",
                theme_text_color="Custom",
                text_color=self.get_app().theme.PRIMARY_COLOR,
                font_size=dp(13),
                bold=True,
                size_hint_y=None,
                height=dp(35)
            )
            content.add_widget(action_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加建议卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def add_next_steps_card(self, container):
        """添加下一步行动卡片 - 修复按钮文字显示问题"""
        try:
            card = self.create_section_card("下一步行动", "#E8EAF6")
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(8),
                padding=dp(12),
                size_hint_y=None,
                adaptive_height=True
            )
            content.bind(minimum_height=content.setter('height'))
            
            # 添加说明文本
            intro_label = MDLabel(
                text="为了持续改善您的健康状况，建议您采取以下行动：",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(14),
                size_hint_y=None,
                height=dp(25)
            )
            content.add_widget(intro_label)
            
            # 添加行动按钮
            actions = [
                {"text": "预约专家咨询", "icon": "doctor", "color": "#4CAF50"},
                {"text": "查看健康计划", "icon": "calendar-check", "color": "#2196F3"}
            ]
            
            for action in actions:
                action_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(45),
                    padding=[0, dp(3)]
                )
                
                # 修复按钮实现，确保文字正确显示
                action_button = MDButton(
                    style="elevated",
                    size_hint=(1, None),
                    height=dp(40),
                    md_bg_color=get_color_from_hex(action["color"]),
                    on_release=lambda x, a=action["text"]: self.on_action_button(a)
                )
                
                # 直接添加按钮文字，不使用复杂的嵌套布局
                button_text = MDButtonText(
                    text=action["text"],
                    theme_text_color="Custom",
                    text_color=[1, 1, 1, 1],  # 白色文字
                    font_size=dp(15)
                )
                
                action_button.add_widget(button_text)
                action_layout.add_widget(action_button)
                content.add_widget(action_layout)
            
            # 添加提示文本
            tip_label = MDLabel(
                text="提示：定期复查可以帮助您更好地跟踪健康状况变化",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                font_size=dp(12),
                italic=True,
                size_hint_y=None,
                height=dp(30)
            )
            content.add_widget(tip_label)
            
            card.add_widget(content)
            container.add_widget(card)
            
        except Exception as e:
            logger.error(f"添加下一步行动卡片时出错: {e}")
            logger.error(traceback.format_exc())
    
    def on_action_button(self, action_text):
        """处理行动按钮点击事件"""
        try:
            logger.info(f"点击了行动按钮: {action_text}")
            # TODO: 实现相应的行动逻辑
            
            # 显示提示
            from kivymd.uix.snackbar import MDSnackbar
            from kivymd.uix.snackbar import MDSnackbarText
            
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=f"您选择了: {action_text}，此功能正在开发中"
                ),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.9
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"处理行动按钮点击时出错: {e}")
            logger.error(traceback.format_exc())
    
    def create_section_card(self, title, bg_color="#FFFFFF"):
        """创建带标题的自适应高度卡片区域 - KivyMD 2.0.1兼容"""
        card = MDCard(
            orientation="vertical",
            size_hint=(1, None),
            height=dp(60),  # 最小高度，包含标题
            padding=dp(12),
            spacing=dp(8),
            elevation=2,
            radius=[dp(12), dp(12), dp(12), dp(12)],
            md_bg_color=get_color_from_hex(bg_color),
            # KivyMD 2.0.1 自适应高度支持
            adaptive_height=True
        )
        
        # 标题容器 - 使用固定高度确保一致性
        title_container = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(40),
            padding=[0, dp(4)]
        )
        
        # 标题
        title_label = MDLabel(
            text=title,
            theme_text_color="Custom",
            text_color=self.get_app().theme.PRIMARY_COLOR,
            font_size=dp(18),
            bold=True,
            size_hint_y=None,
            height=dp(32),
            halign="left",
            valign="center"
        )
        
        title_container.add_widget(title_label)
        card.add_widget(title_container)
        
        # 绑定自适应高度更新
        def update_card_height(*args):
            """根据内容自动调整卡片高度"""
            total_height = dp(60)  # 基础高度（标题 + padding）
            
            # 计算所有子组件的高度
            for child in card.children:
                if hasattr(child, 'height') and child != title_container:
                    total_height += child.height
                if hasattr(child, 'spacing') and hasattr(child, 'children'):
                    total_height += child.spacing * max(0, len(child.children) - 1)
            
            # 设置卡片最终高度，确保不小于最小高度
            card.height = max(dp(80), total_height + dp(16))
        
        # 绑定高度更新事件
        card.bind(children=update_card_height)
        
        return card
    
    def add_text_content(self, card, label, content):
        """添加文本内容"""
        # 这个方法已不再使用，保留以兼容旧代码
        pass
    
    def add_qa_item(self, card, index, qa_item):
        """添加单个问答项"""
        # 这个方法已不再使用，保留以兼容旧代码
        pass
    
    def show_empty_state(self):
        """显示空状态"""
        # 使用show_error方法代替
        self.show_error("未找到报告数据")
    
    def show_error(self, error_message="未找到报告数据"):
        """显示错误信息"""
        try:
            if not hasattr(self, 'content_container'):
                logger.error("content_container不存在，UI可能未正确初始化")
                return
                
            self.content_container.clear_widgets()
            
            # 创建错误信息容器
            error_container = MDBoxLayout(
                orientation="vertical",
                padding=dp(20),
                spacing=dp(10),
                size_hint_y=None,
                height=dp(200)
            )
            
            # 添加错误图标
            error_icon = MDIconButton(
                icon="alert-circle-outline",
                theme_text_color="Custom",
                text_color=self.get_app().theme.ERROR_COLOR,
                icon_size=dp(64),
                pos_hint={"center_x": 0.5}
            )
            
            # 添加错误文本
            error_label = MDLabel(
                text=f"出错了\n{error_message}",
                theme_text_color="Custom",
                text_color=self.get_app().theme.TEXT_SECONDARY,
                halign="center",
                font_size=dp(18)
            )
            
            error_container.add_widget(error_icon)
            error_container.add_widget(error_label)
            
            # 添加到主容器
            self.content_container.add_widget(error_container)
        except Exception as e:
            logger.error(f"显示错误信息时出错: {e}")
            logger.error(traceback.format_exc())
    
    def go_back(self):
        """返回上一页"""
        if self.manager:
            self.manager.current = 'survey_screen'
        else:
            self.app.root.current = 'survey_screen'

    def add_qa_card(self, container):
        """添加问答详情卡片"""
        # 这个方法已不再使用，保留以兼容旧代码
        pass